body {
  font-family: '<PERSON><PERSON>', sans-serif;
  background-color: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card {
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);
  background-color: #ffffff;
  color: #212121;
  border-radius: 2px;
  margin: 15px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 400px;
  max-width: 90vw;
}

.card .card-content {
  padding: 15px 25px;
  border-bottom: 1px solid #bdbdbd;
}

.card .card-actions {
  padding: 15px 25px;
}

h1 {
  font-size: 24px;
}

img {
  max-height: 60px;
}

ol {
  margin: 28px 0 0 0;
  padding: 0;
  list-style-type: none;
}

ol li {
  counter-increment: step-counter;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

ol li::before {
  content: counter(step-counter);
  margin-right: 5px;
  padding: 3px 8px;
  border-radius: 9999px;
  border: 1px solid #bdbdbd;
}

input {
  border: none;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  border-bottom: 1px solid black;
  font-size: 14px;
  flex-grow: 1;
  height: 30px;
  width: 100%;
  background: transparent;
  outline: none;
  transition: border-bottom 0.225s ease;
}

input:focus {
  border-bottom: 2px solid black;
}

button {
  font-size: 16px;
  padding: 8px 10px;
  background-color: transparent;
  border: none;
  color: #03a9f4;
  cursor: pointer;
  border-radius: 4px;
  outline: none;
  transition: background-color 0.225s ease;
}

button:hover {
  background-color: #03a8f425;
}

button:active {
  background-color: #03a8f44f;
}
