import 'package:get/get.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/components/permission_widgets.dart';

HomeController controller = Get.find();

commandRoom(bool command, String roomId) {
  // التحقق من صلاحية التحكم في الأجهزة
  if (!controller.canControlDevices()) {
    showNoPermissionDialog(
        customMessage: 'ليس لديك صلاحية للتحكم في أجهزة الغرفة');
    return;
  }

  if (client.connectionStatus!.state.name == 'connected') {
    controller.rooms[roomId]['state'] = command;
    for (var i in controller.rooms[roomId]['devices'].values) {
      if (i['device'].contains('ZAIN') != true && controller.devices[i['id']]) {
        final builder = MqttClientPayloadBuilder();

        if (controller.rooms[roomId]['devices'][i['id']]['state'] == null) {
          continue;
        }
        bool state = controller.rooms[roomId]['devices'][i['id']]['state'];
        // if (i['device'] != 'ZAIN') {
        //   controller.rooms[roomId]['devices'][i['id']]['state'] = command;
        // }
        if (i['device'] == 'AC') {
          if (command == true && state != true) {
            builder.addString(i['id'] +
                ' AC RUN ' +
                i['degree'].toInt().toString() +
                ' VAN ' +
                i['speed'].toString() +
                ' ' +
                (i['type'].toString() == 'تبريد'
                    ? 'AC'
                    : i['type'].toString() == 'تدفئه'
                        ? 'HEAT'
                        : 'VAN'));
            client.publishMessage(controller.homeId + "/app/zain",
                MqttQos.atLeastOnce, builder.payload!);
            controller.rooms[roomId]['devices'][i['id']]['state'] = command;
          } else if (command == false && state != false) {
            builder.addString(i['id'] + ' AC OFF');
            client.publishMessage(controller.homeId + "/app/zain",
                MqttQos.atLeastOnce, builder.payload!);
            controller.rooms[roomId]['devices'][i['id']]['state'] = command;
          }
        } else if (i['device'] == 'TV') {
          if (command == true && state != true) {
            builder.addString(i['id'] + ' TV POWER-ON');
            client.publishMessage(controller.homeId + "/app/zain",
                MqttQos.atLeastOnce, builder.payload!);
            controller.rooms[roomId]['devices'][i['id']]['state'] = command;
          } else if (command == false && state != false) {
            builder.addString(i['id'] + ' TV POWER-OFF');
            client.publishMessage(controller.homeId + "/app/zain",
                MqttQos.atLeastOnce, builder.payload!);
            controller.rooms[roomId]['devices'][i['id']]['state'] = command;
          }
        } else if (i['device'] == 'SWITCH') {
          controller.rooms[roomId]['devices'][i['id']]['state'] = command;
          String pubMassege = i['id'] + ' SWITCH';
          for (var j in i.keys.toList()) {
            if (j != 'id' &&
                j != 'device' &&
                j != 'state' &&
                j != 'pub' &&
                j != 'priv' &&
                j != 'pubName' &&
                j != 'privName') {
              if (command == true) {
                pubMassege += ' ' + j + '_RUN';
              } else {
                pubMassege += ' ' + j + '_OFF';
              }

              controller.rooms[roomId]['devices'][i['id']][j]['state'] =
                  command;
            }
          }
          builder.addString(pubMassege);
          client.publishMessage(controller.homeId + "/app/zain",
              MqttQos.atLeastOnce, builder.payload!);
        }
      }
    }
    // });
    print('22222222222222');
    if (command == true) {
      controller.homeState = true;
      print('333333333333true');
    } else {
      controller.homeState = false;
      print('33333333333false');
      for (var i in controller.rooms.values) {
        if (i['state'] == true) {
          controller.homeState = true;
          print('222222222222225555555555');
        }
      }
    }

    controller.update();
  }
}
