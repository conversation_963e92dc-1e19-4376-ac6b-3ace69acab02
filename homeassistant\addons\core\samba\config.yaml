---
version: 12.5.2
slug: samba
name: Samba share
description: Expose Home Assistant folders with SMB/CIFS
url: https://github.com/home-assistant/addons/tree/master/samba
codenotary: <EMAIL>
arch:
  - armhf
  - armv7
  - aarch64
  - amd64
  - i386
hassio_api: true
host_network: true
image: homeassistant/{arch}-addon-samba
init: false
map:
  - addons:rw
  - all_addon_configs:rw
  - backup:rw
  - homeassistant_config:rw
  - media:rw
  - share:rw
  - ssl:rw
options:
  username: homeassistant
  password: null
  workgroup: WORKGROUP
  local_master: true
  enabled_shares:
    - addons
    - addon_configs
    - backup
    - config
    - media
    - share
    - ssl
  compatibility_mode: false
  apple_compatibility_mode: true
  veto_files:
    - ._*
    - .DS_Store
    - Thumbs.db
    - icon?
    - .Trashes
  allow_hosts:
    - 10.0.0.0/8
    - **********/12
    - ***********/16
    - ***********/16
    - fe80::/10
    - fc00::/7
schema:
  username: str
  password: password
  workgroup: str
  local_master: bool
  enabled_shares:
    - "match(^(?i:(addons|addon_configs|backup|config|media|share|ssl))$)"
  compatibility_mode: bool
  apple_compatibility_mode: bool
  veto_files:
    - str
  allow_hosts:
    - str
startup: services
