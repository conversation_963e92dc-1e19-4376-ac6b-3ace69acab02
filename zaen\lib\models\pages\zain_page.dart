import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/themes/app_colors.dart';

int? _activePage;

Widget ZainPage(
    {String? id,
    bool deviceState = false,
    String? roomN,
    required Function() Dfavorite,
    required Function() editRoom,
    required Function(String?) editNames,
    required Function(bool?, String?) editPrivName,
    required List alarmList,
    Function()? addAlarm,
    required Function(int? id, int? h, int? m, bool? isAM, List? days,
            bool? allday, bool? re)
        editAlarm,
    required bool connect,
    required String ZName,
    bool Zmain = false,
    required double sizedWidth,
    required double sizedHeight,
    required double sized,
    var conn}) {
  bool privN = false;
  if (_activePage == null) {
    _activePage = 0;
  }
  PageController _pageController = PageController(initialPage: _activePage!);

  // the index of the current page

  // this list holds all the pages
  // all of them are constructed in the very end of this file for readability

  return pageSlide(
    content: GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: StatefulBuilder(
        builder: ((context, setState) => PageView(
              scrollDirection: Axis.vertical,
              // shrinkWrap: true,
              onPageChanged: (i) {
                FocusManager.instance.primaryFocus?.unfocus();
                setState(() {
                  _activePage;
                });
              },
              physics: BouncingScrollPhysics(),
              children:
                  connect == true &&
                          client.connectionStatus!.state.name == 'connected'
                      ? [
                          Container(
                            child: SingleChildScrollView(
                              physics: BouncingScrollPhysics(
                                  parent: NeverScrollableScrollPhysics()),
                              child: Column(
                                children: [
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: sizedWidth * 0.035),
                                    child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Zmain
                                              ? Padding(
                                                  padding: EdgeInsets.only(
                                                      right: sizedWidth * 0.01),
                                                  child: IconButton(
                                                    onPressed: () {},
                                                    icon: iconStyle(
                                                      icon: Icons.star_rounded,
                                                      color: AppColors
                                                          .warningColor,
                                                    ),
                                                  ))
                                              : Container(),
                                          Expanded(
                                            child: Container(
                                              alignment: Alignment.bottomRight,
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.end,
                                                children: [
                                                  Container(
                                                      width: controller
                                                              .sizedWidth *
                                                          0.43,
                                                      padding: EdgeInsets.only(
                                                          right:
                                                              controller.sized *
                                                                  0.01),
                                                      child: txtStyle(
                                                          align:
                                                              TextAlign.right,
                                                          txt: 'زين')),
                                                  Container(
                                                      padding: EdgeInsets.only(
                                                          left: controller
                                                                  .sizedWidth *
                                                              0.01),
                                                      decoration: BoxDecoration(
                                                          border: Border(
                                                              left: BorderSide(
                                                                  color: AppColors
                                                                      .textColor
                                                                      .withOpacity(
                                                                          0.25),
                                                                  width: 1.5))),
                                                      child: txtStyle(
                                                          align:
                                                              TextAlign.right,
                                                          txt: 'مساعد صوتي',
                                                          color: AppColors
                                                              .textColor3)),
                                                ],
                                              ),
                                            ),
                                          ),
                                          SizedBox(
                                            width: sizedWidth * 0.01,
                                          ),
                                          iconStyle(
                                            icon: Icons.flutter_dash_rounded,
                                            color: AppColors.warningColor,
                                          ),
                                        ]),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          GetBuilder<HomeController>(
                              builder: (controller) => Column(
                                    children: [
                                      // the page view
                                      SizedBox(
                                        height: controller.sizedHight * 0.68,
                                        child: PageView(
                                          controller: _pageController,
                                          physics: BouncingScrollPhysics(
                                              parent:
                                                  NeverScrollableScrollPhysics()),
                                          // onPageChanged: (int page) {
                                          //   setState(() {
                                          //     _activePage = page;
                                          //   });
                                          //   print(_activePage);
                                          // },
                                          children: [
                                            Column(
                                              children: [
                                                Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal:
                                                          sizedWidth * 0.05),
                                                  child: Row(children: [
                                                    IconButton(
                                                      onPressed: controller
                                                              .canManageData()
                                                          ? addAlarm
                                                          : () {
                                                              showNoPermissionDialog(
                                                                  customMessage:
                                                                      'ليس لديك صلاحية لإدارة المنبهات');
                                                            },
                                                      icon:
                                                          Icon(Icons.add_alarm),
                                                      iconSize: sized * 0.03,
                                                      color: controller
                                                              .canManageData()
                                                          ? AppColors
                                                              .warningColor
                                                          : AppColors.textHint,
                                                    ),
                                                    Expanded(
                                                        child: Text('المنبه',
                                                            textDirection:
                                                                TextDirection
                                                                    .rtl,
                                                            style: TextStyle(
                                                              color: AppColors
                                                                  .textColor2,
                                                              fontSize:
                                                                  sized * 0.023,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                            )))
                                                  ]),
                                                ),
                                                Divider(
                                                    color: AppColors.textColor2
                                                        .withOpacity(0.25)),
                                                Expanded(
                                                  child: SingleChildScrollView(
                                                    physics: ScrollPhysics(
                                                        parent:
                                                            BouncingScrollPhysics()),
                                                    child: Column(children: [
                                                      for (var alarm
                                                          in alarmList.reversed)
                                                        GestureDetector(
                                                          onLongPress: controller
                                                                  .canManageData()
                                                              ? () {
                                                                  print(alarm[
                                                                      'wday']);
                                                                  print(
                                                                      '222222222222222222222222222222222222');

                                                                  editAlarm(
                                                                      alarm[
                                                                          'id'],
                                                                      int.parse(
                                                                          alarm['clock'].split(':')[
                                                                              0]),
                                                                      int.parse(
                                                                          alarm['clock'].replaceAll(RegExp(r'\s?(AM|PM)'), '').split(':')[
                                                                              1]),
                                                                      alarm['clock'].contains(
                                                                              'AM')
                                                                          ? true
                                                                          : false,
                                                                      alarm['wday'] !=
                                                                              'None'
                                                                          ? [
                                                                              for (var d in alarm['wday'].split(' '))
                                                                                d == 'Sat'
                                                                                    ? 'سبت'
                                                                                    : d == 'Sun'
                                                                                        ? 'أحد'
                                                                                        : d == 'Mon'
                                                                                            ? 'إثنين'
                                                                                            : d == 'Tue'
                                                                                                ? 'ثلاثاء'
                                                                                                : d == 'Wed'
                                                                                                    ? 'اربعاء'
                                                                                                    : d == 'Thu'
                                                                                                        ? 'خميس'
                                                                                                        : 'جمعة'
                                                                            ]
                                                                          : [],
                                                                      alarm['wday'].split(' ').length ==
                                                                              7
                                                                          ? true
                                                                          : false,
                                                                      alarm['re'] ==
                                                                              'ON'
                                                                          ? true
                                                                          : false);
                                                                }
                                                              : () {
                                                                  showNoPermissionDialog(
                                                                      customMessage:
                                                                          'ليس لديك صلاحية لتعديل المنبهات');
                                                                },
                                                          child: Column(
                                                            children: [
                                                              Directionality(
                                                                textDirection:
                                                                    TextDirection
                                                                        .rtl,
                                                                child:
                                                                    Container(
                                                                        padding: EdgeInsets.symmetric(
                                                                            horizontal: controller.sizedWidth *
                                                                                0.02),
                                                                        child:
                                                                            Column(
                                                                          children: [
                                                                            Row(
                                                                              crossAxisAlignment: CrossAxisAlignment.end,
                                                                              children: [
                                                                                Text(
                                                                                  alarm['clock'].replaceAll(RegExp(r'\s?(AM|PM)'), '').trim(),
                                                                                  textDirection: TextDirection.rtl,
                                                                                  style: TextStyle(
                                                                                    color: AppColors.warningColor,
                                                                                    fontSize: controller.sized * 0.04,
                                                                                  ),
                                                                                ),
                                                                                SizedBox(
                                                                                  width: controller.sizedWidth * 0.005,
                                                                                ),
                                                                                Expanded(
                                                                                  child: Padding(
                                                                                    padding: EdgeInsets.only(bottom: controller.sizedHight * 0.01, right: controller.sizedHight * 0.005),
                                                                                    child: Text(
                                                                                      alarm['clock'].contains('AM') ? 'صباحاً' : 'مسائاً',
                                                                                      textDirection: TextDirection.rtl,
                                                                                      style: TextStyle(color: AppColors.textColor2, fontSize: controller.sized * 0.015, fontWeight: FontWeight.bold),
                                                                                    ),
                                                                                  ),
                                                                                ),
                                                                                Padding(
                                                                                    padding: EdgeInsets.all(controller.sized * 0.0035),
                                                                                    child: switchStyle(
                                                                                        onChanged: (val) async {
                                                                                          await conn.query("UPDATE Alarm SET state = ? WHERE id = ?", [
                                                                                            val! ? 'ON' : 'STOP',
                                                                                            alarm['id']
                                                                                          ]);
                                                                                          final builder = MqttClientPayloadBuilder();
                                                                                          builder.addString('1');
                                                                                          client.publishMessage('edit', MqttQos.atLeastOnce, builder.payload!);
                                                                                        },
                                                                                        size: controller.sized * 0.0008,
                                                                                        value: alarm['state'] == 'ON' ? true : false)),
                                                                              ],
                                                                            ),
                                                                            Padding(
                                                                              padding: EdgeInsets.symmetric(horizontal: sizedWidth * 0.01),
                                                                              child: Row(
                                                                                children: [
                                                                                  Text(
                                                                                    'سبت',
                                                                                    textDirection: TextDirection.rtl,
                                                                                    style: TextStyle(color: alarm['wday'].contains(weekDays['سبت']) ? AppColors.warningColor : AppColors.backgroundColor2, fontSize: controller.sized * 0.012, fontWeight: FontWeight.bold),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width: sizedWidth * 0.02,
                                                                                  ),
                                                                                  Text(
                                                                                    'أحد',
                                                                                    textDirection: TextDirection.rtl,
                                                                                    style: TextStyle(color: alarm['wday'].contains(weekDays['أحد']) ? AppColors.warningColor : AppColors.backgroundColor2, fontSize: controller.sized * 0.012, fontWeight: FontWeight.bold),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width: sizedWidth * 0.02,
                                                                                  ),
                                                                                  Text(
                                                                                    'إثنين',
                                                                                    textDirection: TextDirection.rtl,
                                                                                    style: TextStyle(color: alarm['wday'].contains(weekDays['إثنين']) ? AppColors.warningColor : AppColors.backgroundColor2, fontSize: controller.sized * 0.012, fontWeight: FontWeight.bold),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width: sizedWidth * 0.02,
                                                                                  ),
                                                                                  Text(
                                                                                    'ثلاثاء',
                                                                                    textDirection: TextDirection.rtl,
                                                                                    style: TextStyle(color: alarm['wday'].contains(weekDays['ثلاثاء']) ? AppColors.warningColor : AppColors.backgroundColor2, fontSize: controller.sized * 0.012, fontWeight: FontWeight.bold),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width: sizedWidth * 0.02,
                                                                                  ),
                                                                                  Text(
                                                                                    'اربعاء',
                                                                                    textDirection: TextDirection.rtl,
                                                                                    style: TextStyle(color: alarm['wday'].contains(weekDays['اربعاء']) ? AppColors.warningColor : AppColors.backgroundColor2, fontSize: controller.sized * 0.012, fontWeight: FontWeight.bold),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width: sizedWidth * 0.02,
                                                                                  ),
                                                                                  Text(
                                                                                    'خميس',
                                                                                    textDirection: TextDirection.rtl,
                                                                                    style: TextStyle(color: alarm['wday'].contains(weekDays['خميس']) ? AppColors.warningColor : AppColors.backgroundColor2, fontSize: controller.sized * 0.012, fontWeight: FontWeight.bold),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width: sizedWidth * 0.02,
                                                                                  ),
                                                                                  Text(
                                                                                    'جمعة',
                                                                                    textDirection: TextDirection.rtl,
                                                                                    style: TextStyle(color: alarm['wday'].contains(weekDays['جمعة']) ? AppColors.warningColor : AppColors.backgroundColor2, fontSize: controller.sized * 0.012, fontWeight: FontWeight.bold),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width: sizedWidth * 0.05,
                                                                                  ),
                                                                                  Icon(
                                                                                    Icons.refresh_rounded,
                                                                                    size: sized * 0.018,
                                                                                    color: alarm['re'].contains('ON') ? AppColors.warningColor : AppColors.backgroundColor2,
                                                                                  )
                                                                                ],
                                                                              ),
                                                                            )
                                                                          ],
                                                                        )),
                                                              ),
                                                              Divider(
                                                                color: AppColors
                                                                    .textColor2
                                                                    .withOpacity(
                                                                        0.25),
                                                                endIndent:
                                                                    controller
                                                                            .sizedWidth *
                                                                        0.1,
                                                              )
                                                            ],
                                                          ),
                                                        )
                                                    ]),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Container(
                                              alignment: Alignment.center,
                                              child: Text('التذكيرات',
                                                  style: TextStyle(
                                                      color:
                                                          AppColors.textColor2,
                                                      fontSize: sized * 0.02)),
                                            ),
                                            Container(
                                              alignment: Alignment.center,
                                              child: Text('المؤقت',
                                                  style: TextStyle(
                                                      color:
                                                          AppColors.textColor2,
                                                      fontSize: sized * 0.02)),
                                            )
                                          ],
                                        ),
                                      ),
                                      // Display the dots indicator

                                      Divider(
                                          color: AppColors.textColor2
                                              .withOpacity(0.25)),
                                      Container(
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: List<Widget>.generate(
                                              3,
                                              (index) => Padding(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal:
                                                                sizedWidth *
                                                                    0.1),
                                                    child: InkWell(
                                                        onTap: () {
                                                          _pageController.animateToPage(
                                                              index,
                                                              duration:
                                                                  const Duration(
                                                                      milliseconds:
                                                                          3),
                                                              curve: Curves
                                                                  .easeIn);
                                                          setState(() {
                                                            _activePage = index;
                                                          });
                                                        },
                                                        child: Column(
                                                          children: [
                                                            Icon(
                                                              index == 0
                                                                  ? Icons
                                                                      .alarm_rounded
                                                                  : index == 1
                                                                      ? Icons
                                                                          .calendar_today
                                                                      : Icons
                                                                          .timer_rounded,
                                                              size: _activePage == index
                                                                  ? controller
                                                                          .sized *
                                                                      0.033
                                                                  : controller
                                                                          .sized *
                                                                      0.03,
                                                              color: _activePage ==
                                                                      index
                                                                  ? AppColors
                                                                      .warningColor
                                                                  : AppColors
                                                                      .textColor3,
                                                            ),
                                                            Text(
                                                              index == 0
                                                                  ? 'منبه'
                                                                  : index == 1
                                                                      ? 'تذكيرات'
                                                                      : 'مـؤقت',
                                                              style: TextStyle(
                                                                  fontSize: controller
                                                                          .sized *
                                                                      0.012,
                                                                  color: _activePage ==
                                                                          index
                                                                      ? AppColors
                                                                          .warningColor
                                                                      : AppColors
                                                                          .textColor3,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold),
                                                            )
                                                          ],
                                                        )
                                                        // CircleAvatar(
                                                        //   radius: _activePage == index ? controller.sized * 0.004 : controller.sized * 0.0035,
                                                        //   // check if a dot is connected to the current page
                                                        //   // if true, give it a different color
                                                        //   backgroundColor: _activePage == index ? AppColors.warningColor : AppColors.textColor2.withOpacity(0.5),
                                                        // ),
                                                        ),
                                                  )),
                                        ),
                                      ),
                                    ],
                                  )),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: sizedWidth * 0.03),
                            child: Column(
                              children: [
                                SizedBox(
                                  height: sizedHeight * 0.01,
                                ),
                                MaterialButton(
                                  padding: EdgeInsets.zero,
                                  onPressed: () {
                                    roomN = editRoom();
                                    print(roomN);
                                  },
                                  child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.arrow_drop_down,
                                          size: sized * 0.03,
                                          color: AppColors.textColor3,
                                        ),
                                        Expanded(
                                            child: SizedBox(
                                                width: double.infinity)),
                                        Text(
                                          'الغرفة : $roomN',
                                          style: TextStyle(
                                              fontSize: sized * 0.015,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.textColor3),
                                        ),
                                      ]),
                                ),
                                SizedBox(
                                  height: sizedHeight * 0.05,
                                ),
                                containerPageOption(
                                  content: MaterialButton(
                                      padding: EdgeInsets.zero,
                                      onPressed: Dfavorite,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          iconStyle(
                                              icon: Icons.favorite,
                                              size: controller.sized * 0.02,
                                              color: controller.favorite
                                                      .contains(id)
                                                  ? AppColors.errorColor
                                                  : AppColors.textColor3),
                                          Expanded(
                                              child: SizedBox(
                                                  width: double.infinity)),
                                          txtStyle(
                                              txt: 'الملحقات المفضله',
                                              color: AppColors.textColor2),
                                        ],
                                      )),
                                ),
                                SizedBox(
                                  height: controller.sizedHight * 0.015,
                                ),
                              ],
                            ),
                          )
                        ]
                      : [
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: sizedWidth * 0.035),
                            child: Column(
                              children: [
                                Row(mainAxisSize: MainAxisSize.min, children: [
                                  Text(
                                    'غير متصل',
                                    textDirection: TextDirection.rtl,
                                    style: TextStyle(
                                        color: AppColors.errorColor,
                                        fontSize: sized * 0.013,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  Expanded(
                                    child: Container(
                                      margin: EdgeInsets.zero,
                                      // padding: EdgeInsets.symmetric(horizontal: 5),
                                      // color: Colors.blueGrey.shade600,
                                      alignment: Alignment.bottomRight,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          Container(
                                              padding: EdgeInsets.only(
                                                  right: sized * 0.01),
                                              child: Text(
                                                'زين',
                                                textDirection:
                                                    TextDirection.rtl,
                                                style: TextStyle(
                                                    color: AppColors.textColor
                                                        .withOpacity(0.8),
                                                    fontSize: sized * 0.013,
                                                    fontWeight:
                                                        FontWeight.bold),
                                              )),
                                          Container(
                                            padding: EdgeInsets.only(
                                                left: sizedWidth * 0.01),
                                            decoration: BoxDecoration(
                                                border: Border(
                                                    left: BorderSide(
                                                        color: AppColors
                                                            .textColor
                                                            .withOpacity(0.25),
                                                        width: 1.5))),
                                            child: Text(
                                              'مساعد صوتي',
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                  color: AppColors.textColor
                                                      .withOpacity(0.8),
                                                  fontSize: sized * 0.013,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    width: sizedWidth * 0.01,
                                  ),
                                  Icon(
                                    Icons.flutter_dash_rounded,
                                    color: AppColors.warningColor,
                                    size: sized * 0.035,
                                  ),
                                ]),
                                SizedBox(
                                  height: sizedHeight * 0.05,
                                ),
                                SizedBox(
                                  height: sizedHeight * 0.05,
                                ),
                              ],
                            ),
                          )
                        ],
            )),
      ),
    ),
  );
}
