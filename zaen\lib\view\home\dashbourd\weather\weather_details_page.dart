import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/modules/local/weather_service.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/components/components.dart';

/// صفحة تفاصيل الطقس
class WeatherDetailsPage extends StatelessWidget {
  const WeatherDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final HomeController controller = Get.find();
    final WeatherService weatherService = Get.find<WeatherService>();

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        title: Text(
          'تفاصيل الطقس',
          style: TextStyle(
            color: AppColors.textColor,
            fontSize: controller.sized * 0.016,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.backgroundColor2.withOpacity(0.8),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: AppColors.textColor,
            size: controller.sized * 0.02,
          ),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: AppColors.primaryColor,
              size: controller.sized * 0.02,
            ),
            onPressed: () => weatherService.requestWeatherUpdate(),
          ),
        ],
      ),
      body: Obx(() {
        final weather = weatherService.currentWeather.value;
        final isLoading = weatherService.isLoading.value;
        final error = weatherService.errorMessage.value;

        if (isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (error.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: controller.sized * 0.05,
                  color: AppColors.errorColor,
                ),
                SizedBox(height: controller.sizedHight * 0.02),
                Text(
                  error,
                  style: TextStyle(
                    color: AppColors.errorColor,
                    fontSize: controller.sized * 0.012,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: controller.sizedHight * 0.03),
                ElevatedButton(
                  onPressed: () => weatherService.requestWeatherUpdate(),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        if (weather == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.cloud_off,
                  size: controller.sized * 0.05,
                  color: AppColors.textColor.withOpacity(0.5),
                ),
                SizedBox(height: controller.sizedHight * 0.02),
                Text(
                  'لا توجد بيانات طقس متاحة',
                  style: TextStyle(
                    color: AppColors.textColor.withOpacity(0.7),
                    fontSize: controller.sized * 0.014,
                  ),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: EdgeInsets.all(controller.sized * 0.02),
          child: Column(
            children: [
              // بطاقة الطقس الرئيسية
              _buildMainWeatherCard(controller, weather, weatherService),

              SizedBox(height: controller.sizedHight * 0.02),

              // بطاقة التفاصيل
              _buildDetailsCard(controller, weather),

              SizedBox(height: controller.sizedHight * 0.02),

              // بطاقة معلومات إضافية
              _buildAdditionalInfoCard(controller, weather),
            ],
          ),
        );
      }),
    );
  }

  /// بناء بطاقة الطقس الرئيسية
  Widget _buildMainWeatherCard(
    HomeController controller,
    WeatherData weather,
    WeatherService weatherService,
  ) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(controller.sized * 0.02),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.primaryColor.withOpacity(0.2),
              AppColors.primaryColor.withOpacity(0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: AppColors.border.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            // أيقونة الطقس الكبيرة
            Container(
              width: controller.sized * 0.06,
              height: controller.sized * 0.06,
              decoration: BoxDecoration(
                color: AppColors.backgroundColor2.withOpacity(0.3),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Center(
                child: Text(
                  weatherService.getWeatherIcon(weather.condition),
                  style: TextStyle(fontSize: controller.sized * 0.04),
                ),
              ),
            ),
            // وصف الطقس
            Text(
              weatherService.getWeatherDescriptionArabic(weather.condition),
              style: TextStyle(
                color: AppColors.textColor2,
                fontSize: controller.sized * 0.016,
                fontWeight: FontWeight.w600,
              ),
            ),

            SizedBox(height: controller.sizedHight * 0.005),

            // درجة الحرارة
            Text(
              '${weather.temperature?.toStringAsFixed(1) ?? '--'}°C',
              style: TextStyle(
                color: AppColors.textColor,
                fontSize: controller.sized * 0.03,
              ),
            ),

            SizedBox(height: controller.sizedHight * 0.005),

            if (weather.description.isNotEmpty)
              Padding(
                padding: EdgeInsets.only(top: controller.sizedHight * 0.005),
                child: Text(
                  weather.description,
                  style: TextStyle(
                    color: AppColors.textColor.withOpacity(0.6),
                    fontSize: controller.sized * 0.012,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

            SizedBox(height: controller.sizedHight * 0.01),

            // الموقع
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.location_on,
                  color: AppColors.primaryColor.withOpacity(0.7),
                  size: controller.sized * 0.015,
                ),
                SizedBox(width: controller.sizedWidth * 0.01),
                Text(
                  weather.location.isNotEmpty
                      ? weather.location
                      : controller.home.value,
                  style: TextStyle(
                    color: AppColors.textColor2,
                    fontSize: controller.sized * 0.011,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة التفاصيل
  Widget _buildDetailsCard(HomeController controller, WeatherData weather) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Container(
        width: double.infinity,
        height: controller.sizedHight * 0.3,
        padding: EdgeInsets.all(controller.sized * 0.02),
        decoration: BoxDecoration(
          color: AppColors.backgroundColor2.withOpacity(0.1),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: AppColors.border.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'التفاصيل',
              style: TextStyle(
                color: AppColors.textColor,
                fontSize: controller.sized * 0.014,
                fontWeight: FontWeight.bold,
              ),
            ),

            SizedBox(height: controller.sizedHight * 0.015),

            // شبكة التفاصيل
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                // physics: const NeverScrollableScrollPhysics(),
                childAspectRatio: 2,
                crossAxisSpacing: controller.sizedWidth * 0.02,
                mainAxisSpacing: controller.sizedHight * 0.01,
                children: [
                  if (weather.humidity != null)
                    _buildDetailItem(
                      controller,
                      Icons.water_drop,
                      'الرطوبة',
                      '${weather.humidity}%',
                    ),
                  if (weather.windSpeed != null)
                    _buildDetailItem(
                      controller,
                      Icons.air,
                      'سرعة الرياح',
                      '${weather.windSpeed?.toStringAsFixed(1)} م/ث',
                    ),
                  if (weather.pressure != null)
                    _buildDetailItem(
                      controller,
                      Icons.compress,
                      'الضغط الجوي',
                      '${weather.pressure?.toStringAsFixed(0)} هكتوباسكال',
                    ),
                  if (weather.visibility != null)
                    _buildDetailItem(
                      controller,
                      Icons.visibility,
                      'الرؤية',
                      '${weather.visibility?.toStringAsFixed(1)} كم',
                    ),
                  if (weather.uvIndex != null)
                    _buildDetailItem(
                      controller,
                      Icons.wb_sunny,
                      'مؤشر الأشعة فوق البنفسجية',
                      weather.uvIndex!.toStringAsFixed(1),
                    ),
                  if (weather.windDirection != null)
                    _buildDetailItem(
                      controller,
                      Icons.navigation,
                      'اتجاه الرياح',
                      weather.windDirection!,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر تفصيل
  Widget _buildDetailItem(
    HomeController controller,
    IconData icon,
    String label,
    String value,
  ) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Container(
        padding: EdgeInsets.all(controller.sized * 0.01),
        decoration: BoxDecoration(
          color: AppColors.backgroundColor.withOpacity(0.5),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: AppColors.primaryColor.withOpacity(0.7),
              size: controller.sized * 0.015,
            ),
            SizedBox(width: controller.sizedWidth * 0.02),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      color: AppColors.textColor.withOpacity(0.6),
                      fontSize: controller.sized * 0.008,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    value,
                    style: TextStyle(
                      color: AppColors.textColor.withOpacity(0.9),
                      fontSize: controller.sized * 0.01,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة المعلومات الإضافية
  Widget _buildAdditionalInfoCard(
      HomeController controller, WeatherData weather) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(controller.sized * 0.02),
        decoration: BoxDecoration(
          color: AppColors.backgroundColor2.withOpacity(0.1),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: AppColors.border.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات إضافية',
              style: TextStyle(
                color: AppColors.textColor,
                fontSize: controller.sized * 0.014,
                fontWeight: FontWeight.bold,
              ),
            ),

            SizedBox(height: controller.sizedHight * 0.015),

            // وقت آخر تحديث
            Row(
              children: [
                Icon(
                  Icons.update,
                  color: AppColors.primaryColor.withOpacity(0.7),
                  size: controller.sized * 0.015,
                ),
                SizedBox(width: controller.sizedWidth * 0.02),
                Text(
                  'آخر تحديث: ${_formatDateTime(weather.lastUpdated)}',
                  style: TextStyle(
                    color: AppColors.textColor.withOpacity(0.7),
                    fontSize: controller.sized * 0.01,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} - ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
