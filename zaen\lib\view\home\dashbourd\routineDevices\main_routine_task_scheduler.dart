import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/alarm.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Widget taskSchedulerWidget({
  required HomeController controller,
  required bool add,
  required bool isTask,
  required bool isScheduler,
  required int h,
  required int m,
  required bool isAM,
  required List<String> days,
  required bool re,
  required Function(bool) onTaskChanged,
  required Function(bool) onSchedulerChanged,
  required Function setState,
}) {
  return add || isTask
      ? containerPageOption(
          content: MaterialButton(
          padding: EdgeInsets.symmetric(vertical: controller.sizedHight * 0.02),
          onPressed: () async {
            var setState1 = setState;
            await alarm(
                txt: 'قائمة المهام',
                context: Get.context!,
                setState1: setState1,
                submit: () {
                  setState(
                    () {
                      onSchedulerChanged(true);
                      onTaskChanged(true);
                      isTask = true;
                      isScheduler = true;
                    },
                  );
                },
                del: () {
                  setState(
                    () {
                      onSchedulerChanged(false);
                      onTaskChanged(false);
                      isTask = false;
                      isScheduler = false;
                    },
                  );
                });
            2.delay();
            setState1(
              () {
                onTaskChanged(isScheduler);
              },
            );
          },
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  iconStyle(
                      icon: Icons.more_time_rounded,
                      color: AppColors.warningColor,
                      size: controller.sized * 0.025),
                  SizedBox(
                    width: controller.sizedWidth * 0.02,
                  ),
                  Expanded(
                    child: txtStyle(
                      align: TextAlign.start,
                      txt: "الجدولة الى قائمة المهام",
                    ),
                  ),
                  SizedBox(
                    width: controller.sizedWidth * 0.05,
                  ),
                  Icon(
                    isTask
                        ? Icons.check_box_rounded
                        : Icons.check_box_outline_blank_rounded,
                    color: isTask
                        ? AppColors.primaryColor
                        : AppColors.textColor2.withOpacity(0.5),
                    size: controller.sized * 0.027,
                  ),
                ],
              ),
              isTask
                  ? Padding(
                      padding: EdgeInsets.only(
                          right: controller.sizedWidth * 0.02,
                          left: controller.sizedWidth * 0.02,
                          top: controller.sizedHight * 0.02),
                      child: Divider(
                        color: AppColors.textColor2.withOpacity(0.3),
                        endIndent: 2,
                        indent: 2,
                      ),
                    )
                  : Container(),
              isTask && isScheduler == false
                  ? txtStyle(
                      txt: 'إضغط لإختيار موعد للروتين',
                      color: AppColors.errorColor)
                  : isTask && isScheduler
                      ? Directionality(
                          textDirection: TextDirection.rtl,
                          child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: controller.sizedWidth * 0.02),
                              child: Column(
                                children: [
                                  Row(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Text(
                                        h.toString() +
                                            ':' +
                                            ([0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
                                                    .contains(m)
                                                ? '0' + m.toString()
                                                : m.toString()),
                                        textDirection: TextDirection.rtl,
                                        style: TextStyle(
                                          color: AppColors.warningColor,
                                          fontSize: controller.sized * 0.035,
                                        ),
                                      ),
                                      SizedBox(
                                        width: controller.sizedWidth * 0.005,
                                      ),
                                      Padding(
                                        padding: EdgeInsets.only(
                                            bottom:
                                                controller.sizedHight * 0.008,
                                            right: controller.sized * 0.001),
                                        child: Text(
                                          isAM ? 'صباحاً' : 'مسائاً',
                                          textAlign: TextAlign.justify,
                                          textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                              color: AppColors.textColor2,
                                              fontSize:
                                                  controller.sized * 0.014,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal:
                                            controller.sizedWidth * 0.01),
                                    child: Row(
                                      children: [
                                        Text(
                                          'سبت',
                                          textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                              color: days.contains('سبت')
                                                  ? AppColors.warningColor
                                                  : AppColors.textColor2
                                                      .withOpacity(0.2),
                                              fontSize: controller.sized * 0.01,
                                              fontWeight: FontWeight.bold),
                                        ),
                                        SizedBox(
                                          width: controller.sizedWidth * 0.02,
                                        ),
                                        Text(
                                          'أحد',
                                          textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                              color: days.contains("أحد")
                                                  ? AppColors.warningColor
                                                  : AppColors.textColor2
                                                      .withOpacity(0.2),
                                              fontSize: controller.sized * 0.01,
                                              fontWeight: FontWeight.bold),
                                        ),
                                        SizedBox(
                                          width: controller.sizedWidth * 0.02,
                                        ),
                                        Text(
                                          'إثنين',
                                          textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                              color: days.contains("إثنين")
                                                  ? AppColors.warningColor
                                                  : AppColors.textColor2
                                                      .withOpacity(0.2),
                                              fontSize: controller.sized * 0.01,
                                              fontWeight: FontWeight.bold),
                                        ),
                                        SizedBox(
                                          width: controller.sizedWidth * 0.02,
                                        ),
                                        Text(
                                          'ثلاثاء',
                                          textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                              color: days.contains("ثلاثاء")
                                                  ? AppColors.warningColor
                                                  : AppColors.textColor2
                                                      .withOpacity(0.2),
                                              fontSize: controller.sized * 0.01,
                                              fontWeight: FontWeight.bold),
                                        ),
                                        SizedBox(
                                          width: controller.sizedWidth * 0.02,
                                        ),
                                        Text(
                                          'اربعاء',
                                          textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                              color: days.contains("اربعاء")
                                                  ? AppColors.warningColor
                                                  : AppColors.textColor2
                                                      .withOpacity(0.2),
                                              fontSize: controller.sized * 0.01,
                                              fontWeight: FontWeight.bold),
                                        ),
                                        SizedBox(
                                          width: controller.sizedWidth * 0.02,
                                        ),
                                        Text(
                                          'خميس',
                                          textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                              color: days.contains("خميس")
                                                  ? AppColors.warningColor
                                                  : AppColors.textColor2
                                                      .withOpacity(0.2),
                                              fontSize: controller.sized * 0.01,
                                              fontWeight: FontWeight.bold),
                                        ),
                                        SizedBox(
                                          width: controller.sizedWidth * 0.02,
                                        ),
                                        Text(
                                          'جمعة',
                                          textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                              color: days.contains("جمعة")
                                                  ? AppColors.warningColor
                                                  : AppColors.textColor2
                                                      .withOpacity(0.2),
                                              fontSize: controller.sized * 0.01,
                                              fontWeight: FontWeight.bold),
                                        ),
                                        SizedBox(
                                          width: controller.sizedWidth * 0.05,
                                        ),
                                        Icon(
                                          Icons.refresh_rounded,
                                          size: controller.sized * 0.018,
                                          color: re
                                              ? AppColors.warningColor
                                              : AppColors.textColor2
                                                  .withOpacity(0.2),
                                        )
                                      ],
                                    ),
                                  )
                                ],
                              )),
                        )
                      : Container()
            ],
          ),
        ))
      : Container();
}
