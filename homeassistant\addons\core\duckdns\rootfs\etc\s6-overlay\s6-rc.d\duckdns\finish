#!/command/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Home Assistant Community Add-on: chrony
# Take down the S6 supervision tree when chrony fails
# ==============================================================================
# shellcheck disable=SC2155
readonly exit_code_container=$(</run/s6-linux-init-container-results/exitcode)
readonly exit_code_service="${1}"
readonly exit_code_signal="${2}"
readonly service="duckdns"

bashio::log.info \
  "Service ${service} exited with code ${exit_code_service}" \
  "(by signal ${exit_code_signal})"

if [[ "${exit_code_service}" -eq 256 ]]; then
  if [[ "${exit_code_container}" -eq 0 ]]; then
    echo $((128 + exit_code_signal)) > /run/s6-linux-init-container-results/exitcode
  fi
  [[ "${exit_code_signal}" -eq 15 ]] && exec /run/s6/basedir/bin/halt
elif [[ "${exit_code_service}" -ne 0 ]]; then
  if [[ "${exit_code_container}" -eq 0 ]]; then
    echo "${exit_code_service}" > /run/s6-linux-init-container-results/exitcode
  fi
  exec /run/s6/basedir/bin/halt
fi