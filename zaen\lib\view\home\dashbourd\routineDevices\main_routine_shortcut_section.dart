import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:expansion_tile_card/expansion_tile_card.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Widget shortcutSectionWidget({
  required HomeController controller,
  required bool add,
  required bool isShortcut,
  required String routineIcon,
  required TextEditingController name1,
  required GlobalKey<FormState> kname1,
  required Function(bool) onShortcutChanged,
  required Function(String) onRoutineIconChanged,
  required bool edit,
}) {
  return add || isShortcut
      ? containerPageOption(
          content: add
              ? ExpansionTileCard(
                  onExpansionChanged: (shortCut) {
                    onShortcutChanged(shortCut);
                  },
                  initiallyExpanded: isShortcut ? true : false,
                  duration: Duration(milliseconds: 400),
                  baseColor: Colors.transparent,
                  expandedColor: Colors.transparent,
                  borderRadius: BorderRadius.all(Radius.circular(17)),
                  shadowColor: Colors.transparent,
                  contentPadding: EdgeInsets.zero,
                  trailing: Icon(
                    isShortcut
                        ? Icons.check_box_rounded
                        : Icons.check_box_outline_blank_rounded,
                    color: isShortcut
                        ? AppColors.primaryColor
                        : AppColors.textColor2.withOpacity(0.5),
                    size: controller.sized * 0.027,
                  ),
                  title: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      iconStyle(
                          icon: Icons.shortcut, color: AppColors.warningColor),
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                      Expanded(
                        child: txtStyle(
                          align: TextAlign.start,
                          txt: 'اضافة الى الاختصارات',
                        ),
                      ),
                    ],
                  ),
                  children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: controller.sizedWidth * 0.02),
                        child: Divider(
                          color: AppColors.textColor2.withOpacity(0.3),
                          endIndent: 2,
                          indent: 2,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: controller.sizedWidth * 0.01),
                        child: Row(
                          children: [
                            Container(
                                height: controller.sizedHight * 0.063,
                                width: controller.sizedWidth * 0.13,
                                decoration: BoxDecoration(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(10)),
                                    border: Border.all(
                                        color: Colors.amber, width: 2)),
                                child: Center(
                                    child: IconButton(
                                        onPressed: () {
                                          showIconSelectionDialog(
                                            controller: controller,
                                            routineIcon: routineIcon,
                                            onIconSelected:
                                                onRoutineIconChanged,
                                          );
                                        },
                                        icon: Icon(
                                          routineIcons[routineIcon],
                                          color: Colors.amber,
                                          size: controller.sized * 0.026,
                                        )))),
                            SizedBox(
                              width: controller.sizedWidth * 0.02,
                            ),
                            Form(
                              key: kname1,
                              child: Flexible(
                                child: TextFormField(
                                  controller: name1,
                                  validator: (val) {
                                    for (var i = 0;
                                        i < name1.text.length;
                                        i++) {
                                      if (arabic.contains(name1.text[i]) ||
                                          name1.text[i].isNumericOnly) {
                                        edit = true;
                                      } else {
                                        return 'قم بادخال حروف عربية او ارقام فقط';
                                      }
                                    }

                                    return null;
                                  },
                                  maxLength: 25,
                                  showCursor: true,
                                  cursorColor: AppColors.textColor,
                                  // textDirection: TextDirection.rtl,
                                  style: TextStyle(
                                    color: AppColors.textColor,
                                    fontSize: controller.sized * 0.012,
                                    fontWeight: FontWeight.bold,
                                  ),

                                  onEditingComplete: () {
                                    FocusManager.instance.primaryFocus
                                        ?.unfocus();
                                    if (kname1.currentState != null) {
                                      var formdata = kname1.currentState;
                                      formdata!.validate();
                                    }
                                  },
                                  decoration: InputDecoration(
                                    hintText: 'اسم الاختصار - اختياري',
                                    hintStyle: TextStyle(
                                        color: AppColors.textHint
                                            .withOpacity(0.5)),
                                    focusedBorder: UnderlineInputBorder(
                                        borderSide: BorderSide(
                                            color: AppColors.textColor2
                                                .withOpacity(0.5))),
                                    suffixIcon: Icon(
                                      Icons.edit_rounded,
                                      color:
                                          AppColors.textColor2.withOpacity(0.5),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ])
              : shortcutEditModeWidget(
                  controller: controller,
                  routineIcon: routineIcon,
                  name1: name1,
                  kname1: kname1,
                  onRoutineIconChanged: onRoutineIconChanged,
                  edit: edit,
                ),
        )
      : Container();
}

Widget shortcutEditModeWidget({
  required HomeController controller,
  required String routineIcon,
  required TextEditingController name1,
  required GlobalKey<FormState> kname1,
  required Function(String) onRoutineIconChanged,
  required bool edit,
}) {
  return Row(
    children: [
      Container(
          height: controller.sizedHight * 0.063,
          width: controller.sizedWidth * 0.125,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              border: Border.all(color: Colors.amber, width: 2)),
          child: Center(
              child: IconButton(
                  onPressed: () {
                    showIconSelectionDialog(
                      controller: controller,
                      routineIcon: routineIcon,
                      onIconSelected: onRoutineIconChanged,
                    );
                  },
                  icon: Icon(
                    routineIcons[routineIcon],
                    color: Colors.amber,
                    size: controller.sized * 0.026,
                  )))),
      SizedBox(
        width: controller.sizedWidth * 0.02,
      ),
      Form(
        key: kname1,
        child: Flexible(
          child: TextFormField(
            controller: name1,
            validator: (val) {
              for (var i = 0; i < name1.text.length; i++) {
                if (arabic.contains(name1.text[i]) ||
                    name1.text[i].isNumericOnly) {
                  edit = true;
                } else {
                  return 'قم بادخال حروف عربية او ارقام فقط';
                }
              }

              return null;
            },
            maxLength: 25,
            showCursor: true,
            cursorColor: AppColors.textColor,
            // textDirection: TextDirection.rtl,
            style: TextStyle(
              color: AppColors.textColor.withOpacity(0.6),
              fontSize: controller.sized * 0.015,
              fontWeight: FontWeight.bold,
            ),

            onEditingComplete: () {
              FocusManager.instance.primaryFocus?.unfocus();
              if (kname1.currentState != null) {
                var formdata = kname1.currentState;
                formdata!.validate();
              }
            },
            decoration: InputDecoration(
              hintText: 'اسم الاختصار - اختياري',
              hintStyle:
                  TextStyle(color: AppColors.textColor2.withOpacity(0.5)),
              focusedBorder: UnderlineInputBorder(
                  borderSide:
                      BorderSide(color: AppColors.textColor2.withOpacity(0.5))),
              suffixIcon: Icon(
                Icons.edit_rounded,
                color: AppColors.textColor2.withOpacity(0.5),
              ),
            ),
          ),
        ),
      ),
    ],
  );
}

void showIconSelectionDialog({
  required HomeController controller,
  required String routineIcon,
  required Function(String) onIconSelected,
}) {
  AwesomeDialog(
      context: Get.context!,
      dialogType: DialogType.noHeader,
      headerAnimationLoop: true,
      animType: AnimType.topSlide,
      dialogBackgroundColor: AppColors.backgroundColor2,
      width: controller.sizedWidth,
      body: GetBuilder<HomeController>(
          builder: (controller) => StatefulBuilder(
                builder: (context, setState) => Center(
                    child: Container(
                  margin: EdgeInsets.symmetric(
                      vertical: controller.sizedHight * 0.01),
                  child: Column(
                    children: [
                      Text(
                        'اختيار ايقونة للاختصار',
                        style: TextStyle(
                            color: AppColors.textColor2.withOpacity(0.5),
                            fontSize: controller.sized * 0.015,
                            fontWeight: FontWeight.bold),
                      ),
                      GridView(
                        padding: const EdgeInsets.only(top: 5),
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 4,
                          childAspectRatio: 0.85,
                        ),
                        children: [
                          for (String i in routineIcons.keys.toList())
                            IconButton(
                              onPressed: () {
                                onIconSelected(i);
                                if (Navigator.of(context).canPop()) {
                                  Navigator.of(context).pop();
                                }
                              },
                              icon: Icon(
                                routineIcons[i],
                                size: controller.sized * 0.03,
                              ),
                              color: Colors.amber,
                            )
                        ],
                      ),
                    ],
                  ),
                )),
              ))).show();
}
