import 'package:mysql1/mysql1.dart';
import 'package:zaen/controller/controller.dart';
import 'package:get/get.dart';

/// دالة مساعدة لإنشاء اتصال آمن بقاعدة البيانات MySQL
/// تضمن إنشاء اتصال جديد في كل مرة لتجنب مشاكل الاتصالات المغلقة
Future<MySqlConnection> createSafeConnection() async {
  final HomeController controller = Get.find();

  return await MySqlConnection.connect(ConnectionSettings(
    host: controller.hostZain.value,
    user: 'root',
    db: 'zain',
    password: 'zain',
    characterSet: CharacterSet.UTF8,
  ));
}

/// دالة مساعدة لتنفيذ استعلام مع إدارة آمنة للاتصال
/// تنشئ اتصال جديد، تنفذ الاستعلام، ثم تغلق الاتصال
Future<Results> executeSafeQuery(String query, [List<Object?>? values]) async {
  MySqlConnection? conn;
  try {
    conn = await createSafeConnection();
    return await conn.query(query, values);
  } finally {
    await conn?.close();
  }
}

/// دالة مساعدة لتنفيذ عدة استعلامات في نفس الاتصال
/// مفيدة للعمليات التي تحتاج لعدة استعلامات متتالية
Future<T> executeWithConnection<T>(
    Future<T> Function(MySqlConnection conn) operation) async {
  MySqlConnection? conn;
  try {
    conn = await createSafeConnection();
    return await operation(conn);
  } finally {
    await conn?.close();
  }
}

/// دالة مساعدة لتنفيذ استعلام مع معالجة الأخطاء
/// ترجع true في حالة النجاح، false في حالة الفشل
Future<bool> executeSafeQueryWithErrorHandling(String query,
    [List<Object?>? values, String? errorMessage]) async {
  try {
    await executeSafeQuery(query, values);
    return true;
  } catch (e) {
    print('${errorMessage ?? "خطأ في تنفيذ الاستعلام"}: $e');
    return false;
  }
}

/// دالة مساعدة للتحقق من حالة الاتصال وإعادة الاتصال إذا لزم الأمر
Future<MySqlConnection> ensureConnection(MySqlConnection? conn) async {
  if (conn == null) {
    return await createSafeConnection();
  }

  // محاولة اختبار الاتصال
  try {
    await conn.query('SELECT 1');
    return conn;
  } catch (e) {
    // إذا فشل الاختبار، إنشاء اتصال جديد
    try {
      await conn.close();
    } catch (_) {
      // تجاهل أخطاء الإغلاق
    }
    return await createSafeConnection();
  }
}

/// دالة مساعدة لتنفيذ transaction آمن
Future<T> executeTransaction<T>(
    Future<T> Function(MySqlConnection conn) operation) async {
  MySqlConnection? conn;
  try {
    conn = await createSafeConnection();

    // بدء المعاملة
    await conn.query('START TRANSACTION');

    try {
      // تنفيذ العملية
      T result = await operation(conn);

      // تأكيد المعاملة
      await conn.query('COMMIT');

      return result;
    } catch (e) {
      // إلغاء المعاملة في حالة الخطأ
      await conn.query('ROLLBACK');
      rethrow;
    }
  } finally {
    await conn?.close();
  }
}

/// دالة مساعدة لتنفيذ استعلام مع إعادة المحاولة
Future<Results?> executeQueryWithRetry(String query,
    [List<Object?>? values, int maxRetries = 3]) async {
  for (int attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await executeSafeQuery(query, values);
    } catch (e) {
      print('محاولة $attempt فشلت: $e');

      if (attempt == maxRetries) {
        print('فشل في تنفيذ الاستعلام بعد $maxRetries محاولات');
        rethrow;
      }

      // انتظار قبل إعادة المحاولة
      await Future.delayed(Duration(seconds: attempt));
    }
  }

  return null;
}

/// دالة مساعدة للتحقق من وجود جدول
Future<bool> tableExists(String tableName) async {
  try {
    final result = await executeSafeQuery(
        "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = 'zain' AND table_name = ?",
        [tableName]);

    return result.first['count'] > 0;
  } catch (e) {
    print('خطأ في التحقق من وجود الجدول $tableName: $e');
    return false;
  }
}

/// دالة مساعدة للتحقق من وجود عمود في جدول
Future<bool> columnExists(String tableName, String columnName) async {
  try {
    final result = await executeSafeQuery(
        "SELECT COUNT(*) as count FROM information_schema.columns WHERE table_schema = 'zain' AND table_name = ? AND column_name = ?",
        [tableName, columnName]);

    return result.first['count'] > 0;
  } catch (e) {
    print('خطأ في التحقق من وجود العمود $columnName في الجدول $tableName: $e');
    return false;
  }
}

/// دالة مساعدة لإنشاء جدول إذا لم يكن موجوداً
Future<bool> createTableIfNotExists(String createTableQuery) async {
  try {
    await executeSafeQuery(createTableQuery);
    return true;
  } catch (e) {
    print('خطأ في إنشاء الجدول: $e');
    return false;
  }
}

/// دالة مساعدة لتنظيف البيانات القديمة
Future<bool> cleanupOldData(
    String tableName, String dateColumn, int daysToKeep) async {
  try {
    await executeSafeQuery(
        "DELETE FROM $tableName WHERE $dateColumn < DATE_SUB(NOW(), INTERVAL ? DAY)",
        [daysToKeep]);
    return true;
  } catch (e) {
    print('خطأ في تنظيف البيانات القديمة من $tableName: $e');
    return false;
  }
}

/// دالة مساعدة لعمل backup لجدول
Future<bool> backupTable(String tableName, String backupTableName) async {
  try {
    // إنشاء جدول النسخ الاحتياطي
    await executeSafeQuery("DROP TABLE IF EXISTS $backupTableName");
    await executeSafeQuery(
        "CREATE TABLE $backupTableName AS SELECT * FROM $tableName");
    return true;
  } catch (e) {
    print('خطأ في عمل backup للجدول $tableName: $e');
    return false;
  }
}
