import 'package:flutter/material.dart';
import 'package:zaen/shared/themes/app_colors.dart';

List arabic = [
  'ا',
  'أ',
  'ء',
  'ى',
  'ئ',
  'ء',
  'ؤ',
  'ب',
  'ت',
  'ج',
  'ح',
  'خ',
  'د',
  'ذ',
  'س',
  'ش',
  'ص',
  'ض',
  'ع',
  'غ',
  'ق',
  'ك',
  'ف',
  'ط',
  'م',
  'ن',
  'ل',
  'ر',
  'ة',
  'و',
  'ز',
  'ظ',
  'ه',
  'ث',
  'ي',
  'َ',
  ' ُ',
  ' ِ',
  ' ً',
  ' ٌ',
  ' ٍ',
  ' ْ',
  ' ّ',
  ' '
];

Map weekDays = {
  'سبت': 'Sat',
  'أحد': 'Sun',
  'إثنين': 'Mon',
  'ثلاثاء': 'Tue',
  'اربعاء': 'Wed',
  'خميس': 'Thu',
  'جمعة': 'Fri'
};

String editChar(String phrase) {
  phrase = phrase.trim();
  phrase = phrase.replaceFirst('أ', 'ا');
  phrase = phrase.replaceFirst('إ', 'ا');
  phrase = phrase.replaceFirst('ئ', 'ء');
  phrase = phrase.replaceFirst('ة', 'ه');
  phrase = phrase.replaceFirst('ى', 'ي');
  phrase = phrase.replaceFirst('ؤ', 'ء');
  phrase = phrase.replaceFirst('َ', '');
  phrase = phrase.replaceFirst('ً', '');
  phrase = phrase.replaceFirst('ُ', '');
  phrase = phrase.replaceFirst('ٌ', '');
  phrase = phrase.replaceFirst('ِ', '');
  phrase = phrase.replaceFirst('ٍ', '');
  phrase = phrase.replaceFirst('', '');
  phrase = phrase.replaceFirst('ّ', '');
  phrase = phrase.replaceFirst('`', '');
  phrase = phrase.replaceFirst('ْ', '');
  phrase = phrase.replaceFirst('آ', 'ا');
  phrase = ' ' + phrase + ' ';
  // phrase = phrase.replaceFirst(' و ', ' ');
  phrase = phrase.trim();
  for (String i in phrase.split(" ")) {
    if (i.length > 2 &&
        i.startsWith("ال") &&
        ![
          "الله",
          "الان",
          "الي",
          "الغي",
          "اللغاء",
          "الف",
          "الفين",
          "الفان",
          "الاف",
          "المانيا"
        ].contains(i)) {
      phrase = phrase.replaceFirst(i, i.substring(2));
    } else if (i.length > 3 &&
        (i.startsWith("و") || i.startsWith("ل") || i.startsWith("ب")) &&
        i.substring(1, 3) == 'ال' &&
        ![
          "الله",
          "الان",
          "الي",
          "الغي",
          "اللغاء",
          "الف",
          "الفين",
          "الفان",
          "الاف"
        ].contains(i.substring(1))) {
      phrase = phrase.replaceFirst(i, i.substring(3));
    } else if (i.length > 2 && i.startsWith("لل")) {
      phrase = phrase.replaceFirst(i, i.substring(2));
    }
  }
  return phrase;
}

Map routineIcons = {
  '1': Icons.home_rounded,
  '2': Icons.store_mall_directory_rounded,
  '3': Icons.business_rounded,
  '4': Icons.deck_rounded,
  '5': Icons.login_rounded,
  '6': Icons.logout_rounded,
  '7': Icons.vpn_key_rounded,
  '8': Icons.bathtub_rounded,
  '9': Icons.bed_rounded,
  '10': Icons.bedroom_baby_rounded,
  '11': Icons.chair_rounded,
  '12': Icons.menu_book_rounded,
  '13': Icons.nightlight_round_sharp,
  '14': Icons.wb_sunny,
  '15': Icons.watch_later_rounded,
  '16': Icons.audiotrack_rounded,
  '17': Icons.coffee_rounded,
  '18': Icons.cake_rounded,
  '19': Icons.celebration_rounded,
  '20': Icons.blur_on_rounded,
  '21': Icons.coffee_maker_rounded,
  '22': Icons.dining_rounded,
  '23': Icons.emoji_objects_rounded,
  '24': Icons.live_tv_rounded,
  '25': Icons.lightbulb,
  '26': Icons.local_fire_department_sharp,
  '27': Icons.fastfood_rounded,
  '28': Icons.thermostat_rounded,
  '29': Icons.devices_rounded,
  '30': Icons.build_sharp,
  '31': Icons.business_center_rounded,
  '32': Icons.bolt_rounded,
  '34': Icons.child_friendly_rounded,
  '36': Icons.beach_access_rounded,
  '37': Icons.calendar_today_rounded,
  '38': Icons.call_rounded,
  '39': Icons.category_rounded,
  '40': Icons.cloud_rounded,
  '42': Icons.color_lens_rounded,
  '43': Icons.commute_rounded,
  '44': Icons.directions_bus_rounded,
  '45': Icons.directions_car_rounded,
  '46': Icons.directions_run_rounded,
  '47': Icons.delivery_dining_rounded,
  '48': Icons.eco_rounded,
  '49': Icons.favorite_rounded,
  '50': Icons.grade_rounded,
  '51': Icons.local_florist_rounded,
  '54': Icons.thumb_up_rounded,
};

List<String> acFanSpeed = ['1', '2', '3', 'تلقائي'];
