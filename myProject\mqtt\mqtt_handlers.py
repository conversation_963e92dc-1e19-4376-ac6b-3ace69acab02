import mysql.connector
import paho.mqtt.publish as publish
import time
from datetime import datetime
import threading
import sys

sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/nlt')
sys.path.append('/home/<USER>/myProject/modules')
import static as st
import tts as tts

class MQTTHandlers:
    def __init__(self, system):
        self.system = system
        self.tts = tts.SmartHomeTTSCache()
        
    def handle_message(self, client, msg):
        """معالج الرسائل الرئيسي"""
        newtopic = str(msg.topic)
        
        # الحصول على اتصال قاعدة البيانات
        db = self._get_db_connection()
        cursor = db.cursor()
        
        try:
            cursor.execute("SELECT Rooms FROM Devices WHERE id LIKE '%s'" % (st.device_nb))
            result = cursor.fetchone()
            self.system.room = result[0] if result else ''
            
            print('4444445')
            print(newtopic)
            print(msg.payload.decode("utf-8"))
            print(self.system.room)
            
            if 'homebridge' in newtopic:
                self._handle_homebridge_message(client, newtopic, msg, db)
            elif "input/" in newtopic and " response " not in msg.payload.decode("utf-8"):
                self._handle_input_message(client, newtopic, msg, db)
            elif newtopic == st.device_nb + '/app/zain':
                self._handle_app_message(newtopic, msg)
            elif (st.device_nb == newtopic or newtopic == 'XX:XX:XX:XX:XX:XX' or 
                  " response " in msg.payload.decode("utf-8")):
                self._handle_device_response(newtopic, msg, db)
            elif newtopic == 'connect':
                self._handle_connect_message(msg, db)
            elif newtopic == 'phone/ask':
                self._handle_phone_ask(msg, db)
            elif newtopic == 'phone/join':
                self._handle_phone_join(msg, db)
            elif newtopic == 'phone/edit':
                pass
                
        except Exception as e:
            print(f"خطأ في معالجة الرسالة: {e}")
        finally:
            cursor.close()
            db.close()
    
    def _get_db_connection(self):
        while True:
            try:
                return mysql.connector.connect(
                    user='root', 
                    host=st.ip, 
                    passwd='zain', 
                    database='zain'
                )
            except:
                time.sleep(5)
                continue

    def _handle_homebridge_message(self, client, newtopic, msg, db):
        if 'homebridgeReset' in newtopic:
            homeBridgeSerial = st.randomSerial()
            with open(st.pathFiles + 'homeBridgeSerial.txt', 'w') as f:
                f.write(homeBridgeSerial)
            st.homeBridgeSerial = homeBridgeSerial
            from homebridge_manager import HomebridgeManager
            homebridge_manager = HomebridgeManager(self.system)
            homebridge_manager.setup_homebridge(client)
        else:
            self._process_homebridge_device_command(client, newtopic, msg, db)
    
    def _process_homebridge_device_command(self, client, newtopic, msg, db):
        cursor = db.cursor()
        try:
            cursor.execute("SELECT rooms,connect FROM Devices WHERE id = '%s'" % 
                          (newtopic.split('/')[1].split('::')[0]))
            result = cursor.fetchone()
            
            if not result:
                return
                
            d = datetime.now()
            result = list(result)
            
            if result[1] and '-' in result[1]:
                t = result[1].split('-')
                if (str(d.year) + str(d.month) + str(d.day) + str(d.hour) == 
                    str(t[0]) + str(t[1]) + str(t[2]) + str(t[3]) and 
                    d.minute - int(t[4]) == 0):
                    result[1] = 'متصل'
                else:
                    result[1] = 'غير متصل'
            else:
                result[1] = 'غير متصل'
            
            if result[0] == 'x' or result[1] == 'غير متصل':
                publish.single('homebridge/%s/getActive' % (newtopic.split('/')[1]), 
                             'false', hostname=st.ip)
                publish.single('homebridge/%s/getActive' % (newtopic.split('/')[1]), 
                             'POWER-OFF', hostname=st.ip)
            else:
                self._execute_device_command(client, newtopic, msg)
                
        except Exception as e:
            print(f"خطأ في معالجة أمر Homebridge: {e}")
        finally:
            cursor.close()
    
    def _execute_device_command(self, client, newtopic, msg):
        def exee(de):
            device_id = de.split('::')[0]
            if device_id not in self.system.listdevices:
                return
                
            device_type = self.system.listdevices[device_id]['device']
            
            if device_type == 'AC':
                self._handle_ac_command(de, newtopic)
            elif device_type == 'TV':
                self._handle_tv_command(de)
            elif "SWITCH" in device_type:
                self._handle_switch_command(de)
        
        device_id = newtopic.split('/')[1].split('::')[0]
        if device_id not in self.system.listdevices:
            self.system.listdevices[device_id] = {'device': 'UNKNOWN'}
            
        self.system.listdevices[device_id][newtopic] = str(msg.payload.decode("utf-8"))
        
        if (self.system.listdevices[device_id]['device'] == 'AC' and 
            newtopic.split('/')[1] not in self.system.re):
            self.system.re.append(newtopic.split('/')[1])
            t = threading.Timer(1.1, exee, args=[newtopic.split('/')[1]])
            t.start()
        else:
            exee(newtopic.split('/')[1])
    
    def _handle_ac_command(self, de, newtopic):
        try:
            self.system.re.remove(newtopic.split('/')[1])
        except:
            pass
            
        if ('homebridge/%s/setActive' % de in self.system.listdevices[de] and 
            self.system.listdevices[de]['homebridge/%s/setActive' % de] == 'false'):
            m = 'OFF'
        else:
            m = 'RUN'
            
            if 'homebridge/%s/setCoolingThresholdTemperature' % de in self.system.listdevices[de]:
                temp = str(int(float(self.system.listdevices[de]['homebridge/%s/setCoolingThresholdTemperature' % de])))
                m += ' ' + temp
            elif 'homebridge/%s/setHeatingThresholdTemperature' % de in self.system.listdevices[de]:
                temp = str(int(float(self.system.listdevices[de]['homebridge/%s/setHeatingThresholdTemperature' % de])))
                m += ' ' + temp
            else:
                m += ' 0'
                
            m += ' VAN'
            
            if 'homebridge/%s/setRotationSpeed' % de in self.system.listdevices[de]:
                m += ' ' + self.system.listdevices[de]['homebridge/%s/setRotationSpeed' % de]
            else:
                m += ' 0'
                
            target_state_key = 'homebridge/%s/setTargetHeaterCoolerState' % de
            if target_state_key in self.system.listdevices[de]:
                state = self.system.listdevices[de][target_state_key]
                if state == 'COOL':
                    m += ' AC'
                elif state == 'HEAT':
                    m += ' HEAT'
                elif state == 'AUTO':
                    m = 'RUN X VAN'
                    if 'homebridge/%s/setRotationSpeed' % de in self.system.listdevices[de]:
                        m += ' %s VAN' % self.system.listdevices[de]['homebridge/%s/setRotationSpeed' % de]
                    else:
                        m += ' 0 VAN'
                else:
                    m += ' XX'
            else:
                m += ' XX'
                
        m += ' XX:XX:XX:XX:XX:XX'
        print(m)
        publish.single('ROOMS/*/AC%/' + de, m, hostname=st.ip)
        #self.system.listdevices[de] = {'device': 'AC'}
    
    # ... (بقية الدوال بنفس التعديلات لاستخدام self.system)
    def _handle_tv_command(self, de):
        """معالجة أوامر التلفزيون"""
        i = self.system.listdevices[de]['homebridge/%s/setActive' % de]
        
        if 'SIL' in i or 'POWER-ON' in i or 'POWER-OFF' in i:
            m = i
        elif 'UP' in i.upper():
            m = 'CH + 1'
        elif 'DOWN' in i.upper():
            m = 'CH - 1'
        elif 'RIGHT' in i.upper() or 'VOLUME_UP' in i.upper():
            m = 'VOICE + 1'
        elif 'LEFT' in i.upper() or 'VOLUME_DOWN' in i.upper():
            m = 'VOICE - 1'
        else:
            m = 'CH = ' + i
            
        m += ' XX:XX:XX:XX:XX:XX'
        print(m)
        publish.single('ROOMS/*/TV%/' + de, m, hostname=st.ip)
        #self.system.listdevices[de] = {'device': 'TV'}
    
    def _handle_switch_command(self, de):
        """معالجة أوامر المفاتيح"""
        device_id = de.split("::")[0]
        
        for i in self.system.listdevices[device_id]:
            if i != 'device':
                switch_id = i.split('/')[1]
                active_key = 'homebridge/%s/setActive' % switch_id
                
                if active_key in self.system.listdevices[switch_id.split("::")[0]]:
                    if self.system.listdevices[switch_id.split("::")[0]][active_key] == 'false':
                        m = switch_id.split("::")[1] + '_OFF'
                    elif self.system.listdevices[switch_id.split("::")[0]][active_key] == 'true':
                        m = switch_id.split("::")[1] + '_RUN'
                    else:
                        continue
                        
                    m += ' XX:XX:XX:XX:XX:XX'
                    print(m)
                    publish.single('ROOMS/*/SWITCH%/' + switch_id.split("::")[0], m, hostname=st.ip)
                    
        #self.system.listdevices[device_id] = {'device': 'SWITCH'}

    def _handle_input_message(self, client, newtopic, msg, db):
        """معالجة رسائل الإدخال من الأجهزة"""
        ID = msg.payload.decode("utf-8")
        cursor = db.cursor()

        try:
            # الحصول على جميع الأجهزة
            cursor.execute('SELECT * FROM Devices')
            result = cursor.fetchall()
            myID = [i[0] for i in result]

            if ID in myID:
                self._handle_existing_device(ID, db)
            else:
                self._handle_new_device(ID, newtopic, db, client)

        except Exception as e:
            print(f"خطأ في معالجة رسالة الإدخال: {e}")
        finally:
            cursor.close()

    def _handle_existing_device(self, ID, db):
        """معالجة جهاز موجود"""
        cursor = db.cursor()

        try:
            cursor.execute("SELECT * FROM Devices WHERE id LIKE '%s'" % ID)
            result = cursor.fetchone()

            if result[2] == 'x':
                print('هذا الجهاز موجود لكن لم يتم تحديد غرفة له')
            else:
                publish.single(ID, result[2], hostname=st.ip)

                if ID in self.system.listdevices:
                    device_type = self.system.listdevices[ID]['device']

                    if device_type == 'AC':
                        self._sync_ac_state(ID, result)
                    elif device_type == 'TV':
                        self._sync_tv_state(ID, result)
                    elif 'SWITCH' in device_type:
                        self._sync_switch_state(ID, result)

        except Exception as e:
            print(f"خطأ في معالجة الجهاز الموجود: {e}")
        finally:
            cursor.close()

    def _sync_ac_state(self, ID, result):
        """مزامنة حالة المكيف مع Homebridge"""
        if 'OFF' in result[4]:
            publish.single('homebridge/' + ID + '/getActive', 'false', hostname=st.ip)
        else:
            publish.single('homebridge/' + ID + '/getActive', 'true', hostname=st.ip)

            if 'AC' in result[1]:
                publish.single('homebridge/' + ID + '/getTargetHeaterCoolerState', 'COOL', hostname=st.ip)
                publish.single('homebridge/' + ID + '/getCoolingThresholdTemperature',
                             result[4].split(' ')[1], hostname=st.ip)
            elif 'HEAT' in result[1]:
                publish.single('homebridge/' + ID + '/getTargetHeaterCoolerState', 'HEAT', hostname=st.ip)
                publish.single('homebridge/' + ID + '/getHeatingThresholdTemperature',
                             result[4].split(' ')[1], hostname=st.ip)
            elif 'VAN' in result[1]:
                publish.single('homebridge/' + ID + '/getTargetHeaterCoolerState', 'AUTO', hostname=st.ip)
                publish.single('homebridge/' + ID + '/getHeatingThresholdTemperature', '30', hostname=st.ip)
                publish.single('homebridge/' + ID + '/getCoolingThresholdTemperature', '16', hostname=st.ip)

            publish.single('homebridge/' + ID + '/getRotationSpeed',
                         str(int(result[4].split(' ')[3]) * 25), hostname=st.ip)
            time.sleep(2)

            if 'OFF' not in result[4]:
                publish.single(result[0], 'state ' + result[4], hostname=st.ip)

    def _sync_tv_state(self, ID, result):
        """مزامنة حالة التلفزيون مع Homebridge"""
        if 'POWER-OFF' in result[4] or result[4] == 'OFF':
            publish.single('homebridge/' + ID + '/getActive', 'POWER-OFF', hostname=st.ip)
        elif 'POWER-ON' in result[4]:
            publish.single('homebridge/' + ID + '/getActive', 'POWER-ON', hostname=st.ip)

        time.sleep(1)

        if result[4] != 'OFF':
            publish.single(result[0], 'state ' + result[4], hostname=st.ip)

    def _sync_switch_state(self, ID, result):
        """مزامنة حالة المفاتيح مع Homebridge"""
        time.sleep(3)

        if result[4] == 'OFF':
            # استخراج عدد المفاتيح من نوع الجهاز
            device_type = result[1]  # Type column
            if 'v' in device_type:
                switch_count = int(device_type.split('v')[1])
                for r in range(switch_count):
                    publish.single('homebridge/' + ID + '::v' + str(r + 1) + '/getActive',
                                 'false', hostname=st.ip)
                    publish.single('ROOMS/*/SWITCH%/' + ID,
                                 'v' + str(r + 1) + '_OFF XX:XX:XX:XX:XX:XX', hostname=st.ip)
        else:
            for r in result[4].split(' '):
                if '_' in r:
                    switch_parts = r.split('_')
                    if len(switch_parts) >= 2:
                        if switch_parts[1] == 'OFF':
                            publish.single('homebridge/' + ID + '::' + switch_parts[0] + '/getActive',
                                         'false', hostname=st.ip)
                        elif switch_parts[1] == 'RUN':
                            publish.single('homebridge/' + ID + '::' + switch_parts[0] + '/getActive',
                                         'true', hostname=st.ip)
                        publish.single('ROOMS/*/SWITCH%/' + ID, r + ' XX:XX:XX:XX:XX:XX', hostname=st.ip)

    def _handle_new_device(self, ID, newtopic, db, client):
        """معالجة جهاز جديد"""
        Type = newtopic[6:]
        cursor = db.cursor()

        try:
            d = datetime.now()

            # إدراج الجهاز الجديد
            if 'SWITCH' not in Type:
                cursor.execute(
                    "INSERT INTO Devices(id, Type, Rooms, connect, state) values('%s','%s','%s','%s','%s')" %
                    (ID, Type, 'x',
                     str(d.year) + '-' + str(d.month) + '-' + str(d.day) + '-' + str(d.hour) + '-' + str(d.minute),
                     'OFF'))
            else:
                # إنشاء حالة المفاتيح
                s = ''
                for i in range(int(Type.split('v')[1])):
                    i += 1
                    s += 'v%s_OFF ' % str(i)
                s = s.strip()

                cursor.execute(
                    "INSERT INTO Devices(id, Type, Rooms, connect, state) values('%s','%s','%s','%s','%s')" %
                    (ID, Type, 'x',
                     str(d.year) + '-' + str(d.month) + '-' + str(d.day) + '-' + str(d.hour) + '-' + str(d.minute),
                     s))

            db.commit()
            self.tts.say('beep_lo')
            print("input new device in database : id = %s , type = %s" % (ID, Type))

            # إنشاء الجداول المطلوبة
            self._create_device_tables(ID, Type, db)

        except Exception as e:
            print(f"خطأ في إضافة جهاز جديد: {e}")
        finally:
            cursor.close()
            client.reconnect()

    def _create_device_tables(self, ID, Type, db):
        """إنشاء الجداول المطلوبة للجهاز الجديد"""
        cursor = db.cursor()

        try:
            if 'SWITCH' not in Type:
                cursor.execute("ALTER TABLE NDevice ADD COLUMN %s VARCHAR(30)" % ID)
                db.commit()

            if 'TV' in Type:
                cursor.execute("CREATE TABLE %s_TV (chaneel VARCHAR(255), number VARCHAR(255))" % ID)
                db.commit()

            if 'SWITCH' in Type:
                # إنشاء جدول المفاتيح
                s = 'id INT AUTO_INCREMENT PRIMARY KEY, '
                for i in range(int(Type.split('v')[1])):
                    i += 1
                    s += 'v%s VARCHAR(255), ' % str(i)

                cursor.execute("CREATE TABLE %s_SWITCH (%s)" % (ID, s[:-2]))
                db.commit()

                # إدراج البيانات الأولية
                s = ''
                p = ''
                for i in range(int(Type.split('v')[1])):
                    i += 1
                    s += "'SWITCH', "
                    p += 'v' + str(i) + ', '

                s = s[:-2]
                p = p[:-2]
                sql = 'INSERT INTO ' + ID + '_SWITCH(' + p + ') values(' + s + ')'
                cursor.execute(sql)
                db.commit()

                cursor.execute('INSERT INTO %s_SWITCH(v1) values(NULL)' % ID)
                db.commit()

        except Exception as e:
            print(f"خطأ في إنشاء جداول الجهاز: {e}")
        finally:
            cursor.close()

    def _handle_app_message(self, newtopic, msg):
        """معالجة رسائل التطبيق"""
        M = msg.payload.decode("utf-8")
        m = M.split(' ')
        print('ROOMS/*/' + m[1] + '%/' + m[0])
        print(M.replace(m[0] + ' ' + m[1] + ' ', '') + ' XX:XX:XX:XX:XX:XX')
        publish.single('ROOMS/*/' + m[1] + '%/' + m[0],
                      M.replace(m[0] + ' ' + m[1] + ' ', '') + ' XX:XX:XX:XX:XX:XX',
                      hostname=st.ip)

    def _handle_device_response(self, newtopic, msg, db):
        M = msg.payload.decode("utf-8")
        m = M.split(' ')

        if m[0] == 'request':
            self._handle_sensor_request(m)
        elif m[0] == 'response':
            self._handle_sensor_response(m)
        elif " response " in M:
            self._handle_device_state_response(M, db, newtopic)
        else:
            print('Err : om message')
    
    # ... (بقية الدوال)
    def _handle_sensor_request(self, m):
        """معالجة طلب قراءة المستشعرات"""
        while True:
            print('reading temp & humid ...')
            result = st.instance.read()
            if result.is_valid():
                temp = result.temperature
                humid = result.humidity
                print('read done .')
                message = 'response ' + str(temp) + ' ' + str(humid)
                for v in m[2:]:
                    message += ' ' + v
                publish.single(m[1], message, hostname=st.ip)
                break

    def _handle_sensor_response(self, m):
        """معالجة استجابة المستشعرات"""
        k = "دَرَجَةْ الحَرارَهْ هي  " + m[1] + ' دَرَجَهْ و الرُطوبَه تصل الى  ' + m[2] + ' دَرَجَهْ في '
        print('res')
        for v in m[3:]:
            k += ' ' + v
        self.tts.say(k)

    def _handle_device_state_response(self, M, db, newtopic):
        """معالجة استجابة حالة الجهاز"""
        M = M.replace(' response ', ' ')
        m = M.split(' ')
        cursor = db.cursor()

        try:
            # تحديث حالة الجهاز في قاعدة البيانات
            cursor.execute("UPDATE Devices SET state = '%s' WHERE id = '%s'" %
                          (M.replace(M.split(' ')[0] + ' ', ''), M.split(' ')[0]))
            db.commit()

            # الحصول على معلومات الغرفة
            cursor.execute("SELECT Rooms FROM Devices WHERE id LIKE '%s'" % M.split(' ')[0])
            result = cursor.fetchone()
            roomd = result[0]

            # إرسال التحديث للتطبيق
            device_id = M.split(' ')[0]
            if device_id in self.system.listdevices:
                device_type = self.system.listdevices[device_id]['device']
                publish.single(st.device_nb + '/app/phone',
                              roomd + ' ' + device_type + ' ' + M, hostname=st.ip)

                # مزامنة مع Homebridge
                self._sync_homebridge_state(device_id, device_type, M, m)

            if newtopic == st.device_nb:
                self.tts.say('done')

        except Exception as e:
            print(f"خطأ في معالجة استجابة الجهاز: {e}")
        finally:
            cursor.close()

    def _sync_homebridge_state(self, device_id, device_type, M, m):
        """مزامنة الحالة مع Homebridge"""
        if device_type == 'AC':
            if 'OFF' in M:
                publish.single('homebridge/' + device_id + '/getActive', 'false', hostname=st.ip)
            else:
                publish.single('homebridge/' + device_id + '/getActive', 'true', hostname=st.ip)

                if 'AC' in m[5]:
                    publish.single('homebridge/' + device_id + '/getTargetHeaterCoolerState',
                                 'COOL', hostname=st.ip)
                    publish.single('homebridge/' + device_id + '/getCoolingThresholdTemperature',
                                 m[2], hostname=st.ip)
                elif 'HEAT' in m[5]:
                    publish.single('homebridge/' + device_id + '/getTargetHeaterCoolerState',
                                 'HEAT', hostname=st.ip)
                    publish.single('homebridge/' + device_id + '/getHeatingThresholdTemperature',
                                 m[2], hostname=st.ip)
                elif 'VAN' in m[5]:
                    publish.single('homebridge/' + device_id + '/getTargetHeaterCoolerState',
                                 'AUTO', hostname=st.ip)
                    publish.single('homebridge/' + device_id + '/getHeatingThresholdTemperature',
                                 '30', hostname=st.ip)
                    publish.single('homebridge/' + device_id + '/getCoolingThresholdTemperature',
                                 '16', hostname=st.ip)

                publish.single('homebridge/' + device_id + '/getRotationSpeed',
                             str(int(m[4]) * 25), hostname=st.ip)

        elif device_type == 'TV':
            if 'POWER-OFF' in M:
                publish.single('homebridge/' + device_id + '/getActive', 'POWER-OFF', hostname=st.ip)
            elif 'POWER-ON' in M:
                publish.single('homebridge/' + device_id + '/getActive', 'POWER-ON', hostname=st.ip)

            time.sleep(1)

            if 'SIL-OFF' in M:
                publish.single('homebridge/' + device_id + '/getSil', 'SIL-OFF', hostname=st.ip)
            elif 'SIL-ON' in M:
                publish.single('homebridge/' + device_id + '/getSil', 'SIL-ON', hostname=st.ip)

        elif 'SWITCH' in device_type:
            for i in M.split(' ')[1:]:
                if i.split('_')[1] == 'OFF':
                    publish.single('homebridge/' + device_id + '::' + i.split('_')[0] + '/getActive',
                                 'false', hostname=st.ip)
                elif i.split('_')[1] == 'RUN':
                    publish.single('homebridge/' + device_id + '::' + i.split('_')[0] + '/getActive',
                                 'true', hostname=st.ip)

    def _handle_connect_message(self, msg, db):
        """معالجة رسائل الاتصال"""
        M = msg.payload.decode("utf-8")
        d = datetime.now()
        cursor = db.cursor()

        try:
            cursor.execute("UPDATE Devices SET connect = '%s' WHERE id = '%s'" %
                          (str(d.year) + '-' + str(d.month) + '-' + str(d.day) + '-' +
                           str(d.hour) + '-' + str(d.minute) + '-' + str(d.second), M))
            db.commit()
        except Exception as e:
            print(f"خطأ في تحديث حالة الاتصال: {e}")
        finally:
            cursor.close()

    def _handle_phone_ask(self, msg, db):
        """معالجة طلب التحقق من الهاتف"""
        M = msg.payload.decode("utf-8")
        cursor = db.cursor()

        try:
            cursor.execute("SELECT * FROM phones WHERE mac ='%s'" % M)
            result = cursor.fetchone()

            if result:
                publish.single(M, 'true/' + st.device_nb + '/' + result[3], hostname=st.ip)
            else:
                publish.single(M, 'false', hostname=st.ip)

        except Exception as e:
            print(f"خطأ في التحقق من الهاتف: {e}")
        finally:
            cursor.close()

    def _handle_phone_join(self, msg, db):
        """معالجة طلب انضمام الهاتف"""
        M = msg.payload.decode("utf-8")
        cursor = db.cursor()

        try:
            cursor.execute("SELECT * FROM phones")
            result = cursor.fetchall()

            if result:
                findMac = False
                for mac in result:
                    if M.split('/')[0] in mac:
                        publish.single(M.split('/')[0],
                                     'true/' + st.device_nb + '/' + mac[3], hostname=st.ip)
                        findMac = True
                        break

                if not findMac:
                    cursor.execute("INSERT INTO phones(name, type, mac, access) values('%s','%s','%s','%s')" %
                                 (M.split('/')[1], M.split('/')[2], M.split('/')[0], 'never'))
                    db.commit()
                    publish.single(M.split('/')[0], 'true/' + st.device_nb + '/never', hostname=st.ip)
            else:
                cursor.execute("INSERT INTO phones(name, type, mac, access) values('%s','%s','%s','%s')" %
                             (M.split('/')[1], M.split('/')[2], M.split('/')[0], 'full'))
                db.commit()
                publish.single(M.split('/')[0], 'True/' + st.device_nb + '/full', hostname=st.ip)

        except Exception as e:
            print(f"خطأ في انضمام الهاتف: {e}")
        finally:
            cursor.close()
