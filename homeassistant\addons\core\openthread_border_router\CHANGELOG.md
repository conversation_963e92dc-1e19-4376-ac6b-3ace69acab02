# Changelog

## 2.13.0
- Bump to OTBR POSIX version b067e5ac (2025-01-13 22:32:22 -0500)
- Bump universal-silabs-flasher to 0.0.28
- Remove dataset deletion REST API backwards compatibility patch. The minimum Core version for this add-on is now 2023.9.0

## 2.12.4

- Fix OTBR addon does not start after updating containerd.io to 1.7.24-1

## 2.12.3

- Enable recovery mechanism from "radio tx timeout" errors
- Increase the number of mesh header fragmentation tag entries to address
  "Failed to get forwarded frame priority" notice messages in logs. Note that
  these types of messages are non-critical (default priority will be applied in
  that case).
- Make some compile time configurations via project header file

## 2.12.2

- Update flasher script to work with Home Assistant Yellow with CM5

## 2.12.1
- Fix possible race condition between otbr-agent-configure and otbr-agent-rest-discovery
  services causing failed startup ([#3826](https://github.com/home-assistant/addons/issues/3826))

## 2.12.0
- Bump universal-silabs-flasher to 0.0.23
- Bump OTBR firmwares to latest versions
- Bump to OTBR POSIX version b041fa52daa (2024-11-14 08:18:28 -0800)
- Add radio firmware version to discovery information

## 2.11.1

-  Fix issue with USB TI CC2652 based devices

## 2.11.0

- Bump to OTBR POSIX version ff7227ea9a2 (2024-09-25 14:54:08 -0700)
- Make log output unbuffered
- Avoid ipset errors when firewall is disabled

## 2.10.0

- Bump to OTBR POSIX version b66cabfaa0 (2024-08-14 08:01:56 -0700)
- Avoid OTBR Web spamming system console
- Bump universal SiLabs flasher to 0.0.22

## 2.9.1

- Abort firmware flasher if network device is selected

## 2.9.0

- Avoid triggering reset/boot loader on TI CC2652 based devices

## 2.8.0

- Bump to OTBR POSIX version 41474ce29a (2024-06-21 08:41:31 -0700)

## 2.7.0

- Support auto firmware updates for Sonoff ZBDongle-E
- Support auto firmware updates for SMLIGHT SLZB-07
- Bump universal SiLabs flasher to 0.0.20

## 2.6.0

- Add support for network sockets using socat

## 2.5.1

- Support Home Assistant Connect ZBT-1.

## 2.5.0

- Bump to OTBR POSIX version 2279c02f3c (2024-02-28 22:36:55 -0800)
- Bump base image to Debian bookworm

## 2.4.7

- Better fix for container shutdown in case of OTBR agent failures

## 2.4.6

- Bump to OTBR POSIX version 9bdaa91016 (2024-02-15 08:50:34 -0800)
- Bump universal SiLabs flasher to 0.0.18
- Fix container shutdown in case OTBR agent fails to startup
- Shutdown mDNS daemon after OTBR agent (allows the OTBR service to
  properly sign off on the network)

## 2.4.5

- Set default transmit power on startup
- Enable DNS when NAT64 is enabled
- Bump universal SiLabs flasher to 0.0.17
- Bump to OTBR POSIX version 13d583e361 (2024-01-26 09:51:26 -0800)

## 2.4.4

- Fix Thread network interface (wpan0) route metric
  This fixes devices becoming unreachable when operating the OTBR with other TBRs
- Bump to OTBR POSIX version 02421b0ea6 (2024-01-19 15:58:03 -0800)

## 2.4.3

- Enable TREL support on infrastructure link
- Enable Channel Monitor support (disabled by default)
- Bump to OTBR POSIX version 657e775cd9 (2024-01-05 17:10:13 -0800)

## 2.4.2

- Update firmare for Home Assistant SkyConnect and Yellow to the latest version
  built from Gecko SDK v4.4.0.0.
- Bump universal SiLabs flasher to 0.0.16

## 2.4.1

- Fix NAT64 enable script

## 2.4.0

- Enable TREL
- Enable NAT64 (disabled by default)
- Bump to OTBR POSIX version 27ed99f375 (2023-12-13 10:11:52 -0800)
- Bump universal SiLabs flasher to 0.0.15
- Shutdown add-on on otbr-agent crash (use Supervisor Watchdog functionality
  for automatic restarts)

## 2.3.2

- Bump to OTBR POSIX version 9e50efa8de (2023-08-23 21:28:30 -0700)
  This updates mDNSResponder to 1790.80.10

## 2.3.1

- Update firmare for Home Assistant SkyConnect and Yellow to the latest version
  built from Gecko SDK v4.3.1.0.

## 2.3.0

- Bump to OTBR POSIX version 8d12b242db (2023-07-13 20:00:34 +0200)
  This update includes the new REST API to reset the OTBR
- Bump universal SiLabs flasher to 0.0.13
- Use add-on hostname to connect to OTBR REST API

## 2.2.0

- Update firmare for Home Assistant SkyConnect and Yellow to the latest version
  built from Gecko SDK v4.3.0.0.

## 2.1.0

- Add REST API patches to fix a bugs and support deleting datasets

## 2.0.0

- Bump to OTBR POSIX version f46f68956b (2023-05-23 09:28:30 -0700)
  This update includes the new REST API part of upstream OTBR

## 1.2.0

- Fix firmware flashing on Home Assistant Yellow
- Bump universal SiLabs flasher to 0.0.12
- Bump to OTBR POSIX version cbeaf817c5 (2023-03-29 11:06:31 -0700)
- Don't start Web interface unnecessarily

## 1.1.0

- Automatically flash firmware for Home Assistant SkyConnect and Yellow
- Update serial port defaults to match latest firmware builds
- Drop armv7 support

## 1.0.0

- Bump to OTBR POSIX version d83fee189a (2023-02-28 08:48:56 -0800)
- Remove Web UI via ingress (expose ports to use the Web UI, see documentation)
- Change vendor name to "Home Assistant" and product name to Silicon Labs
  Multiprotocol" (used in OTBR mDNS/DNS-SD announcments)
- Set default baudrate 115200 correctly
- Let the OTBR REST API listen on local interface only by default
- Fix REST API to correctly set the Connection HTTP header
- Fix REST API to return an HTTP compliant status line
- Add OTBR discovery support

## 0.3.0

- Bump to OTBR POSIX version 079bbce34a (2022-12-22 19:00:41 -0800)
- Add REST API with full active and pending dataset as well as state support
- Avoid start error in case multiple primary interfaces are returned
- Add fine grained OTBR log level control
- Fix service stop (finish) scripts

## 0.2.6

- Accept IPv6 forwarding explicitly (required for HAOS 9.x)
- Add egress firewall rules for forwarding if firewall is enabled

## 0.2.5

- Bump to OTBR POSIX version 110eb2507c (2022-11-24 14:36:14 -0800)

## 0.2.4

- Bump to OTBR POSIX version 0e15296792 (2022-11-07 12:33:00 +0100)

## 0.2.3

- Fix Firewall shutdown

## 0.2.2

- Bump to OTBR POSIX version 9fea68cfbe (2022-06-03 11:53:19 -0700)
- Use s6-overlay v3 style services

## 0.2.1

- Fix missing common script

## 0.2.0

- Support OpenThread Border Router firewall to avoid unnecessary traffic in the
  OpenThread network.

## 0.1.4

- Enable OpenThread diagnostic mode

## 0.1.3

- Fix startup without hardware flow control

## 0.1.2

- Bump OTBR to ot-br-posix git f8399eb08/openthread git 7dfde1f12

## 0.1.1

- Add baudrate and hardware flow control configurations

## 0.1.0

- initial version
