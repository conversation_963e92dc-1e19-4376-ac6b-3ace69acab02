---
version: 2.13.0
slug: openthread_border_router
name: OpenThread Border Router
description: OpenThread Border Router add-on
url: >-
  https://github.com/home-assistant/addons/tree/master/openthread_border_router
arch:
  - aarch64
  - amd64
homeassistant: 2023.9.0
gpio: true
hassio_api: true
discovery:
  - otbr
# IPC is only used within the Add-on
host_ipc: false
host_network: true
host_uts: true
privileged:
  - IPC_LOCK
  - NET_ADMIN
devices:
  - /dev/net/tun
image: homeassistant/{arch}-addon-otbr
init: false
options:
  device: null
  baudrate: "460800"
  flow_control: true
  autoflash_firmware: true
  otbr_log_level: notice
  firewall: true
  nat64: false
ports:
  8080/tcp: null
  8081/tcp: null
ports_description:
  8080/tcp: OpenThread Web port
  8081/tcp: OpenThread REST API port
schema:
  device: device(subsystem=tty)
  baudrate: list(57600|115200|230400|460800|921600)
  flow_control: bool
  network_device: str?
  autoflash_firmware: bool
  otbr_log_level: list(debug|info|notice|warning|error|critical|alert|emergency)
  firewall: bool
  nat64: bool
startup: services
