# C/C++ build system timings
generate_cxx_metadata
  [gap of 40ms]
  create-invalidation-state 129ms
  [gap of 39ms]
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 222ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 42ms]
  create-invalidation-state 125ms
  [gap of 51ms]
generate_cxx_metadata completed in 218ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 44ms]
  create-invalidation-state 181ms
  [gap of 51ms]
  write-metadata-json-to-file 20ms
generate_cxx_metadata completed in 298ms

