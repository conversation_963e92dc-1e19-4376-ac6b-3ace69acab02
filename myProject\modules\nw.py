import subprocess
import netifaces as ni
import sys
sys.path.append('/home/<USER>/myProject/resources')
import static as st


HOTSPOT_SSID = "ZAIN/"+ni.ifaddresses('wlan0')[17][0]['addr']
HOTSPOT_PASSWORD = "12345678"
HOTSPOT_IP = "***********/24"

def create_hotspot():
    """إنشاء نقطة وصول Wi-Fi باستخدام NetworkManager مع IP ثابت"""
    try:
        # حذف أي نقطة وصول سابقة
        subprocess.run(["nmcli", "connection", "delete", "ZAIN_HOTSPOT"], check=False)

        # إنشاء نقطة وصول جديدة
        subprocess.run(["nmcli", "connection", "add", "type", "wifi", "ifname", "wlan0", "con-name", "ZAIN_HOTSPOT",
                        "autoconnect", "yes", "ssid", HOTSPOT_SSID], check=True)
        subprocess.run(["nmcli", "connection", "modify", "ZAIN_HOTSPOT", "802-11-wireless.mode", "ap",
                        "802-11-wireless.band", "bg", "ipv4.method", "shared"], check=True)
        subprocess.run(["nmcli", "connection", "modify", "ZAIN_HOTSPOT", "wifi-sec.key-mgmt", "wpa-psk"], check=True)
        subprocess.run(["nmcli", "connection", "modify", "ZAIN_HOTSPOT", "wifi-sec.psk", HOTSPOT_PASSWORD], check=True)

        # تعيين عنوان IP ثابت لنقطة الوصول
        subprocess.run(["nmcli", "connection", "modify", "ZAIN_HOTSPOT", "ipv4.addresses", HOTSPOT_IP], check=True)
        subprocess.run(["nmcli", "connection", "modify", "ZAIN_HOTSPOT", "ipv4.method", "shared"], check=True)
        
         # تكوين نقطة الوصول لتبقى مستقرة
        # تفعيل الاتصال التلقائي لكن مع منع NetworkManager من التدخل المتكرر
        subprocess.run(["nmcli", "connection", "modify", HOTSPOT_SSID, "connection.autoconnect", "yes"], check=True)
        subprocess.run(["nmcli", "connection", "modify", HOTSPOT_SSID, "connection.autoconnect-priority", "10"], check=True)
        subprocess.run(["nmcli", "connection", "modify", HOTSPOT_SSID, "connection.autoconnect-retries", "-1"], check=True)
        # منع معالج الشبكة من محاولة إصلاح الاتصال
        subprocess.run(["nmcli", "connection", "modify", HOTSPOT_SSID, "connection.auth-retries", "0"], check=True)
        # إعداد طاقة الإرسال
        subprocess.run(["nmcli", "connection", "modify", HOTSPOT_SSID, "802-11-wireless.powersave", "2"], check=True)

        # تشغيل نقطة الوصول
        subprocess.run(["nmcli", "connection", "up", "ZAIN_HOTSPOT"], check=True)

        print(f"✅ تم تشغيل نقطة الوصول: {HOTSPOT_SSID} مع IP ثابت {HOTSPOT_IP}")
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل تشغيل نقطة الوصول: {e}")
        
def connect_wifi(ssid, password,main):
    """الاتصال بشبكة Wi-Fi عادية"""
    try:
        stop_hotspot()
        subprocess.run(["nmcli", "device", "wifi", "connect", ssid, "password", password], check=True)
        print(f"✅ تم الاتصال بالشبكة: {ssid}")
        if main=='1':
            c=open(st.pathFiles+'main.txt','w')
            c.write('1')
            c.close()
            
            c=open(st.pathFiles+'state.txt','w')
            c.write('0')
            c.close()
            
        else:
            c=open(st.pathFiles+'main.txt','w')
            c.write('0')
            c.close()
            c=open(st.pathFiles+'state.txt','w')
            c.write('0')
            c.close()
            
        AP=open(st.pathFiles+'ap.txt','w')
        AP.write('0')
        AP.close()    

    except subprocess.CalledProcessError:
        start_hotspot()        
        print("❌ فشل الاتصال بشبكة Wi-Fi")

def stop_hotspot():
    """إيقاف نقطة الوصول وإعادة تمكين Wi-Fi العادي"""
    try:
        subprocess.run(["nmcli", "radio", "wifi", "on"], check=False)
        subprocess.run(["nmcli", "connection", "down", HOTSPOT_SSID], check=False)
        print("✅ تم إيقاف نقطة الوصول")
    except subprocess.CalledProcessError:
        print("❌ فشل في إيقاف نقطة الوصول")
def start_hotspot():
    """إيقاف نقطة الوصول وإعادة تمكين Wi-Fi العادي"""
    try:
#         subprocess.run(["nmcli", "radio", "wifi", "off"], check=True)
        subprocess.run(["nmcli", "connection", "up", HOTSPOT_SSID], check=False)
        print("✅ تم إيقاف نقطة الوصول")
    except subprocess.CalledProcessError:
        print("❌ فشل في إيقاف نقطة الوصول")
