---
version: 5.8.0
slug: configurator
name: File editor
description: Simple browser-based file editor for Home Assistant
url: https://github.com/home-assistant/addons/tree/master/configurator
codenotary: <EMAIL>
arch:
  - armhf
  - armv7
  - aarch64
  - amd64
  - i386
homeassistant: 0.91.1
homeassistant_api: true
image: homeassistant/{arch}-addon-configurator
ingress: true
init: false
map:
  - all_addon_configs:rw
  - backup:rw
  - homeassistant_config:rw
  - media:rw
  - share:rw
  - ssl:rw
options:
  dirsfirst: false
  enforce_basepath: true
  git: true
  ignore_pattern:
    - __pycache__
    - .cloud
    - .storage
    - deps
  ssh_keys: []
panel_icon: mdi:wrench
schema:
  dirsfirst: bool
  enforce_basepath: bool
  git: bool
  ignore_pattern:
    - str
  ssh_keys:
    - str
