import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:mqtt_client/mqtt_client.dart';
import '../modules/local/mqtt.dart' as mqtt_module;
import 'advanced_automation_service.dart';
import 'smart_recommendations.dart';

/// خدمة الإشعارات الذكية للطقس
class NotificationService extends GetxController {
  static NotificationService get instance => Get.find<NotificationService>();

  final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();

  // إعدادات الإشعارات
  final RxBool _weatherNotificationsEnabled = true.obs;
  final RxBool _smartSuggestionsEnabled = true.obs;
  final RxBool _extremeWeatherAlerts = true.obs;
  final RxBool _deviceAutomationSuggestions = true.obs;

  // إعدادات إشعارات قواعد الأتمتة (يتم تحديثها ديناميكياً)
  final RxMap<String, bool> _automationRuleNotifications = <String, bool>{}.obs;

  // قائمة الإشعارات النشطة
  final RxList<WeatherNotification> _activeNotifications =
      <WeatherNotification>[].obs;

  // بيانات الطقس السابقة للمقارنة
  Map<String, dynamic> _previousWeatherData = {};

  // قائمة الإشعارات المرسلة لمنع التكرار
  final Set<String> _sentNotifications = <String>{};

  // آخر وقت تم فيه إعادة تعيين قائمة الإشعارات
  DateTime _lastNotificationReset = DateTime.now();

  // Getters للإعدادات
  bool get weatherNotificationsEnabled => _weatherNotificationsEnabled.value;
  bool get smartSuggestionsEnabled => _smartSuggestionsEnabled.value;
  bool get extremeWeatherAlerts => _extremeWeatherAlerts.value;
  bool get deviceAutomationSuggestions => _deviceAutomationSuggestions.value;
  List<WeatherNotification> get activeNotifications => _activeNotifications;
  Map<String, bool> get automationRuleNotifications =>
      _automationRuleNotifications;

  @override
  Future<void> onInit() async {
    print('🚀 بدء تهيئة خدمة الإشعارات...');
    super.onInit();

    await _initializeNotifications();
    print('✅ تم تهيئة نظام الإشعارات');

    await _loadSettings();
    print('✅ تم تحميل إعدادات الإشعارات');

    // تحميل إعدادات قواعد الأتمتة
    await _loadAutomationRuleSettings();

    // إعداد مستمعي MQTT للإشعارات الخارجية
    _setupMqttListeners();

    print('🎉 خدمة الإشعارات جاهزة للعمل!');
  }

  /// تهيئة نظام الإشعارات
  Future<void> _initializeNotifications() async {
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // طلب الأذونات
    await _requestPermissions();
  }

  /// طلب أذونات الإشعارات
  Future<void> _requestPermissions() async {
    await _notifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.requestNotificationsPermission();
  }

  /// إعداد مستمعي MQTT للإشعارات الخارجية
  void _setupMqttListeners() {
    try {
      if (mqtt_module.client.updates != null) {
        mqtt_module.client.updates!
            .listen((List<MqttReceivedMessage<MqttMessage>> messages) {
          for (var message in messages) {
            _handleMqttMessage(message);
          }
        });

        // الاشتراك في موضوع الإشعارات الخارجية
        mqtt_module.client.subscribe('notifications/send', MqttQos.atLeastOnce);
        print('📡 تم الاشتراك في موضوع notifications/send');
      }
    } catch (e) {
      print('❌ خطأ في إعداد مستمعي MQTT للإشعارات: $e');
    }
  }

  /// معالجة رسائل MQTT
  void _handleMqttMessage(MqttReceivedMessage<MqttMessage> message) {
    try {
      final topic = message.topic;
      final payload = MqttPublishPayload.bytesToStringAsString(
        (message.payload as MqttPublishMessage).payload.message,
      );

      if (topic == 'notifications/send') {
        handleExternalNotification(payload);
      }
    } catch (e) {
      print('❌ خطأ في معالجة رسالة MQTT: $e');
    }
  }

  /// معالجة الإشعارات الخارجية من النظام
  Future<void> handleExternalNotification(String payload) async {
    try {
      final data = json.decode(payload);
      final ruleId = data['rule_id'] as String?;

      if (ruleId == null) {
        print('❌ لم يتم توفير rule_id في الإشعار');
        return;
      }

      // فحص ما إذا كانت الإشعارات مفعلة لهذه القاعدة
      if (!_isAutomationRuleNotificationEnabled(ruleId)) {
        print('🔕 إشعارات قاعدة الأتمتة $ruleId معطلة');
        return;
      }

      // جلب بيانات القاعدة من خدمة الأتمتة
      final notificationData = await _getNotificationDataFromRule(ruleId);
      if (notificationData == null) {
        print('❌ لم يتم العثور على قاعدة الأتمتة: $ruleId');
        return;
      }

      // إنشاء الإشعار
      await _showExternalNotification(
        title: notificationData['title']!,
        body: notificationData['message']!,
        priority: _mapStringToPriority(notificationData['priority']!),
        ruleId: ruleId,
      );

      print('🔔 تم استقبال وعرض إشعار خارجي: ${notificationData['title']}');
    } catch (e) {
      print('❌ خطأ في معالجة الإشعار الخارجي: $e');
    }
  }

  /// فحص ما إذا كانت إشعارات قاعدة الأتمتة مفعلة
  bool _isAutomationRuleNotificationEnabled(String ruleId) {
    return _automationRuleNotifications[ruleId] ?? true; // افتراضياً مفعلة
  }

  /// جلب بيانات الإشعار من قاعدة الأتمتة
  Future<Map<String, String>?> _getNotificationDataFromRule(
      String ruleId) async {
    try {
      final automationService = Get.find<AdvancedAutomationService>();
      final rule = automationService.getAutomationRule(ruleId);

      if (rule == null) {
        return null;
      }

      // البحث عن إجراء الإشعار في القاعدة
      final notificationAction = rule.actions.firstWhere(
        (action) =>
            action.type.toString().contains('notification') ||
            action.type.toString().contains('send_notification'),
        orElse: () => throw StateError('No notification action found'),
      );

      // استخراج بيانات الإشعار من الإجراء
      final title =
          notificationAction.config['title'] as String? ?? '🤖 ${rule.name}';
      final message = notificationAction.config['message'] as String? ??
          (rule.description.isNotEmpty
              ? rule.description
              : 'تم تنفيذ قاعدة الأتمتة');
      final priority =
          notificationAction.config['priority'] as String? ?? 'normal';

      return {
        'title': title,
        'message': message,
        'priority': priority,
      };
    } catch (e) {
      print('❌ خطأ في جلب بيانات الإشعار من القاعدة: $e');
      return null;
    }
  }

  /// تحويل النص إلى أولوية الإشعار
  NotificationPriority _mapStringToPriority(String priority) {
    switch (priority.toLowerCase()) {
      case 'urgent':
        return NotificationPriority.urgent;
      case 'high':
        return NotificationPriority.high;
      case 'low':
        return NotificationPriority.low;
      default:
        return NotificationPriority.normal;
    }
  }

  /// عرض إشعار خارجي
  Future<void> _showExternalNotification({
    required String title,
    required String body,
    required NotificationPriority priority,
    String? ruleId,
  }) async {
    final payload = {
      'type': 'automation_notification',
      'rule_id': ruleId,
      'source': 'external',
    };

    await _showNotification(
      title: title,
      body: body,
      type: NotificationType.automationNotification,
      priority: priority,
      payload: payload,
    );
  }

  /// معالجة النقر على الإشعار
  void _onNotificationTapped(NotificationResponse response) {
    final payload = response.payload;
    if (payload != null) {
      try {
        final data = json.decode(payload);
        _handleNotificationAction(data);
      } catch (e) {
        print('خطأ في معالجة payload الإشعار: $e');
      }
    }
  }

  /// معالجة إجراءات الإشعار
  void _handleNotificationAction(Map<String, dynamic> data) {
    final type = data['type'] as String?;
    final action = data['action'] as String?;

    switch (type) {
      case 'weather_alert':
        _handleWeatherAlert(data);
        break;
      case 'smart_suggestion':
        _handleSmartSuggestion(data);
        break;
      case 'device_automation':
        _handleDeviceAutomation(data);
        break;
    }
  }

  /// إعادة تعيين قائمة الإشعارات كل 30 دقيقة لتجنب التراكم
  void _resetNotificationsIfNeeded() {
    final now = DateTime.now();
    if (now.difference(_lastNotificationReset).inMinutes >= 30) {
      _sentNotifications.clear();
      _lastNotificationReset = now;
      _lastWeatherDataHash = null; // إعادة تعيين hash البيانات أيضاً
      print('🔄 تم إعادة تعيين قائمة الإشعارات المرسلة');
    }
  }

  /// إنشاء مفتاح فريد للإشعار بناءً على المحتوى
  String _generateNotificationKey(String title, String body, String type) {
    // إزالة الرموز التعبيرية والمسافات الزائدة لضمان التطابق الصحيح
    final cleanTitle = title.replaceAll(RegExp(r'[^\w\s]'), '').trim();
    final cleanBody = body.replaceAll(RegExp(r'[^\w\s]'), '').trim();
    final content = '$type:$cleanTitle:$cleanBody';
    return content.hashCode.toString();
  }

  /// فحص ما إذا كان يجب إرسال إشعار بناءً على التغييرات المعنوية والوقت
  bool _shouldSendNotification(
      Map<String, dynamic> weatherData, String notificationType) {
    final now = DateTime.now();

    // فحص الفترة الزمنية الدنيا بين الإشعارات
    if (_lastNotificationTime != null) {
      final timeDifference = now.difference(_lastNotificationTime!).inMinutes;
      if (timeDifference < _minimumNotificationIntervalMinutes) {
        print(
            '⏰ تم تجاهل الإشعار: لم تمر فترة كافية (${timeDifference} دقيقة < ${_minimumNotificationIntervalMinutes} دقيقة)');
        return false;
      }
    }

    final temperature = (weatherData['temperature'] as num?)?.toDouble();
    final humidity = (weatherData['humidity'] as num?)?.toDouble();
    final windSpeed = (weatherData['wind_speed'] as num?)?.toDouble();
    final condition = weatherData['condition'] as String?;

    // فحص التغييرات المعنوية
    bool significantChange = false;

    // فحص تغيير درجة الحرارة
    if (temperature != null && _lastNotifiedTemperature != null) {
      final tempDifference = (temperature - _lastNotifiedTemperature!).abs();
      if (tempDifference >= _temperatureTolerance) {
        significantChange = true;
        print(
            '🌡️ تغيير معنوي في درجة الحرارة: ${tempDifference.toStringAsFixed(1)}°C');
      }
    } else if (temperature != null && _lastNotifiedTemperature == null) {
      significantChange = true; // أول إشعار
    }

    // فحص تغيير الرطوبة
    if (humidity != null && _lastNotifiedHumidity != null) {
      final humidityDifference = (humidity - _lastNotifiedHumidity!).abs();
      if (humidityDifference >= _humidityTolerance) {
        significantChange = true;
        print(
            '💧 تغيير معنوي في الرطوبة: ${humidityDifference.toStringAsFixed(1)}%');
      }
    }

    // فحص تغيير حالة الطقس
    if (condition != null &&
        _lastNotifiedCondition != null &&
        condition != _lastNotifiedCondition) {
      significantChange = true;
      print('🌤️ تغيير في حالة الطقس: $_lastNotifiedCondition → $condition');
    }

    if (significantChange) {
      // تحديث القيم المحفوظة
      _lastNotifiedTemperature = temperature;
      _lastNotifiedHumidity = humidity;
      _lastNotifiedWindSpeed = windSpeed;
      _lastNotifiedCondition = condition;
      _lastNotificationTime = now;
      return true;
    }

    print('📊 لا توجد تغييرات معنوية في الطقس');
    return false;
  }

  // متغير لتتبع آخر تحليل للطقس لمنع التكرار
  String? _lastWeatherDataHash;

  // متغيرات لتتبع آخر القيم المرسلة للإشعارات
  double? _lastNotifiedTemperature;
  double? _lastNotifiedHumidity;
  double? _lastNotifiedWindSpeed;
  String? _lastNotifiedCondition;
  DateTime? _lastNotificationTime;

  // حدود التسامح للتغييرات
  static const double _temperatureTolerance = 2.0; // درجتان مئويتان
  static const double _humidityTolerance = 10.0; // 10%
  static const double _windSpeedTolerance = 5.0; // 5 كم/ساعة
  static const int _minimumNotificationIntervalMinutes = 15; // 15 دقيقة

  /// تحليل بيانات الطقس وإرسال الإشعارات المناسبة
  Future<void> analyzeWeatherAndNotify(Map<String, dynamic> weatherData) async {
    print('🔍 بدء تحليل بيانات الطقس: $weatherData');

    // إنشاء hash للبيانات لمنع التحليل المتكرر لنفس البيانات
    final weatherDataHash = weatherData.toString().hashCode.toString();
    if (_lastWeatherDataHash == weatherDataHash) {
      print('⏭️ تم تجاهل تحليل الطقس (نفس البيانات السابقة)');
      return;
    }
    _lastWeatherDataHash = weatherDataHash;

    // إعادة تعيين قائمة الإشعارات إذا لزم الأمر
    _resetNotificationsIfNeeded();

    if (!weatherNotificationsEnabled) {
      print('❌ إشعارات الطقس معطلة');
      return;
    }

    try {
      // فحص التغيرات الجذرية في الطقس
      await _checkWeatherChanges(weatherData);

      // فحص الطقس المتطرف
      await _checkExtremeWeather(weatherData);

      // إنشاء التوصيات الذكية
      await _generateSmartSuggestions(weatherData);

      // حفظ البيانات الحالية للمقارنة المستقبلية
      _previousWeatherData = Map.from(weatherData);

      print('✅ تم تحليل بيانات الطقس بنجاح');
      print('📱 عدد الإشعارات النشطة: ${_activeNotifications.length}');
    } catch (e) {
      print('خطأ في تحليل بيانات الطقس: $e');
    }
  }

  /// فحص التغيرات الجذرية في الطقس
  Future<void> _checkWeatherChanges(Map<String, dynamic> currentWeather) async {
    if (_previousWeatherData.isEmpty) return;

    final currentTemp = currentWeather['temperature']?.toDouble() ?? 0.0;
    final previousTemp = _previousWeatherData['temperature']?.toDouble() ?? 0.0;
    final tempDifference = (currentTemp - previousTemp).abs();

    // تغير كبير في درجة الحرارة (أكثر من 5 درجات)
    if (tempDifference > 5.0) {
      final message = tempDifference > 0
          ? 'ارتفعت درجة الحرارة بشكل كبير إلى ${currentTemp.toStringAsFixed(1)}°C'
          : 'انخفضت درجة الحرارة بشكل كبير إلى ${currentTemp.toStringAsFixed(1)}°C';

      await _showNotification(
        title: 'تغير كبير في الطقس',
        body: message,
        type: NotificationType.weatherChange,
        priority: NotificationPriority.high,
      );
    }

    // تغير في حالة الطقس
    final currentCondition = currentWeather['condition'] as String?;
    final previousCondition = _previousWeatherData['condition'] as String?;

    if (currentCondition != previousCondition && currentCondition != null) {
      final conditionText = _getWeatherConditionText(currentCondition);
      await _showNotification(
        title: 'تغيرت حالة الطقس',
        body: 'الطقس الآن: $conditionText',
        type: NotificationType.weatherChange,
      );
    }
  }

  /// فحص الطقس المتطرف
  Future<void> _checkExtremeWeather(Map<String, dynamic> weatherData) async {
    print('🌡️ فحص الطقس المتطرف...');
    print('🔧 إعدادات التحذيرات المتطرفة: $extremeWeatherAlerts');

    if (!extremeWeatherAlerts) {
      print('❌ تحذيرات الطقس المتطرف معطلة');
      return;
    }

    final temperature = weatherData['temperature']?.toDouble() ?? 0.0;
    final humidity = weatherData['humidity']?.toDouble() ?? 0.0;
    final windSpeed = weatherData['wind_speed']?.toDouble() ?? 0.0;
    final condition = weatherData['condition'] as String?;

    print('🌡️ درجة الحرارة الحالية: ${temperature}°C');
    print('💧 الرطوبة الحالية: ${humidity}%');

    // فحص ما إذا كان يجب إرسال تحذيرات الطقس المتطرف
    if (!_shouldSendNotification(weatherData, 'extreme_weather')) {
      print(
          '⏭️ تم تجاهل تحذيرات الطقس المتطرف: لا توجد تغييرات معنوية أو لم تمر فترة كافية');
      return;
    }

    // درجة حرارة عالية جداً
    if (temperature > 40.0) {
      print('🔥 درجة الحرارة عالية! إرسال إشعار تحذير...');
      await _showNotification(
        title: '⚠️ تحذير: حرارة عالية جداً',
        body:
            'درجة الحرارة ${temperature.toStringAsFixed(1)}°C - تجنب التعرض للشمس',
        type: NotificationType.extremeWeather,
        priority: NotificationPriority.urgent,
      );
      print('✅ تم إرسال إشعار درجة الحرارة العالية');
    } else {
      print('✅ درجة الحرارة ضمن المعدل الطبيعي');
    }

    // درجة حرارة منخفضة جداً
    if (temperature < 5.0) {
      await _showNotification(
        title: '🥶 تحذير: برودة شديدة',
        body:
            'درجة الحرارة ${temperature.toStringAsFixed(1)}°C - ارتدي ملابس دافئة',
        type: NotificationType.extremeWeather,
        priority: NotificationPriority.urgent,
      );
    }

    // رطوبة عالية جداً
    if (humidity > 90.0) {
      await _showNotification(
        title: '💧 رطوبة عالية',
        body: 'الرطوبة ${humidity.toStringAsFixed(0)}% - قد تشعر بعدم الراحة',
        type: NotificationType.extremeWeather,
      );
      print('✅ تم إرسال إشعار الرطوبة العالية');
    }

    // رياح قوية
    if (windSpeed > 50.0) {
      await _showNotification(
        title: '💨 رياح قوية',
        body: 'سرعة الرياح ${windSpeed.toStringAsFixed(1)} كم/س - كن حذراً',
        type: NotificationType.extremeWeather,
        priority: NotificationPriority.high,
      );
    }

    // طقس ممطر
    if (condition == 'rainy' || condition == 'pouring') {
      await _showNotification(
        title: '🌧️ طقس ممطر',
        body: 'تأكد من إغلاق النوافذ وأخذ المظلة معك',
        type: NotificationType.weatherAlert,
      );
    }
  }

  /// إنشاء التوصيات الذكية
  Future<void> _generateSmartSuggestions(
      Map<String, dynamic> weatherData) async {
    if (!smartSuggestionsEnabled) return;

    // فحص ما إذا كان يجب إرسال توصيات جديدة
    if (!_shouldSendNotification(weatherData, 'smart_suggestions')) {
      print(
          '⏭️ تم تجاهل التوصيات الذكية: لا توجد تغييرات معنوية أو لم تمر فترة كافية');
      return;
    }

    try {
      // استخدام محرك التوصيات الذكية المتقدم
      final recommendations =
          SmartRecommendations.analyzeWeatherAndGenerateRecommendations(
        weatherData,
        _previousWeatherData,
      );

      // إرسال أفضل 3 توصيات كإشعارات
      for (int i = 0; i < recommendations.length && i < 3; i++) {
        final recommendation = recommendations[i];

        await _showSmartSuggestion(
          title: '${recommendation.getCategoryIcon()} ${recommendation.title}',
          body: recommendation.description,
          action: recommendation.action,
          deviceType: recommendation.deviceType,
          metadata: {
            'recommendation_id': recommendation.id,
            'category': recommendation.category.toString(),
            'energy_saving': recommendation.estimatedEnergySaving,
            'comfort_impact': recommendation.comfortImpact,
            'priority': recommendation.priority,
          },
        );

        // تأخير قصير بين الإشعارات لتجنب الإزعاج
        await Future.delayed(const Duration(seconds: 1));
      }
    } catch (e) {
      print('خطأ في إنشاء التوصيات الذكية: $e');
    }
  }

  // عداد للإشعارات لضمان معرفات فريدة وصغيرة
  static int _notificationIdCounter = 1000;

  /// عرض إشعار عادي
  Future<void> _showNotification({
    required String title,
    required String body,
    required NotificationType type,
    NotificationPriority priority = NotificationPriority.normal,
    Map<String, dynamic>? payload,
  }) async {
    // فحص التكرار بناءً على المحتوى
    final notificationKey =
        _generateNotificationKey(title, body, type.toString());
    if (_sentNotifications.contains(notificationKey)) {
      print('⏭️ تم تجاهل إشعار مكرر: $title');
      return;
    }

    print('📢 إنشاء إشعار جديد: $title');

    // إنشاء معرف صغير وفريد للإشعار
    final notificationId = _notificationIdCounter++;
    if (_notificationIdCounter > 999999) {
      _notificationIdCounter = 1000; // إعادة تعيين العداد
    }

    final notification = WeatherNotification(
      id: notificationId,
      title: title,
      body: body,
      type: type,
      priority: priority,
      timestamp: DateTime.now(),
      payload: payload,
    );

    print('📱 عرض الإشعار: ${notification.id}');
    await _displayNotification(notification);

    _activeNotifications.add(notification);
    _sentNotifications.add(notificationKey); // إضافة المفتاح لمنع التكرار
    print(
        '✅ تم إضافة الإشعار للقائمة النشطة. العدد الحالي: ${_activeNotifications.length}');

    // تحديث الواجهة
    update();
    print('🔄 تم تحديث الواجهة');
  }

  /// عرض اقتراح ذكي
  Future<void> _showSmartSuggestion({
    required String title,
    required String body,
    required String action,
    required String deviceType,
    Map<String, dynamic>? metadata,
  }) async {
    if (!deviceAutomationSuggestions) return;

    final payload = {
      'type': 'smart_suggestion',
      'action': action,
      'device_type': deviceType,
      if (metadata != null) ...metadata,
    };

    await _showNotification(
      title: title,
      body: body,
      type: NotificationType.smartSuggestion,
      payload: payload,
    );
  }

  /// عرض الإشعار الفعلي
  Future<void> _displayNotification(WeatherNotification notification) async {
    final androidDetails = AndroidNotificationDetails(
      'weather_channel',
      'إشعارات الطقس',
      channelDescription: 'إشعارات وتوصيات ذكية بناءً على حالة الطقس',
      importance: _getAndroidImportance(notification.priority),
      priority: _getAndroidPriority(notification.priority),
      icon: _getNotificationIcon(notification.type),
      color: _getNotificationColor(notification.type),
      enableVibration: notification.priority != NotificationPriority.low,
      playSound: notification.priority == NotificationPriority.urgent,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      notification.id,
      notification.title,
      notification.body,
      details,
      payload: notification.payload != null
          ? json.encode(notification.payload)
          : null,
    );
  }

  /// معالجة تنبيه الطقس
  void _handleWeatherAlert(Map<String, dynamic> data) {
    // يمكن إضافة منطق خاص لمعالجة تنبيهات الطقس
    print('تم النقر على تنبيه الطقس: $data');
  }

  /// معالجة الاقتراح الذكي
  void _handleSmartSuggestion(Map<String, dynamic> data) {
    final action = data['action'] as String?;
    final deviceType = data['device_type'] as String?;

    // عرض حوار تأكيد للمستخدم
    Get.dialog(
      AlertDialog(
        title: Text('تنفيذ الاقتراح الذكي'),
        content: Text('هل تريد تنفيذ هذا الاقتراح؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _executeSmartAction(action!, deviceType!);
            },
            child: Text('تنفيذ'),
          ),
        ],
      ),
    );
  }

  /// معالجة أتمتة الجهاز
  void _handleDeviceAutomation(Map<String, dynamic> data) {
    // منطق أتمتة الأجهزة
    print('تم النقر على أتمتة الجهاز: $data');
  }

  /// تنفيذ الإجراء الذكي
  void _executeSmartAction(String action, String deviceType) {
    // هنا يمكن إرسال أوامر MQTT للأجهزة
    print('تنفيذ الإجراء: $action للجهاز: $deviceType');

    // مثال على إرسال أمر MQTT
    switch (action) {
      case 'turn_on_ac':
        _sendMQTTCommand('room_01/ac/command', 'turn_on');
        break;
      case 'turn_on_heater':
        _sendMQTTCommand('room_01/heater/command', 'turn_on');
        break;
      case 'open_windows_turn_off_ac':
        _sendMQTTCommand('room_01/ac/command', 'turn_off');
        _sendMQTTCommand('room_01/windows/command', 'open');
        break;
      case 'water_plants':
        _sendMQTTCommand('garden/irrigation/command', 'start');
        break;
    }

    Get.snackbar(
      'تم التنفيذ',
      'تم تنفيذ الاقتراح الذكي بنجاح',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  /// إرسال أمر MQTT
  void _sendMQTTCommand(String topic, String command) {
    // هذه الدالة ستتصل بخدمة MQTT
    // يمكن تطويرها لاحقاً للاتصال الفعلي
    print('إرسال أمر MQTT: $topic -> $command');
  }

  /// الحصول على نص حالة الطقس بالعربية
  String _getWeatherConditionText(String condition) {
    switch (condition.toLowerCase()) {
      case 'clear':
      case 'sunny':
        return 'صافي ومشمس';
      case 'partlycloudy':
        return 'غائم جزئياً';
      case 'cloudy':
        return 'غائم';
      case 'rainy':
        return 'ممطر';
      case 'pouring':
        return 'مطر غزير';
      case 'snowy':
        return 'ثلجي';
      case 'windy':
        return 'عاصف';
      case 'fog':
        return 'ضبابي';
      default:
        return condition;
    }
  }

  /// الحصول على أهمية Android
  Importance _getAndroidImportance(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Importance.low;
      case NotificationPriority.normal:
        return Importance.defaultImportance;
      case NotificationPriority.high:
        return Importance.high;
      case NotificationPriority.urgent:
        return Importance.max;
    }
  }

  /// الحصول على أولوية Android
  Priority _getAndroidPriority(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Priority.low;
      case NotificationPriority.normal:
        return Priority.defaultPriority;
      case NotificationPriority.high:
        return Priority.high;
      case NotificationPriority.urgent:
        return Priority.max;
    }
  }

  /// الحصول على أيقونة الإشعار
  String _getNotificationIcon(NotificationType type) {
    // استخدام أيقونة التطبيق الافتراضية لجميع الإشعارات
    // يمكن إضافة أيقونات مخصصة لاحقاً في مجلد drawable
    return '@mipmap/ic_launcher';
  }

  /// الحصول على لون الإشعار
  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.weatherChange:
        return Colors.blue;
      case NotificationType.extremeWeather:
        return Colors.red;
      case NotificationType.smartSuggestion:
        return Colors.green;
      case NotificationType.weatherAlert:
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }

  /// تحميل الإعدادات من SharedPreferences
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _weatherNotificationsEnabled.value =
        prefs.getBool('weather_notifications') ?? true;
    _smartSuggestionsEnabled.value = prefs.getBool('smart_suggestions') ?? true;
    _extremeWeatherAlerts.value =
        prefs.getBool('extreme_weather_alerts') ?? true;
    _deviceAutomationSuggestions.value =
        prefs.getBool('device_automation_suggestions') ?? true;
  }

  /// حفظ الإعدادات في SharedPreferences
  Future<void> saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(
        'weather_notifications', _weatherNotificationsEnabled.value);
    await prefs.setBool('smart_suggestions', _smartSuggestionsEnabled.value);
    await prefs.setBool('extreme_weather_alerts', _extremeWeatherAlerts.value);
    await prefs.setBool(
        'device_automation_suggestions', _deviceAutomationSuggestions.value);
  }

  /// تحديث إعدادات الإشعارات
  void updateSettings({
    bool? weatherNotifications,
    bool? smartSuggestions,
    bool? extremeWeatherAlerts,
    bool? deviceAutomationSuggestions,
  }) {
    if (weatherNotifications != null)
      _weatherNotificationsEnabled.value = weatherNotifications;
    if (smartSuggestions != null)
      _smartSuggestionsEnabled.value = smartSuggestions;
    if (extremeWeatherAlerts != null)
      _extremeWeatherAlerts.value = extremeWeatherAlerts;
    if (deviceAutomationSuggestions != null)
      _deviceAutomationSuggestions.value = deviceAutomationSuggestions;

    saveSettings();
  }

  /// تحديث إعدادات إشعارات قاعدة أتمتة محددة
  void updateAutomationRuleNotification(String ruleId, bool enabled) {
    _automationRuleNotifications[ruleId] = enabled;
    _saveAutomationRuleSettings();
  }

  /// تحديث إعدادات قواعد الأتمتة من خدمة الأتمتة
  void syncAutomationRulesSettings() {
    try {
      final automationService = AdvancedAutomationService.instance;

      // إضافة قواعد جديدة تحتوي على إجراءات إشعار
      for (var rule in automationService.automationRules) {
        final hasNotificationAction = rule.actions.any((action) =>
            action.type.toString().contains('notification') ||
            action.type.toString().contains('send_notification'));

        if (hasNotificationAction &&
            !_automationRuleNotifications.containsKey(rule.id)) {
          _automationRuleNotifications[rule.id] = true; // افتراضياً مفعلة
        }
      }

      _saveAutomationRuleSettings();
      print('🔄 تم مزامنة إعدادات قواعد الأتمتة');
    } catch (e) {
      print('❌ خطأ في مزامنة إعدادات قواعد الأتمتة: $e');
    }
  }

  /// حفظ إعدادات قواعد الأتمتة
  Future<void> _saveAutomationRuleSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = json.encode(_automationRuleNotifications);
      await prefs.setString('automation_rule_notifications', settingsJson);
    } catch (e) {
      print('❌ خطأ في حفظ إعدادات قواعد الأتمتة: $e');
    }
  }

  /// تحميل إعدادات قواعد الأتمتة
  Future<void> _loadAutomationRuleSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString('automation_rule_notifications');

      if (settingsJson != null) {
        final settings = json.decode(settingsJson) as Map<String, dynamic>;
        _automationRuleNotifications.clear();
        settings.forEach((key, value) {
          _automationRuleNotifications[key] = value as bool;
        });
      }
    } catch (e) {
      print('❌ خطأ في تحميل إعدادات قواعد الأتمتة: $e');
    }
  }

  /// مسح جميع الإشعارات
  Future<void> clearAllNotifications() async {
    await _notifications.cancelAll();
    _activeNotifications.clear();
    _sentNotifications.clear(); // مسح قائمة الإشعارات المرسلة أيضاً
    update();
    print('🗑️ تم مسح جميع الإشعارات');
  }

  /// إزالة إشعار محدد
  Future<void> removeNotification(int notificationId) async {
    await _notifications.cancel(notificationId);
    _activeNotifications
        .removeWhere((notification) => notification.id == notificationId);
    update();
  }

  /// مسح إشعار محدد
  Future<void> clearNotification(int id) async {
    await _notifications.cancel(id);
    _activeNotifications.removeWhere((notification) => notification.id == id);
  }
}

/// تعداد أنواع الإشعارات
enum NotificationType {
  weatherChange,
  extremeWeather,
  smartSuggestion,
  weatherAlert,
  automationNotification,
}

/// تعداد أولويات الإشعارات
enum NotificationPriority {
  low,
  normal,
  high,
  urgent,
}

/// نموذج الإشعار
class WeatherNotification {
  final int id;
  final String title;
  final String body;
  final NotificationType type;
  final NotificationPriority priority;
  final DateTime timestamp;
  final Map<String, dynamic>? payload;

  WeatherNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    this.priority = NotificationPriority.normal,
    required this.timestamp,
    this.payload,
  });

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type.toString(),
      'priority': priority.toString(),
      'timestamp': timestamp.toIso8601String(),
      'payload': payload,
    };
  }

  /// إنشاء من JSON
  factory WeatherNotification.fromJson(Map<String, dynamic> json) {
    return WeatherNotification(
      id: json['id'],
      title: json['title'],
      body: json['body'],
      type: NotificationType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => NotificationType.weatherAlert,
      ),
      priority: NotificationPriority.values.firstWhere(
        (e) => e.toString() == json['priority'],
        orElse: () => NotificationPriority.normal,
      ),
      timestamp: DateTime.parse(json['timestamp']),
      payload: json['payload'],
    );
  }
}
