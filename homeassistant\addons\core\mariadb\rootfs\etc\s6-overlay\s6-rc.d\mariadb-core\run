#!/command/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Start MariaDB service
# ==============================================================================

if bashio::config.has_value "mariadb_server_args"; then
  readarray -t extra_args <<< "$(bashio::config 'mariadb_server_args')"
  bashio::log.info "Starting MariaDB with command line parameters: ${extra_args[*]}"
else
  bashio::log.info "Starting MariaDB"
fi

# Start mariadb
mkdir -p /run/mysqld

if [ -z "${extra_args+x}" ] || [ ${#extra_args[@]} -eq 0 ]; then
  exec mysqld --datadir="${MARIADB_DATA}" --user=root < /dev/null
else
  exec mysqld --datadir="${MARIADB_DATA}" --user=root "${extra_args[@]}" < /dev/null
fi
