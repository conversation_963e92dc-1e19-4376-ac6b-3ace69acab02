import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../services/notification_service.dart';
import '../../services/advanced_automation_service.dart';

class NotificationSettingsPage extends StatelessWidget {
  const NotificationSettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final notificationService = Get.find<NotificationService>();
    final automationService = Get.find<AdvancedAutomationService>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الإشعارات'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: Obx(() => ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // إعدادات الطقس
          _buildSectionHeader('🌤️ إشعارات الطقس'),
          _buildSwitchTile(
            title: 'إشعارات تغيير الطقس',
            subtitle: 'تلقي إشعارات عند تغيير حالة الطقس',
            value: notificationService.weatherNotificationsEnabled,
            onChanged: (value) => notificationService.updateSettings(
              weatherNotifications: value,
            ),
          ),
          _buildSwitchTile(
            title: 'تحذيرات الطقس المتطرف',
            subtitle: 'تحذيرات عند درجات الحرارة المرتفعة أو المنخفضة',
            value: notificationService.extremeWeatherAlerts,
            onChanged: (value) => notificationService.updateSettings(
              extremeWeatherAlerts: value,
            ),
          ),

          const SizedBox(height: 20),

          // إعدادات التوصيات الذكية
          _buildSectionHeader('🤖 التوصيات الذكية'),
          _buildSwitchTile(
            title: 'التوصيات الذكية',
            subtitle: 'اقتراحات تلقائية لتحسين استخدام الأجهزة',
            value: notificationService.smartSuggestionsEnabled,
            onChanged: (value) => notificationService.updateSettings(
              smartSuggestions: value,
            ),
          ),
          _buildSwitchTile(
            title: 'اقتراحات أتمتة الأجهزة',
            subtitle: 'اقتراحات لأتمتة الأجهزة بناءً على الاستخدام',
            value: notificationService.deviceAutomationSuggestions,
            onChanged: (value) => notificationService.updateSettings(
              deviceAutomationSuggestions: value,
            ),
          ),

          const SizedBox(height: 20),

          // إعدادات قواعد الأتمتة
          _buildSectionHeader('🔔 إشعارات قواعد الأتمتة'),
          _buildAutomationRulesSection(notificationService, automationService),

          const SizedBox(height: 20),

          // إحصائيات الإشعارات
          _buildSectionHeader('📊 إحصائيات الإشعارات'),
          _buildStatisticsCard(notificationService),
        ],
      )),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: SwitchListTile(
        title: Text(title),
        subtitle: Text(subtitle),
        value: value,
        onChanged: onChanged,
        activeColor: Colors.blue,
      ),
    );
  }

  Widget _buildAutomationRulesSection(
    NotificationService notificationService,
    AdvancedAutomationService automationService,
  ) {
    final rulesWithNotifications = automationService.automationRules
        .where((rule) => rule.actions.any((action) => 
            action.type.toString().contains('notification') ||
            action.type.toString().contains('send_notification')))
        .toList();

    if (rulesWithNotifications.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(Icons.info_outline, size: 48, color: Colors.grey[400]),
              const SizedBox(height: 8),
              Text(
                'لا توجد قواعد أتمتة تحتوي على إشعارات',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: rulesWithNotifications.map((rule) {
        final isEnabled = notificationService
            .automationRuleNotifications[rule.id] ?? true;
        
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: SwitchListTile(
            title: Text('🤖 ${rule.name}'),
            subtitle: Text(rule.description.isNotEmpty 
                ? rule.description 
                : 'قاعدة أتمتة تحتوي على إشعارات'),
            value: isEnabled,
            onChanged: (value) {
              notificationService.updateAutomationRuleNotification(
                rule.id, 
                value,
              );
            },
            activeColor: Colors.green,
            secondary: Icon(
              rule.enabled ? Icons.play_circle : Icons.pause_circle,
              color: rule.enabled ? Colors.green : Colors.orange,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildStatisticsCard(NotificationService notificationService) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.notifications_active, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'الإشعارات النشطة',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'عدد الإشعارات النشطة: ${notificationService.activeNotifications.length}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 8),
            Text(
              'قواعد الأتمتة المفعلة: ${notificationService.automationRuleNotifications.values.where((enabled) => enabled).length}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  notificationService.clearAllNotifications();
                  Get.snackbar(
                    'تم',
                    'تم مسح جميع الإشعارات',
                    snackPosition: SnackPosition.BOTTOM,
                  );
                },
                icon: const Icon(Icons.clear_all),
                label: const Text('مسح جميع الإشعارات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[400],
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
