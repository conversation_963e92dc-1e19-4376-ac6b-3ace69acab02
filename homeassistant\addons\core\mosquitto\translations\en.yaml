---
configuration:
  logins:
    name: Logins
    description: >-
      A list of local users that will be created with username and password.
      You don't need to do this because you can use Home Assistant users too,
      without any configuration. You can also specify
      `password_pre_hashed: true` to utilize a pre-hashed password from the
      output of the `pw` command (which is present inside the container).
  require_certificate:
    name: Require Client Certificate
    description: >-
      If enabled client will need to provide its own certificate on top of
      username/password. 'cafile' must be set.
  certfile:
    name: Certificate File
    description: >-
      A file containing a certificate, including its chain. Place this file in
      the Home Assistant `ssl` folder.
  cafile:
    name: CA File
    description: >-
      A file containing a root certificate that signed the client certificate
      (only used if 'require_certificate' enabled). Place this file in the Home
      Assistant `ssl` folder.
  keyfile:
    name: Private Key File
    description: >-
      A file containing the private key. Place this file in the Home Assistant
      `ssl` folder.
  customize:
    name: Customize
    description: >-
      See the Documentation tab for more information about these options.
  debug:
    name: Debug
    description: >-
      If enabled will turn on debug logging for mos<PERSON>tto and the auth plugin.
network:
  1883/tcp: Normal MQTT
  1884/tcp: MQTT over WebSocket
  8883/tcp: Normal MQTT with SSL
  8884/tcp: MQTT over WebSocket with SSL
