#!/command/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Sends discovery information to Home Assistant.
# ==============================================================================
declare config

# Wait for openWakeWord to become available
bash -c \
    "until
        echo '{ \"type\": \"describe\" }' 
            > /dev/tcp/localhost/10400; do sleep 0.5;
    done" > /dev/null 2>&1 || true;

config=$(\
    bashio::var.json \
        uri "tcp://$(hostname):10400" \
)

if bashio::discovery "wyoming" "${config}" > /dev/null; then
    bashio::log.info "Successfully sent discovery information to Home Assistant."
else
    bashio::log.error "Discovery message to Home Assistant failed!"
fi
