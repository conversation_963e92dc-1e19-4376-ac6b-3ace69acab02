import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/services/notification_service.dart';
import 'package:zaen/services/advanced_automation_service.dart';
import 'package:zaen/shared/themes/app_colors.dart';

/// صفحة إعدادات الإشعارات والتوصيات الذكية
class NotificationsSettingsPage extends StatelessWidget {
  const NotificationsSettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final HomeController controller = Get.find();
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        title: Text(
          'الاشعارات',
          style: TextStyle(
            color: AppColors.textColor,
            fontSize: controller.sized * 0.016,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.backgroundColor2.withOpacity(0.8),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: AppColors.textColor,
            size: controller.sized * 0.02,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: Obx(
        () {
          final notificationService = NotificationService.instance;
          return Directionality(
            textDirection: TextDirection.rtl,
            child: SingleChildScrollView(
              padding: EdgeInsets.all(controller.sized * 0.01),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // قسم الإشعارات العامة
                  _buildSectionHeader('🔔 الإشعارات العامة'),
                  SizedBox(height: controller.sizedHight * 0.01),
                  _buildSettingCard(
                    title: 'إشعارات الطقس',
                    subtitle: 'تلقي إشعارات عند تغير حالة الطقس',
                    icon: Icons.wb_sunny,
                    value: notificationService.weatherNotificationsEnabled,
                    onChanged: (value) {
                      notificationService.updateSettings(
                        weatherNotifications: value,
                      );
                    },
                  ),

                  _buildSettingCard(
                    title: 'تنبيهات الطقس المتطرف',
                    subtitle:
                        'تحذيرات عند الطقس الخطير (حر شديد، برد، رياح قوية)',
                    icon: Icons.warning,
                    iconColor: AppColors.errorColor,
                    value: notificationService.extremeWeatherAlerts,
                    onChanged: (value) {
                      notificationService.updateSettings(
                        extremeWeatherAlerts: value,
                      );
                    },
                  ),

                  SizedBox(height: controller.sizedHight * 0.02),

                  // قسم التوصيات الذكية
                  _buildSectionHeader('🤖 التوصيات الذكية'),
                  SizedBox(height: controller.sizedHight * 0.01),
                  _buildSettingCard(
                    title: 'الاقتراحات الذكية',
                    subtitle: 'توصيات مخصصة بناءً على حالة الطقس والوقت',
                    icon: Icons.lightbulb,
                    iconColor: Colors.amber,
                    value: notificationService.smartSuggestionsEnabled,
                    onChanged: (value) {
                      notificationService.updateSettings(
                        smartSuggestions: value,
                      );
                    },
                  ),

                  _buildSettingCard(
                    title: 'اقتراحات أتمتة الأجهزة',
                    subtitle: 'توصيات لتشغيل/إيقاف الأجهزة حسب الطقس',
                    icon: Icons.home_outlined,
                    iconColor: Colors.green,
                    value: notificationService.deviceAutomationSuggestions,
                    onChanged: (value) {
                      notificationService.updateSettings(
                        deviceAutomationSuggestions: value,
                      );
                    },
                  ),

                  SizedBox(height: controller.sizedHight * 0.02),

                  // قسم إشعارات قواعد الأتمتة
                  _buildSectionHeader('🔔 إشعارات قواعد الأتمتة'),
                  SizedBox(height: controller.sizedHight * 0.01),
                  _buildAutomationRulesSection(notificationService),

                  SizedBox(height: controller.sizedHight * 0.02),

                  // قسم الإشعارات النشطة
                  _buildSectionHeader('📋 الإشعارات النشطة'),
                  SizedBox(height: controller.sizedHight * 0.01),
                  _buildActiveNotifications(notificationService),

                  SizedBox(height: controller.sizedHight * 0.01),

                  // زر مسح الإشعارات
                  _buildClearButton(notificationService),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 15,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  Widget _buildSettingCard({
    required String title,
    required String subtitle,
    required IconData icon,
    Color? iconColor,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: (iconColor ?? Colors.blue).withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: iconColor ?? Colors.blue,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: value,
              onChanged: onChanged,
              activeColor: Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveNotifications(NotificationService service) {
    if (service.activeNotifications.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Center(
            child: Column(
              children: [
                Icon(
                  Icons.notifications_off,
                  size: 48,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 12),
                Text(
                  'لا توجد إشعارات نشطة',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Column(
      children: service.activeNotifications.take(5).map((notification) {
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getNotificationTypeColor(notification.type),
              child: Icon(
                _getNotificationTypeIcon(notification.type),
                color: Colors.white,
                size: 20,
              ),
            ),
            title: Text(
              notification.title,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(notification.body),
                const SizedBox(height: 4),
                Text(
                  _formatTime(notification.timestamp),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade500,
                  ),
                ),
              ],
            ),
            trailing: IconButton(
              icon: const Icon(Icons.close, size: 20),
              onPressed: () {
                service.clearNotification(notification.id);
              },
            ),
            isThreeLine: true,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildClearButton(NotificationService service) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () async {
          await service.clearAllNotifications();
          Get.snackbar(
            'تم المسح',
            'تم مسح جميع الإشعارات',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
        },
        icon: const Icon(Icons.clear_all),
        label: const Text('مسح جميع الإشعارات'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Color _getNotificationTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.weatherChange:
        return Colors.blue;
      case NotificationType.extremeWeather:
        return Colors.red;
      case NotificationType.smartSuggestion:
        return Colors.green;
      case NotificationType.weatherAlert:
        return Colors.orange;
      case NotificationType.automationNotification:
        return Colors.purple;
    }
  }

  IconData _getNotificationTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.weatherChange:
        return Icons.wb_sunny;
      case NotificationType.extremeWeather:
        return Icons.warning;
      case NotificationType.smartSuggestion:
        return Icons.lightbulb;
      case NotificationType.weatherAlert:
        return Icons.info;
      case NotificationType.automationNotification:
        return Icons.smart_toy;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  /// بناء قسم إعدادات قواعد الأتمتة
  Widget _buildAutomationRulesSection(NotificationService notificationService) {
    try {
      final automationService = Get.find<AdvancedAutomationService>();

      return Obx(() {
        final rulesWithNotifications = automationService.automationRules
            .where((rule) => rule.actions.any((action) =>
                action.type.toString().contains('notification') ||
                action.type.toString().contains('send_notification')))
            .toList();

        if (rulesWithNotifications.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              children: [
                Icon(Icons.info_outline, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 8),
                Text(
                  'لا توجد قواعد أتمتة تحتوي على إشعارات',
                  style: TextStyle(color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // السويتش العام
            Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                color: Colors.purple[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.purple[200]!),
              ),
              child: SwitchListTile(
                title: const Text(
                  '🔔 تفعيل جميع إشعارات الأتمتة',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: const Text('تفعيل/إيقاف جميع إشعارات قواعد الأتمتة'),
                value: _getGlobalAutomationNotificationSetting(
                    notificationService, automationService),
                onChanged: (value) =>
                    _updateGlobalAutomationNotificationSetting(
                        notificationService, automationService, value),
                activeColor: Colors.purple,
                secondary: const Icon(
                  Icons.notifications_active,
                  color: Colors.purple,
                ),
              ),
            ),

            // عرض عدد القواعد
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info_outline, color: Colors.blue, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'عدد قواعد الأتمتة: ${rulesWithNotifications.length}',
                    style: const TextStyle(
                      color: Colors.blue,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

            // قائمة القواعد الفردية
            ...rulesWithNotifications.map((rule) {
              final isEnabled =
                  notificationService.automationRuleNotifications[rule.id] ??
                      true;

              return Container(
                margin: const EdgeInsets.symmetric(vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[300]!),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: SwitchListTile(
                  title: Text(
                    '🤖 ${rule.name}',
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        rule.description.isNotEmpty
                            ? rule.description
                            : 'قاعدة أتمتة تحتوي على إشعارات',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            rule.enabled
                                ? Icons.play_circle
                                : Icons.pause_circle,
                            size: 16,
                            color: rule.enabled ? Colors.green : Colors.orange,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            rule.enabled ? 'نشطة' : 'معطلة',
                            style: TextStyle(
                              fontSize: 12,
                              color:
                                  rule.enabled ? Colors.green : Colors.orange,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  value: isEnabled,
                  onChanged: (value) {
                    notificationService.updateAutomationRuleNotification(
                      rule.id,
                      value,
                    );

                    // إظهار رسالة تأكيد
                    Get.snackbar(
                      value ? 'تم تفعيل الإشعارات' : 'تم إيقاف الإشعارات',
                      '${rule.name}: ${value ? 'سيتم إرسال إشعارات' : 'لن يتم إرسال إشعارات'}',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: value ? Colors.green : Colors.orange,
                      colorText: Colors.white,
                      duration: const Duration(seconds: 2),
                    );
                  },
                  activeColor: Colors.purple,
                  secondary: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.purple.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.smart_toy,
                      color: Colors.purple,
                      size: 20,
                    ),
                  ),
                ),
              );
            }),
          ],
        );
      });
    } catch (e) {
      // إذا لم تكن خدمة الأتمتة متاحة
      return Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: Colors.red[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.red[200]!),
        ),
        child: Column(
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red[400]),
            const SizedBox(height: 8),
            Text(
              'خطأ في تحميل قواعد الأتمتة',
              style: TextStyle(color: Colors.red[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
  }

  /// الحصول على إعداد الإشعارات العام للأتمتة
  bool _getGlobalAutomationNotificationSetting(
    NotificationService notificationService,
    AdvancedAutomationService automationService,
  ) {
    // جلب قواعد الأتمتة التي تحتوي على إشعارات
    final rulesWithNotifications = automationService.automationRules
        .where((rule) => rule.actions.any((action) =>
            action.type.toString().contains('notification') ||
            action.type.toString().contains('send_notification')))
        .toList();

    if (rulesWithNotifications.isEmpty) return true;

    // فحص ما إذا كانت جميع القواعد مفعلة
    final enabledCount = rulesWithNotifications
        .where((rule) =>
            notificationService.automationRuleNotifications[rule.id] ?? true)
        .length;

    return enabledCount == rulesWithNotifications.length;
  }

  /// تحديث إعداد الإشعارات العام للأتمتة
  void _updateGlobalAutomationNotificationSetting(
    NotificationService notificationService,
    AdvancedAutomationService automationService,
    bool enabled,
  ) {
    // جلب قواعد الأتمتة التي تحتوي على إشعارات
    final rulesWithNotifications = automationService.automationRules
        .where((rule) => rule.actions.any((action) =>
            action.type.toString().contains('notification') ||
            action.type.toString().contains('send_notification')))
        .toList();

    // تحديث جميع قواعد الأتمتة التي تحتوي على إشعارات
    int updatedCount = 0;
    for (var rule in rulesWithNotifications) {
      notificationService.updateAutomationRuleNotification(rule.id, enabled);
      updatedCount++;
    }

    // إظهار رسالة تأكيد مع عدد القواعد المحدثة
    Get.snackbar(
      enabled ? 'تم تفعيل الإشعارات' : 'تم إيقاف الإشعارات',
      enabled
          ? 'تم تفعيل إشعارات $updatedCount قاعدة أتمتة'
          : 'تم إيقاف إشعارات $updatedCount قاعدة أتمتة',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: enabled ? Colors.green : Colors.orange,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );

    print(
        '🔄 تم تحديث إعدادات $updatedCount قاعدة أتمتة إلى: ${enabled ? "مفعل" : "معطل"}');
  }
}
