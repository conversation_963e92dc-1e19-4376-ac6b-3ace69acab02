import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:sleek_circular_slider/sleek_circular_slider.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'routine_variables.dart';

Widget addRoutineSw({
  required Map device,
  required Map roomData,
  setState1,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  print(roomData);
  var swPrivName = device['priv'];

  if (controller.addRoutine.containsKey(roomData['id']) &&
      controller.addRoutine[roomData['id']] != true &&
      controller.addRoutine[roomData['id']] != false &&
      controller.addRoutine[roomData['id']].containsKey(device['id'])) {
    for (String v
        in controller.addRoutine[roomData['id']][device['id']].keys.toList()) {
      if (controller.addRoutine[roomData['id']][device['id']][v] != null) {
        sw[v] = controller.addRoutine[roomData['id']][device['id']][v];
      } else {
        sw[v] = device[v]['state'] ?? false;
      }
    }
  } else {
    for (String v in device.keys.toList().getRange(0, device.length - 4)) {
      sw[v] = device[v]['state'] ?? false;
    }
  }

  return StatefulBuilder(builder: ((context, setState) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Column(
        children: [
          SizedBox(
            height: controller.sizedHight * 0.02,
          ),
          Row(mainAxisSize: MainAxisSize.min, children: [
            containerIconsOption(
              content: Row(
                children: [
                  Directionality(
                    textDirection: TextDirection.rtl,
                    child: switchStyle(
                        size: controller.sized * 0.001,
                        value: sw.containsValue(true) == false ? false : true,
                        onChanged: (s) {
                          setState(
                            () {
                              for (var v in sw.keys) {
                                sw[v] = s;
                              }
                            },
                          );

                          if (controller.addRoutine
                              .containsKey(roomData['id'])) {
                            if (controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id'])) {
                              controller.addRoutine[roomData['id']]
                                      [device['id']]
                                  .forEach((key, value) {
                                if (value != null) {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']][key] = s;
                                }
                              });
                            }
                            print(controller.addRoutine);
                          }
                        }),
                  ),
                  IconButton(
                      onPressed: () {
                        if (controller.addRoutine.containsKey('home')) {
                          controller.addRoutine = {};
                        }
                        if (controller.addRoutine.containsKey(roomData['id'])) {
                          if (controller.addRoutine[roomData['id']] == true ||
                              controller.addRoutine[roomData['id']] == false) {
                            controller.addRoutine[roomData['id']] = {};
                            print(1111);
                            controller.addRoutine[roomData['id']]
                                [device['id']] = {};
                            controller.addRoutine[roomData['id']][device['id']]
                                .addAll(sw);
                          } else {
                            if (controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                              if (controller.addRoutine[roomData['id']]
                                          [device['id']]
                                      .containsValue(null) ==
                                  true) {
                                controller.addRoutine[roomData['id']]
                                    [device['id']] = {};
                                controller.addRoutine[roomData['id']]
                                        [device['id']]
                                    .addAll(sw);
                              } else {
                                controller.addRoutine[roomData['id']]
                                    .remove(device['id']);
                                if (controller
                                    .addRoutine[roomData['id']].isEmpty) {
                                  controller.addRoutine.remove(roomData['id']);
                                }
                              }
                            } else {
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {};
                              controller.addRoutine[roomData['id']]
                                      [device['id']]
                                  .addAll(sw);
                            }
                          }
                        } else {
                          controller.addRoutine[roomData['id']] = {};
                          controller.addRoutine[roomData['id']]
                              [device['id']] = {};
                          controller.addRoutine[roomData['id']][device['id']]
                              .addAll(sw);
                        }

                        setState1(
                          () {
                            controller.addRoutine;
                          },
                        );
                        print(controller.addRoutine);
                        print(sw);
                      },
                      iconSize: controller.sized * 0.03,
                      icon: iconStyle(
                        icon: controller.addRoutine
                                    .containsKey(roomData['id']) &&
                                controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id']) &&
                                controller.addRoutine[roomData['id']]
                                            [device['id']]
                                        .containsValue(null) ==
                                    false
                            ? Icons.check_circle_rounded
                            : Icons.add_circle_outline_rounded,
                        color: controller.addRoutine
                                    .containsKey(roomData['id']) &&
                                controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id']) &&
                                controller.addRoutine[roomData['id']]
                                            [device['id']]
                                        .containsValue(null) ==
                                    false
                            ? AppColors.primaryColor
                            : AppColors.warningColor,
                      ))
                ],
              ),
            ),
            Expanded(
              child: Container(
                margin: EdgeInsets.zero,
                alignment: Alignment.bottomRight,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                        child: txtStyle(
                      txt: swPrivName.split('_')[0] == 'x'
                          ? 'لا يوجد اسم'
                          : swPrivName.split('_')[0]!,
                    )),
                    SizedBox(
                      width: sized * 0.01,
                    ),
                    Container(
                      padding: EdgeInsets.only(left: sizedWidth * 0.01),
                      decoration: BoxDecoration(
                          border: Border(
                              left: BorderSide(
                                  color: AppColors.textColor.withOpacity(0.25),
                                  width: 1.5))),
                      child: txtStyle(
                        txt: 'مفاتيح',
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              width: sizedWidth * 0.01,
            ),
            iconStyle(
              icon: Icons.power_rounded,
              color: AppColors.warningColor,
              size: sized * 0.035,
            ),
          ]),
          SizedBox(
            height: sizedHeight * 0.02,
          ),
          Divider(
            color: AppColors.textColor2.withOpacity(0.3),
          ),
          Directionality(
            textDirection: TextDirection.rtl,
            child: ListView(
              shrinkWrap: true,
              children: [
                for (String v in sw.keys)
                  Container(
                    margin: EdgeInsets.symmetric(
                        horizontal: controller.sizedWidth * 0.01,
                        vertical: controller.sizedHight * 0.01),
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.all(Radius.circular(25)),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.only(
                                right: controller.sizedWidth * 0.03,
                                top: controller.sizedHight * 0.005),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                txtStyle(
                                  txt: v,
                                ),
                                txtStyle(
                                  txt: (device[v]['type'] == 'SWITCH'
                                          ? 'مفتاح : '
                                          : device[v]['type'] == 'VAN'
                                              ? 'مروحة : '
                                              : 'إضائة : ') +
                                      (device['priv'].split('_')[int.parse(
                                                  v.replaceAll('v', ''))] ==
                                              'x'
                                          ? 'لا يوجد اسم للمفتاح'
                                          : device['priv'].split('_')[int.parse(
                                              v.replaceAll('v', ''))]),
                                ),
                                Divider(
                                  color: AppColors.textColor2.withOpacity(0.5),
                                )
                              ],
                            ),
                          ),
                        ),
                        IconButton(
                            onPressed: () {
                              if (controller.addRoutine.containsKey('home')) {
                                controller.addRoutine = {};
                              }
                              if (controller.addRoutine
                                  .containsKey(roomData['id'])) {
                                if (controller.addRoutine[roomData['id']] ==
                                        true ||
                                    controller.addRoutine[roomData['id']] ==
                                        false) {
                                  controller.addRoutine[roomData['id']] = {};
                                  controller.addRoutine[roomData['id']]
                                      [device['id']] = {
                                    for (String vv in sw.keys)
                                      vv: vv == v ? sw[vv] : null
                                  };
                                } else {
                                  if (controller.addRoutine[roomData['id']]
                                      .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']][v] ==
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']][v] = sw[v];
                                    } else {
                                      print('1');
                                      print(sw);
                                      controller.addRoutine[roomData['id']]
                                          [device['id']][v] = null;
                                      print('11');
                                      print(sw);

                                      if (controller.addRoutine[roomData['id']]
                                                      [device['id']]
                                                  .containsValue(true) ==
                                              false &&
                                          controller.addRoutine[roomData['id']]
                                                      [device['id']]
                                                  .containsValue(false) ==
                                              false) {
                                        controller.addRoutine[roomData['id']]
                                            .remove(device['id']);
                                        if (controller
                                            .addRoutine[roomData['id']]
                                            .isEmpty) {
                                          controller.addRoutine
                                              .remove(roomData['id']);
                                        }
                                      }
                                    }
                                  } else {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']] = {
                                      for (String vv in sw.keys)
                                        vv: vv == v ? sw[vv] : null
                                    };
                                  }
                                }
                              } else {
                                controller.addRoutine[roomData['id']] = {};
                                controller.addRoutine[roomData['id']]
                                    [device['id']] = {
                                  for (String vv in sw.keys)
                                    vv: vv == v ? sw[vv] : null
                                };
                              }
                              print('5555555555555555555555555555');
                              print(sw);
                              setState1(
                                () {
                                  controller.addRoutine;
                                },
                              );
                              print(controller.addRoutine);
                              print(sw);
                            },
                            icon: iconStyle(
                              icon: controller.addRoutine
                                          .containsKey(roomData['id']) &&
                                      controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id']) &&
                                      controller.addRoutine[roomData['id']]
                                              [device['id']][v] !=
                                          null
                                  ? Icons.check_circle_rounded
                                  : Icons.add_circle_outline_rounded,
                              color: controller.addRoutine
                                          .containsKey(roomData['id']) &&
                                      controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id']) &&
                                      controller.addRoutine[roomData['id']]
                                              [device['id']][v] !=
                                          null
                                  ? AppColors.primaryColor
                                  : AppColors.warningColor,
                            )),
                        Directionality(
                          textDirection: TextDirection.rtl,
                          child: switchStyle(
                              size: controller.sized * 0.001,
                              value: sw[v],
                              onChanged: (s) {
                                setState(
                                  () {
                                    sw[v] = s;
                                  },
                                );

                                if (controller.addRoutine
                                    .containsKey(roomData['id'])) {
                                  if (controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']][v] !=
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']][v] = s;
                                    }
                                  }
                                }
                                print(controller.addRoutine);
                                print(sw);
                              }),
                        ),
                      ],
                    ),
                  )
              ],
            ),
          ),
        ],
      ),
    );
  }));
}
