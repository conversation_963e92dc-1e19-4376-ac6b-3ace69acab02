# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 121ms
create_cxx_tasks completed in 125ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 16ms
    create-X86-model 13ms
    create-X86_64-model 14ms
    create-module-model 10ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 33ms
    create-X86-model 15ms
    create-X86_64-model 14ms
    create-module-model 14ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 262ms
create_cxx_tasks completed in 270ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-ARM64_V8A-model 15ms
    create-module-model
      create-ndk-meta-abi-list 16ms
    create-module-model completed in 16ms
    create-ARM64_V8A-model 16ms
    create-module-model
      create-project-model 15ms
    create-module-model completed in 15ms
    create-ARM64_V8A-model 16ms
  create-initial-cxx-model completed in 82ms
create_cxx_tasks completed in 83ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-ARMEABI_V7A-model 17ms
    create-X86-model 16ms
    create-variant-model 17ms
    create-X86-model 17ms
    create-variant-model 16ms
    create-ARM64_V8A-model 19ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 120ms
create_cxx_tasks completed in 124ms

