import mysql.connector
from datetime import datetime, timedelta
import requests
import sys

sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/nlt')
sys.path.append('/home/<USER>/myProject/modules')
import static as st
import tts as tts

tts = tts.SmartHomeTTSCache()

def weather(city, day, time, weather):
    db = mysql.connector.connect(user='root', host=st.ip, passwd='zain', database='zain')
    dt = datetime.now()
    
    cursor = db.cursor()
    if city == 'here':
        cursor.execute("SELECT lat,lon FROM City WHERE mycity='here'")
        coords = cursor.fetchone()
        if coords:
            lat = coords[0]
            lon = coords[1]
        else:
            tts.say('لم يَتِم تَحديد مَدينة بَعد, الرَجاء التَوَجُه الى الاعدادات لإختيار مَدينه')
            return
    else:
        cursor.execute("SELECT lat,lon FROM City WHERE city='%s'" % (city,))
        coords = cursor.fetchone()
        if coords:
            lat = coords[0]
            lon = coords[1]
        else:
            tts.say('لم يَتِم تَحديد مَدينة بَعد, الرَجاء التَوَجُه الى الاعدادات لإختيار مَدينه')
            return
    cursor.close()
    db.close()

    if day == 'current':
        url = 'http://api.openweathermap.org/data/2.5/weather?lat=%s&lon=%s&lang=ar&appid=0ce830fc768bcf4f02cf5d89d8b7b49c&units=metric' % (lat, lon)
        res = requests.get(url)
        data = res.json()
        
        temp = str(data['main']['temp'])
        tempNight = str(data['main']['temp_min'])
        tempEve = str(data['main']['temp_max'])
        tempMorn = str(data['main']['temp_min'])
        description = data['weather'][0]['description']
        humidity = str(data['main']['humidity'])
        
    else:
        url = 'https://api.openweathermap.org/data/2.5/forecast?lat=%s&lon=%s&lang=ar&appid=0ce830fc768bcf4f02cf5d89d8b7b49c&units=metric' % (lat, lon)
        res = requests.get(url)
        data = res.json()
        
        # Calculate target date
        if isinstance(day, str):
            target_date = dt.date()
        else:
            target_date = (dt + timedelta(days=int(day))).date()
        
        # Find forecasts for target date
        day_forecasts = []
        for item in data['list']:
            forecast_dt = datetime.strptime(item['dt_txt'], '%Y-%m-%d %H:%M:%S')
            if forecast_dt.date() == target_date:
                day_forecasts.append(item)
        
        if not day_forecasts:
            tts.say('عذرا، لا تَتَوَفَر معلومات الطَقس هذا اليوم')
            return
            
        # Calculate daily statistics
        temps = [f['main']['temp'] for f in day_forecasts]
        temp = str(sum(temps) / len(temps))  # average temperature
        tempMorn = str(day_forecasts[0]['main']['temp'] if day_forecasts else temp)
        tempEve = str(max((f['main']['temp'] for f in day_forecasts), default=temp))
        tempNight = str(day_forecasts[-1]['main']['temp'] if day_forecasts else temp)
        humidity = str(day_forecasts[0]['main']['humidity'])
        description = day_forecasts[0]['weather'][0]['description']

    # Format the day name for Arabic output
    day_name = 'غد' if day == 1 else f'بعد {day} ايام' if isinstance(day, int) else ''
        
    if weather == 'weather':
        if day == 'current':
            if time == 'PM':
                tts.say('تَكون الأجواءْ مَسائَاً %s %s و يتوقع أن تَصِلَ درجةْ الحَرارَهْ الى %s درجهْ و الرُطوبَه %s' % (
                    description, 'في '+city.replace(' ','-') if city != 'here' else '', tempNight, humidity))
            elif time == 'AM':
                tts.say('تَكون الأجواءْ صَباحاً %s %s و تَصِلُ دَرَجَةْ الحَرارَهْ الى %s دَرَجَهْ و الرٌطوبَه %s' % (
                    description, 'في '+city.replace(' ','-') if city != 'here' else '', tempMorn, humidity))
            elif time == 'eve':
                tts.say('الجَوْ بَعدَ الظَهيرَه %s %s و تَصِلُ دَرَجَةْ الحَرارَهْ الى %s دَرَجَهْ و الرُطوبَه %s ' % (
                    description, 'في '+city.replace(' ','-') if city != 'here' else '', tempEve, humidity))
            else:
                tts.say('''الأجواء %s %s و دَرَجَةْ الحَرارَهْ تُساوي %s دَرَجَهْ و الرُطوبَه %s , و يُتَوَقَعُ أن تَصِلَ الحَرارَهْ في ساعاتِ الليل %s دَرَجَهْ''' % (
                    description, 'في '+city.replace(' ','-') if city != 'here' else '', temp, humidity, tempNight))
        else:
            tts.say('''تُشيرُ تَوَقعاتِ الطَقسْ أنَ الجَوْ سَيَكون %s %s %s , و تَصِلُ دَرَجَةْ الحَرارَه صَباحاً الى %s و في مُنتًصًف اليوم %s و في ساعاتِ الليل %s دَرَجَهْ , و مُعَدَل الرُطوبَه %s''' % (
                description, 'يوم' if day_name else '', day_name + ' ' + ('في '+city.replace(' ','-') if city != 'here' else ''), 
                tempMorn, tempEve, tempNight, humidity))
    elif weather == 'حراره':
        if day == 'current':
            if time == 'PM':
                tts.say('تَصِلُ دَرَجَةْ الحَرارَهْ مَسائَاً %s الى %s دَرَجَهْ' % (
                    'في '+city.replace(' ','-') if city != 'here' else '', tempNight))
            elif time == 'AM':
                tts.say('تَصِلُ دَرَجَةْ الحَرارَهْ صَباحَاً %s الى %s دَرَجَهْ' % (
                    'في '+city.replace(' ','-') if city != 'here' else '', tempMorn))
            elif time == 'eve':
                tts.say('تَصِلُ دَرَجَةْ الحَرارَهْ بَعدَ الظَهيرَهْ %s الى %s دَرَجَهْ' % (
                    'في '+city.replace(' ','-') if city != 'here' else '', tempEve))
            else:
                tts.say('دَرَجَةْ الحَرارَهْ %s %s دَرَجَهْ' % (
                    'في '+city.replace(' ','-') if city != 'here' else '', temp))
        else:
            tts.say('مِنَ المُفتَرَض أن تَكونَ دَرَجَةُ الحَرارَهْ %s %s %s هيَ %s دَرَجَهْ حَيث تَكون دَرَجَةْ الحَرارَهْ صَباحاً %s و تَصِلُ في مُنتَصَف اليَوم الى %s و في ساعاتِ الليل الى %s و تُشيرُ تَوَقُعاتِ الطَقسْ أنَ الجَو سَيَكونُ %s' % (
                'يوم' if day_name else '', day_name, 'في '+city.replace(' ','-') if city != 'here' else '', 
                temp, tempMorn, tempEve, tempNight, description))
    elif weather == 'رطوبه':
        if day == 'current':
            tts.say('مُعَدَل الرُطوبَهْ اليوم هو %s' % (humidity))
        else:
            tts.say('مُعَدَل الرُطوبَه %s %s %s هو %s' % (
                'يوم' if day_name else '', day_name, 'في '+city.replace(' ','-') if city != 'here' else '', humidity))