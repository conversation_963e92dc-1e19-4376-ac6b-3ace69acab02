# C/C++ build system timings
generate_cxx_metadata
  [gap of 62ms]
  create-invalidation-state 21ms
  [gap of 24ms]
generate_cxx_metadata completed in 107ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 16ms
  [gap of 33ms]
generate_cxx_metadata completed in 67ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 16ms
  [gap of 34ms]
generate_cxx_metadata completed in 53ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 16ms
  [gap of 20ms]
generate_cxx_metadata completed in 39ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 11ms
  [gap of 11ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 39ms

