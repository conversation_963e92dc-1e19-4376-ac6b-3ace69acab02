def editClock(phrase):
    # قائمة الكلمات التي تشير إلى الدقائق
    minute_words = {
        'نص': '30',
        'نصف': '30',
        'ثلث': '20',
        'ربع': '15'
    }

    # قائمة الكلمات التي تشير إلى الوقت (ساعة، دقيقة، إلخ)
    if 'ALARM' in phrase or 'REMIND' in phrase:
        timeBefore = ['ساع', 'وقت','في','علي']
    else:
        timeBefore = ['ساع', 'وقت']
    timeAfter = ['AM', 'PM']
    minutes = ['دقيق', 'دقايق', 'دقاءق']
    tens = ['10', '20', '30', '40', '50']
    
    # تقسيم الجملة إلى كلمات
    STT = phrase.split(' ')
    k = 0
    for i in STT:
        # التعرف على الأوقات إذا كانت الكلمة رقمًا أو تشير إلى الدقائق
        if i.isnumeric():
            clock = int(i)
            Xclock = i
            minute = 0
            Xminute = ''

            # إذا كانت الكلمة رقمًا، فهي تمثل الساعة
            if k != 0 and STT[k - 1] in timeBefore:
                Xclock = STT[k - 1] + ' ' + Xclock
                if k + 1 < len(STT) and STT[k + 1] == 'دقيق':
                    minute = 1
                    Xminute = ' ' + STT[k + 1]
            if k + 2 < len(STT) and STT[k + 1] == 'و' and STT[k + 2] == 'دقيق':
                minute = 1
                Xminute = 'و ' + STT[k + 2]

            # إذا كانت الكلمة تشير إلى الدقائق (مثل "ربع"، "نصف")
            if k + 1 < len(STT) and STT[k + 1] in minute_words:
                minute = int(minute_words[STT[k + 1]])
                Xminute = STT[k + 1]
            elif k + 2 < len(STT) and STT[k + 1] == 'و' and STT[k + 2] in minute_words:
                minute = int(minute_words[STT[k + 2]])
                Xminute = 'و ' + STT[k + 2]

            # التعامل مع الأوقات
            if k + 1 < len(STT) and STT[k + 1].isnumeric() and (
                not Xclock.isnumeric() or (k + 2 < len(STT) and STT[k + 2] in timeAfter) or (
                    k + 3 < len(STT) and STT[k + 3] in timeAfter)):
                minute += int(STT[k + 1])
                Xminute = STT[k + 1]
                if k + 2 < len(STT) and STT[k + 2] in minutes:
                    Xminute = Xminute + ' ' + STT[k + 2]
                elif k + 2 < len(STT) and STT[k + 2] in tens:
                    minute += int(STT[k + 2])
                    Xminute = Xminute + ' ' + STT[k + 2]
                    if k + 3 < len(STT) and STT[k + 3] in minutes:
                        Xminute = Xminute + ' ' + STT[k + 3]

            elif k + 2 < len(STT) and STT[k + 1].isnumeric() and STT[k + 2] in minutes:
                minute += int(STT[k + 1])
                Xminute = STT[k + 1] + ' ' + STT[k + 2]

            # التعامل مع الحالات التي تحتوي على "و"
            elif k + 2 < len(STT) and STT[k + 1] == 'و' and STT[k + 2].isnumeric() and (
                not Xclock.isnumeric() or (k + 3 < len(STT) and STT[k + 3] in timeAfter) or (
                    k + 4 < len(STT) and STT[k + 4] in timeAfter) or (
                    k + 5 < len(STT) and STT[k + 5] in timeAfter)):
                minute += int(STT[k + 2])
                Xminute = 'و ' + STT[k + 2]
                if k + 3 < len(STT) and STT[k + 3] in minutes:
                    Xminute = Xminute + ' ' + STT[k + 3]
                elif k + 3 < len(STT) and STT[k + 3] in tens:
                    minute += int(STT[k + 3])
                    Xminute = Xminute + ' ' + STT[k + 3]
                    if k + 4 < len(STT) and STT[k + 4] in minutes:
                        Xminute = Xminute + ' ' + STT[k + 4]
                elif k + 4 < len(STT) and STT[k + 3] == 'و' and STT[k + 4] in tens:
                    minute += int(STT[k + 4])
                    Xminute = Xminute + ' و ' + STT[k + 4]
                    if k + 5 < len(STT) and STT[k + 5] in minutes:
                        Xminute = Xminute + ' ' + STT[k + 5]

            elif k + 3 < len(STT) and STT[k + 1] == 'و' and STT[k + 2].isnumeric() and STT[k + 3] in minutes:
                Xminute = 'و ' + STT[k + 2] + ' ' + STT[k + 3]

            # بناء الوقت النهائي إذا كانت هناك إشارة إلى الوقت
            if clock != 0 and minute == 0 and (not Xclock.isnumeric() or (
                k + 1 < len(STT) and STT[k + 1] in timeAfter)):
                time_str = f"{clock}:00"
                phrase = phrase.replace(f"{Xclock}", time_str)
                return phrase
            elif minute != 0:
                time_str = f"{clock}:{minute:02d}"
                phrase = phrase.replace(f"{Xclock} {Xminute}", time_str)
                return phrase

        k += 1

    return phrase
