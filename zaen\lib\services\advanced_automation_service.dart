/// خدمة الأتمتة المتقدمة
/// تدعم تحديث الإحصائيات عند استقبال تنفيذ قواعد الأتمتة والإشعارات

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mysql1/mysql1.dart';
import 'package:zaen/shared/components/config.dart';
import '../models/automation_models.dart';
import '../modules/local/mqtt.dart' as mqtt_module;
import 'notification_service.dart';

class AdvancedAutomationService extends GetxService {
  static AdvancedAutomationService get instance =>
      Get.find<AdvancedAutomationService>();

  // قائمة قواعد الأتمتة
  final RxList<AdvancedAutomationRule> _automationRules =
      <AdvancedAutomationRule>[].obs;
  List<AdvancedAutomationRule> get automationRules => _automationRules;

  // حالة الخدمة
  final RxBool _isRunning = false.obs;
  bool get isRunning => _isRunning.value;

  // إحصائيات
  final RxInt _totalRules = 0.obs;
  final RxInt _activeRules = 0.obs;
  final RxInt _triggeredToday = 0.obs;
  final Rx<DateTime?> _lastTriggered = Rx<DateTime?>(null);

  int get totalRules => _totalRules.value;
  int get activeRules => _activeRules.value;
  int get triggeredToday => _triggeredToday.value;
  DateTime? get lastTriggered => _lastTriggered.value;

  @override
  void onInit() {
    super.onInit();
    _initializeService();
  }

  /// تهيئة الخدمة
  Future<void> _initializeService() async {
    try {
      print('🤖 تهيئة خدمة الأتمتة المتقدمة...');

      // تحميل قواعد الأتمتة من قاعدة البيانات
      await _loadAutomationRules();

      // بدء مراقبة رسائل MQTT
      _setupMqttListeners();

      // تحديث الإحصائيات
      _updateStatistics();

      // مزامنة إعدادات الإشعارات مع خدمة الإشعارات
      _syncNotificationSettings();

      _isRunning.value = true;
      print('✅ تم تهيئة خدمة الأتمتة المتقدمة بنجاح');
    } catch (e) {
      print('❌ خطأ في تهيئة خدمة الأتمتة: $e');
    }
  }

  /// تحميل قواعد الأتمتة من قاعدة البيانات
  Future<void> _loadAutomationRules() async {
    try {
      _automationRules.clear();
      await _loadFromLocalDatabase();
      print(
          '📋 تم تحميل ${_automationRules.length} قاعدة من قاعدة البيانات المحلية');
    } catch (e) {
      print('❌ خطأ في تحميل قواعد الأتمتة: $e');
    }
  }

  /// تحميل من قاعدة البيانات المحلية
  Future<void> _loadFromLocalDatabase() async {
    try {
      final conn = await MySqlConnection.connect(ConnectionSettings(
          host: controller.hostZain.value,
          user: 'root',
          db: 'zain',
          password: 'zain',
          characterSet: CharacterSet.UTF8));

      var results = await conn.query(
          'SELECT id, name, description, triggers, conditions, actions, '
          'logic_operator, enabled, priority, last_triggered, trigger_count '
          'FROM advanced_automation_rules');

      _automationRules.clear();

      for (var row in results) {
        final rule = AdvancedAutomationRule(
          id: row['id'].toString(),
          name: row['name'].toString(),
          description: row['description'].toString(),
          triggers: (json.decode(row['triggers'].toString()) as List<dynamic>)
              .map((t) => AutomationTrigger.fromJson(t))
              .toList(),
          conditions:
              (json.decode(row['conditions'].toString()) as List<dynamic>)
                  .map((c) => AutomationCondition.fromJson(c))
                  .toList(),
          actions: (json.decode(row['actions'].toString()) as List<dynamic>)
              .map((a) => AutomationAction.fromJson(a))
              .toList(),
          logicOperator: row['logic_operator'] == 'OR'
              ? LogicOperator.or
              : LogicOperator.and,
          enabled: row['enabled'] == 1,
          priority: row['priority'].toString(),
          lastTriggered: row['last_triggered'] != null
              ? (row['last_triggered'] as DateTime)
              : null,
          triggerCount: row['trigger_count'] as int,
        );
        _automationRules.add(rule);
      }

      await conn.close();
    } catch (e) {
      print('❌ خطأ في تحميل من قاعدة البيانات المحلية: $e');
    }
  }

  /// إعداد مستمعي MQTT
  void _setupMqttListeners() {
    try {
      if (mqtt_module.client.updates != null) {
        mqtt_module.client.updates!
            .listen((List<MqttReceivedMessage<MqttMessage>> messages) {
          for (var message in messages) {
            _handleMqttMessage(message);
          }
        });

        // الاشتراك في المواضيع المطلوبة فقط
        _subscribeToTopics();

        print('🔗 تم إعداد مستمعي MQTT للأتمتة');
      }
    } catch (e) {
      print('❌ خطأ في إعداد مستمعي MQTT: $e');
    }
  }

  /// الاشتراك في المواضيع المطلوبة
  void _subscribeToTopics() {
    final topics = [
      'automation/executed', // موضوع تنفيذ القواعد
      // تم إزالة notifications/send لتجنب التداخل مع NotificationService
    ];

    for (String topic in topics) {
      mqtt_module.client.subscribe(topic, MqttQos.atLeastOnce);
      print('📡 اشتراك في موضوع: $topic');
    }
  }

  /// معالجة رسائل MQTT
  void _handleMqttMessage(MqttReceivedMessage<MqttMessage> message) {
    try {
      final topic = message.topic;
      final payload = MqttPublishPayload.bytesToStringAsString(
        (message.payload as MqttPublishMessage).payload.message,
      );

      print('📨 رسالة MQTT: $topic -> $payload');

      // توجيه الرسالة للدالة المناسبة
      if (topic == 'automation/executed') {
        _handleAutomationExecuted(payload);
      } else if (topic == 'notifications/send') {
        _handleNotificationSend(payload);
      }
    } catch (e) {
      print('❌ خطأ في معالجة رسالة MQTT: $e');
    }
  }

  /// معالجة رسالة تنفيذ قاعدة أتمتة
  void _handleAutomationExecuted(String payload) {
    try {
      final data = json.decode(payload);
      final ruleId = data['rule_id'];

      if (ruleId == null) {
        print('⚠️ rule_id غير موجود في بيانات التنفيذ');
        return;
      }

      // البحث عن القاعدة وتحديث إحصائياتها
      final ruleIndex =
          _automationRules.indexWhere((rule) => rule.id == ruleId);
      if (ruleIndex != -1) {
        final rule = _automationRules[ruleIndex];
        final updatedRule = rule.copyWith(
          lastTriggered: DateTime.now(),
          triggerCount: rule.triggerCount + 1,
        );

        _automationRules[ruleIndex] = updatedRule;

        // تحديث الإحصائيات العامة (جلب من قاعدة البيانات)
        _loadDailyTriggerCountFromDatabase();
        _lastTriggered.value = DateTime.now();

        print('🎯 تم تحديث قاعدة الأتمتة: ${rule.name}');
      } else {
        print('⚠️ قاعدة الأتمتة ذات المعرف $ruleId غير موجودة');
      }
    } catch (e) {
      print('❌ خطأ في معالجة تنفيذ قاعدة الأتمتة: $e');
    }
  }

  /// معالجة رسالة إرسال إشعار
  void _handleNotificationSend(String payload) {
    try {
      // تمرير الرسالة لخدمة الإشعارات
      NotificationService.instance.handleExternalNotification(payload);
    } catch (e) {
      print('❌ خطأ في معالجة رسالة الإشعار: $e');
    }
  }

  /// تحديث الإحصائيات العامة
  void _updateStatistics() {
    _totalRules.value = _automationRules.length;
    _activeRules.value = _automationRules.where((rule) => rule.enabled).length;
    _loadDailyTriggerCountFromDatabase(); // جلب عداد اليوم من قاعدة البيانات
  }

  /// جلب عداد التفعيلات اليومية من قاعدة البيانات
  void _loadDailyTriggerCountFromDatabase() async {
    try {
      final conn = await MySqlConnection.connect(ConnectionSettings(
          host: controller.hostZain.value,
          user: 'root',
          db: 'zain',
          password: 'zain',
          characterSet: CharacterSet.UTF8));

      // جلب مجموع التفعيلات لليوم الحالي
      final results = await conn.query(
          'SELECT COUNT(*) as daily_count FROM advanced_automation_rules '
          'WHERE DATE(last_triggered) = CURDATE()');

      if (results.isNotEmpty) {
        final row = results.first;
        _triggeredToday.value = row['daily_count'] as int? ?? 0;
      } else {
        _triggeredToday.value = 0;
      }

      await conn.close();
      print('📊 تم جلب عداد التفعيلات اليومية: ${_triggeredToday.value}');
    } catch (e) {
      print('❌ خطأ في جلب عداد التفعيلات اليومية: $e');
      _triggeredToday.value = 0;
    }
  }

  /// مزامنة إعدادات الإشعارات مع خدمة الإشعارات
  void _syncNotificationSettings() {
    try {
      final notificationService = Get.find<NotificationService>();
      notificationService.syncAutomationRulesSettings();
      print('🔄 تم مزامنة إعدادات الإشعارات');
    } catch (e) {
      print('⚠️ لم يتم العثور على خدمة الإشعارات للمزامنة: $e');
    }
  }

  /// إضافة قاعدة أتمتة جديدة
  Future<void> addAutomationRule(AdvancedAutomationRule rule) async {
    try {
      final conn = await MySqlConnection.connect(ConnectionSettings(
          host: controller.hostZain.value,
          user: 'root',
          db: 'zain',
          password: 'zain',
          characterSet: CharacterSet.UTF8));

      await conn.query(
          'INSERT INTO advanced_automation_rules '
          '(id, name, description, triggers, conditions, actions, '
          'logic_operator, enabled, priority) '
          'VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
          [
            rule.id,
            rule.name,
            rule.description,
            json.encode(rule.triggers.map((t) => t.toJson()).toList()),
            json.encode(rule.conditions.map((c) => c.toJson()).toList()),
            json.encode(rule.actions.map((a) => a.toJson()).toList()),
            rule.logicOperator == LogicOperator.or ? 'OR' : 'AND',
            rule.enabled ? 1 : 0,
            rule.priority,
          ]);

      _automationRules.add(rule);
      _updateStatistics();
      print('✅ تم إضافة قاعدة أتمتة جديدة: ${rule.name}');
    } catch (e) {
      print('❌ خطأ في إضافة قاعدة الأتمتة: $e');
    }
  }

  /// تحديث قاعدة أتمتة موجودة
  Future<void> updateAutomationRule(AdvancedAutomationRule updatedRule) async {
    try {
      final conn = await MySqlConnection.connect(ConnectionSettings(
          host: controller.hostZain.value,
          user: 'root',
          db: 'zain',
          password: 'zain',
          characterSet: CharacterSet.UTF8));

      await conn.query(
          'UPDATE advanced_automation_rules SET '
          'name = ?, description = ?, triggers = ?, conditions = ?, '
          'actions = ?, logic_operator = ?, enabled = ?, priority = ?, '
          'trigger_count = 0, last_triggered = NULL '
          'WHERE id = ?',
          [
            updatedRule.name,
            updatedRule.description,
            json.encode(updatedRule.triggers.map((t) => t.toJson()).toList()),
            json.encode(updatedRule.conditions.map((c) => c.toJson()).toList()),
            json.encode(updatedRule.actions.map((a) => a.toJson()).toList()),
            updatedRule.logicOperator == LogicOperator.or ? 'OR' : 'AND',
            updatedRule.enabled ? 1 : 0,
            updatedRule.priority,
            updatedRule.id,
          ]);

      // تحديث القاعدة في القائمة المحلية
      final index =
          _automationRules.indexWhere((rule) => rule.id == updatedRule.id);
      if (index != -1) {
        _automationRules[index] = updatedRule;
        _updateStatistics();
        print('✅ تم تحديث قاعدة الأتمتة: ${updatedRule.name}');
      } else {
        print(
            '❌ لم يتم العثور على قاعدة الأتمتة في القائمة المحلية: ${updatedRule.id}');
      }

      await conn.close();
    } catch (e) {
      print('❌ خطأ في تحديث قاعدة الأتمتة: $e');
    }
  }

  Future<void> removeAutomationRule(String ruleId) async {
    try {
      final conn = await MySqlConnection.connect(ConnectionSettings(
          host: controller.hostZain.value,
          user: 'root',
          db: 'zain',
          password: 'zain',
          characterSet: CharacterSet.UTF8));

      await conn.query(
          'DELETE FROM advanced_automation_rules WHERE id = ?', [ruleId]);

      _automationRules.removeWhere((rule) => rule.id == ruleId);
      _updateStatistics();
      print('✅ تم حذف قاعدة الأتمتة: $ruleId');
    } catch (e) {
      print('❌ خطأ في حذف قاعدة الأتمتة: $e');
    }
  }

  Future<void> toggleAutomationRule(String ruleId, bool enabled) async {
    try {
      final conn = await MySqlConnection.connect(ConnectionSettings(
          host: controller.hostZain.value,
          user: 'root',
          db: 'zain',
          password: 'zain',
          characterSet: CharacterSet.UTF8));

      await conn.query(
          'UPDATE advanced_automation_rules SET enabled = ? WHERE id = ?',
          [enabled ? 1 : 0, ruleId]);

      final index = _automationRules.indexWhere((r) => r.id == ruleId);
      if (index != -1) {
        _automationRules[index] =
            _automationRules[index].copyWith(enabled: enabled);
        _updateStatistics();
        print(
            '✅ تم ${enabled ? 'تفعيل' : 'إلغاء تفعيل'} قاعدة الأتمتة: $ruleId');
      }
    } catch (e) {
      print('❌ خطأ في تغيير حالة قاعدة الأتمتة: $e');
    }
  }

  /// الحصول على قاعدة أتمتة بالمعرف
  AdvancedAutomationRule? getAutomationRule(String ruleId) {
    try {
      return _automationRules.firstWhere((rule) => rule.id == ruleId);
    } catch (e) {
      return null;
    }
  }

  /// إيقاف الخدمة
  void stopService() {
    _isRunning.value = false;
    print('⏹️ تم إيقاف خدمة الأتمتة المتقدمة');
  }

  /// تنظيف النصوص العربية من الأحرف الخاطئة
  static Map<String, dynamic> _cleanArabicText(Map<String, dynamic> data) {
    final cleanedData = Map<String, dynamic>.from(data);

    cleanedData.forEach((key, value) {
      if (value is String) {
        cleanedData[key] = _sanitizeArabicString(value);
      } else if (value is List) {
        cleanedData[key] = _cleanArabicList(value);
      } else if (value is Map<String, dynamic>) {
        cleanedData[key] = _cleanArabicText(value);
      }
    });

    return cleanedData;
  }

  /// تنظيف قائمة تحتوي على نصوص عربية
  static List<dynamic> _cleanArabicList(List<dynamic> list) {
    return list.map((item) {
      if (item is String) {
        return _sanitizeArabicString(item);
      } else if (item is Map<String, dynamic>) {
        return _cleanArabicText(item);
      } else if (item is List) {
        return _cleanArabicList(item);
      }
      return item;
    }).toList();
  }

  /// تنظيف نص عربي من الأحرف الخاطئة
  static String _sanitizeArabicString(String text) {
    if (text.isEmpty) return text;

    String cleaned = text
        .replaceAll(
            RegExp(
                r'[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\u0020-\u007E\u00A0-\u00FF]'),
            '')
        .replaceAll(RegExp(r'[\x00-\x1F\x7F-\x9F]'), '')
        .trim();

    if (cleaned.isEmpty && text.isNotEmpty) {
      if (text.contains('*F(JG') || text.contains('F8\'E')) {
        return 'إشعار من نظام الأتمتة';
      } else if (text.contains('*E *FAJ0') || text.contains('\'D#*E*')) {
        return 'تم تنفيذ قاعدة الأتمتة بنجاح';
      } else {
        return 'نص تلقائي';
      }
    }

    return cleaned;
  }
}
