#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات التحكم في الرسائل والسجلات
"""

import logging
import sys
from datetime import datetime

# مستويات الرسائل
class LogLevel:
    SILENT = 0      # لا رسائل
    ERROR = 1       # أخطاء فقط
    WARNING = 2     # تحذيرات وأخطاء
    INFO = 3        # معلومات عامة
    DEBUG = 4       # جميع الرسائل
    VERBOSE = 5     # رسائل مفصلة

# الإعدادات الافتراضية
DEFAULT_SETTINGS = {
    'mqtt_connection': LogLevel.INFO,      # رسائل الاتصال بـ MQTT
    'mqtt_messages': LogLevel.WARNING,     # رسائل MQTT العادية
    'automation': LogLevel.INFO,           # رسائل الأتمتة
    'database': LogLevel.WARNING,          # رسائل قاعدة البيانات
    'system': LogLevel.INFO,               # رسائل النظام
    'debug': LogLevel.ERROR,               # رسائل التشخيص
}

class SmartLogger:
    """مسجل ذكي للتحكم في الرسائل"""
    
    def __init__(self, settings=None):
        self.settings = settings or DEFAULT_SETTINGS.copy()
        self._setup_logging()
    
    def _setup_logging(self):
        """إعداد نظام السجلات"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        self.logger = logging.getLogger('SmartHome')
    
    def should_log(self, category, level):
        """تحديد ما إذا كان يجب تسجيل الرسالة"""
        category_level = self.settings.get(category, LogLevel.INFO)
        return level <= category_level
    
    def log_mqtt_connection(self, message, level=LogLevel.INFO):
        """تسجيل رسائل الاتصال بـ MQTT"""
        if self.should_log('mqtt_connection', level):
            if level == LogLevel.ERROR:
                print(f"❌ {message}")
            elif level == LogLevel.WARNING:
                print(f"⚠️ {message}")
            else:
                print(f"✅ {message}")
    
    def log_mqtt_message(self, message, level=LogLevel.DEBUG):
        """تسجيل رسائل MQTT العادية"""
        if self.should_log('mqtt_messages', level):
            if level <= LogLevel.WARNING:
                print(f"📡 {message}")
    
    def log_automation(self, message, level=LogLevel.INFO):
        """تسجيل رسائل الأتمتة"""
        if self.should_log('automation', level):
            if level == LogLevel.ERROR:
                print(f"❌ {message}")
            elif level == LogLevel.WARNING:
                print(f"⚠️ {message}")
            else:
                print(f"🤖 {message}")
    
    def log_database(self, message, level=LogLevel.WARNING):
        """تسجيل رسائل قاعدة البيانات"""
        if self.should_log('database', level):
            if level == LogLevel.ERROR:
                print(f"❌ DB: {message}")
            elif level == LogLevel.WARNING:
                print(f"⚠️ DB: {message}")
            else:
                print(f"🗄️ DB: {message}")
    
    def log_system(self, message, level=LogLevel.INFO):
        """تسجيل رسائل النظام"""
        if self.should_log('system', level):
            if level == LogLevel.ERROR:
                print(f"❌ {message}")
            elif level == LogLevel.WARNING:
                print(f"⚠️ {message}")
            else:
                print(f"🔧 {message}")
    
    def log_debug(self, message, level=LogLevel.DEBUG):
        """تسجيل رسائل التشخيص"""
        if self.should_log('debug', level):
            print(f"🔍 {message}")
    
    def set_level(self, category, level):
        """تعديل مستوى فئة معينة"""
        self.settings[category] = level
    
    def set_quiet_mode(self):
        """تفعيل الوضع الهادئ - أخطاء فقط"""
        for category in self.settings:
            self.settings[category] = LogLevel.ERROR
    
    def set_normal_mode(self):
        """تفعيل الوضع العادي"""
        self.settings.update(DEFAULT_SETTINGS)
    
    def set_verbose_mode(self):
        """تفعيل الوضع المفصل"""
        for category in self.settings:
            self.settings[category] = LogLevel.VERBOSE

# إنشاء مثيل عام
smart_logger = SmartLogger()

# دوال مساعدة للاستخدام السهل
def log_mqtt_connection(message, level=LogLevel.INFO):
    smart_logger.log_mqtt_connection(message, level)

def log_mqtt_message(message, level=LogLevel.DEBUG):
    smart_logger.log_mqtt_message(message, level)

def log_automation(message, level=LogLevel.INFO):
    smart_logger.log_automation(message, level)

def log_database(message, level=LogLevel.WARNING):
    smart_logger.log_database(message, level)

def log_system(message, level=LogLevel.INFO):
    smart_logger.log_system(message, level)

def log_debug(message, level=LogLevel.DEBUG):
    smart_logger.log_debug(message, level)

# تطبيق الوضع الهادئ افتراضياً لتقليل الرسائل
smart_logger.set_level('mqtt_connection', LogLevel.ERROR)    # أخطاء MQTT فقط
smart_logger.set_level('mqtt_messages', LogLevel.ERROR)      # لا رسائل MQTT عادية
smart_logger.set_level('debug', LogLevel.ERROR)              # لا رسائل تشخيص

# نظام بسيط لتحكم في رسائل قطع الاتصال المتكررة
_last_disconnect_messages = {}

def should_show_disconnect(message_key):
    """تحديد ما إذا كان يجب إظهار رسالة قطع الاتصال"""
    import time
    current_time = time.time()

    # إظهار الرسالة كل 60 ثانية فقط
    if message_key not in _last_disconnect_messages:
        _last_disconnect_messages[message_key] = current_time
        return True

    if current_time - _last_disconnect_messages[message_key] > 60:
        _last_disconnect_messages[message_key] = current_time
        return True

    return False
