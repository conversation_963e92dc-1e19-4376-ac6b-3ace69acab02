{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-11.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility-6.0.0\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications-17.2.4\\\\", "native_build": true, "dependencies": []}, {"name": "geolocator_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_apple-2.3.7\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_ios-0.8.12+1\\\\", "native_build": true, "dependencies": []}, {"name": "network_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\network_info_plus-4.1.0+1\\\\", "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_apple-9.4.5\\\\", "native_build": true, "dependencies": []}, {"name": "pointer_interceptor_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\pointer_interceptor_ios-0.10.1\\\\", "native_build": true, "dependencies": []}, {"name": "rive_common", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\rive_common-0.4.12\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.3\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "wifi_scan", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wifi_scan-0.4.1+1\\\\", "native_build": true, "dependencies": []}], "android": [{"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-11.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility-6.0.0\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications-17.2.4\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_plugin_android_lifecycle-2.0.23\\\\", "native_build": true, "dependencies": []}, {"name": "geolocator_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_android-4.6.1\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_android-0.8.12+17\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "network_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\network_info_plus-4.1.0+1\\\\", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_android-12.0.13\\\\", "native_build": true, "dependencies": []}, {"name": "rive_common", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\rive_common-0.4.12\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.3.3\\\\", "native_build": true, "dependencies": []}, {"name": "sqflite_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_android-2.4.0\\\\", "native_build": true, "dependencies": []}, {"name": "wifi_scan", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wifi_scan-0.4.1+1\\\\", "native_build": true, "dependencies": []}], "macos": [{"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-11.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "file_selector_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_macos-0.9.4+2\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_macos-1.0.0\\\\", "native_build": false, "dependencies": []}, {"name": "flutter_local_notifications", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications-17.2.4\\\\", "native_build": true, "dependencies": []}, {"name": "geolocator_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_apple-2.3.7\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_macos-0.2.1+1\\\\", "native_build": false, "dependencies": ["file_selector_macos"]}, {"name": "network_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\network_info_plus-4.1.0+1\\\\", "native_build": true, "dependencies": []}, {"name": "rive_common", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\rive_common-0.4.12\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.3\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "linux": [{"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-11.2.0\\\\", "native_build": false, "dependencies": []}, {"name": "file_selector_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_linux-0.9.3\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_linux-1.0.0\\\\", "native_build": false, "dependencies": []}, {"name": "flutter_local_notifications_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications_linux-4.0.1\\\\", "native_build": false, "dependencies": []}, {"name": "image_picker_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_linux-0.2.1+1\\\\", "native_build": false, "dependencies": ["file_selector_linux"]}, {"name": "network_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\network_info_plus-4.1.0+1\\\\", "native_build": false, "dependencies": []}, {"name": "path_provider_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": []}, {"name": "rive_common", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\rive_common-0.4.12\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"]}], "windows": [{"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-11.2.0\\\\", "native_build": false, "dependencies": []}, {"name": "file_selector_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_windows-0.9.3+3\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_windows-1.0.0\\\\", "native_build": false, "dependencies": []}, {"name": "geolocator_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_windows-0.2.3\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_windows-0.2.1+1\\\\", "native_build": false, "dependencies": ["file_selector_windows"]}, {"name": "network_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\network_info_plus-4.1.0+1\\\\", "native_build": false, "dependencies": []}, {"name": "path_provider_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_windows-0.2.1\\\\", "native_build": true, "dependencies": []}, {"name": "rive_common", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\rive_common-0.4.12\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"]}], "web": [{"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-11.2.0\\\\", "dependencies": []}, {"name": "flutter_keyboard_visibility_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_web-2.0.0\\\\", "dependencies": []}, {"name": "geolocator_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_web-4.1.1\\\\", "dependencies": []}, {"name": "image_picker_for_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_for_web-3.0.6\\\\", "dependencies": []}, {"name": "network_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\network_info_plus-4.1.0+1\\\\", "dependencies": []}, {"name": "permission_handler_html", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_html-0.1.3+2\\\\", "dependencies": []}, {"name": "pointer_interceptor_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\pointer_interceptor_web-0.10.2+1\\\\", "dependencies": []}, {"name": "rive_common", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\rive_common-0.4.12\\\\", "dependencies": []}, {"name": "shared_preferences_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.4.2\\\\", "dependencies": []}]}, "dependencyGraph": [{"name": "device_info_plus", "dependencies": []}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "flutter_keyboard_visibility", "dependencies": ["flutter_keyboard_visibility_linux", "flutter_keyboard_visibility_macos", "flutter_keyboard_visibility_web", "flutter_keyboard_visibility_windows"]}, {"name": "flutter_keyboard_visibility_linux", "dependencies": []}, {"name": "flutter_keyboard_visibility_macos", "dependencies": []}, {"name": "flutter_keyboard_visibility_web", "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "dependencies": []}, {"name": "flutter_local_notifications", "dependencies": ["flutter_local_notifications_linux"]}, {"name": "flutter_local_notifications_linux", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "geolocator", "dependencies": ["geolocator_android", "geolocator_apple", "geolocator_web", "geolocator_windows"]}, {"name": "geolocator_android", "dependencies": []}, {"name": "geolocator_apple", "dependencies": []}, {"name": "geolocator_web", "dependencies": []}, {"name": "geolocator_windows", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "network_info_plus", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "pointer_interceptor", "dependencies": ["pointer_interceptor_ios", "pointer_interceptor_web"]}, {"name": "pointer_interceptor_ios", "dependencies": []}, {"name": "pointer_interceptor_web", "dependencies": []}, {"name": "rive_common", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "wifi_scan", "dependencies": []}], "date_created": "2025-08-10 18:59:25.366907", "version": "3.24.4", "swift_package_manager_enabled": false}