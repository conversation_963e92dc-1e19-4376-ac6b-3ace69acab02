import os
import hashlib
import json
import wave
from datetime import datetime, timedelta
from gtts import gTTS
from pathlib import Path
from pydub import AudioSegment
from pygame import mixer

class SmartHomeTTSCache:
    def __init__(self, cache_dir="/home/<USER>/myProject/resources/wav/speech_cache", max_age_days=30, max_size_mb=500, output_dir="/home/<USER>/myProject", sounds_dir="/home/<USER>/myProject/resources/wav"):
        """
        تهيئة نظام التخزين المؤقت والصوت
        
        :param sounds_dir: مجلد الملفات الصوتية الجاهزة
        """
        self.cache_dir = Path(cache_dir)
        self.output_dir = Path(output_dir)
        self.sounds_dir = Path(sounds_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
        self.metadata_file = self.cache_dir / "metadata.json"
        self.max_age_days = max_age_days
        self.max_size_mb = max_size_mb
        self.metadata = self._load_metadata()
        mixer.init()
        
    def say(self, text, lang='ar', speed=1.1, pitch_shift=1.25):
        """
        دالة موحدة للنطق أو تشغيل ملف صوتي محدد
        
        :param text: النص أو اسم الملف الصوتي
        :param lang: لغة النص (إذا كان نصاً)
        :param speed: سرعة النطق
        :param pitch_shift: تغيير الطبقة الصوتية
        """
        # التحقق من وجود الملف الصوتي الجاهز
        print(text)
        if len(text)<15:
            sound_path = self.sounds_dir / f"{text}.wav"
            if sound_path.exists():
                self.play_audio(str(sound_path))
                return
        
        # إنشاء الصوت المنطوق
        audio_path = self.get_speech(text, lang, speed, pitch_shift)
        self.play_audio(audio_path)

    
    def _load_metadata(self):
        """تحميل البيانات الوصفية للملفات المخزنة"""
        if self.metadata_file.exists():
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def _save_metadata(self):
        """حفظ البيانات الوصفية"""
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(self.metadata, f, ensure_ascii=False, indent=2)
    
    def _get_file_hash(self, text: str, lang: str) -> str:
        """إنشاء اسم فريد للملف بناءً على النص واللغة"""
        return hashlib.md5(f"{text}_{lang}".encode('utf-8')).hexdigest()
    
    def get_speech(self, text: str, lang: str = 'ar', speed: float = 1.1, pitch_shift: float = 1.25) -> str:
        """
        الحصول على ملف الصوت من التخزين المؤقت أو إنشاؤه مع التسريع وتغيير الطبقة
        
        :param text: النص المراد تحويله لصوت
        :param lang: لغة النص
        :param speed: سرعة تشغيل الصوت
        :param pitch_shift: معامل تغيير الطبقة الصوتية
        :return: مسار ملف الصوت
        """
        file_hash = self._get_file_hash(text, lang)
        cache_path = self.cache_dir / f"{file_hash}.mp3"
        outfile = self.output_dir / f"output.wav"
        
        # التحقق من وجود الملف في التخزين المؤقت
        if cache_path.exists():
            self.metadata[file_hash]['last_accessed'] = datetime.now().isoformat()
            self._save_metadata()
            
            # إنشاء الصوت وتسريعه وتغيير طبقته
            self._process_audio(str(cache_path), str(outfile), speed, pitch_shift)
            return str(outfile)
        
        # إنشاء ملف صوتي جديد
        tts = gTTS(text=text, lang=lang)
        tts.save(str(cache_path))
        
        # تحديث البيانات الوصفية
        self.metadata[file_hash] = {
            'text': text,
            'lang': lang,
            'created': datetime.now().isoformat(),
            'last_accessed': datetime.now().isoformat(),
            'file_size': os.path.getsize(cache_path)
        }
        self._save_metadata()
        
        # معالجة وتسريع الصوت وتغيير طبقته
        self._process_audio(str(cache_path), str(outfile), speed, pitch_shift)
        
        # تنظيف التخزين المؤقت إذا تجاوز الحد الأقصى
        self._cleanup_cache()
        
        return str(outfile)
    
    def _process_audio(self, input_path: str, output_path: str, speed: float = 1.1, pitch_shift: float = 1.25):
        """
        معالجة الصوت وتغيير الطبقة والسرعة
        
        :param input_path: مسار الملف الصوتي الأصلي
        :param output_path: مسار الملف الصوتي بعد المعالجة
        :param speed: سرعة تشغيل الصوت
        :param pitch_shift: معامل تغيير الطبقة الصوتية
        """
        # تحويل MP3 إلى WAV مؤقت للدمج
        temp_wav = self.output_dir / "temp.wav"
        AudioSegment.from_mp3(input_path).export(temp_wav, format="wav")
        
        # قائمة الملفات للدمج
        wav_files = [str(temp_wav)]
        
        # دمج الملفات
        with wave.open(output_path, 'wb') as wav_out:      
            for wav_path in wav_files:
                with wave.open(wav_path, 'rb') as wav_in:
                    if not wav_out.getnframes():
                        wav_out.setparams(wav_in.getparams())
                    wav_out.writeframes(wav_in.readframes(wav_in.getnframes()))
        
        # تغيير الطبقة والسرعة
        audio = AudioSegment.from_wav(output_path)
        pitch_changed_sound = audio._spawn(audio.raw_data, overrides={
            "frame_rate": int(audio.frame_rate * pitch_shift)
        }).set_frame_rate(audio.frame_rate)
        
        speed_changed_sound = pitch_changed_sound.speedup(playback_speed=speed)
        speed_changed_sound.export(output_path, format="wav")
        
        # حذف الملف المؤقت
        os.remove(temp_wav)
    
    def play_audio(self, audio_path: str):
        """
        تشغيل الملف الصوتي
        
        :param audio_path: مسار الملف الصوتي
        """
        mixer.music.load(audio_path)
        mixer.music.play()
    
    def _cleanup_cache(self):
        """تنظيف الملفات القديمة وإدارة حجم التخزين"""
        try:
            # حذف الملفات القديمة
            now = datetime.now()
            for file_hash, metadata in list(self.metadata.items()):
                created = datetime.fromisoformat(metadata['created'])
                if (now - created).days > self.max_age_days:
                    cache_path = self.cache_dir / f"{file_hash}.mp3"
                    if cache_path.exists():
                        os.remove(cache_path)
                    del self.metadata[file_hash]
            
            # التحقق من الحجم الإجمالي
            total_size = sum(
                os.path.getsize(self.cache_dir / f"{hash}.mp3") 
                for hash in self.metadata.keys() 
                if (self.cache_dir / f"{hash}.mp3").exists()
            ) / (1024 * 1024)  # تحويل إلى ميجابايت
            
            # حذف أقدم الملفات إذا تجاوز الحد الأقصى
            if total_size > self.max_size_mb:
                sorted_files = sorted(
                    self.metadata.items(), 
                    key=lambda x: datetime.fromisoformat(x[1]['last_accessed'])
                )
                for file_hash, _ in sorted_files:
                    cache_path = self.cache_dir / f"{file_hash}.mp3"
                    if cache_path.exists():
                        os.remove(cache_path)
                        del self.metadata[file_hash]
                        total_size -= os.path.getsize(cache_path) / (1024 * 1024)
                    if total_size <= self.max_size_mb:
                        break
            
            # حفظ التغييرات في البيانات الوصفية
            self._save_metadata()
        except Exception as e:
            print(f"خطأ في تنظيف التخزين المؤقت: {e}")

    def get_cache_stats(self):
        """الحصول على إحصائيات التخزين المؤقت"""
        try:
            total_files = len(self.metadata)
            total_size = sum(
                os.path.getsize(self.cache_dir / f"{hash}.mp3") 
                for hash in self.metadata.keys() 
                if (self.cache_dir / f"{hash}.mp3").exists()
            ) / (1024 * 1024)  # تحويل إلى ميجابايت
            
            oldest_file = min(
                (datetime.fromisoformat(metadata['created']) for metadata in self.metadata.values()),
                default=datetime.now()
            )
            newest_file = max(
                (datetime.fromisoformat(metadata['created']) for metadata in self.metadata.values()),
                default=datetime.now()
            )
            
            return {
                'total_files': total_files,
                'total_size_mb': round(total_size, 2),
                'oldest_file_date': oldest_file.isoformat(),
                'newest_file_date': newest_file.isoformat()
            }
        except Exception as e:
            print(f"خطأ في استرجاع إحصائيات التخزين المؤقت: {e}")
            return {}
