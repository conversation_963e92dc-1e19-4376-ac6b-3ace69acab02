---
name: Restrict task creation

# yamllint disable-line rule:truthy
on:
  issues:
    types: [opened]

jobs:
  check-authorization:
    runs-on: ubuntu-latest
    # Only run if this is a Task issue type (from the issue form)
    if: github.event.issue.issue_type == 'Task'
    steps:
      - name: Check if user is authorized
        uses: actions/github-script@v7
        with:
          script: |
            const issueAuthor = context.payload.issue.user.login;

            // Check if user is an organization member
            try {
              await github.rest.orgs.checkMembershipForUser({
                org: 'home-assistant',
                username: issueAuth<PERSON>
              });
              console.log(`✅ ${issueAuth<PERSON>} is an organization member`);
              return; // Authorized
            } catch (error) {
              console.log(
                `❌ ${issueAuthor} is not authorized to create Task issues`
              );
            }

            // Close the issue with a comment
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: `Hi @${issueAuthor}, thank you for your contribution!\n\n` +
                    `Task issues are restricted to Open Home Foundation ` +
                    `staff and authorized contributors.\n\n` +
                    `If you would like to:\n` +
                    `- Report a bug: Please use the ` +
                    `[bug report form](https://github.com/home-assistant/` +
                    `addons/issues/new?template=bug_report.yml)\n` +
                    `- Request a feature: Please submit to ` +
                    `[Feature Requests](` +
                    `https://github.com/orgs/home-assistant/discussions)\n\n` +
                    `If you believe you should have access to create ` +
                    `Task issues, please contact the maintainers.`
            });

            await github.rest.issues.update({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              state: 'closed'
            });

            // Add a label to indicate this was auto-closed
            await github.rest.issues.addLabels({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              labels: ['auto-closed']
            });
