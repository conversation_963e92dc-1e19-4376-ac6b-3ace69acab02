import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsController extends GetxController {
  // متغير لحفظ حالة الوضع الداكن/الفاتح
  RxBool isDarkMode = false.obs;

  // متغير لتتبع ما إذا كان المستخدم قد غير الإعداد يدوياً
  bool _userHasManuallyChanged = false;

  // مفاتيح حفظ الإعدادات
  static const String _darkModeKey = 'dark_mode';
  static const String _userChangedKey = 'user_changed_theme';

  @override
  void onInit() {
    super.onInit();
    _loadSettings();
  }

  // تحميل الإعدادات المحفوظة
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _userHasManuallyChanged = prefs.getBool(_userChangedKey) ?? false;

      if (_userHasManuallyChanged) {
        // إذا كان المستخدم قد غير الإعداد يدوياً، استخدم الإعداد المحفوظ
        isDarkMode.value = prefs.getBool(_darkModeKey) ?? false;
      } else {
        // إذا لم يغير المستخدم الإعداد، اتبع نظام الهاتف
        final brightness =
            WidgetsBinding.instance.platformDispatcher.platformBrightness;
        isDarkMode.value = brightness == Brightness.dark;
      }

      // تحديث الثيم
      Get.changeThemeMode(isDarkMode.value ? ThemeMode.dark : ThemeMode.light);
    } catch (e) {
      print('خطأ في تحميل الإعدادات: $e');
    }
  }

  // تبديل الوضع الداكن/الفاتح
  Future<void> toggleTheme() async {
    try {
      isDarkMode.value = !isDarkMode.value;
      _userHasManuallyChanged = true;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_darkModeKey, isDarkMode.value);
      await prefs.setBool(_userChangedKey, true);

      // تحديث الثيم في التطبيق
      Get.changeThemeMode(isDarkMode.value ? ThemeMode.dark : ThemeMode.light);

      // تحديث جميع GetBuilder widgets
      update();
    } catch (e) {
      print('خطأ في حفظ الإعدادات: $e');
    }
  }

  // تعيين الوضع الداكن
  Future<void> setDarkMode(bool value) async {
    try {
      isDarkMode.value = value;
      _userHasManuallyChanged = true;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_darkModeKey, value);
      await prefs.setBool(_userChangedKey, true);

      // تحديث الثيم في التطبيق
      Get.changeThemeMode(value ? ThemeMode.dark : ThemeMode.light);

      // تحديث جميع GetBuilder widgets
      update();
    } catch (e) {
      print('خطأ في حفظ الإعدادات: $e');
    }
  }

  // إعادة تعيين الإعدادات لتتبع نظام الهاتف
  Future<void> resetToSystemTheme() async {
    try {
      _userHasManuallyChanged = false;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_userChangedKey, false);

      // اتبع نظام الهاتف
      final brightness =
          WidgetsBinding.instance.platformDispatcher.platformBrightness;
      isDarkMode.value = brightness == Brightness.dark;
      await prefs.setBool(_darkModeKey, isDarkMode.value);

      // تحديث الثيم في التطبيق
      Get.changeThemeMode(isDarkMode.value ? ThemeMode.dark : ThemeMode.light);

      // تحديث جميع GetBuilder widgets
      update();
    } catch (e) {
      print('خطأ في إعادة تعيين الإعدادات: $e');
    }
  }

  // الحصول على الوضع الحالي
  bool get currentThemeMode => isDarkMode.value;

  // الحصول على نص الوضع الحالي
  String get currentThemeName =>
      isDarkMode.value ? 'الوضع الداكن' : 'الوضع الفاتح';

  // التحقق من ما إذا كان المستخدم قد غير الإعداد يدوياً
  bool get hasUserManuallyChanged => _userHasManuallyChanged;
}
