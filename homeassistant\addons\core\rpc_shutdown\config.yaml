---
version: "2.5"
slug: rpc_shutdown
name: RPC Shutdown
description: Shutdown Windows machines remotely
url: https://github.com/home-assistant/addons/tree/master/rpc_shutdown
arch:
  - armhf
  - armv7
  - aarch64
  - amd64
  - i386
host_network: true
image: homeassistant/{arch}-addon-rpc_shutdown
options:
  computers:
    - address: ***********
      alias: test-pc
      credentials: user%password
      delay: 0
      message:
        Home Assistant is shutting down this PC. This cannot be canceled. Please
        save your work!
schema:
  computers:
    - address: str
      alias: match(^[\w-]*$)
      credentials: match(^[^%]*(?:%.*)?$)
      delay: int(0,600)?
      message: str?
startup: services
stdin: true
init: false
