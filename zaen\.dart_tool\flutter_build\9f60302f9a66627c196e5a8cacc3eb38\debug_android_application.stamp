{"inputs": ["C:\\Users\\<USER>\\Desktop\\zain\\zaen\\.dart_tool\\flutter_build\\9f60302f9a66627c196e5a8cacc3eb38\\app.dill", "C:\\src\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "C:\\src\\flutter\\bin\\internal\\engine.version", "C:\\src\\flutter\\bin\\internal\\engine.version", "C:\\src\\flutter\\bin\\internal\\engine.version", "C:\\src\\flutter\\bin\\internal\\engine.version", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\pubspec.yaml", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\4444.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\44444.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\44445.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\454.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\fff.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\bathroom1.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\bathroom2.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\bathroom3.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\bedroom1.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\bedroom2.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\bedroom3.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\eatingroom1.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\eatingroom2.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\eatingroom3.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\garden1.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\garden2.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\garden3.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\garden4.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\garden5.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\garden6.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\garden7.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\home1.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\home2.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\home3.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\home4.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\home5.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\home6.jpeg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\home7.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\home8.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\kitchen1.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\kitchen2.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\kitchen3.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\kitchen4.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\livingroom1.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\livingroom2.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\livingroom3.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\office1.JPG", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\office2.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\office3.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\office4.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\office5.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\office6.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\office7.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\passage1.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\passage10.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\passage2.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\passage3.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\passage4.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\passage5.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\passage6.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\passage7.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\passage8.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\places\\home\\passage9.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\devices\\ac.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\devices\\light.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\devices\\switch light.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\devices\\switch light2.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\devices\\switch.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\devices\\tv.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\assets\\images\\devices\\zain.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\flare\\error.flr", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\flare\\info.flr", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\flare\\info2.flr", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\flare\\info_without_loop.flr", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\flare\\question.flr", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\flare\\succes.flr", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\flare\\succes_without_loop.flr", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\flare\\warning.flr", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\flare\\warning_without_loop.flr", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\rive\\error.riv", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\rive\\info.riv", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\rive\\info_reverse.riv", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\rive\\question.riv", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\rive\\success.riv", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\assets\\rive\\warning.riv", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\lib\\fonts\\fa-brands-400.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\lib\\fonts\\fa-regular-400.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\lib\\fonts\\fa-solid-900.ttf", "C:\\src\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\args-2.6.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_circular_chart-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\awesome_dialog-3.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\charcode-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\conditional_builder_null_safety-0.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dashed_circle-0.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.10\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-11.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus_platform_interface-7.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\event_bus-2.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\expansion_tile_card-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_linux-0.9.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_macos-0.9.4+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_windows-0.9.3+3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility-6.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility_linux-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility_macos-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility_platform_interface-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility_web-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility_windows-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-4.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-17.2.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_platform_interface-7.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.23\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_time_picker_spinner-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_typeahead-5.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator-13.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_android-4.6.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_apple-2.3.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_platform_interface-4.2.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_web-4.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_windows-0.2.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.6.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\graphs-2.3.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_android-0.8.12+17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_for_web-3.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_ios-0.8.12+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_linux-0.2.1+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_macos-0.2.1+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_platform_interface-2.10.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_windows-0.2.1+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-4.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.16+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.15.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mqtt_client-10.5.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mrx_charts-0.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\multicast_dns-0.3.2+7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mysql1-0.20.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\network_info_plus-4.1.0+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\network_info_plus_platform_interface-1.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\nm-0.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\percent_indicator-4.2.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler-11.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_android-12.0.13\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_apple-9.4.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_html-0.1.3+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.2.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_windows-0.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pointer_interceptor-0.10.1+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pointer_interceptor_ios-0.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pointer_interceptor_platform_interface-0.10.0+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pointer_interceptor_web-0.10.2+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pool-1.5.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\reorderable_grid_view-2.2.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rive-0.13.15\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rive_common-0.4.12\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.3.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.3.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_web-2.4.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sleek_circular_slider-2.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_android-2.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4+5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_darwin-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.0+3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timezone-0.9.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\toggle_switch-2.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\universal_html-2.2.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\universal_io-2.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-14.2.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\wifi_scan-0.4.1+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.8.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-1.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\LICENSE", "C:\\src\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "C:\\src\\flutter\\packages\\flutter\\LICENSE", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD480528950"], "outputs": ["C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/4444.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/44444.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/44445.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/454.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/fff.jpg", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/places/home/<USER>", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/devices/ac.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/devices/light.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/devices/switch%20light.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/devices/switch%20light2.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/devices/switch.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/devices/tv.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/devices/zain.png", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/flare/error.flr", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/flare/info.flr", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/flare/info2.flr", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/flare/info_without_loop.flr", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/flare/question.flr", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/flare/succes.flr", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/flare/succes_without_loop.flr", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/flare/warning.flr", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/flare/warning_without_loop.flr", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/rive/error.riv", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/rive/info.riv", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/rive/info_reverse.riv", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/rive/question.riv", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/rive/success.riv", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/awesome_dialog/assets/rive/warning.riv", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z"]}