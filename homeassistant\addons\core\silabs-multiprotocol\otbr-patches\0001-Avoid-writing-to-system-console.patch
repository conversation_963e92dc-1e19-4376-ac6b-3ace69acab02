From 53d4be5c893dac04a909563444453fb471852ccc Mon Sep 17 00:00:00 2001
Message-Id: <<EMAIL>>
From: <PERSON> <<EMAIL>>
Date: Thu, 17 Feb 2022 22:57:16 +0100
Subject: [PATCH] Avoid writing to system console

Add-ons do not have a working syslog infrastructure. The LOG_CONS
options causes syslog() calls to log to the system console
(/dev/console). Avoid writing to system console as we use logging to
stderr.
---
 src/common/logging.cpp | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/src/common/logging.cpp b/src/common/logging.cpp
index 5398e47133..69f150dadb 100644
--- a/src/common/logging.cpp
+++ b/src/common/logging.cpp
@@ -83,7 +83,7 @@ void otbrLogInit(const char *aIdent, otbrLogLevel aLevel, bool aPrintStderr)
     assert(aIdent);
     assert(aLevel >= OTBR_LOG_EMERG && aLevel <= OTBR_LOG_DEBUG);
 
-    openlog(aIdent, (LOG_CONS | LOG_PID) | (aPrintStderr ? LOG_PERROR : 0), OTBR_SYSLOG_FACILITY_ID);
+    openlog(aIdent, LOG_PID | (aPrintStderr ? LOG_PERROR : 0), LOG_USER);
     sLevel        = aLevel;
     sDefaultLevel = sLevel;
 }
-- 
2.39.1

