#!/command/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Start Configurator service
# ==============================================================================
export HC_DIRSFIRST
export HC_ENFORCE_BASEPATH
export HC_GIT
export HC_HASS_API_PASSWORD
export HC_IGNORE_PATTERN

# If any SSH key files are defined in the configuration options, add them for use by git
if bashio::config.has_value "ssh_keys"; then
  # Start the SSH agent
  bashio::log.info "Starting SSH agent"
  eval "$(ssh-agent -s)"

  # Add the keys defined in the configuration options
  while read -r filename; do
    if bashio::fs.file_exists "${filename}"; then
      bashio::log.info "Adding SSH private key file \"${filename}\""
      ssh-add -q "${filename}"
    else
      bashio::log.error "SSH key file \"${filename}\" not found"
    fi
  done <<< "$(bashio::config 'ssh_keys')"
fi

# Setup and run configurator
HC_DIRSFIRST=$(bashio::config 'dirsfirst')
HC_ENFORCE_BASEPATH=$(bashio::config 'enforce_basepath')
HC_GIT=$(bashio::config 'git')
HC_HASS_API_PASSWORD="${SUPERVISOR_TOKEN}"
HC_IGNORE_PATTERN="$(bashio::config 'ignore_pattern | join(",")')"

exec hass-configurator /etc/configurator.conf
