# C/C++ build system timings
generate_cxx_metadata
  execute-generate-process
    [gap of 115ms]
    exec-configure 7305ms
    [gap of 113ms]
  execute-generate-process completed in 7533ms
  [gap of 47ms]
generate_cxx_metadata completed in 7616ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 13ms
  [gap of 17ms]
generate_cxx_metadata completed in 33ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 26ms
  [gap of 35ms]
generate_cxx_metadata completed in 66ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 19ms
  [gap of 21ms]
generate_cxx_metadata completed in 46ms

