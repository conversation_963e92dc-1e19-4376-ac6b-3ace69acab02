/// نماذج البيانات للأتمتة المتقدمة
/// يدعم multiple triggers, conditions, actions مع منطق AND/OR

import 'dart:convert';

/// أنواع المحفزات المتاحة
enum TriggerType {
  entityState, // تغيير حالة الكيان
  time, // وقت محدد
  sun, // أحداث الشمس
  weather, // أحداث الطقس
}

/// أنواع الشروط المتاحة
enum ConditionType {
  weather, // شروط الطقس
  sun, // شروط الشمس
  person, // شروط الأشخاص
  time, // شروط الوقت
  entity, // شروط الكيانات
  device, // شروط الأجهزة
}

/// أنواع الإجراءات المتاحة
enum ActionType {
  deviceControl, // التحكم في الأجهزة
  notification, // إرسال إشعار
  scene, // تفعيل مشهد
  delay, // تأخير
}

/// العمليات المنطقية
enum LogicOperator {
  and, // جميع الشروط يجب أن تتحقق
  or, // شرط واحد على الأقل يجب أن يتحقق
}

/// العمليات الرقمية للمقارنة
enum NumericOperator {
  equal, // ==
  notEqual, // !=
  greaterThan, // >
  lessThan, // <
  greaterThanOrEqual, // >=
  lessThanOrEqual, // <=
}

/// نموذج المحفز
class AutomationTrigger {
  final String id;
  final TriggerType type;
  final Map<String, dynamic> config;

  AutomationTrigger({
    required this.id,
    required this.type,
    required this.config,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'type': type.name,
        'config': config,
      };

  factory AutomationTrigger.fromJson(Map<String, dynamic> json) {
    return AutomationTrigger(
      id: json['id'],
      type: TriggerType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => TriggerType.entityState,
      ),
      config: json['config'],
    );
  }

  /// إنشاء محفز تغيير حالة الكيان
  factory AutomationTrigger.entityState({
    required String id,
    required String entityType,
    required String entityId,
    String? expectedState,
  }) {
    return AutomationTrigger(
      id: id,
      type: TriggerType.entityState,
      config: {
        'entity_type': entityType,
        'entity_id': entityId,
        if (expectedState != null) 'state': expectedState,
      },
    );
  }

  /// إنشاء محفز الوقت
  factory AutomationTrigger.time({
    required String id,
    required String time, // HH:MM format
  }) {
    return AutomationTrigger(
      id: id,
      type: TriggerType.time,
      config: {
        'time': time,
      },
    );
  }

  /// إنشاء محفز الشمس
  factory AutomationTrigger.sun({
    required String id,
    required String event, // sunrise, sunset, dawn, dusk, noon, midnight
  }) {
    return AutomationTrigger(
      id: id,
      type: TriggerType.sun,
      config: {
        'event': event,
      },
    );
  }

  /// إنشاء محفز الطقس
  factory AutomationTrigger.weather({
    required String id,
    required String condition, // temperature_above, rain_probability, etc.
    required double threshold,
  }) {
    return AutomationTrigger(
      id: id,
      type: TriggerType.weather,
      config: {
        'condition': condition,
        'threshold': threshold,
      },
    );
  }
}

/// نموذج الشرط
class AutomationCondition {
  final String id;
  final ConditionType type;
  final Map<String, dynamic> config;

  AutomationCondition({
    required this.id,
    required this.type,
    required this.config,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'type': type.name,
        'config': config,
      };

  factory AutomationCondition.fromJson(Map<String, dynamic> json) {
    return AutomationCondition(
      id: json['id'],
      type: ConditionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ConditionType.entity,
      ),
      config: json['config'],
    );
  }

  /// إنشاء شرط الطقس
  factory AutomationCondition.weather({
    required String id,
    double? temperatureMin,
    double? temperatureMax,
    double? humidityMin,
    double? humidityMax,
    String? condition,
  }) {
    return AutomationCondition(
      id: id,
      type: ConditionType.weather,
      config: {
        if (temperatureMin != null) 'temperature_min': temperatureMin,
        if (temperatureMax != null) 'temperature_max': temperatureMax,
        if (humidityMin != null) 'humidity_min': humidityMin,
        if (humidityMax != null) 'humidity_max': humidityMax,
        if (condition != null) 'condition': condition,
      },
    );
  }

  /// إنشاء شرط الشمس
  factory AutomationCondition.sun({
    required String id,
    required String
        condition, // above_horizon, below_horizon, after_sunrise, etc.
    double? elevationThreshold,
  }) {
    return AutomationCondition(
      id: id,
      type: ConditionType.sun,
      config: {
        'condition': condition,
        if (elevationThreshold != null) 'threshold': elevationThreshold,
      },
    );
  }

  /// إنشاء شرط الشخص
  factory AutomationCondition.person({
    required String id,
    required String personId,
    required String state, // home, not_home, unknown
    String? zone,
  }) {
    return AutomationCondition(
      id: id,
      type: ConditionType.person,
      config: {
        'person_id': personId,
        'state': state,
        if (zone != null) 'zone': zone,
      },
    );
  }
}

/// نموذج الإجراء
class AutomationAction {
  final String id;
  final ActionType type;
  final Map<String, dynamic> config;

  AutomationAction({
    required this.id,
    required this.type,
    required this.config,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'type': type.name,
        'config': config,
      };

  factory AutomationAction.fromJson(Map<String, dynamic> json) {
    return AutomationAction(
      id: json['id'],
      type: ActionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ActionType.deviceControl,
      ),
      config: json['config'],
    );
  }

  /// إنشاء إجراء التحكم في الجهاز
  factory AutomationAction.deviceControl({
    required String id,
    required String deviceId,
    required String command,
    String? room,
    String? deviceType,
  }) {
    return AutomationAction(
      id: id,
      type: ActionType.deviceControl,
      config: {
        'device_id': deviceId,
        'command': command,
        if (room != null) 'room': room,
        if (deviceType != null) 'device_type': deviceType,
      },
    );
  }

  /// إنشاء إجراء الإشعار
  factory AutomationAction.notification({
    required String id,
    required String title,
    required String message,
    String priority = 'normal',
  }) {
    return AutomationAction(
      id: id,
      type: ActionType.notification,
      config: {
        'title': title,
        'message': message,
        'priority': priority,
      },
    );
  }

  /// إنشاء إجراء التأخير
  factory AutomationAction.delay({
    required String id,
    required int seconds,
  }) {
    return AutomationAction(
      id: id,
      type: ActionType.delay,
      config: {
        'seconds': seconds,
      },
    );
  }
}

/// نموذج قاعدة الأتمتة المتقدمة
class AdvancedAutomationRule {
  final String id;
  final String name;
  final String description;
  final List<AutomationTrigger> triggers;
  final List<AutomationCondition> conditions;
  final List<AutomationAction> actions;
  final LogicOperator logicOperator;
  final bool enabled;
  final String priority;
  final DateTime? lastTriggered;
  final int triggerCount;

  AdvancedAutomationRule({
    required this.id,
    required this.name,
    required this.description,
    required this.triggers,
    required this.conditions,
    required this.actions,
    this.logicOperator = LogicOperator.and,
    this.enabled = true,
    this.priority = 'normal',
    this.lastTriggered,
    this.triggerCount = 0,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'description': description,
        'triggers': triggers.map((t) => t.toJson()).toList(),
        'conditions': conditions.map((c) => c.toJson()).toList(),
        'actions': actions.map((a) => a.toJson()).toList(),
        'logic_operator': logicOperator.name.toUpperCase(),
        'enabled': enabled,
        'priority': priority,
        'last_triggered': lastTriggered?.toIso8601String(),
        'trigger_count': triggerCount,
      };

  factory AdvancedAutomationRule.fromJson(Map<String, dynamic> json) {
    return AdvancedAutomationRule(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      triggers: (json['triggers'] as List)
          .map((t) => AutomationTrigger.fromJson(t))
          .toList(),
      conditions: (json['conditions'] as List)
          .map((c) => AutomationCondition.fromJson(c))
          .toList(),
      actions: (json['actions'] as List)
          .map((a) => AutomationAction.fromJson(a))
          .toList(),
      logicOperator:
          json['logic_operator'] == 'OR' ? LogicOperator.or : LogicOperator.and,
      enabled: json['enabled'] ?? true,
      priority: json['priority'] ?? 'normal',
      lastTriggered: json['last_triggered'] != null
          ? DateTime.parse(json['last_triggered'])
          : null,
      triggerCount: json['trigger_count'] ?? 0,
    );
  }

  /// إنشاء نسخة محدثة من القاعدة
  AdvancedAutomationRule copyWith({
    String? id,
    String? name,
    String? description,
    List<AutomationTrigger>? triggers,
    List<AutomationCondition>? conditions,
    List<AutomationAction>? actions,
    LogicOperator? logicOperator,
    bool? enabled,
    String? priority,
    DateTime? lastTriggered,
    int? triggerCount,
  }) {
    return AdvancedAutomationRule(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      triggers: triggers ?? this.triggers,
      conditions: conditions ?? this.conditions,
      actions: actions ?? this.actions,
      logicOperator: logicOperator ?? this.logicOperator,
      enabled: enabled ?? this.enabled,
      priority: priority ?? this.priority,
      lastTriggered: lastTriggered ?? this.lastTriggered,
      triggerCount: triggerCount ?? this.triggerCount,
    );
  }
}
