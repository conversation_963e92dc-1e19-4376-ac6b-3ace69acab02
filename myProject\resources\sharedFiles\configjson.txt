{
    "bridge": {
        "name": "ZainAssistant DC:A6:32:9D:B3:B6",
        "username": "96:7C:5A:4C:5B:87",
        "port": 51722,
        "pin": "123-45-678"
    },
    "platforms": [
        {
            "name": "Config",
            "port": 8581,
            "platform": "config"
        }
    ],
    "accessories": [
    
,
        {
            "accessory": "mqttthing",
            "type": "heaterCooler",
            "name": "مكيف cc_a_50_a_e3_a_56_a_2a_a_f6",
            "logMqtt": true,
            "topics": {
                "setActive": "homebridge/cc_a_50_a_e3_a_56_a_2a_a_f6/setActive",
                "getActive": "homebridge/cc_a_50_a_e3_a_56_a_2a_a_f6/getActive",
                "getCurrentHeaterCoolerState": "homebridge/cc_a_50_a_e3_a_56_a_2a_a_f6/getCurrentHeaterCoolerState",
                "setTargetHeaterCoolerState": "homebridge/cc_a_50_a_e3_a_56_a_2a_a_f6/setTargetHeaterCoolerState",
                "getTargetHeaterCoolerState": "homebridge/cc_a_50_a_e3_a_56_a_2a_a_f6/getTargetHeaterCoolerState",
                "getCurrentTemperature": "homebridge/cc_a_50_a_e3_a_56_a_2a_a_f6/getCurrentTemperature",
                "setCoolingThresholdTemperature": "homebridge/cc_a_50_a_e3_a_56_a_2a_a_f6/setCoolingThresholdTemperature",
                "setHeatingThresholdTemperature": "homebridge/cc_a_50_a_e3_a_56_a_2a_a_f6/setHeatingThresholdTemperature",
                "getCoolingThresholdTemperature": "homebridge/cc_a_50_a_e3_a_56_a_2a_a_f6/getCoolingThresholdTemperature",
                "getHeatingThresholdTemperature": "homebridge/cc_a_50_a_e3_a_56_a_2a_a_f6/getHeatingThresholdTemperature",
                "setSwingMode": "homebridge/cc_a_50_a_e3_a_56_a_2a_a_f6/setSwingMode",
                "getSwingMode": "homebridge/cc_a_50_a_e3_a_56_a_2a_a_f6/getSwingMode",
                "getRotationSpeed": "homebridge/cc_a_50_a_e3_a_56_a_2a_a_f6/getRotationSpeed",
                "setRotationSpeed": {
                    "topic": "homebridge/cc_a_50_a_e3_a_56_a_2a_a_f6/setRotationSpeed",
                    "apply": "return [ '0', '1', '2', '3', '4' ][ Math.floor( message / 25 ) ];"
                }
            },
            "minTemperature": 16,
            "maxTemperature": 32
        },
         {
            "accessory": "mqttthing",
            "type": "television",
            "name": "50_a_2_a_91_a_d5_a_e4_a_69 تلفزيون",
            "topics": {
                "setActive": "homebridge/50_a_2_a_91_a_d5_a_e4_a_69/setActive",
                "getActive": "homebridge/50_a_2_a_91_a_d5_a_e4_a_69/getActive",
                "setActiveInput": "homebridge/50_a_2_a_91_a_d5_a_e4_a_69/setActive",
                "getActiveInput": "homebridge/50_a_2_a_91_a_d5_a_e4_a_69/getActive",
                "setRemoteKey": "homebridge/50_a_2_a_91_a_d5_a_e4_a_69/setActive"
            },
            "onValue": "POWER-ON",
            "offValue": "POWER-OFF"
        },
        {
            "accessory": "mqttthing",
            "type": "custom",
            "name": "مفاتيح 24_a_6f_a_28_a_27_a_49_a_28",
            "logMqtt": true,
            "services": [
                {
                    "type": "switch",
                    "name": "مفتاح v1",
                    "topics": {
                        "getOn": "homebridge/24_a_6f_a_28_a_27_a_49_a_28::v1/getActive",
                        "setOn": "homebridge/24_a_6f_a_28_a_27_a_49_a_28::v1/setActive"
                    }
                },

                {
                    "type": "lightbulb-OnOff",
                    "name": "اضائه v2",
                    "topics": {
                        "getOn": "homebridge/24_a_6f_a_28_a_27_a_49_a_28::v2/getActive",
                        "setOn": "homebridge/24_a_6f_a_28_a_27_a_49_a_28::v2/setActive"
                    }
                },

                {
                    "type": "fan",
                    "name": "مروحه v3",
                    "topics": {
                        "getOn": "homebridge/24_a_6f_a_28_a_27_a_49_a_28::v3/getActive",
                        "setOn": "homebridge/24_a_6f_a_28_a_27_a_49_a_28::v3/setActive"
                    }
                },

                {
                    "type": "switch",
                    "name": "مفتاح v4",
                    "topics": {
                        "getOn": "homebridge/24_a_6f_a_28_a_27_a_49_a_28::v4/getActive",
                        "setOn": "homebridge/24_a_6f_a_28_a_27_a_49_a_28::v4/setActive"
                    }
                }
            ]
        }
    ]
}