import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/modules/local/weather_service.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/controller/controller.dart';
import 'weather_details_page.dart';

/// ويدجت عرض معلومات الطقس
class WeatherWidget extends StatelessWidget {
  final bool isCompact;
  final VoidCallback? onTap;

  const WeatherWidget({
    super.key,
    this.isCompact = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final HomeController controller = Get.find();
    final WeatherService weatherService = Get.put(WeatherService());

    return Obx(() {
      final weather = weatherService.currentWeather.value;
      final isLoading = weatherService.isLoading.value;
      final error = weatherService.errorMessage.value;

      return GetBuilder<HomeController>(
        builder: (controller) => GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.only(left: controller.sizedWidth * 0.03),
            height: controller.sized * 0.035,
            decoration: BoxDecoration(
              color: AppColors.surface.withOpacity(0.75),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                  color: AppColors.border.withOpacity(0.1), width: 1),
            ),
            child: isLoading
                ? _buildLoadingWidget(controller)
                : error.isNotEmpty
                    ? _buildErrorWidget(controller, error)
                    : weather != null
                        ? isCompact
                            ? _buildCompactWeatherWidget(
                                controller, weather, weatherService)
                            : _buildFullWeatherWidget(
                                controller, weather, weatherService)
                        : _buildNoDataWidget(controller),
          ),
        ),
      );
    });
  }

  /// ويدجت التحميل
  Widget _buildLoadingWidget(HomeController controller) {
    return SizedBox(
      height: controller.sizedHight * 0.08,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: controller.sized * 0.02,
            height: controller.sized * 0.02,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                AppColors.primaryColor.withOpacity(0.7),
              ),
            ),
          ),
          SizedBox(width: controller.sizedWidth * 0.03),
          Text(
            'جاري تحديث بيانات الطقس...',
            style: TextStyle(
              color: AppColors.textColor.withOpacity(0.7),
              fontSize: controller.sized * 0.01,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// ويدجت الخطأ
  Widget _buildErrorWidget(HomeController controller, String error) {
    return SizedBox(
      height: controller.sized * 0.03,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.errorColor.withOpacity(0.7),
            size: controller.sized * 0.01,
          ),
          Expanded(
            child: Text(
              error,
              style: TextStyle(
                color: AppColors.errorColor.withOpacity(0.8),
                fontSize: controller.sized * 0.009,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// ويدجت عدم وجود بيانات
  Widget _buildNoDataWidget(HomeController controller) {
    return SizedBox(
      height: controller.sizedHight * 0.08,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.cloud_off,
            color: AppColors.textColor.withOpacity(0.5),
            size: controller.sized * 0.025,
          ),
          SizedBox(height: controller.sizedHight * 0.01),
          Text(
            'لا توجد بيانات طقس متاحة',
            style: TextStyle(
              color: AppColors.textColor.withOpacity(0.6),
              fontSize: controller.sized * 0.01,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// ويدجت الطقس المضغوط
  Widget _buildCompactWeatherWidget(
    HomeController controller,
    WeatherData weather,
    WeatherService weatherService,
  ) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // أيقونة الطقس
          Container(
            width: controller.sized * 0.03,
            child: Center(
              child: Text(
                weatherService.getWeatherIcon(weather.condition),
                style: TextStyle(fontSize: controller.sized * 0.018),
              ),
            ),
          ),

          // معلومات الطقس
          Text(
            '${weather.temperature?.toStringAsFixed(1) ?? '--'}° \n ${weatherService.getWeatherDescriptionArabic(weather.condition)}',
            style: TextStyle(
              height: 0.9,
              color: AppColors.textColor2,
              fontSize: controller.sized * 0.012,
            ),
          ),
        ],
      ),
    );
  }

  /// ويدجت الطقس الكامل
  Widget _buildFullWeatherWidget(
    HomeController controller,
    WeatherData weather,
    WeatherService weatherService,
  ) {
    return Column(
      children: [
        // الصف الأول: درجة الحرارة والحالة
        Row(
          children: [
            // أيقونة الطقس الكبيرة
            Container(
              width: controller.sized * 0.06,
              height: controller.sized * 0.06,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primaryColor.withOpacity(0.2),
                    AppColors.primaryColor.withOpacity(0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Center(
                child: Text(
                  weatherService.getWeatherIcon(weather.condition),
                  style: TextStyle(fontSize: controller.sized * 0.03),
                ),
              ),
            ),

            SizedBox(width: controller.sizedWidth * 0.04),

            // درجة الحرارة والوصف
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${weather.temperature?.toStringAsFixed(1) ?? '--'}°C',
                    style: TextStyle(
                      color: AppColors.textColor.withOpacity(0.9),
                      fontSize: controller.sized * 0.02,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    weatherService
                        .getWeatherDescriptionArabic(weather.condition),
                    style: TextStyle(
                      color: AppColors.textColor.withOpacity(0.7),
                      fontSize: controller.sized * 0.012,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (weather.description.isNotEmpty)
                    Text(
                      weather.description,
                      style: TextStyle(
                        color: AppColors.textColor.withOpacity(0.6),
                        fontSize: controller.sized * 0.009,
                      ),
                    ),
                ],
              ),
            ),

            // زر التحديث
            IconButton(
              onPressed: () => weatherService.requestWeatherUpdate(),
              icon: Icon(
                Icons.refresh,
                color: AppColors.primaryColor.withOpacity(0.7),
                size: controller.sized * 0.02,
              ),
            ),
          ],
        ),

        SizedBox(height: controller.sizedHight * 0.015),

        // الصف الثاني: تفاصيل إضافية
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            if (weather.humidity != null)
              _buildWeatherDetail(
                controller,
                Icons.water_drop,
                'الرطوبة',
                '${weather.humidity}%',
              ),
            if (weather.windSpeed != null)
              _buildWeatherDetail(
                controller,
                Icons.air,
                'الرياح',
                '${weather.windSpeed?.toStringAsFixed(1)} م/ث',
              ),
            if (weather.pressure != null)
              _buildWeatherDetail(
                controller,
                Icons.compress,
                'الضغط',
                '${weather.pressure?.toStringAsFixed(0)} هكتوباسكال',
              ),
          ],
        ),

        // وقت آخر تحديث
        if (weather.lastUpdated != null)
          Padding(
            padding: EdgeInsets.only(top: controller.sizedHight * 0.01),
            child: Text(
              'آخر تحديث: ${_formatTime(weather.lastUpdated)}',
              style: TextStyle(
                color: AppColors.textColor.withOpacity(0.5),
                fontSize: controller.sized * 0.008,
              ),
            ),
          ),
      ],
    );
  }

  /// بناء تفصيل الطقس
  Widget _buildWeatherDetail(
    HomeController controller,
    IconData icon,
    String label,
    String value,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppColors.primaryColor.withOpacity(0.7),
          size: controller.sized * 0.015,
        ),
        SizedBox(height: controller.sizedHight * 0.005),
        Text(
          label,
          style: TextStyle(
            color: AppColors.textColor.withOpacity(0.6),
            fontSize: controller.sized * 0.008,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: AppColors.textColor.withOpacity(0.8),
            fontSize: controller.sized * 0.009,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// تنسيق الوقت
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
}
