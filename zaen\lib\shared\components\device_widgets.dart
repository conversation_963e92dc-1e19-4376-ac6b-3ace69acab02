import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/settings/settings.dart';
import 'text_icon_widgets.dart';
import 'container_widgets.dart';
import 'form_widgets.dart';

Widget pageSetting({
  String? roomN,
  bool connect = true,
  String? privName,
  required String type,
  bool isTv = false,
  bool isSw = false,
  String? id,
  required Function() del,
  required Function() Dfavorite,
  required Function() editRoom,
  required Function(String?) editNames,
  Function(bool?, String?)? editPrivName,
  Function(bool?, String?, int?)? editPrivNameSw,
  Function(String?)? tapOn_star,
}) {
  TextEditingController? editPriv;
  bool privN = false;
  if (isSw) {
    editPriv = TextEditingController(
        text: privName!.split('_')[0] != 'x' ? privName.split('_')[0] : 'X');
  } else {
    editPriv = TextEditingController(
      text: privName != 'x' ? privName : 'X',
    );
  }

  return SingleChildScrollView(
    child: Container(
      padding: EdgeInsets.symmetric(horizontal: controller.sizedWidth * 0.03),
      child: Column(
        children: [
          connect == false
              ? Column(
                  children: [
                    Row(mainAxisSize: MainAxisSize.min, children: [
                      txtStyle(
                        txt: 'غير متصل',
                        color: AppColors.errorColor,
                        align: TextAlign.right,
                      ),
                      Expanded(
                        child: Container(
                          alignment: Alignment.bottomRight,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Container(
                                  width: controller.sizedWidth * 0.44,
                                  padding: EdgeInsets.only(
                                      right: controller.sized * 0.01),
                                  child: FittedBox(
                                    alignment: Alignment.centerRight,
                                    fit: BoxFit.scaleDown,
                                    child: txtStyle(
                                        align: TextAlign.right,
                                        txt: privName! != 'x'
                                            ? privName
                                            : 'لا يوجد اسم'),
                                  )),
                              Container(
                                  padding: EdgeInsets.only(
                                      left: controller.sizedWidth * 0.01),
                                  decoration: BoxDecoration(
                                      border: Border(
                                          left: BorderSide(
                                              color: AppColors.textColor
                                                  .withOpacity(0.25),
                                              width: 1.5))),
                                  child: txtStyle(
                                    align: TextAlign.right,
                                    txt: type,
                                    color: AppColors.textColor3,
                                  )),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                      iconStyle(
                        icon: type == 'مكيف هواء'
                            ? Icons.ac_unit_rounded
                            : type == 'تلفاز'
                                ? Icons.tv_rounded
                                : Icons.power_outlined,
                        color: AppColors.warningColor,
                      ),
                    ]),
                    SizedBox(
                      height: controller.sizedHight * 0.05,
                    ),
                  ],
                )
              : Container(),
          Container(
            width: controller.sizedWidth * 0.85,
            child: TextFormField(
              controller: editPriv,
              maxLength: 16,
              showCursor: true,
              cursorColor: AppColors.primary,
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.right,
              style: TextStyle(
                color: AppColors.textPrimary,
                fontSize: controller.sized * 0.015,
                fontWeight: FontWeight.w500,
              ),
              onChanged: (i) {
                privN = true;
              },
              onEditingComplete: () {
                FocusManager.instance.primaryFocus?.unfocus();
                if (isSw) {
                  if (editPriv!.text == '' ||
                      editPriv.text == null ||
                      editPriv.text == 'X' ||
                      editPriv.text == 'x') {
                    editPriv.text = privName!.split('_')[0] != 'x'
                        ? privName.split('_')[0]
                        : 'X';
                    privN = false;
                  } else if (editPriv.text != privName) {
                    for (var i = 0; i < editPriv.text.length; i++) {
                      if (arabic.contains(editPriv.text[i]) ||
                          editPriv.text[i].isNumericOnly) {
                        privN = true;
                      } else {
                        editPriv.text = privName!.split('_')[0] != 'x'
                            ? privName.split('_')[0]
                            : 'X';
                        privN = false;
                        break;
                      }
                    }
                    if (privN) {
                      editPrivNameSw!(privN, editPriv.text, 0);
                      privN = false;
                    }
                  }
                } else {
                  if (editPriv!.text == '' ||
                      editPriv.text == null ||
                      editPriv.text == 'X' ||
                      editPriv.text == 'x') {
                    editPriv.text = (privName != 'x' ? privName : 'X')!;
                    privN = false;
                  } else if (editPriv.text != privName) {
                    for (var i = 0; i < editPriv.text.length; i++) {
                      if (arabic.contains(editPriv.text[i]) ||
                          editPriv.text[i].isNumericOnly) {
                        privN = true;
                      } else {
                        editPriv.text = (privName != 'x' ? privName : 'X')!;
                        privN = false;
                        break;
                      }
                    }
                    if (privN) {
                      editPrivName!(privN, editPriv.text);
                      privN = false;
                    }
                  }
                }
              },
              decoration: InputDecoration(
                hintText: 'اسم الملحق الخاص',
                hintStyle: TextStyle(
                  color: AppColors.textHint,
                  fontSize: controller.sized * 0.014,
                  fontWeight: FontWeight.normal,
                ),
                filled: true,
                fillColor: AppColors.surface,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: controller.sizedWidth * 0.04,
                  vertical: controller.sizedHight * 0.015,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: AppColors.border,
                    width: 1.0,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: AppColors.border,
                    width: 1.0,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: AppColors.primary,
                    width: 2.0,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(
            height: controller.sizedHight * 0.015,
          ),
          isTv
              ? Column(
                  children: [
                    containerPageOption(
                      content: Column(
                        children: [
                          MaterialButton(
                            padding: EdgeInsets.zero,
                            onPressed: () {
                              tapOn_star!('show');
                            },
                            child:
                                Row(mainAxisSize: MainAxisSize.min, children: [
                              iconStyle(
                                icon: Icons.menu_open_rounded,
                              ),
                              Expanded(child: SizedBox(width: double.infinity)),
                              txtStyle(
                                align: TextAlign.right,
                                txt: 'اسماء القنوات المفضله',
                              ),
                            ]),
                          ),
                          Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                IconButton(
                                  onPressed: () {
                                    tapOn_star!('edit');
                                  },
                                  icon: iconStyle(
                                      icon: Icons.edit_outlined,
                                      color: Colors.cyan),
                                ),
                                SizedBox(
                                  width: controller.sizedWidth * 0.075,
                                ),
                                IconButton(
                                  onPressed: () {
                                    tapOn_star!('del');
                                  },
                                  icon: iconStyle(
                                      icon: Icons.delete_sweep_rounded,
                                      color: AppColors.errorColor),
                                ),
                                SizedBox(
                                  width: controller.sizedWidth * 0.075,
                                ),
                                IconButton(
                                  onPressed: () {
                                    tapOn_star!('add');
                                  },
                                  icon: iconStyle(
                                      icon: Icons.add_rounded,
                                      color: AppColors.primaryColor),
                                ),
                              ]),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: controller.sizedHight * 0.015,
                    ),
                  ],
                )
              : Container(),
          isSw == false
              ? containerPageOption(
                  content: Column(
                    children: [
                      MaterialButton(
                        padding: EdgeInsets.zero,
                        onPressed: () {
                          editNames('show');
                        },
                        child: Row(mainAxisSize: MainAxisSize.min, children: [
                          iconStyle(
                            icon: Icons.menu_open_rounded,
                          ),
                          Expanded(child: SizedBox(width: double.infinity)),
                          txtStyle(
                            align: TextAlign.right,
                            txt: 'اسماء و صفات الجهاز العامة',
                          ),
                        ]),
                      ),
                      Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            IconButton(
                              onPressed: () {
                                editNames('edit');
                              },
                              icon: iconStyle(
                                  icon: Icons.edit_outlined,
                                  color: Colors.cyan),
                            ),
                            SizedBox(
                              width: controller.sizedWidth * 0.075,
                            ),
                            IconButton(
                              onPressed: () {
                                editNames('del');
                              },
                              icon: iconStyle(
                                  icon: Icons.delete_sweep_rounded,
                                  color: AppColors.errorColor),
                            ),
                            SizedBox(
                              width: controller.sizedWidth * 0.075,
                            ),
                            IconButton(
                              onPressed: () {
                                editNames('add');
                              },
                              icon: iconStyle(
                                  icon: Icons.add_rounded,
                                  color: AppColors.primaryColor),
                            ),
                          ]),
                    ],
                  ),
                )
              : Container(),
          SizedBox(
            height: controller.sizedHight * 0.015,
          ),
          containerPageOption(
            content: MaterialButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                roomN = editRoom();
                print(roomN);
              },
              child: Row(mainAxisSize: MainAxisSize.min, children: [
                iconStyle(
                  icon: Icons.arrow_drop_down,
                ),
                Expanded(child: SizedBox(width: double.infinity)),
                Row(
                  children: [
                    txtStyle(
                      align: TextAlign.right,
                      txt: '$roomN',
                    ),
                    txtStyle(
                        align: TextAlign.right,
                        txt: 'الغرفة : ',
                        color: AppColors.textColor3),
                  ],
                ),
              ]),
            ),
          ),
          SizedBox(
            height: controller.sizedHight * 0.015,
          ),
          containerPageOption(
            content: MaterialButton(
                padding: EdgeInsets.zero,
                onPressed: Dfavorite,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    iconStyle(
                        icon: Icons.favorite,
                        size: controller.sized * 0.02,
                        color: controller.favorite.contains(id)
                            ? AppColors.errorColor
                            : AppColors.textColor3),
                    Expanded(child: SizedBox(width: double.infinity)),
                    txtStyle(
                        txt: 'الملحقات المفضله', color: AppColors.textColor2),
                  ],
                )),
          ),
          SizedBox(
            height: controller.sizedHight * 0.015,
          ),
          containerPageOption(
            content: MaterialButton(
                padding: EdgeInsets.zero,
                onPressed: del,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    txtStyle(txt: 'حذف الملحق', color: AppColors.errorColor),
                  ],
                )),
          )
        ],
      ),
    ),
  );
}
