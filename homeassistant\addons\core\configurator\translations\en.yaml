---
configuration:
  dirsfirst:
    name: Directories First
    description: >-
      This option allows you to list directories before files in the file
      browser tree.
  enforce_basepath:
    name: Enforce Basepath
    description: >-
      If set to `true`, access is limited to files within the `/config`
      directory.
  git:
    name: Git
    description: >-
      If set to `true`, add-on will initialize git for directories which support
      it.
  ignore_pattern:
    name: Ignore Pattern
    description: >-
      This option allows you to hide files and folders from the file browser
      tree.
  ssh_keys:
    name: SSH Keys
    description: >-
      A list of filenames containing SSH private keys. These can be used to
      allow for access to remote git repositories.
