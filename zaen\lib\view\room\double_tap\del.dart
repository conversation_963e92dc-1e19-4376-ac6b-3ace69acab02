import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart';
import 'package:get/get.dart';
import 'package:sqflite/sqflite.dart';
import 'package:zaen/modules/local/ip.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/permission_widgets.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/controller/controller.dart';

del(
    {required var context,
    required var i,
    required Database appDB,
    required var conn}) {
  final HomeController controller = Get.find();
  AwesomeDialog(
    context: context,
    dialogType: DialogType.error,
    headerAnimationLoop: true,
    animType: AnimType.topSlide,
    dialogBackgroundColor: AppColors.surfaceElevated,
    titleTextStyle: TextStyle(
        color: AppColors.textHint,
        fontWeight: FontWeight.bold,
        fontSize: controller.sized * 0.015),
    descTextStyle: TextStyle(
        color: AppColors.textSecondary,
        fontWeight: FontWeight.bold,
        fontSize: controller.sized * 0.017),
    // showCloseIcon: true,
    // closeIcon: const Icon(Icons.close_fullscreen_outlined),
    title: 'هل تريد حذف الجهاز التالي',
    desc: i['device'] == 'SWITCH' ? i['priv'].split('_')[0] : i['priv'],
    // btnCancelText:
    //     'لا اريد',
    btnOkColor: AppColors.primary,
    buttonsTextStyle: TextStyle(
        color: AppColors.textColor,
        fontWeight: FontWeight.bold,
        fontSize: controller.sized * 0.02),
    btnOkText: 'موافق',
    btnOkOnPress: () async {
      final builder = MqttClientPayloadBuilder();

      builder.addString('remove');

      await client.publishMessage(
          i['id'], MqttQos.atLeastOnce, builder.payload!);
      10.seconds.delay();

      if (controller.favorite.contains(i['id'])) {
        print(controller.favorite);
        await appDB.rawQuery('DELETE FROM favorite WHERE id = "${i['id']}"');
        await controller.favorite.remove(i['id']);
        print(11111);
        print(controller.favorite);
      }
      // for (Map r in controller.routines) {
      //   try {
      //     // تحويل routine إلى Map باستخدام jsonDecode
      //     Map routineData = jsonDecode(r['routine']);

      //     bool shouldUpdateRoutine = false;

      //     // نسخ المفاتيح لتجنب تعديل المجموعة أثناء التكرار
      //     List<String> roomKeys = List.from(routineData.keys);

      //     for (String room in roomKeys) {
      //       // التحقق من أن القيمة هي Map
      //       if (routineData[room] is Map) {
      //         Map devices = routineData[room];

      //         if (devices.containsKey(i['id'])) {
      //           // إزالة الجهاز من الغرفة
      //           devices.remove(i['id']);
      //           shouldUpdateRoutine = true;

      //           // إذا كانت الغرفة فارغة، احذفها
      //           if (devices.isEmpty) {
      //             routineData.remove(room);
      //           }
      //         }
      //       } else {
      //         print('Warning: Non-Map data in room $room');
      //         // يمكنك إضافة معالجة خاصة هنا
      //       }
      //     }

      //     // إذا كان الروتين فارغًا، احذفه من قاعدة البيانات
      //     if (routineData.isEmpty) {
      //       await appDB
      //           .rawDelete('DELETE FROM routine WHERE id = ?', [r['id']]);
      //     }
      //     // وإلا إذا تم تعديل الروتين، قم بتحديثه
      //     else if (shouldUpdateRoutine) {
      //       await appDB.rawUpdate('UPDATE routine SET routine = ? WHERE id = ?',
      //           [json.encode(routineData), r['id']]);
      //     }
      //   } catch (e) {
      //     print('Error processing routine: $e');
      //     // معالجة الخطأ حسب الحاجة
      //   }
      // }
      // التحقق من صلاحية حذف الأجهزة
      if (!controller.canManageData()) {
        showNoPermissionDialog(customMessage: 'ليس لديك صلاحية لحذف الأجهزة');
        return;
      }

      await appDB.rawQuery('DELETE FROM devices WHERE id = "${i['id']}"');

      await conn.query('DELETE FROM Devices WHERE id = "${i['id']}"');
      if (i['device'] == 'SWITCH' || i['device'] == 'TV') {
        await conn.query('DROP TABLE ${i['id']}_${i['device']}');
      }
      if (i['device'] == 'AC' || i['device'] == 'TV') {
        await conn.query('ALTER TABLE NDevice DROP ${i['id']}');
      }
      builder.clear();
      builder.addString('1');
      await client.publishMessage(
          'edit', MqttQos.atLeastOnce, builder.payload!);

      Get.back();
    },
    // btnCancelOnPress:
    //     () {},
  ).show();
}

Dfavorite(
    {required context,
    required String device,
    required var appDB,
    required bool state}) async {
  if (controller.favorite.contains(device)) {
    print(controller.favorite);
    await appDB.rawQuery('DELETE FROM favorite WHERE id = ?', [device]);
    await controller.favorite.remove(device);
    controller.update();
    print(11111);
    print(controller.favorite);

    print(device);
  } else {
    print(controller.favorite);
    await appDB.rawQuery('insert into favorite(id) values(?)', [device]);
    controller.update();
    print(controller.favorite);

    print(11111);
  }
  final builder = MqttClientPayloadBuilder();

  builder.addString('re');

  builder.addString('1');
  client.publishMessage('edit', MqttQos.atLeastOnce, builder.payload!);
}
