---
version: 6.5.1
slug: mosquitto
name: Mosquitto broker
description: An Open Source MQTT broker
url: https://github.com/home-assistant/addons/tree/master/mosquitto
codenotary: <EMAIL>
arch:
  - armhf
  - armv7
  - aarch64
  - amd64
  - i386
auth_api: true
discovery:
  - mqtt
image: homeassistant/{arch}-addon-mosquitto
map:
  - ssl
  - share
options:
  logins: []
  require_certificate: false
  certfile: fullchain.pem
  keyfile: privkey.pem
  customize:
    active: false
    folder: mosquitto
ports:
  1883/tcp: 1883
  1884/tcp: 1884
  8883/tcp: 8883
  8884/tcp: 8884
schema:
  logins:
    - username: str
      password: password
      password_pre_hashed: "bool?"
  require_certificate: bool
  certfile: str
  cafile: str?
  keyfile: str
  customize:
    active: bool
    folder: str
  debug: bool?
services:
  - mqtt:provide
startup: system
watchdog: tcp://[HOST]:1883
init: false
