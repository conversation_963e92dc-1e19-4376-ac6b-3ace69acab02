# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 92ms
create_cxx_tasks completed in 95ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 17ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 59ms
    create-module-model 21ms
    create-ARMEABI_V7A-model 78ms
    create-ARM64_V8A-model 13ms
    create-X86-model 14ms
    create-X86_64-model 81ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 37ms
    create-X86-model 41ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 479ms
  [gap of 11ms]
create_cxx_tasks completed in 490ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 67ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 11ms
    [gap of 43ms]
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 170ms
create_cxx_tasks completed in 177ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 30ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 26ms
    create-X86-model 20ms
    create-X86_64-model 23ms
    create-module-model 32ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 22ms
    create-ARM64_V8A-model 30ms
    create-X86-model 18ms
    create-X86_64-model 20ms
    create-module-model 14ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 13ms
    create-X86-model 36ms
    create-X86_64-model 27ms
  create-initial-cxx-model completed in 420ms
  [gap of 29ms]
create_cxx_tasks completed in 450ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 11ms]
    create-ARMEABI_V7A-model 16ms
    create-X86-model 18ms
    create-module-model 15ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 24ms
    create-X86_64-model 19ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 22ms
    create-X86-model 12ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 210ms
create_cxx_tasks completed in 219ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    [gap of 11ms]
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 12ms
    create-X86-model 11ms
    create-X86_64-model 10ms
    create-module-model 12ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 14ms
    create-X86-model 10ms
    create-X86_64-model 13ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    [gap of 10ms]
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 220ms
  [gap of 10ms]
create_cxx_tasks completed in 231ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 112ms
create_cxx_tasks completed in 117ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 16ms
    [gap of 11ms]
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 15ms
    create-X86-model 15ms
    create-X86_64-model 13ms
    create-module-model 13ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 23ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 15ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 12ms
    create-X86-model 13ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 262ms
  [gap of 10ms]
create_cxx_tasks completed in 272ms

