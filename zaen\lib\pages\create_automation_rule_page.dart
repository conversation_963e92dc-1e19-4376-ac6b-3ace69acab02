import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/services/advanced_automation_service.dart';
import 'package:zaen/models/automation_models.dart';
import 'package:uuid/uuid.dart';

class CreateAutomationRulePage extends StatefulWidget {
  final AdvancedAutomationRule? ruleToEdit;

  const CreateAutomationRulePage({super.key, this.ruleToEdit});

  @override
  State<CreateAutomationRulePage> createState() =>
      _CreateAutomationRulePageState();
}

class _CreateAutomationRulePageState extends State<CreateAutomationRulePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  List<AutomationTrigger> _triggers = [];
  List<AutomationCondition> _conditions = [];
  List<AutomationAction> _actions = [];
  LogicOperator _logicOperator = LogicOperator.and;
  String _priority = 'normal';
  bool _enabled = true;

  @override
  void initState() {
    super.initState();
    _loadRuleForEditing();
  }

  /// تحميل بيانات القاعدة للتعديل
  void _loadRuleForEditing() {
    if (widget.ruleToEdit != null) {
      final rule = widget.ruleToEdit!;
      _nameController.text = rule.name;
      _descriptionController.text = rule.description;
      _triggers = List.from(rule.triggers);
      _conditions = List.from(rule.conditions);
      _actions = List.from(rule.actions);
      _logicOperator = rule.logicOperator;
      _priority = rule.priority;
      _enabled = rule.enabled;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0E27),
      appBar: AppBar(
        title: const Text('إنشاء قاعدة أتمتة جديدة',
            style: TextStyle(color: Colors.white)),
        backgroundColor: const Color(0xFF16213E),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          TextButton(
            onPressed: _saveRule,
            child: const Text('حفظ',
                style: TextStyle(color: Colors.blue, fontSize: 16)),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: 20),
              _buildTriggersSection(),
              const SizedBox(height: 20),
              _buildConditionsSection(),
              const SizedBox(height: 20),
              _buildActionsSection(),
              const SizedBox(height: 20),
              _buildSettingsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      color: const Color(0xFF16213E),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('المعلومات الأساسية',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                labelText: 'اسم القاعدة',
                labelStyle: TextStyle(color: Colors.grey),
                border: OutlineInputBorder(),
                enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey)),
                focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.blue)),
              ),
              validator: (value) =>
                  value?.isEmpty == true ? 'يرجى إدخال اسم القاعدة' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              style: const TextStyle(color: Colors.white),
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'وصف القاعدة',
                labelStyle: TextStyle(color: Colors.grey),
                border: OutlineInputBorder(),
                enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey)),
                focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.blue)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTriggersSection() {
    return Card(
      color: const Color(0xFF16213E),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('المحفزات',
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold)),
                IconButton(
                  onPressed: _addTrigger,
                  icon: const Icon(Icons.add, color: Colors.blue),
                ),
              ],
            ),
            if (_triggers.isEmpty)
              const Padding(
                padding: EdgeInsets.all(16),
                child: Text('لا توجد محفزات. اضغط + لإضافة محفز',
                    style: TextStyle(color: Colors.grey)),
              ),
            ..._triggers.asMap().entries.map((entry) {
              int index = entry.key;
              AutomationTrigger trigger = entry.value;
              return _buildTriggerItem(trigger, index);
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildConditionsSection() {
    return Card(
      color: const Color(0xFF16213E),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('الشروط',
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold)),
                IconButton(
                  onPressed: _addCondition,
                  icon: const Icon(Icons.add, color: Colors.blue),
                ),
              ],
            ),
            if (_conditions.isNotEmpty) ...[
              Row(
                children: [
                  const Text('منطق الشروط: ',
                      style: TextStyle(color: Colors.grey)),
                  DropdownButton<LogicOperator>(
                    value: _logicOperator,
                    dropdownColor: const Color(0xFF16213E),
                    style: const TextStyle(color: Colors.white),
                    items: const [
                      DropdownMenuItem(
                          value: LogicOperator.and,
                          child: Text('AND (جميع الشروط)')),
                      DropdownMenuItem(
                          value: LogicOperator.or, child: Text('OR (أي شرط)')),
                    ],
                    onChanged: (value) =>
                        setState(() => _logicOperator = value!),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],
            if (_conditions.isEmpty)
              const Padding(
                padding: EdgeInsets.all(16),
                child: Text('لا توجد شروط. اضغط + لإضافة شرط',
                    style: TextStyle(color: Colors.grey)),
              ),
            ..._conditions.asMap().entries.map((entry) {
              int index = entry.key;
              AutomationCondition condition = entry.value;
              return _buildConditionItem(condition, index);
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsSection() {
    return Card(
      color: const Color(0xFF16213E),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('الإجراءات',
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold)),
                IconButton(
                  onPressed: _addAction,
                  icon: const Icon(Icons.add, color: Colors.blue),
                ),
              ],
            ),
            if (_actions.isEmpty)
              const Padding(
                padding: EdgeInsets.all(16),
                child: Text('لا توجد إجراءات. اضغط + لإضافة إجراء',
                    style: TextStyle(color: Colors.grey)),
              ),
            ..._actions.asMap().entries.map((entry) {
              int index = entry.key;
              AutomationAction action = entry.value;
              return _buildActionItem(action, index);
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsSection() {
    return Card(
      color: const Color(0xFF16213E),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('الإعدادات',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('الأولوية: ', style: TextStyle(color: Colors.grey)),
                DropdownButton<String>(
                  value: _priority,
                  dropdownColor: const Color(0xFF16213E),
                  style: const TextStyle(color: Colors.white),
                  items: const [
                    DropdownMenuItem(value: "low", child: Text('منخفضة')),
                    DropdownMenuItem(value: "normal", child: Text('عادية')),
                    DropdownMenuItem(value: "high", child: Text('عالية')),
                    DropdownMenuItem(value: "urgent", child: Text('عاجلة')),
                  ],
                  onChanged: (value) => setState(() => _priority = value!),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('مفعلة: ', style: TextStyle(color: Colors.grey)),
                Switch(
                  value: _enabled,
                  onChanged: (value) => setState(() => _enabled = value),
                  activeColor: Colors.blue,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTriggerItem(AutomationTrigger trigger, int index) {
    return Card(
      color: const Color(0xFF0A0E27),
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(_getTriggerIcon(trigger.type), color: Colors.blue),
        title: Text(_getTriggerTitle(trigger),
            style: const TextStyle(color: Colors.white)),
        subtitle: Text(_getTriggerSubtitle(trigger),
            style: const TextStyle(color: Colors.grey)),
        trailing: IconButton(
          onPressed: () => setState(() => _triggers.removeAt(index)),
          icon: const Icon(Icons.delete, color: Colors.red),
        ),
      ),
    );
  }

  Widget _buildConditionItem(AutomationCondition condition, int index) {
    return Card(
      color: const Color(0xFF0A0E27),
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(_getConditionIcon(condition.type), color: Colors.orange),
        title: Text(_getConditionTitle(condition),
            style: const TextStyle(color: Colors.white)),
        subtitle: Text(_getConditionSubtitle(condition),
            style: const TextStyle(color: Colors.grey)),
        trailing: IconButton(
          onPressed: () => setState(() => _conditions.removeAt(index)),
          icon: const Icon(Icons.delete, color: Colors.red),
        ),
      ),
    );
  }

  Widget _buildActionItem(AutomationAction action, int index) {
    return Card(
      color: const Color(0xFF0A0E27),
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(_getActionIcon(action.type), color: Colors.green),
        title: Text(_getActionTitle(action),
            style: const TextStyle(color: Colors.white)),
        subtitle: Text(_getActionSubtitle(action),
            style: const TextStyle(color: Colors.grey)),
        trailing: IconButton(
          onPressed: () => setState(() => _actions.removeAt(index)),
          icon: const Icon(Icons.delete, color: Colors.red),
        ),
      ),
    );
  }

  // دوال المساعدة للمحفزات
  IconData _getTriggerIcon(TriggerType type) {
    switch (type) {
      case TriggerType.time:
        return Icons.access_time;
      case TriggerType.weather:
        return Icons.cloud;
      case TriggerType.sun:
        return Icons.wb_sunny;
      case TriggerType.entityState:
        return Icons.home;
      default:
        return Icons.flash_on;
    }
  }

  String _getTriggerTitle(AutomationTrigger trigger) {
    switch (trigger.type) {
      case TriggerType.time:
        return 'محفز الوقت';
      case TriggerType.weather:
        return 'محفز الطقس';
      case TriggerType.sun:
        return 'محفز الشمس';
      case TriggerType.entityState:
        return 'محفز الكيان';
      default:
        return 'محفز غير معروف';
    }
  }

  String _getTriggerSubtitle(AutomationTrigger trigger) {
    switch (trigger.type) {
      case TriggerType.time:
        final time = trigger.config['time'] ?? 'غير محدد';
        final days = trigger.config['days'] as List<dynamic>?;
        String daysText = '';
        if (days != null && days.isNotEmpty) {
          if (days.length == 7) {
            daysText = ' (يومياً)';
          } else {
            final arabicDays = days.map((day) {
              switch (day) {
                case 'monday':
                  return 'الاثنين';
                case 'tuesday':
                  return 'الثلاثاء';
                case 'wednesday':
                  return 'الأربعاء';
                case 'thursday':
                  return 'الخميس';
                case 'friday':
                  return 'الجمعة';
                case 'saturday':
                  return 'السبت';
                case 'sunday':
                  return 'الأحد';
                default:
                  return day.toString();
              }
            }).join(', ');
            daysText = ' ($arabicDays)';
          }
        }
        return 'الوقت: $time$daysText';
      case TriggerType.weather:
        final condition = trigger.config['condition'] ?? 'غير محدد';
        final threshold = trigger.config['threshold'];
        String conditionText = '';
        switch (condition) {
          case 'temperature_above':
            conditionText =
                'درجة الحرارة أعلى من ${threshold?.round() ?? ''}°C';
            break;
          case 'temperature_below':
            conditionText = 'درجة الحرارة أقل من ${threshold?.round() ?? ''}°C';
            break;
          case 'humidity_above':
            conditionText = 'الرطوبة أعلى من ${threshold?.round() ?? ''}%';
            break;
          case 'humidity_below':
            conditionText = 'الرطوبة أقل من ${threshold?.round() ?? ''}%';
            break;
          case 'rain_start':
            conditionText = 'بداية المطر';
            break;
          case 'rain_stop':
            conditionText = 'توقف المطر';
            break;
          default:
            conditionText = condition;
        }
        return conditionText;
      case TriggerType.sun:
        final event = trigger.config['event'] ?? 'غير محدد';
        final eventText = event == 'sunrise'
            ? 'شروق الشمس'
            : event == 'sunset'
                ? 'غروب الشمس'
                : event;
        return eventText;
      case TriggerType.entityState:
        final entityId = trigger.config['entity_id'] ?? 'غير محدد';
        final state = trigger.config['state'] ?? 'غير محدد';
        String entityText = entityId;
        if (entityId == 'person.zaen') {
          entityText = 'شخص زين';
        } else if (entityId == 'sun.sun') {
          entityText = 'الشمس';
        } else if (entityId == 'weather.home') {
          entityText = 'طقس المنزل';
        }

        String stateText = state;
        if (state == 'home') {
          stateText = 'في المنزل';
        } else if (state == 'away') {
          stateText = 'خارج المنزل';
        } else if (state == 'above_horizon') {
          stateText = 'فوق الأفق';
        } else if (state == 'below_horizon') {
          stateText = 'تحت الأفق';
        }

        return '$entityText: $stateText';
      default:
        return 'تكوين غير معروف';
    }
  }

  // دوال المساعدة للشروط
  IconData _getConditionIcon(ConditionType type) {
    switch (type) {
      case ConditionType.weather:
        return Icons.thermostat;
      case ConditionType.sun:
        return Icons.brightness_6;
      case ConditionType.person:
        return Icons.person;
      case ConditionType.time:
        return Icons.access_time;
      case ConditionType.entity:
        return Icons.home;
      default:
        return Icons.help;
    }
  }

  String _getConditionTitle(AutomationCondition condition) {
    switch (condition.type) {
      case ConditionType.weather:
        return 'شرط الطقس';
      case ConditionType.sun:
        return 'شرط الشمس';
      case ConditionType.person:
        return 'شرط الشخص';
      case ConditionType.time:
        return 'شرط الوقت';
      case ConditionType.entity:
        return 'شرط الكيان';
      default:
        return 'شرط غير معروف';
    }
  }

  String _getConditionSubtitle(AutomationCondition condition) {
    switch (condition.type) {
      case ConditionType.weather:
        final conditionType = condition.config['condition'] ?? 'غير محدد';
        final threshold = condition.config['threshold'];
        String conditionText = '';
        switch (conditionType) {
          case 'temperature_above':
            conditionText =
                'درجة الحرارة أعلى من ${threshold?.round() ?? ''}°C';
            break;
          case 'temperature_below':
            conditionText = 'درجة الحرارة أقل من ${threshold?.round() ?? ''}°C';
            break;
          case 'humidity_above':
            conditionText = 'الرطوبة أعلى من ${threshold?.round() ?? ''}%';
            break;
          case 'humidity_below':
            conditionText = 'الرطوبة أقل من ${threshold?.round() ?? ''}%';
            break;
          default:
            conditionText = conditionType;
        }
        return conditionText;
      case ConditionType.sun:
        final conditionType = condition.config['condition'] ?? 'غير محدد';
        String conditionText = '';
        switch (conditionType) {
          case 'after_sunset':
            conditionText = 'بعد غروب الشمس';
            break;
          case 'after_sunrise':
            conditionText = 'بعد شروق الشمس';
            break;
          case 'before_sunset':
            conditionText = 'قبل غروب الشمس';
            break;
          case 'before_sunrise':
            conditionText = 'قبل شروق الشمس';
            break;
          default:
            conditionText = conditionType;
        }
        return conditionText;
      case ConditionType.person:
        final personId = condition.config['person_id'] ?? 'غير محدد';
        final state = condition.config['state'] ?? 'غير محدد';
        String personText = personId == 'zaen' ? 'زين' : personId;
        String stateText = state == 'home'
            ? 'في المنزل'
            : state == 'away'
                ? 'خارج المنزل'
                : state;
        return '$personText: $stateText';
      case ConditionType.time:
        return 'الوقت: ${condition.config['time'] ?? 'غير محدد'}';
      case ConditionType.entity:
        return 'الكيان: ${condition.config['entity_id'] ?? 'غير محدد'}';
      default:
        return 'تكوين غير معروف';
    }
  }

  // دوال المساعدة للإجراءات
  IconData _getActionIcon(ActionType type) {
    switch (type) {
      case ActionType.deviceControl:
        return Icons.power_settings_new;
      case ActionType.notification:
        return Icons.notifications;
      case ActionType.scene:
        return Icons.home_work;
      case ActionType.delay:
        return Icons.timer;
      default:
        return Icons.play_arrow;
    }
  }

  String _getActionTitle(AutomationAction action) {
    switch (action.type) {
      case ActionType.deviceControl:
        return 'تحكم بالجهاز';
      case ActionType.notification:
        return 'إشعار';
      case ActionType.scene:
        return 'مشهد';
      case ActionType.delay:
        return 'تأخير';
      default:
        return 'إجراء غير معروف';
    }
  }

  String _getActionSubtitle(AutomationAction action) {
    switch (action.type) {
      case ActionType.deviceControl:
        final deviceType = action.config['device_type'] ?? 'غير محدد';
        final room = action.config['room'] ?? 'غير محدد';
        final command = action.config['command'] ?? 'غير محدد';

        String deviceText = deviceType;
        if (deviceType == 'AC') {
          deviceText = 'مكيف';
        } else if (deviceType == 'TV') {
          deviceText = 'تلفاز';
        } else if (deviceType == 'SWITCH') {
          deviceText = 'مفتاح/إضاءة';
        }

        String commandText = command;
        if (command == 'ON') {
          commandText = 'تشغيل';
        } else if (command == 'OFF') {
          commandText = 'إيقاف';
        } else if (command == 'POWER-ON') {
          commandText = 'تشغيل';
        } else if (command == 'POWER-OFF') {
          commandText = 'إيقاف';
        }

        String details = '$deviceText في $room: $commandText';

        if (deviceType == 'AC' && command == 'ON') {
          final temp = action.config['temperature'];
          final speed = action.config['fan_speed'];
          if (temp != null) details += ' ($temp°C';
          if (speed != null) details += ', سرعة $speed';
          if (temp != null || speed != null) details += ')';
        }

        return details;
      case ActionType.notification:
        final title = action.config['title'] ?? 'غير محدد';
        final priority = action.config['priority'] ?? 'normal';
        String priorityText = priority;
        if (priority == 'low') {
          priorityText = 'منخفضة';
        } else if (priority == 'normal') {
          priorityText = 'عادية';
        } else if (priority == 'high') {
          priorityText = 'عالية';
        } else if (priority == 'urgent') {
          priorityText = 'عاجلة';
        }

        return '$title (أولوية: $priorityText)';
      case ActionType.scene:
        final sceneId = action.config['scene_id'] ?? 'غير محدد';
        String sceneText = sceneId;
        if (sceneId.contains('evening')) {
          sceneText = 'مشهد المساء';
        } else if (sceneId.contains('morning')) {
          sceneText = 'مشهد الصباح';
        } else if (sceneId.contains('night')) {
          sceneText = 'مشهد الليل';
        } else if (sceneId.contains('away')) {
          sceneText = 'مشهد الخروج';
        } else if (sceneId.contains('home')) {
          sceneText = 'مشهد العودة';
        } else if (sceneId.contains('movie')) {
          sceneText = 'مشهد الأفلام';
        } else if (sceneId.contains('reading')) {
          sceneText = 'مشهد القراءة';
        }

        return sceneText;
      case ActionType.delay:
        return 'التأخير: ${action.config['duration'] ?? 'غير محدد'}';
      default:
        return 'تكوين غير معروف';
    }
  }

  // دوال إضافة العناصر
  void _addTrigger() {
    showDialog(
      context: context,
      builder: (context) => _buildTriggerSelectionDialog(),
    );
  }

  void _addCondition() {
    showDialog(
      context: context,
      builder: (context) => _buildConditionSelectionDialog(),
    );
  }

  void _addAction() {
    showDialog(
      context: context,
      builder: (context) => _buildActionSelectionDialog(),
    );
  }

  // حوارات اختيار العناصر
  Widget _buildTriggerSelectionDialog() {
    return AlertDialog(
      backgroundColor: const Color(0xFF16213E),
      title:
          const Text('اختر نوع المحفز', style: TextStyle(color: Colors.white)),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildSelectionTile(
              'محفز الوقت', Icons.access_time, () => _addTimeTrigger()),
          _buildSelectionTile(
              'محفز الطقس', Icons.cloud, () => _addWeatherTrigger()),
          _buildSelectionTile(
              'محفز الشمس', Icons.wb_sunny, () => _addSunTrigger()),
          _buildSelectionTile(
              'محفز الكيان', Icons.home, () => _addEntityTrigger()),
        ],
      ),
    );
  }

  Widget _buildConditionSelectionDialog() {
    return AlertDialog(
      backgroundColor: const Color(0xFF16213E),
      title:
          const Text('اختر نوع الشرط', style: TextStyle(color: Colors.white)),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildSelectionTile(
              'شرط الطقس', Icons.thermostat, () => _addWeatherCondition()),
          _buildSelectionTile(
              'شرط الشمس', Icons.brightness_6, () => _addSunCondition()),
          _buildSelectionTile(
              'شرط الشخص', Icons.person, () => _addPersonCondition()),
        ],
      ),
    );
  }

  Widget _buildActionSelectionDialog() {
    return AlertDialog(
      backgroundColor: const Color(0xFF16213E),
      title:
          const Text('اختر نوع الإجراء', style: TextStyle(color: Colors.white)),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildSelectionTile('تحكم بالجهاز', Icons.power_settings_new,
              () => _addDeviceControlAction()),
          _buildSelectionTile('إرسال إشعار', Icons.notifications,
              () => _addNotificationAction()),
          _buildSelectionTile('خدمة Home Assistant', Icons.home_work,
              () => _addHomeAssistantServiceAction()),
        ],
      ),
    );
  }

  Widget _buildSelectionTile(String title, IconData icon, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue),
      title: Text(title, style: const TextStyle(color: Colors.white)),
      onTap: () {
        Navigator.of(context).pop();
        onTap();
      },
    );
  }

  // دوال إضافة المحفزات
  void _addTimeTrigger() {
    _showTimeTriggerDialog();
  }

  void _addWeatherTrigger() {
    _showWeatherTriggerDialog();
  }

  void _addSunTrigger() {
    _showSunTriggerDialog();
  }

  void _addEntityTrigger() {
    _showEntityTriggerDialog();
  }

  // دوال إضافة الشروط
  void _addWeatherCondition() {
    _showWeatherConditionDialog();
  }

  void _addSunCondition() {
    _showSunConditionDialog();
  }

  void _addPersonCondition() {
    _showPersonConditionDialog();
  }

  // حوارات الشروط
  void _showWeatherConditionDialog() {
    String selectedCondition = 'temperature_below';
    double threshold = 25.0;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: const Color(0xFF16213E),
          title: const Text('شرط الطقس', style: TextStyle(color: Colors.white)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                value: selectedCondition,
                dropdownColor: const Color(0xFF16213E),
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'نوع الشرط',
                  labelStyle: TextStyle(color: Colors.white),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white),
                  ),
                ),
                items: const [
                  DropdownMenuItem(
                      value: 'temperature_above',
                      child: Text('درجة الحرارة أعلى من')),
                  DropdownMenuItem(
                      value: 'temperature_below',
                      child: Text('درجة الحرارة أقل من')),
                  DropdownMenuItem(
                      value: 'humidity_above', child: Text('الرطوبة أعلى من')),
                  DropdownMenuItem(
                      value: 'humidity_below', child: Text('الرطوبة أقل من')),
                ],
                onChanged: (value) {
                  setDialogState(() {
                    selectedCondition = value!;
                    if (value.contains('temperature')) {
                      threshold = 25.0;
                    } else if (value.contains('humidity')) {
                      threshold = 50.0;
                    }
                  });
                },
              ),
              const SizedBox(height: 16),
              Text(
                selectedCondition.contains('temperature')
                    ? 'درجة الحرارة:'
                    : 'الرطوبة (%):',
                style: const TextStyle(color: Colors.white, fontSize: 16),
              ),
              const SizedBox(height: 8),
              Slider(
                value: selectedCondition.contains('temperature')
                    ? threshold.clamp(-10.0, 50.0)
                    : threshold.clamp(0.0, 100.0),
                min: selectedCondition.contains('temperature') ? -10 : 0,
                max: selectedCondition.contains('temperature') ? 50 : 100,
                divisions: selectedCondition.contains('temperature') ? 60 : 100,
                activeColor: Colors.blue,
                inactiveColor: Colors.grey,
                label:
                    '${threshold.round()}${selectedCondition.contains('temperature') ? '°C' : '%'}',
                onChanged: (value) => setDialogState(() => threshold = value),
              ),
              Text(
                '${threshold.round()}${selectedCondition.contains('temperature') ? '°C' : '%'}',
                style: const TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _conditions.add(AutomationCondition(
                    id: const Uuid().v4(),
                    type: ConditionType.weather,
                    config: {
                      'condition': selectedCondition,
                      'threshold': threshold
                    },
                  ));
                });
                Navigator.pop(context);
              },
              child: const Text('إضافة', style: TextStyle(color: Colors.blue)),
            ),
          ],
        ),
      ),
    );
  }

  void _showSunConditionDialog() {
    String selectedCondition = 'after_sunset';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: const Color(0xFF16213E),
          title: const Text('شرط الشمس', style: TextStyle(color: Colors.white)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              RadioListTile<String>(
                title: const Text('بعد غروب الشمس',
                    style: TextStyle(color: Colors.white)),
                value: 'after_sunset',
                groupValue: selectedCondition,
                activeColor: Colors.blue,
                onChanged: (value) =>
                    setDialogState(() => selectedCondition = value!),
              ),
              RadioListTile<String>(
                title: const Text('بعد شروق الشمس',
                    style: TextStyle(color: Colors.white)),
                value: 'after_sunrise',
                groupValue: selectedCondition,
                activeColor: Colors.blue,
                onChanged: (value) =>
                    setDialogState(() => selectedCondition = value!),
              ),
              RadioListTile<String>(
                title: const Text('قبل غروب الشمس',
                    style: TextStyle(color: Colors.white)),
                value: 'before_sunset',
                groupValue: selectedCondition,
                activeColor: Colors.blue,
                onChanged: (value) =>
                    setDialogState(() => selectedCondition = value!),
              ),
              RadioListTile<String>(
                title: const Text('قبل شروق الشمس',
                    style: TextStyle(color: Colors.white)),
                value: 'before_sunrise',
                groupValue: selectedCondition,
                activeColor: Colors.blue,
                onChanged: (value) =>
                    setDialogState(() => selectedCondition = value!),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _conditions.add(AutomationCondition(
                    id: const Uuid().v4(),
                    type: ConditionType.sun,
                    config: {'condition': selectedCondition},
                  ));
                });
                Navigator.pop(context);
              },
              child: const Text('إضافة', style: TextStyle(color: Colors.blue)),
            ),
          ],
        ),
      ),
    );
  }

  void _showPersonConditionDialog() {
    String selectedPerson = 'zaen';
    String selectedState = 'home';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: const Color(0xFF16213E),
          title: const Text('شرط الشخص', style: TextStyle(color: Colors.white)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                value: selectedPerson,
                dropdownColor: const Color(0xFF16213E),
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'الشخص',
                  labelStyle: TextStyle(color: Colors.white),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white),
                  ),
                ),
                items: const [
                  DropdownMenuItem(value: 'zaen', child: Text('زين')),
                ],
                onChanged: (value) =>
                    setDialogState(() => selectedPerson = value!),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedState,
                dropdownColor: const Color(0xFF16213E),
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'الحالة',
                  labelStyle: TextStyle(color: Colors.white),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white),
                  ),
                ),
                items: const [
                  DropdownMenuItem(value: 'home', child: Text('في المنزل')),
                  DropdownMenuItem(value: 'away', child: Text('خارج المنزل')),
                ],
                onChanged: (value) =>
                    setDialogState(() => selectedState = value!),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _conditions.add(AutomationCondition(
                    id: const Uuid().v4(),
                    type: ConditionType.person,
                    config: {
                      'person_id': selectedPerson,
                      'state': selectedState
                    },
                  ));
                });
                Navigator.pop(context);
              },
              child: const Text('إضافة', style: TextStyle(color: Colors.blue)),
            ),
          ],
        ),
      ),
    );
  }

  // دوال إضافة الإجراءات
  void _addDeviceControlAction() {
    _showDeviceControlDialog();
  }

  void _addNotificationAction() {
    _showNotificationDialog();
  }

  void _addHomeAssistantServiceAction() {
    _showSceneDialog();
  }

  // دالة حفظ القاعدة
  void _saveRule() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_triggers.isEmpty) {
      _showErrorDialog('يجب إضافة محفز واحد على الأقل');
      return;
    }

    if (_actions.isEmpty) {
      _showErrorDialog('يجب إضافة إجراء واحد على الأقل');
      return;
    }

    try {
      final automationService = Get.find<AdvancedAutomationService>();

      if (widget.ruleToEdit != null) {
        // تحديث قاعدة موجودة
        final updatedRule = AdvancedAutomationRule(
          id: widget.ruleToEdit!.id, // الاحتفاظ بنفس المعرف
          name: _nameController.text,
          description: _descriptionController.text,
          triggers: _triggers,
          conditions: _conditions,
          actions: _actions,
          logicOperator: _logicOperator,
          enabled: _enabled,
          priority: _priority,
        );

        await automationService.updateAutomationRule(updatedRule);

        if (mounted) {
          Navigator.of(context).pop(true); // إرجاع true للإشارة للنجاح
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث قاعدة الأتمتة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // إنشاء قاعدة جديدة
        final rule = AdvancedAutomationRule(
          id: const Uuid().v4(),
          name: _nameController.text,
          description: _descriptionController.text,
          triggers: _triggers,
          conditions: _conditions,
          actions: _actions,
          logicOperator: _logicOperator,
          enabled: _enabled,
          priority: _priority,
        );

        await automationService.addAutomationRule(rule);

        if (mounted) {
          Navigator.of(context).pop(true); // إرجاع true للإشارة للنجاح
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إنشاء قاعدة الأتمتة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      _showErrorDialog('خطأ في حفظ القاعدة: $e');
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: const Text('خطأ', style: TextStyle(color: Colors.white)),
        content: Text(message, style: const TextStyle(color: Colors.grey)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق', style: TextStyle(color: Colors.blue)),
          ),
        ],
      ),
    );
  }

  // حوارات المحفزات التفصيلية
  void _showTimeTriggerDialog() {
    TimeOfDay selectedTime = TimeOfDay.now();
    List<String> selectedDays = [];
    final List<String> weekDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد'
    ];
    final List<String> weekDaysEn = [
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
      'sunday'
    ];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: const Color(0xFF16213E),
          title:
              const Text('محفز الوقت', style: TextStyle(color: Colors.white)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // اختيار الوقت
                const Text('الوقت:',
                    style: TextStyle(color: Colors.white, fontSize: 16)),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0A0E27),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        selectedTime.format(context),
                        style:
                            const TextStyle(color: Colors.white, fontSize: 18),
                      ),
                      IconButton(
                        onPressed: () async {
                          final TimeOfDay? picked = await showTimePicker(
                            context: context,
                            initialTime: selectedTime,
                            builder: (context, child) => Theme(
                              data: Theme.of(context).copyWith(
                                colorScheme: const ColorScheme.dark(
                                  primary: Colors.blue,
                                  surface: Color(0xFF16213E),
                                ),
                              ),
                              child: child!,
                            ),
                          );
                          if (picked != null) {
                            setDialogState(() => selectedTime = picked);
                          }
                        },
                        icon: const Icon(Icons.access_time, color: Colors.blue),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // اختيار الأيام
                const Text('الأيام:',
                    style: TextStyle(color: Colors.white, fontSize: 16)),
                const SizedBox(height: 8),
                ...weekDays.asMap().entries.map((entry) {
                  int index = entry.key;
                  String day = entry.value;
                  return CheckboxListTile(
                    title:
                        Text(day, style: const TextStyle(color: Colors.white)),
                    value: selectedDays.contains(weekDaysEn[index]),
                    onChanged: (bool? value) {
                      setDialogState(() {
                        if (value == true) {
                          selectedDays.add(weekDaysEn[index]);
                        } else {
                          selectedDays.remove(weekDaysEn[index]);
                        }
                      });
                    },
                    activeColor: Colors.blue,
                    checkColor: Colors.white,
                  );
                }),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () {
                if (selectedDays.isEmpty) {
                  // إذا لم يتم اختيار أيام، سيعمل يومياً
                  selectedDays = weekDaysEn;
                }

                setState(() {
                  _triggers.add(AutomationTrigger(
                    id: const Uuid().v4(),
                    type: TriggerType.time,
                    config: {
                      'time':
                          '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}',
                      'days': selectedDays,
                    },
                  ));
                });
                Navigator.of(context).pop();
              },
              child: const Text('إضافة', style: TextStyle(color: Colors.blue)),
            ),
          ],
        ),
      ),
    );
  }

  void _showWeatherTriggerDialog() {
    String selectedCondition = 'temperature_above';
    double threshold = 30.0;

    final Map<String, String> weatherConditions = {
      'temperature_above': 'درجة الحرارة أعلى من',
      'temperature_below': 'درجة الحرارة أقل من',
      'humidity_above': 'الرطوبة أعلى من',
      'humidity_below': 'الرطوبة أقل من',
      'rain_start': 'بداية المطر',
      'rain_stop': 'توقف المطر',
    };

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: const Color(0xFF16213E),
          title:
              const Text('محفز الطقس', style: TextStyle(color: Colors.white)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('نوع الشرط:',
                  style: TextStyle(color: Colors.white, fontSize: 16)),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedCondition,
                dropdownColor: const Color(0xFF16213E),
                style: const TextStyle(color: Colors.white),
                isExpanded: true,
                items: weatherConditions.entries.map((entry) {
                  return DropdownMenuItem(
                    value: entry.key,
                    child: Text(entry.value),
                  );
                }).toList(),
                onChanged: (value) {
                  setDialogState(() {
                    selectedCondition = value!;
                    // إعادة تعيين القيمة الافتراضية حسب نوع الشرط
                    if (value.contains('temperature')) {
                      threshold = 25.0; // قيمة افتراضية لدرجة الحرارة
                    } else if (value.contains('humidity')) {
                      threshold = 50.0; // قيمة افتراضية للرطوبة
                    }
                  });
                },
              ),
              if (selectedCondition.contains('temperature') ||
                  selectedCondition.contains('humidity')) ...[
                const SizedBox(height: 16),
                Text(
                  selectedCondition.contains('temperature')
                      ? 'درجة الحرارة:'
                      : 'الرطوبة (%):',
                  style: const TextStyle(color: Colors.white, fontSize: 16),
                ),
                const SizedBox(height: 8),
                Slider(
                  value: selectedCondition.contains('temperature')
                      ? threshold.clamp(-10.0, 50.0)
                      : threshold.clamp(0.0, 100.0),
                  min: selectedCondition.contains('temperature') ? -10 : 0,
                  max: selectedCondition.contains('temperature') ? 50 : 100,
                  divisions:
                      selectedCondition.contains('temperature') ? 60 : 100,
                  activeColor: Colors.blue,
                  inactiveColor: Colors.grey,
                  label:
                      '${threshold.round()}${selectedCondition.contains('temperature') ? '°C' : '%'}',
                  onChanged: (value) => setDialogState(() => threshold = value),
                ),
                Text(
                  '${threshold.round()}${selectedCondition.contains('temperature') ? '°C' : '%'}',
                  style: const TextStyle(color: Colors.white, fontSize: 18),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _triggers.add(AutomationTrigger(
                    id: const Uuid().v4(),
                    type: TriggerType.weather,
                    config: {
                      'condition': selectedCondition,
                      if (selectedCondition.contains('temperature') ||
                          selectedCondition.contains('humidity'))
                        'threshold': threshold,
                    },
                  ));
                });
                Navigator.of(context).pop();
              },
              child: const Text('إضافة', style: TextStyle(color: Colors.blue)),
            ),
          ],
        ),
      ),
    );
  }

  void _showSunTriggerDialog() {
    String selectedEvent = 'sunset';

    final Map<String, String> sunEvents = {
      'sunrise': 'شروق الشمس',
      'sunset': 'غروب الشمس',
    };

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: const Color(0xFF16213E),
          title:
              const Text('محفز الشمس', style: TextStyle(color: Colors.white)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('الحدث:',
                  style: TextStyle(color: Colors.white, fontSize: 16)),
              const SizedBox(height: 8),
              ...sunEvents.entries.map((entry) {
                return RadioListTile<String>(
                  title: Text(entry.value,
                      style: const TextStyle(color: Colors.white)),
                  value: entry.key,
                  groupValue: selectedEvent,
                  onChanged: (value) =>
                      setDialogState(() => selectedEvent = value!),
                  activeColor: Colors.blue,
                );
              }),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _triggers.add(AutomationTrigger(
                    id: const Uuid().v4(),
                    type: TriggerType.sun,
                    config: {'event': selectedEvent},
                  ));
                });
                Navigator.of(context).pop();
              },
              child: const Text('إضافة', style: TextStyle(color: Colors.blue)),
            ),
          ],
        ),
      ),
    );
  }

  void _showEntityTriggerDialog() {
    String selectedEntityType = 'person';
    String selectedEntity = 'person.zaen';
    String selectedState = 'home';

    final Map<String, Map<String, String>> entityTypes = {
      'person': {
        'person.zaen': 'شخص زين',
      },
      'sun': {
        'sun.sun': 'الشمس',
      },
      'weather': {
        'weather.home': 'طقس المنزل',
      },
    };

    final Map<String, List<String>> entityStates = {
      'person': ['home', 'away'],
      'sun': ['above_horizon', 'below_horizon'],
      'weather': ['sunny', 'cloudy', 'rainy', 'stormy'],
    };

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: const Color(0xFF16213E),
          title:
              const Text('محفز الكيان', style: TextStyle(color: Colors.white)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('نوع الكيان:',
                    style: TextStyle(color: Colors.white, fontSize: 16)),
                const SizedBox(height: 8),
                DropdownButton<String>(
                  value: selectedEntityType,
                  dropdownColor: const Color(0xFF16213E),
                  style: const TextStyle(color: Colors.white),
                  isExpanded: true,
                  items: entityTypes.keys.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(type == 'person'
                          ? 'شخص'
                          : type == 'sun'
                              ? 'شمس'
                              : 'طقس'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setDialogState(() {
                      selectedEntityType = value!;
                      selectedEntity = entityTypes[value]!.keys.first;
                      selectedState = entityStates[value]!.first;
                    });
                  },
                ),
                const SizedBox(height: 16),
                const Text('الكيان:',
                    style: TextStyle(color: Colors.white, fontSize: 16)),
                const SizedBox(height: 8),
                DropdownButton<String>(
                  value: selectedEntity,
                  dropdownColor: const Color(0xFF16213E),
                  style: const TextStyle(color: Colors.white),
                  isExpanded: true,
                  items: entityTypes[selectedEntityType]!.entries.map((entry) {
                    return DropdownMenuItem(
                      value: entry.key,
                      child: Text(entry.value),
                    );
                  }).toList(),
                  onChanged: (value) =>
                      setDialogState(() => selectedEntity = value!),
                ),
                const SizedBox(height: 16),
                const Text('الحالة:',
                    style: TextStyle(color: Colors.white, fontSize: 16)),
                const SizedBox(height: 8),
                DropdownButton<String>(
                  value: selectedState,
                  dropdownColor: const Color(0xFF16213E),
                  style: const TextStyle(color: Colors.white),
                  isExpanded: true,
                  items: entityStates[selectedEntityType]!.map((state) {
                    String displayName = state;
                    if (selectedEntityType == 'person') {
                      displayName =
                          state == 'home' ? 'في المنزل' : 'خارج المنزل';
                    } else if (selectedEntityType == 'sun') {
                      displayName =
                          state == 'above_horizon' ? 'فوق الأفق' : 'تحت الأفق';
                    } else if (selectedEntityType == 'weather') {
                      displayName = state == 'sunny'
                          ? 'مشمس'
                          : state == 'cloudy'
                              ? 'غائم'
                              : state == 'rainy'
                                  ? 'ممطر'
                                  : 'عاصف';
                    }
                    return DropdownMenuItem(
                      value: state,
                      child: Text(displayName),
                    );
                  }).toList(),
                  onChanged: (value) =>
                      setDialogState(() => selectedState = value!),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _triggers.add(AutomationTrigger(
                    id: const Uuid().v4(),
                    type: TriggerType.entityState,
                    config: {
                      'entity_id': selectedEntity,
                      'state': selectedState,
                    },
                  ));
                });
                Navigator.of(context).pop();
              },
              child: const Text('إضافة', style: TextStyle(color: Colors.blue)),
            ),
          ],
        ),
      ),
    );
  }

  // حوارات الإجراءات التفصيلية
  void _showDeviceControlDialog() {
    String selectedDeviceType = 'AC';
    String selectedRoom = 'غرفة المعيشة';
    String selectedCommand = 'ON';
    double temperature = 22.0;
    int fanSpeed = 1;

    final Map<String, String> deviceTypes = {
      'AC': 'مكيف',
      'TV': 'تلفاز',
      'SWITCH': 'مفتاح/إضاءة',
    };

    final List<String> rooms = [
      'غرفة المعيشة',
      'غرفة النوم',
      'المطبخ',
      'الحمام',
      'المكتب'
    ];

    final Map<String, List<String>> deviceCommands = {
      'AC': ['ON', 'OFF'],
      'TV': ['POWER-ON', 'POWER-OFF', 'MENU', 'VOLUME-UP', 'VOLUME-DOWN'],
      'SWITCH': ['ON', 'OFF'],
    };

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: const Color(0xFF16213E),
          title: const Text('التحكم في الأجهزة',
              style: TextStyle(color: Colors.white)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // نوع الجهاز
                const Text('نوع الجهاز:',
                    style: TextStyle(color: Colors.white, fontSize: 16)),
                const SizedBox(height: 8),
                DropdownButton<String>(
                  value: selectedDeviceType,
                  dropdownColor: const Color(0xFF16213E),
                  style: const TextStyle(color: Colors.white),
                  isExpanded: true,
                  items: deviceTypes.entries.map((entry) {
                    return DropdownMenuItem(
                      value: entry.key,
                      child: Text(entry.value),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setDialogState(() {
                      selectedDeviceType = value!;
                      selectedCommand = deviceCommands[value]!.first;
                    });
                  },
                ),

                const SizedBox(height: 16),
                // الغرفة
                const Text('الغرفة:',
                    style: TextStyle(color: Colors.white, fontSize: 16)),
                const SizedBox(height: 8),
                DropdownButton<String>(
                  value: selectedRoom,
                  dropdownColor: const Color(0xFF16213E),
                  style: const TextStyle(color: Colors.white),
                  isExpanded: true,
                  items: rooms.map((room) {
                    return DropdownMenuItem(
                      value: room,
                      child: Text(room),
                    );
                  }).toList(),
                  onChanged: (value) =>
                      setDialogState(() => selectedRoom = value!),
                ),

                const SizedBox(height: 16),
                // الأمر
                const Text('الأمر:',
                    style: TextStyle(color: Colors.white, fontSize: 16)),
                const SizedBox(height: 8),
                DropdownButton<String>(
                  value: selectedCommand,
                  dropdownColor: const Color(0xFF16213E),
                  style: const TextStyle(color: Colors.white),
                  isExpanded: true,
                  items: deviceCommands[selectedDeviceType]!.map((command) {
                    String displayName = command;
                    if (selectedDeviceType == 'AC') {
                      displayName = command == 'ON' ? 'تشغيل' : 'إيقاف';
                    } else if (selectedDeviceType == 'TV') {
                      displayName = command == 'POWER-ON'
                          ? 'تشغيل'
                          : command == 'POWER-OFF'
                              ? 'إيقاف'
                              : command == 'MENU'
                                  ? 'القائمة'
                                  : command == 'VOLUME-UP'
                                      ? 'رفع الصوت'
                                      : 'خفض الصوت';
                    } else if (selectedDeviceType == 'SWITCH') {
                      displayName = command == 'ON' ? 'تشغيل' : 'إيقاف';
                    }
                    return DropdownMenuItem(
                      value: command,
                      child: Text(displayName),
                    );
                  }).toList(),
                  onChanged: (value) =>
                      setDialogState(() => selectedCommand = value!),
                ),

                // إعدادات المكيف
                if (selectedDeviceType == 'AC' && selectedCommand == 'ON') ...[
                  const SizedBox(height: 16),
                  const Text('درجة الحرارة:',
                      style: TextStyle(color: Colors.white, fontSize: 16)),
                  const SizedBox(height: 8),
                  Slider(
                    value: temperature,
                    min: 16,
                    max: 30,
                    divisions: 14,
                    activeColor: Colors.blue,
                    inactiveColor: Colors.grey,
                    label: '${temperature.round()}°C',
                    onChanged: (value) =>
                        setDialogState(() => temperature = value),
                  ),
                  Text('${temperature.round()}°C',
                      style:
                          const TextStyle(color: Colors.white, fontSize: 18)),
                  const SizedBox(height: 16),
                  const Text('سرعة المروحة:',
                      style: TextStyle(color: Colors.white, fontSize: 16)),
                  const SizedBox(height: 8),
                  Slider(
                    value: fanSpeed.toDouble(),
                    min: 1,
                    max: 5,
                    divisions: 4,
                    activeColor: Colors.blue,
                    inactiveColor: Colors.grey,
                    label: fanSpeed.toString(),
                    onChanged: (value) =>
                        setDialogState(() => fanSpeed = value.round()),
                  ),
                  Text(fanSpeed.toString(),
                      style:
                          const TextStyle(color: Colors.white, fontSize: 18)),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () {
                Map<String, dynamic> config = {
                  'device_type': selectedDeviceType,
                  'room': selectedRoom,
                  'command': selectedCommand,
                };

                if (selectedDeviceType == 'AC' && selectedCommand == 'ON') {
                  config['temperature'] = temperature.round();
                  config['fan_speed'] = fanSpeed;
                }

                setState(() {
                  _actions.add(AutomationAction(
                    id: const Uuid().v4(),
                    type: ActionType.deviceControl,
                    config: config,
                  ));
                });
                Navigator.of(context).pop();
              },
              child: const Text('إضافة', style: TextStyle(color: Colors.blue)),
            ),
          ],
        ),
      ),
    );
  }

  void _showNotificationDialog() {
    final TextEditingController titleController =
        TextEditingController(text: 'تنبيه من نظام زين');
    final TextEditingController messageController =
        TextEditingController(text: 'تم تنفيذ قاعدة الأتمتة');
    String selectedPriority = 'normal';

    final Map<String, String> priorities = {
      'low': 'منخفضة',
      'normal': 'عادية',
      'high': 'عالية',
      'urgent': 'عاجلة',
    };

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: const Color(0xFF16213E),
          title:
              const Text('إرسال إشعار', style: TextStyle(color: Colors.white)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('عنوان الإشعار:',
                    style: TextStyle(color: Colors.white, fontSize: 16)),
                const SizedBox(height: 8),
                TextField(
                  controller: titleController,
                  style: const TextStyle(color: Colors.white),
                  decoration: const InputDecoration(
                    hintText: 'أدخل عنوان الإشعار',
                    hintStyle: TextStyle(color: Colors.grey),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.blue),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                const Text('رسالة الإشعار:',
                    style: TextStyle(color: Colors.white, fontSize: 16)),
                const SizedBox(height: 8),
                TextField(
                  controller: messageController,
                  style: const TextStyle(color: Colors.white),
                  maxLines: 3,
                  decoration: const InputDecoration(
                    hintText: 'أدخل نص الرسالة',
                    hintStyle: TextStyle(color: Colors.grey),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.blue),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                const Text('أولوية الإشعار:',
                    style: TextStyle(color: Colors.white, fontSize: 16)),
                const SizedBox(height: 8),
                DropdownButton<String>(
                  value: selectedPriority,
                  dropdownColor: const Color(0xFF16213E),
                  style: const TextStyle(color: Colors.white),
                  isExpanded: true,
                  items: priorities.entries.map((entry) {
                    return DropdownMenuItem(
                      value: entry.key,
                      child: Text(entry.value),
                    );
                  }).toList(),
                  onChanged: (value) =>
                      setDialogState(() => selectedPriority = value!),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () {
                if (titleController.text.isEmpty ||
                    messageController.text.isEmpty) {
                  return;
                }

                setState(() {
                  _actions.add(AutomationAction(
                    id: const Uuid().v4(),
                    type: ActionType.notification,
                    config: {
                      'title': titleController.text,
                      'message': messageController.text,
                      'priority': selectedPriority,
                    },
                  ));
                });
                Navigator.of(context).pop();
              },
              child: const Text('إضافة', style: TextStyle(color: Colors.blue)),
            ),
          ],
        ),
      ),
    );
  }

  void _showSceneDialog() {
    String selectedScene = 'evening';

    final Map<String, String> scenes = {
      'evening': 'مشهد المساء',
      'morning': 'مشهد الصباح',
      'night': 'مشهد الليل',
      'away': 'مشهد الخروج',
      'home': 'مشهد العودة',
      'movie': 'مشهد الأفلام',
      'reading': 'مشهد القراءة',
    };

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: const Color(0xFF16213E),
          title:
              const Text('تفعيل مشهد', style: TextStyle(color: Colors.white)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('اختر المشهد:',
                  style: TextStyle(color: Colors.white, fontSize: 16)),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedScene,
                dropdownColor: const Color(0xFF16213E),
                style: const TextStyle(color: Colors.white),
                isExpanded: true,
                items: scenes.entries.map((entry) {
                  return DropdownMenuItem(
                    value: entry.key,
                    child: Text(entry.value),
                  );
                }).toList(),
                onChanged: (value) =>
                    setDialogState(() => selectedScene = value!),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _actions.add(AutomationAction(
                    id: const Uuid().v4(),
                    type: ActionType.scene,
                    config: {
                      'scene_id': 'scene.$selectedScene',
                      'service': 'scene.turn_on',
                    },
                  ));
                });
                Navigator.of(context).pop();
              },
              child: const Text('إضافة', style: TextStyle(color: Colors.blue)),
            ),
          ],
        ),
      ),
    );
  }
}
