import 'package:flutter/material.dart';

/// نظام الأحجام والمسافات المتطور لتطبيق Zaen Smart Home
/// يوفر قيم ثابتة ومتسقة للأحجام والمسافات في جميع أنحاء التطبيق
class AppDimensions {
  // === المسافات الأساسية ===

  /// مسافات صغيرة جداً
  static const double spacingXs = 4.0;
  static const double spacingS = 8.0;

  /// مسافات متوسطة
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXl = 32.0;

  /// مسافات كبيرة
  static const double spacingXxl = 48.0;
  static const double spacingXxxl = 64.0;

  // === الحشو (Padding) ===

  /// حشو صغير
  static const EdgeInsets paddingXs = EdgeInsets.all(spacingXs);
  static const EdgeInsets paddingS = EdgeInsets.all(spacingS);

  /// حشو متوسط
  static const EdgeInsets paddingM = EdgeInsets.all(spacingM);
  static const EdgeInsets paddingL = EdgeInsets.all(spacingL);
  static const EdgeInsets paddingXl = EdgeInsets.all(spacingXl);

  /// حشو كبير
  static const EdgeInsets paddingXxl = EdgeInsets.all(spacingXxl);
  static const EdgeInsets paddingXxxl = EdgeInsets.all(spacingXxxl);

  /// حشو أفقي
  static const EdgeInsets paddingHorizontalS =
      EdgeInsets.symmetric(horizontal: spacingS);
  static const EdgeInsets paddingHorizontalM =
      EdgeInsets.symmetric(horizontal: spacingM);
  static const EdgeInsets paddingHorizontalL =
      EdgeInsets.symmetric(horizontal: spacingL);
  static const EdgeInsets paddingHorizontalXl =
      EdgeInsets.symmetric(horizontal: spacingXl);

  /// حشو عمودي
  static const EdgeInsets paddingVerticalS =
      EdgeInsets.symmetric(vertical: spacingS);
  static const EdgeInsets paddingVerticalM =
      EdgeInsets.symmetric(vertical: spacingM);
  static const EdgeInsets paddingVerticalL =
      EdgeInsets.symmetric(vertical: spacingL);
  static const EdgeInsets paddingVerticalXl =
      EdgeInsets.symmetric(vertical: spacingXl);

  // === الهوامش (Margin) ===

  /// هوامش صغيرة
  static const EdgeInsets marginXs = EdgeInsets.all(spacingXs);
  static const EdgeInsets marginS = EdgeInsets.all(spacingS);

  /// هوامش متوسطة
  static const EdgeInsets marginM = EdgeInsets.all(spacingM);
  static const EdgeInsets marginL = EdgeInsets.all(spacingL);
  static const EdgeInsets marginXl = EdgeInsets.all(spacingXl);

  /// هوامش كبيرة
  static const EdgeInsets marginXxl = EdgeInsets.all(spacingXxl);
  static const EdgeInsets marginXxxl = EdgeInsets.all(spacingXxxl);

  // === أحجام الأيقونات ===

  /// أيقونات صغيرة
  static const double iconXs = 12.0;
  static const double iconS = 16.0;

  /// أيقونات متوسطة
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXl = 48.0;

  /// أيقونات كبيرة
  static const double iconXxl = 64.0;
  static const double iconXxxl = 96.0;

  // === أحجام الأزرار ===

  /// ارتفاع الأزرار
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 48.0;
  static const double buttonHeightL = 56.0;
  static const double buttonHeightXl = 64.0;

  /// عرض الأزرار
  static const double buttonWidthS = 80.0;
  static const double buttonWidthM = 120.0;
  static const double buttonWidthL = 160.0;
  static const double buttonWidthXl = 200.0;

  // === أحجام الكروت ===

  /// ارتفاع الكروت
  static const double cardHeightS = 80.0;
  static const double cardHeightM = 120.0;
  static const double cardHeightL = 160.0;
  static const double cardHeightXl = 200.0;

  /// عرض الكروت
  static const double cardWidthS = 120.0;
  static const double cardWidthM = 160.0;
  static const double cardWidthL = 200.0;
  static const double cardWidthXl = 240.0;

  // === نصف القطر (Border Radius) ===

  /// نصف قطر صغير
  static const double radiusXs = 4.0;
  static const double radiusS = 8.0;

  /// نصف قطر متوسط
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXl = 20.0;

  /// نصف قطر كبير
  static const double radiusXxl = 24.0;
  static const double radiusXxxl = 32.0;

  /// نصف قطر دائري
  static const double radiusCircular = 50.0;

  /// BorderRadius objects
  static BorderRadius get borderRadiusXs => BorderRadius.circular(radiusXs);
  static BorderRadius get borderRadiusS => BorderRadius.circular(radiusS);
  static BorderRadius get borderRadiusM => BorderRadius.circular(radiusM);
  static BorderRadius get borderRadiusL => BorderRadius.circular(radiusL);
  static BorderRadius get borderRadiusXl => BorderRadius.circular(radiusXl);
  static BorderRadius get borderRadiusXxl => BorderRadius.circular(radiusXxl);
  static BorderRadius get borderRadiusXxxl => BorderRadius.circular(radiusXxxl);
  static BorderRadius get borderRadiusCircular =>
      BorderRadius.circular(radiusCircular);

  // === سماكة الحدود ===

  static const double borderWidthThin = 0.5;
  static const double borderWidthNormal = 1.0;
  static const double borderWidthThick = 2.0;
  static const double borderWidthBold = 3.0;

  // === الارتفاعات (Elevation) ===

  static const double elevationNone = 0.0;
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  static const double elevationVeryHigh = 16.0;

  // === أحجام خاصة بالتطبيق ===

  /// أحجام شريط التطبيق
  static const double appBarHeight = 56.0;
  static const double appBarElevation = elevationLow;

  /// أحجام شريط التنقل السفلي
  static const double bottomNavHeight = 60.0;
  static const double bottomNavElevation = elevationMedium;

  /// أحجام الدرج الجانبي
  static const double drawerWidth = 280.0;

  /// أحجام الحقول النصية
  static const double textFieldHeight = 48.0;
  static const double textFieldBorderRadius = radiusM;

  /// أحجام الحوارات
  static const double dialogMaxWidth = 400.0;
  static const double dialogBorderRadius = radiusL;

  /// أحجام الصور المصغرة
  static const double thumbnailS = 40.0;
  static const double thumbnailM = 60.0;
  static const double thumbnailL = 80.0;
  static const double thumbnailXl = 120.0;

  // === وظائف مساعدة ===

  /// الحصول على SizedBox للمسافات العمودية
  static Widget verticalSpace(double height) => SizedBox(height: height);

  /// الحصول على SizedBox للمسافات الأفقية
  static Widget horizontalSpace(double width) => SizedBox(width: width);

  /// مسافات عمودية سريعة
  static Widget get verticalSpaceXs => verticalSpace(spacingXs);
  static Widget get verticalSpaceS => verticalSpace(spacingS);
  static Widget get verticalSpaceM => verticalSpace(spacingM);
  static Widget get verticalSpaceL => verticalSpace(spacingL);
  static Widget get verticalSpaceXl => verticalSpace(spacingXl);
  static Widget get verticalSpaceXxl => verticalSpace(spacingXxl);

  /// مسافات أفقية سريعة
  static Widget get horizontalSpaceXs => horizontalSpace(spacingXs);
  static Widget get horizontalSpaceS => horizontalSpace(spacingS);
  static Widget get horizontalSpaceM => horizontalSpace(spacingM);
  static Widget get horizontalSpaceL => horizontalSpace(spacingL);
  static Widget get horizontalSpaceXl => horizontalSpace(spacingXl);
  static Widget get horizontalSpaceXxl => horizontalSpace(spacingXxl);

  /// الحصول على حشو مخصص
  static EdgeInsets customPadding({
    double? all,
    double? horizontal,
    double? vertical,
    double? top,
    double? bottom,
    double? left,
    double? right,
  }) {
    if (all != null) return EdgeInsets.all(all);
    if (horizontal != null || vertical != null) {
      return EdgeInsets.symmetric(
        horizontal: horizontal ?? 0,
        vertical: vertical ?? 0,
      );
    }
    return EdgeInsets.only(
      top: top ?? 0,
      bottom: bottom ?? 0,
      left: left ?? 0,
      right: right ?? 0,
    );
  }

  /// الحصول على هامش مخصص
  static EdgeInsets customMargin({
    double? all,
    double? horizontal,
    double? vertical,
    double? top,
    double? bottom,
    double? left,
    double? right,
  }) {
    return customPadding(
      all: all,
      horizontal: horizontal,
      vertical: vertical,
      top: top,
      bottom: bottom,
      left: left,
      right: right,
    );
  }
}
