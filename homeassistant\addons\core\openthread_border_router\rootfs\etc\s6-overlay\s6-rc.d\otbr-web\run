#!/usr/bin/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Start OpenThread BorderRouter web interface
# ==============================================================================
bashio::log.info "Starting otbr-web..."
declare otbr_web_port

otbr_web_port="$(bashio::addon.port 8080)"

exec stdbuf -oL /usr/sbin/otbr-web -I wpan0 -d6 -s -p "${otbr_web_port}"
