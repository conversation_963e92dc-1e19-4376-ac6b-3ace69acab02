#!/usr/bin/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Runs mosquitto
# ==============================================================================
declare -a options

# Wait for NGINX to start before continuing (for authentication handling)
bashio::net.wait_for 80

options+=(-c /etc/mosquitto/mosquitto.conf)

if bashio::debug; then
  options+=(-v)
fi

# Send out discovery & service information
./discovery &

bashio::log.info "Starting mosquitto MQTT broker..."
exec mosquitto "${options[@]}"
