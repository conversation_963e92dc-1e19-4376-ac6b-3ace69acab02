#!/command/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Start service
# ==============================================================================
extra_args=()
if bashio::config.true 'debug_logging'; then
    extra_args+=('--debug')
fi

if bashio::config.true 'sound_enabled'; then
    extra_args+=('--snd-command' 'aplay -r 16000 -c 1 -f S16_LE -t raw')
fi

exec python3 -m wyoming_satellite \
    --name 'assist microphone' \
    --uri 'tcp://0.0.0.0:10700' \
    --mic-command 'arecord -r 16000 -c 1 -f S16_LE -t raw' \
    --snd-command-rate 16000 \
    --awake-wav "$(bashio::config 'awake_wav')" \
    --done-wav "$(bashio::config 'done_wav')" \
    --timer-finished-wav "$(bashio::config 'timer_finished_wav')" \
    --timer-finished-wav-repeat "$(bashio::config 'timer_repeat_count')" "$(bashio::config 'timer_repeat_delay')" \
    --mic-volume-multiplier "$(bashio::config 'mic_volume_multiplier')" \
    --snd-volume-multiplier "$(bashio::config 'sound_volume_multiplier')" \
    --mic-auto-gain "$(bashio::config 'auto_gain')" \
    --mic-noise-suppression "$(bashio::config 'noise_suppression')" \
    --no-zeroconf "${extra_args[@]}"
