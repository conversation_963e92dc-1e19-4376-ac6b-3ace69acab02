import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mysql1/mysql1.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/themes/app_colors.dart';

editRoom({context, selectroom, rooms, i}) async {
  if (client.connectionStatus!.state.name == 'connected') {
    await AwesomeDialog(
      context: context,
      dialogType: DialogType.noHeader,
      headerAnimationLoop: true,
      animType: AnimType.topSlide,
      dialogBackgroundColor: AppColors.backgroundColor3,
      body: Padding(
        padding: EdgeInsets.all(controller.sized * 0.01),
        child: Column(
          children: [
            SizedBox(
              height: controller.sizedHight * 0.25,
              child: CupertinoPicker(
                squeeze: 1.2,
                // looping: true,
                useMagnifier: true,
                magnification: 1.35,
                scrollController:
                    FixedExtentScrollController(initialItem: selectroom),
                itemExtent: controller.sizedHight * 0.05, //height of each item

                backgroundColor: Colors.transparent,
                children: <Widget>[
                  for (var c in rooms.keys)
                    Center(
                      child: Text(
                        rooms[c],
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            color: AppColors.textColor2,
                            fontSize: controller.sized * 0.017,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                ],
                onSelectedItemChanged: (int index) {
                  selectroom = index;
                },
              ),
            ),
            Container(
              width: double.infinity,
              child: submitButtom(
                onPressed: () async {
                  // التحقق من صلاحية نقل الأجهزة بين الغرف
                  if (!controller.canManageData()) {
                    showNoPermissionDialog(
                        customMessage:
                            'ليس لديك صلاحية لنقل الأجهزة بين الغرف');
                    return;
                  }

                  try {
                    // إنشاء اتصال جديد آمن
                    var newConn =
                        await MySqlConnection.connect(ConnectionSettings(
                      host: controller.hostZain.value,
                      user: 'root',
                      db: 'zain',
                      password: 'zain',
                      characterSet: CharacterSet.UTF8,
                    ));

                    await newConn.query('update Devices set RoomS=? where id=?',
                        [rooms.keys.toList()[selectroom], i['id']]);

                    // إغلاق الاتصال
                    await newConn.close();

                    // إرسال إشعار للنظام بالتحديث
                    final builder = MqttClientPayloadBuilder();
                    builder.addString('1');
                    client.publishMessage(
                        'edit', MqttQos.atLeastOnce, builder.payload!);
                    builder.clear();
                    builder.addString('re');
                    client.publishMessage(
                        i['id'], MqttQos.atLeastOnce, builder.payload!);

                    // إغلاق النافذة
                    Navigator.pop(context);
                  } catch (e) {
                    print('خطأ في تحديث الغرفة: $e');
                    // إظهار رسالة خطأ للمستخدم
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                            'حدث خطأ أثناء تحديث الغرفة. يرجى المحاولة مرة أخرى.'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
                // btnCancelOnPress:
                //     () {},
              ),
            )
          ],
        ),
      ),
    ).show();
    return selectroom;
  }
}
