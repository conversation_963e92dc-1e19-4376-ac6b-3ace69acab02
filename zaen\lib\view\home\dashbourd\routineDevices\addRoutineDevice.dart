// الملف الأصلي المُحدث - يستدعي الملفات المقسمة
// تم تقسيم الملف إلى ملفات أصغر للصيانة الأسهل
// جميع الوظائف تعمل بنفس الطريقة السابقة

import 'package:flutter/material.dart';
import 'add_routine_ac.dart' as ac_module;
import 'add_routine_sw.dart' as sw_module;
import 'add_routine_tv.dart' as tv_module;

// استدعاء دالة المكيف من الملف المنفصل
Widget addRoutineAc({
  required Map device,
  required Map roomData,
  setState1,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  return ac_module.addRoutineAc(
    device: device,
    roomData: roomData,
    setState1: setState1,
    sizedWidth: sizedWidth,
    sizedHeight: sizedHeight,
    sized: sized,
  );
}

// استدعاء دالة المفاتيح من الملف المنفصل
Widget addRoutineSw({
  required Map device,
  required Map roomData,
  setState1,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  return sw_module.addRoutineSw(
    device: device,
    roomData: roomData,
    setState1: setState1,
    sizedWidth: sizedWidth,
    sizedHeight: sizedHeight,
    sized: sized,
  );
}

// استدعاء دالة التلفزيون من الملف المنفصل
Widget addRoutineTv({
  required Map device,
  required Map roomData,
  setState1,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  return tv_module.addRoutineTv(
    device: device,
    roomData: roomData,
    setState1: setState1,
    sizedWidth: sizedWidth,
    sizedHeight: sizedHeight,
    sized: sized,
  );
}
