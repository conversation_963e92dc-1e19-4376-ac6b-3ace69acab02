import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../services/notification_service.dart';
import '../../services/advanced_automation_service.dart';

class NotificationSettingsPage extends StatelessWidget {
  const NotificationSettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final notificationService = Get.find<NotificationService>();
    final automationService = Get.find<AdvancedAutomationService>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الإشعارات'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: Obx(() => ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // إعدادات الطقس
              _buildSectionHeader('🌤️ إشعارات الطقس'),
              _buildSwitchTile(
                title: 'إشعارات تغيير الطقس',
                subtitle: 'تلقي إشعارات عند تغيير حالة الطقس',
                value: notificationService.weatherNotificationsEnabled,
                onChanged: (value) => notificationService.updateSettings(
                  weatherNotifications: value,
                ),
              ),
              _buildSwitchTile(
                title: 'تحذيرات الطقس المتطرف',
                subtitle: 'تحذيرات عند درجات الحرارة المرتفعة أو المنخفضة',
                value: notificationService.extremeWeatherAlerts,
                onChanged: (value) => notificationService.updateSettings(
                  extremeWeatherAlerts: value,
                ),
              ),

              const SizedBox(height: 20),

              // إعدادات التوصيات الذكية
              _buildSectionHeader('🤖 التوصيات الذكية'),
              _buildSwitchTile(
                title: 'التوصيات الذكية',
                subtitle: 'اقتراحات تلقائية لتحسين استخدام الأجهزة',
                value: notificationService.smartSuggestionsEnabled,
                onChanged: (value) => notificationService.updateSettings(
                  smartSuggestions: value,
                ),
              ),
              _buildSwitchTile(
                title: 'اقتراحات أتمتة الأجهزة',
                subtitle: 'اقتراحات لأتمتة الأجهزة بناءً على الاستخدام',
                value: notificationService.deviceAutomationSuggestions,
                onChanged: (value) => notificationService.updateSettings(
                  deviceAutomationSuggestions: value,
                ),
              ),

              const SizedBox(height: 20),

              // إعدادات قواعد الأتمتة
              _buildSectionHeader('🔔 إشعارات قواعد الأتمتة'),
              _buildGlobalAutomationSwitch(
                  notificationService, automationService),
              const SizedBox(height: 10),
              _buildAutomationRulesSection(
                  notificationService, automationService),

              const SizedBox(height: 20),

              // إحصائيات الإشعارات
              _buildSectionHeader('📊 إحصائيات الإشعارات'),
              _buildStatisticsCard(notificationService),
            ],
          )),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: SwitchListTile(
        title: Text(title),
        subtitle: Text(subtitle),
        value: value,
        onChanged: onChanged,
        activeColor: Colors.blue,
      ),
    );
  }

  Widget _buildGlobalAutomationSwitch(
    NotificationService notificationService,
    AdvancedAutomationService automationService,
  ) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Obx(() => SwitchListTile(
            title: const Text(
              '🔔 تفعيل جميع إشعارات الأتمتة',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: const Text('تفعيل/إيقاف جميع إشعارات قواعد الأتمتة'),
            value: _getGlobalAutomationNotificationSetting(
                notificationService, automationService),
            onChanged: (value) => _updateGlobalAutomationNotificationSetting(
                notificationService, automationService, value),
            activeColor: Colors.purple,
            secondary: const Icon(
              Icons.notifications_active,
              color: Colors.purple,
            ),
          )),
    );
  }

  Widget _buildAutomationRulesSection(
    NotificationService notificationService,
    AdvancedAutomationService automationService,
  ) {
    final rulesWithNotifications = automationService.automationRules
        .where((rule) => rule.actions.any((action) =>
            action.type.toString().contains('notification') ||
            action.type.toString().contains('send_notification')))
        .toList();

    if (rulesWithNotifications.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(Icons.info_outline, size: 48, color: Colors.grey[400]),
              const SizedBox(height: 8),
              Text(
                'لا توجد قواعد أتمتة تحتوي على إشعارات',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: [
        // عرض عدد القواعد
        Container(
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Colors.purple.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.purple.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              const Icon(Icons.info_outline, color: Colors.purple, size: 20),
              const SizedBox(width: 8),
              Text(
                'عدد قواعد الأتمتة التي تحتوي على إشعارات: ${rulesWithNotifications.length}',
                style: const TextStyle(
                  color: Colors.purple,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),

        // قائمة القواعد
        ...rulesWithNotifications.map((rule) {
          final isEnabled =
              notificationService.automationRuleNotifications[rule.id] ?? true;

          return Card(
            margin: const EdgeInsets.symmetric(vertical: 4),
            elevation: 2,
            child: SwitchListTile(
              title: Text(
                '🤖 ${rule.name}',
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    rule.description.isNotEmpty
                        ? rule.description
                        : 'قاعدة أتمتة تحتوي على إشعارات',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        rule.enabled ? Icons.play_circle : Icons.pause_circle,
                        size: 16,
                        color: rule.enabled ? Colors.green : Colors.orange,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        rule.enabled ? 'نشطة' : 'معطلة',
                        style: TextStyle(
                          fontSize: 12,
                          color: rule.enabled ? Colors.green : Colors.orange,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              value: isEnabled,
              onChanged: (value) {
                notificationService.updateAutomationRuleNotification(
                  rule.id,
                  value,
                );

                // إظهار رسالة تأكيد
                Get.snackbar(
                  value ? 'تم تفعيل الإشعارات' : 'تم إيقاف الإشعارات',
                  '${rule.name}: ${value ? 'سيتم إرسال إشعارات' : 'لن يتم إرسال إشعارات'}',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: value ? Colors.green : Colors.orange,
                  colorText: Colors.white,
                  duration: const Duration(seconds: 2),
                );
              },
              activeColor: Colors.purple,
              secondary: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.smart_toy,
                  color: Colors.purple,
                  size: 20,
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildStatisticsCard(NotificationService notificationService) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.notifications_active, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'الإشعارات النشطة',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'عدد الإشعارات النشطة: ${notificationService.activeNotifications.length}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 8),
            Text(
              'قواعد الأتمتة المفعلة: ${notificationService.automationRuleNotifications.values.where((enabled) => enabled).length}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  notificationService.clearAllNotifications();
                  Get.snackbar(
                    'تم',
                    'تم مسح جميع الإشعارات',
                    snackPosition: SnackPosition.BOTTOM,
                  );
                },
                icon: const Icon(Icons.clear_all),
                label: const Text('مسح جميع الإشعارات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[400],
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على إعداد الإشعارات العام للأتمتة
  bool _getGlobalAutomationNotificationSetting(
    NotificationService notificationService,
    AdvancedAutomationService automationService,
  ) {
    final automationNotifications =
        notificationService.automationRuleNotifications;
    if (automationNotifications.isEmpty) return true;

    // فحص ما إذا كانت معظم القواعد مفعلة
    final enabledCount =
        automationNotifications.values.where((enabled) => enabled).length;
    return enabledCount > automationNotifications.length / 2;
  }

  /// تحديث إعداد الإشعارات العام للأتمتة
  void _updateGlobalAutomationNotificationSetting(
    NotificationService notificationService,
    AdvancedAutomationService automationService,
    bool enabled,
  ) {
    // تحديث جميع قواعد الأتمتة
    for (var rule in automationService.automationRules) {
      final hasNotificationAction = rule.actions.any((action) =>
          action.type.toString().contains('notification') ||
          action.type.toString().contains('send_notification'));

      if (hasNotificationAction) {
        notificationService.updateAutomationRuleNotification(rule.id, enabled);
      }
    }

    // إظهار رسالة تأكيد
    Get.snackbar(
      enabled ? 'تم تفعيل الإشعارات' : 'تم إيقاف الإشعارات',
      enabled
          ? 'سيتم إرسال إشعارات عند تنفيذ قواعد الأتمتة'
          : 'لن يتم إرسال إشعارات عند تنفيذ قواعد الأتمتة',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: enabled ? Colors.green : Colors.orange,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }
}
