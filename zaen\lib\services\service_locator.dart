import 'package:get/get.dart';
import 'notification_service.dart';
import 'advanced_automation_service.dart';

/// تهيئة وتسجيل جميع الخدمات
class ServiceLocator {
  static Future<void> init() async {
    print('🔧 بدء تهيئة الخدمات...');

    // تسجيل خدمة الإشعارات وتهيئتها
    final notificationService = NotificationService();
    await notificationService.onInit(); // تهيئة يدوية
    Get.put<NotificationService>(notificationService, permanent: true);

    // تسجيل خدمة الأتمتة المتقدمة
    Get.put<AdvancedAutomationService>(AdvancedAutomationService(),
        permanent: true);

    print('✅ تم تسجيل وتهيئة جميع الخدمات بنجاح');
  }
}
