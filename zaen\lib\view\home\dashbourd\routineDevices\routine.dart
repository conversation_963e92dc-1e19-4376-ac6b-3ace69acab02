import 'dart:convert';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:get/get.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:percent_indicator/percent_indicator.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/alarm.dart' as alarm;
import 'package:zaen/modules/local/alarm.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/shared/commands/ac.dart';
import 'package:zaen/shared/commands/home.dart';
import 'package:zaen/shared/commands/room.dart';
import 'package:zaen/shared/commands/tv.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/view/home/<USER>/routineDevices/addRoutineDevice.dart';
import 'package:zaen/view/home/<USER>/routineDevices/routine_variables.dart';
import 'package:zaen/view/home/<USER>/routineDevices/mainRoutine.dart';
import 'package:zaen/view/home/<USER>/roomPage.dart';
import 'package:zaen/view/home/<USER>/setting.dart';
import 'package:zaen/shared/themes/app_colors.dart';

HomeController controller = Get.find();
int p = 1;
bool add = true;
bool isSetting = false;
bool isTask = false;
bool isWords = false;
bool isShortcut = false;
String myId = '';
PageController pageController = PageController(initialPage: p);
Map roomData = {};
Map device = {};
String routineIcon = '1';
TextEditingController name1 = TextEditingController();
TextEditingController name2 = TextEditingController();

// متغيرات المهام المجدولة

// متغيرات للتحكم في الحركة
Map<String, bool> _isRotating = {};
Map<String, AnimationController?> _rotationControllers = {};

Widget routine(context) {
  return GetBuilder<HomeController>(
    builder: (controller) => SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          CircularPercentIndicator(
            radius: controller.sized * 0.025,
            lineWidth: controller.sized * 0.008,
            backgroundWidth: controller.sized * 0.0015,
            backgroundColor: AppColors.backgroundColor.withOpacity(0.3),
            footer: Text(
              'اضافة إختصار',
              textAlign: TextAlign.justify,
              textDirection: TextDirection.rtl,
              style: TextStyle(
                  color: AppColors.backgroundColor.withOpacity(0.55),
                  fontSize: controller.sized * 0.010,
                  fontWeight: FontWeight.bold),
            ),
            center: IconButton(
                color: AppColors.backgroundColor.withOpacity(0.5),
                iconSize: controller.sized * 0.03,
                onPressed: () {
                  // التحقق من صلاحية إضافة الروتينات
                  if (!controller.canControlDevices()) {
                    showNoPermissionDialog(
                        customMessage: 'ليس لديك صلاحية لإضافة الروتينات');
                    return;
                  }

                  isSetting = false;
                  isShortcut = true;
                  isTask = false;
                  isWords = false;
                  add = true;
                  myId = '';
                  addRoutine(context: context);
                },
                icon: Icon(Icons.add_circle)),
          ),
          for (var r in controller.routines)
            Padding(
              padding: EdgeInsets.only(left: controller.sizedWidth * 0.04),
              child: GestureDetector(
                onLongPressEnd: (details) {
                  isSetting = false;
                  isShortcut = true;
                  isTask = false;
                  isWords = false;
                  add = false;
                  myId = r['id'].toString();
                  addRoutine(
                    context: context,
                    myRoutine: r['routine'],
                    myIcon: r['icon'],
                    myName: r['name'],
                  );
                },
                child: StatefulBuilder(
                  builder: (context, setStateLocal) {
                    return TweenAnimationBuilder<double>(
                      duration: const Duration(milliseconds: 600),
                      tween: Tween<double>(
                        begin: 0.0,
                        end: (_isRotating[r['id'].toString()] ?? false)
                            ? 1.0
                            : 0.0,
                      ),
                      builder: (context, rotationValue, child) {
                        return CircularPercentIndicator(
                          percent: 1.0,
                          animation: true,
                          restartAnimation: false,
                          radius: controller.sized * 0.03,
                          lineWidth: controller.sized * 0.003,
                          backgroundWidth: 0,
                          backgroundColor: Colors.transparent,
                          // تدريج لوني جديد مع ألوان Instagram story مع دوران
                          linearGradient: LinearGradient(
                            colors: const [
                              Color(0xFF833AB4), // بنفسجي
                              Color(0xFFE1306C), // وردي
                              Color(0xFFFD1D1D), // أحمر
                              Color(0xFFF77737), // برتقالي
                              Color(0xFFFCAF45), // أصفر ذهبي
                            ],
                            // تدوير التدريج بناءً على قيمة الحركة
                            transform:
                                GradientRotation(rotationValue * 2 * 3.14159),
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          footer: Container(
                            width: controller.sizedWidth * 0.2,
                            child: Center(
                              child: RichText(
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                text: TextSpan(
                                  style: TextStyle(
                                      color: AppColors.backgroundColor
                                          .withOpacity(0.55),
                                      fontSize: controller.sized * 0.010,
                                      fontWeight: FontWeight.bold),
                                  text: r['name'] == 'None'
                                      ? 'اختصار ${r['id']}'
                                      : r['name'],
                                ),
                                textAlign: TextAlign.justify,
                                textDirection: TextDirection.rtl,
                              ),
                            ),
                          ),
                          center: IconButton(
                              color:
                                  AppColors.backgroundColor2.withOpacity(0.65),
                              iconSize: controller.sized * 0.03,
                              onPressed: () {
                                // تفعيل الحركة عند الضغط
                                setStateLocal(() {
                                  _isRotating[r['id'].toString()] = true;
                                });

                                // إيقاف الحركة بعد ثانية واحدة
                                Future.delayed(
                                    const Duration(milliseconds: 1000), () {
                                  setStateLocal(() {
                                    _isRotating[r['id'].toString()] = false;
                                  });
                                });

                                print(r);
                                Map status = jsonDecode(r['routine']);
                                var topic;
                                var command;
                                if (status.containsKey('home')) {
                                  command = status['home'];
                                  // التحقق من صلاحية التحكم في أجهزة المنزل
                                  if (controller.canControlDevices()) {
                                    commandHome(command);
                                  }
                                } else {
                                  for (var r in status.keys.toList()) {
                                    if (status[r] == true ||
                                        status[r] == false) {
                                      command = status[r];
                                      // التحقق من صلاحية التحكم في أجهزة الغرف
                                      if (controller.canControlDevices()) {
                                        commandRoom(command, r);
                                      }
                                    } else {
                                      for (var d in status[r].keys.toList()) {
                                        roomId = r;
                                        if (controller.devices[d]) {
                                          if (status[r][d]
                                              .keys
                                              .toList()
                                              .contains('degree')) {
                                            command = status[r][d]['state'];

                                            status[r][d]['id'] = d;
                                            commandAc(command, status[r][d], r);
                                          } else if (status[r][d]
                                              .keys
                                              .toList()
                                              .contains('ch')) {
                                            print(status[r][d]);
                                            if (status[r][d]['ch'] != null) {
                                              if (status[r][d]['ch']
                                                      .contains('+') ||
                                                  status[r][d]['ch']
                                                      .contains('-')) {
                                                command = status[r][d]['ch']
                                                    .toString()
                                                    .replaceFirst(
                                                        status[r][d]['ch']
                                                            .toString()[0],
                                                        'CH ' +
                                                            status[r][d]['ch']
                                                                .toString()[0] +
                                                            ' ');
                                              } else {
                                                command = 'CH = ' +
                                                    status[r][d]['ch'];
                                              }
                                              Map tv = {
                                                'device': 'TV',
                                                'id': d,
                                                'priv': 'x',
                                                'state': true,
                                                'sil': false,
                                                'ch': {}
                                              };
                                              commandTvRemote(command, tv, r);
                                            }
                                            if (status[r][d]['v'] != null) {
                                              if (status[r][d]['v'] == true) {
                                                command = 'SIL-ON';
                                              } else if (status[r][d]['v'] ==
                                                  false) {
                                                command = 'SIL-OFF';
                                              } else {
                                                command = status[r][d]['v']
                                                    .toString()
                                                    .replaceFirst(
                                                        status[r][d]['v']
                                                            .toString()[0],
                                                        'VOICE ' +
                                                            status[r][d]['v']
                                                                .toString()[0] +
                                                            ' ');
                                              }
                                              Map tv = {
                                                'device': 'TV',
                                                'id': d,
                                                'priv': 'x',
                                                'state': true,
                                                'sil':
                                                    status[r][d]['v'] == true ||
                                                            status[r][d]['v'] ==
                                                                false
                                                        ? status[r][d]['v']
                                                        : null,
                                                'ch': {}
                                              };
                                              commandTvRemote(command, tv, r);
                                            }
                                            if (status[r][d]['v'] == null &&
                                                status[r][d]['ch'] == null) {
                                              Map tv = {
                                                'device': 'TV',
                                                'id': d,
                                                'priv': 'x',
                                                'state': status[r][d]['state'],
                                                'sil': null,
                                                'ch': {}
                                              };
                                              commandTvSw(
                                                  status[r][d]['state'], tv, r);
                                            }
                                          } else {
                                            // topic.add('Rooms/$r/SWITCH%/$d');
                                            final builder =
                                                MqttClientPayloadBuilder();

                                            String pubMassege = d + ' SWITCH ';

                                            for (var v
                                                in status[r][d].keys.toList()) {
                                              if (status[r][d][v] != null) {
                                                if (status[r][d][v] == true) {
                                                  pubMassege += v + '_RUN ';
                                                } else {
                                                  pubMassege += v + '_OFF ';
                                                }
                                              }
                                            }
                                            // pubMassege += 'XX:XX:XX:XX:XX:XX';
                                            builder.addString(pubMassege);

                                            client.publishMessage(
                                                controller.homeId + "/app/zain",
                                                MqttQos.atLeastOnce,
                                                builder.payload!);
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                                print(topic);
                                print(command);
                              },
                              icon: Icon(routineIcons[r['icon']])),
                        );
                      },
                    );
                  },
                ),
              ),
            )
        ],
      ),
    ),
  );
}

addRoutine({
  context,
  String? myRoutine,
  String? myIcon,
  String? myName,
}) async {
  name1 = TextEditingController();
  name2 = TextEditingController();
  routineIcon = '1';
  isScheduler = false;

  isAM = false;
  h = DateTime.now().hour;
  if (h > 12) {
    h = h - 12;
    isAM = false;
  } else {
    isAM = true;
  }
  m = DateTime.now().minute;
  days = [];
  re = false;
  allday = false;
  if (isSetting) {
    p = 4;
    controller.addRoutine = {};
    pageController = PageController(initialPage: p);
  } else if (add) {
    p = 1;
    controller.addRoutine = {};
    pageController = PageController(initialPage: p);
  } else {
    p = 0;

    print('Loading routine data...');
    controller.addRoutine = json.decode(myRoutine!);
    pageController = PageController(initialPage: p);
    if (myName != 'None') {
      name1.text = myName!;
    }
    routineIcon = myIcon!;
    print(name1.text);
    print(routineIcon);

    print('Routine data loaded successfully');
  }

  Map rooms = {};
  rooms = controller.rooms;
  roomState = null;
  deviceState = null;
  typeState = null;
  degree = null;
  speedState = null;
  swingState = null;
  sw = {};

  bool edit = false;

  bool homeState = controller.homeState != null ? controller.homeState : false;
  showAnimatedBottomSheet(
    context: context,
    enableDrag: true,
    content: Directionality(
      textDirection: TextDirection.rtl,
      child: StatefulBuilder(builder: ((context, setState) {
        return Padding(
          padding:
              EdgeInsets.symmetric(horizontal: controller.sizedWidth * 0.05),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              isSetting && p == 4
                  ? Container()
                  : p > 1
                      ? Row(
                          children: [
                            IconButton(
                                padding: EdgeInsets.zero,
                                onPressed: () {
                                  setState(
                                    () {
                                      p = p - 1;
                                      pageController.previousPage(
                                          duration: Duration(milliseconds: 350),
                                          curve: Curves.slowMiddle);
                                    },
                                  );
                                },
                                icon: iconStyle(
                                    icon: Icons.arrow_back_ios,
                                    size: controller.sized * 0.025,
                                    color: AppColors.warningColor)),
                            Expanded(
                                child: txtStyle(
                              txt: 'عمل روتين تحكم',
                            )),
                            Container(
                              width: controller.sizedWidth * 0.13,
                            )
                          ],
                        )
                      : p == 0
                          ? Directionality(
                              textDirection: TextDirection.ltr,
                              child: Row(
                                children: [
                                  IconButton(
                                      padding: EdgeInsets.zero,
                                      onPressed: () {
                                        setState(
                                          () {
                                            p = 1;
                                            pageController.nextPage(
                                                duration:
                                                    Duration(milliseconds: 350),
                                                curve: Curves.slowMiddle);
                                          },
                                        );
                                      },
                                      icon: iconStyle(
                                          icon: Icons.arrow_back_ios,
                                          size: controller.sized * 0.025,
                                          color: AppColors.warningColor)),
                                  Expanded(
                                    child: txtStyle(
                                      txt: 'عمل روتين تحكم',
                                    ),
                                  ),
                                  isSetting
                                      ? Transform.rotate(
                                          angle: 45 * 3.1415926535 / 180,
                                          child: IconButton(
                                              padding: EdgeInsets.zero,
                                              onPressed: () async {
                                                setState(
                                                  () {
                                                    p = 4;
                                                  },
                                                );
                                                await 0.2.delay();
                                                pageController.jumpToPage(p);
                                              },
                                              icon: iconStyle(
                                                  icon:
                                                      Icons.add_circle_rounded,
                                                  size: controller.sized * 0.03,
                                                  color: AppColors.textColor2
                                                      .withOpacity(0.5))),
                                        )
                                      : Container(
                                          width: controller.sizedWidth * 0.13,
                                        )
                                ],
                              ),
                            )
                          : Row(
                              children: [
                                isSetting
                                    ? Transform.rotate(
                                        angle: 45 * 3.1415926535 / 180,
                                        child: IconButton(
                                            padding: EdgeInsets.zero,
                                            onPressed: () async {
                                              setState(
                                                () {
                                                  p = 4;
                                                },
                                              );
                                              await 0.2.delay();
                                              pageController.jumpToPage(p);
                                            },
                                            icon: iconStyle(
                                                icon: Icons.add_circle_rounded,
                                                size: controller.sized * 0.03,
                                                color: AppColors.textColor2
                                                    .withOpacity(0.5))),
                                      )
                                    : Container(
                                        width: controller.sizedWidth * 0.13,
                                      ),
                                Expanded(
                                    child: txtStyle(
                                  txt: 'عمل روتين تحكم',
                                )),
                                Container(
                                  width: controller.sizedWidth * 0.13,
                                )
                              ],
                            ),
              Expanded(
                child: PageView(
                  scrollDirection: Axis.horizontal,
                  controller: pageController,
                  physics: NeverScrollableScrollPhysics(),
                  children: [
                    mainRoutine(
                      context: context,
                      setState: setState,
                      edit: edit,
                    ),
                    Column(
                      children: [
                        SizedBox(
                          height: controller.sizedHight * 0.015,
                        ),
                        containerPageOption(
                          content: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              txtStyle(
                                  txt: controller.addRoutine != {} &&
                                          controller.addRoutine
                                              .containsKey('home')
                                      ? "${controller.addRoutine['home'] ? 'تشغيل' : 'اغلاق'} ${controller.home}"
                                      : '${homeState ? 'تشغيل' : 'اغلاق'} ${controller.home}',
                                  size: controller.sized * 0.012,
                                  color:
                                      AppColors.textColor2.withOpacity(0.65)),
                              Expanded(
                                child: switchStyle(
                                    size: controller.sized * 0.0011,
                                    value: controller.addRoutine != {} &&
                                            controller.addRoutine
                                                .containsKey('home')
                                        ? controller.addRoutine['home']
                                        : homeState,
                                    onChanged: (s) {
                                      setState(() {
                                        homeState = s!;

                                        if (controller.addRoutine
                                            .containsKey('home')) {
                                          controller.addRoutine['home'] =
                                              homeState;
                                        }
                                      });
                                    }),
                              ),
                              IconButton(
                                  onPressed: () {
                                    if (controller.addRoutine
                                            .containsKey('home') ==
                                        false) {
                                      controller.addRoutine = {};
                                      controller.addRoutine['home'] = homeState;
                                    } else {
                                      controller.addRoutine = {};
                                    }
                                    setState(
                                      () {
                                        controller.addRoutine;
                                      },
                                    );
                                  },
                                  iconSize: controller.sized * 0.03,
                                  color:
                                      controller.addRoutine.containsKey('home')
                                          ? AppColors.primaryColor
                                          : Colors.amber,
                                  icon: iconStyle(
                                    icon: controller.addRoutine
                                            .containsKey('home')
                                        ? Icons.check_circle_rounded
                                        : Icons.add_circle_outline_rounded,
                                    color: controller.addRoutine
                                            .containsKey('home')
                                        ? AppColors.primaryColor
                                        : AppColors.warningColor,
                                  ))
                            ],
                          ),
                        ),
                        SizedBox(
                          height: controller.sizedHight * 0.01,
                        ),
                        Container(
                          margin: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02,
                              vertical: controller.sizedHight * 0.01),
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02,
                              vertical: controller.sizedHight * 0.02),
                          decoration: BoxDecoration(
                              color: AppColors.textColor2.withOpacity(0.05),
                              borderRadius: BorderRadius.circular(17)),
                          height: controller.sizedHight * 0.53,
                          child: ListView(
                            shrinkWrap: true,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    ' الغرف',
                                    textAlign: TextAlign.justify,
                                    textDirection: TextDirection.rtl,
                                    style: TextStyle(
                                        color: AppColors.textColor2
                                            .withOpacity(0.65),
                                        fontSize: controller.sized * 0.011,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                              for (dynamic i in rooms.values)
                                if (i['devices'].isNotEmpty)
                                  if (i['devices'].length == 1 &&
                                      i['devices'][i['devices']
                                                  .keys
                                                  .toList()[0]]['device']
                                              .contains('ZAIN') !=
                                          false)
                                    Container()
                                  else
                                    GestureDetector(
                                      onTap: () {
                                        setState(
                                          () {
                                            roomData = i;
                                            device = i['devices']
                                                [i['devices'].keys.toList()[0]];
                                            if (roomData['state'] == null) {
                                              roomData['state'] = false;
                                            }
                                            if (device['state'] == null) {
                                              device['state'] = false;
                                            }
                                            p = 2;
                                            pageController.nextPage(
                                                duration:
                                                    Duration(milliseconds: 350),
                                                curve: Curves.slowMiddle);
                                          },
                                        );
                                      },
                                      child: Container(
                                        margin: EdgeInsets.symmetric(
                                            horizontal:
                                                controller.sizedWidth * 0.04,
                                            vertical:
                                                controller.sizedHight * 0.01),
                                        decoration: BoxDecoration(
                                          borderRadius: const BorderRadius.all(
                                              Radius.circular(25)),
                                        ),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: Container(
                                                padding: EdgeInsets.only(
                                                    right:
                                                        controller.sizedWidth *
                                                            0.03,
                                                    top: controller.sizedHight *
                                                        0.005),
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      i['privName']! == 'x'
                                                          ? 'لا يوجد اسم'
                                                          : i['privName'],
                                                      textDirection:
                                                          TextDirection.rtl,
                                                      style: TextStyle(
                                                          color: AppColors
                                                              .textColor2
                                                              .withOpacity(0.7),
                                                          fontSize:
                                                              controller.sized *
                                                                  0.02,
                                                          fontWeight:
                                                              FontWeight.bold),
                                                    ),
                                                    Text(
                                                      i['pubName'],
                                                      textDirection:
                                                          TextDirection.rtl,
                                                      style: TextStyle(
                                                          color: AppColors
                                                              .textColor2
                                                              .withOpacity(
                                                                  0.55),
                                                          fontSize:
                                                              controller.sized *
                                                                  0.011,
                                                          fontWeight:
                                                              FontWeight.bold),
                                                    ),
                                                    Divider(
                                                      color: AppColors
                                                          .textColor2
                                                          .withOpacity(0.5),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Icon(
                                              Icons.menu_open_rounded,
                                              size: controller.sized * 0.025,
                                              color: AppColors.textColor2,
                                            ),
                                          ],
                                        ),
                                      ),
                                    )
                            ],
                          ),
                        ),
                        controller.addRoutine.isNotEmpty
                            ? submitButtom(
                                onPressed: () async {
                                  setState(
                                    () {
                                      p = 0;
                                      // if (myName != null &&
                                      //     myName != 'None') {
                                      //   name1 = TextEditingController(
                                      //       text: myName);
                                      // } else {
                                      //   name1 = TextEditingController();
                                      // }
                                      edit = false;

                                      pageController.previousPage(
                                          duration: Duration(milliseconds: 350),
                                          curve: Curves.slowMiddle);
                                    },
                                  );
                                },
                              )
                            : Container(),
                      ],
                    ),
                    roomData.isNotEmpty
                        ? Room(setState1: setState)
                        : Container(),
                    device.isNotEmpty
                        ? device['device'] == 'AC'
                            ? addRoutineAc(
                                device: device,
                                roomData: roomData,
                                sized: controller.sized,
                                sizedHeight: controller.sizedHight,
                                sizedWidth: controller.sizedWidth,
                                setState1: setState,
                              )
                            : device['device'] == 'SWITCH'
                                ? addRoutineSw(
                                    device: device,
                                    roomData: roomData,
                                    setState1: setState,
                                    sizedWidth: controller.sizedWidth,
                                    sizedHeight: controller.sizedHight,
                                    sized: controller.sized)
                                : device['device'] == 'TV'
                                    ? addRoutineTv(
                                        device: device,
                                        roomData: roomData,
                                        setState1: setState,
                                        sizedWidth: controller.sizedWidth,
                                        sizedHeight: controller.sizedHight,
                                        sized: controller.sized)
                                    : Container()
                        : Container(),
                    setting(context: context, setState: setState)
                  ],
                ),
              ),
            ],
          ),
        );
      })),
    ),
  );
}
