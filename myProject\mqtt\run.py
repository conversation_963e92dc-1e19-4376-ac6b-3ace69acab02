#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل نظام البيت الذكي زين
تم تقسيم الكود الأصلي إلى ملفات منفصلة لسهولة الصيانة والتطوير
"""

import sys
import os
import signal
import time
import threading
import paho.mqtt.client as mqtt
from flask import Flask

# إضافة المسارات المطلوبة
sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/nlt')
sys.path.append('/home/<USER>/myProject/modules')
sys.path.append('/home/<USER>/myProject/mqtt')

# استيراد المكتبات المحلية
import static as st
from mqtt_handlers import MQTTHandlers
from homebridge_manager import HomebridgeManager
from http_handlers import HTTPHandlers

# استيراد نظام التكامل مع Home Assistant
try:
    from ha_service import HAService
    from automation_engine import AutomationEngine
    HA_INTEGRATION_AVAILABLE = True
    print("✅ تم تحميل نظام التكامل مع Home Assistant")
except ImportError as e:
    print(f"⚠️ نظام التكامل مع Home Assistant غير متاح: {e}")
    HA_INTEGRATION_AVAILABLE = False

class SmartHomeSystem:
    def __init__(self):
        self.room = ''
        self.re = []
        self.listdevices = {}
        self.mqtt_handlers = MQTTHandlers(self)
        self.homebridge_manager = HomebridgeManager(self)
        self.http_handlers = HTTPHandlers()
        self.ha_service = None
        self.automation_engine = None
        self.app = Flask(__name__)
        self.setup_http_routes()
        
    def setup_http_routes(self):
        """إعداد مسارات HTTP"""
        
        @self.app.route('/wifi', methods=['POST'])
        def handle_wifi():
            return self.http_handlers.handle_wifi_connection()

        @self.app.route('/status', methods=['GET'])
        def handle_status():
            return self.http_handlers.handle_system_status()

        @self.app.route('/devices', methods=['GET'])
        def handle_get_devices():
            return self.http_handlers.handle_get_devices()

        @self.app.route('/devices/<device_id>', methods=['GET'])
        def handle_get_device_details(device_id):
            return self.http_handlers.handle_get_device_details(device_id)

        @self.app.route('/control', methods=['POST'])
        def handle_device_control():
            return self.http_handlers.handle_device_control()

        @self.app.route('/health', methods=['GET'])
        def handle_health_check():
            return self.http_handlers.handle_health_check()

        # مسارات Home Assistant
        @self.app.route('/ha/weather', methods=['GET'])
        def handle_ha_weather():
            if self.ha_service:
                try:
                    weather_data = self.ha_service.get_weather_data()
                    return {"status": "success", "data": weather_data}
                except Exception as e:
                    return {"status": "error", "message": str(e)}, 500
            else:
                return {"status": "error", "message": "خدمة Home Assistant غير متاحة"}, 503
        @self.app.route('/ha/entities', methods=['GET'])
        def handle_ha_entities():
            """معالجة طلب كيانات Home Assistant"""
            if self.ha_service:
                try:
                    entities = self.ha_service.get_all_entities()
                    return {"status": "success", "data": entities}
                except Exception as e:
                    return {"status": "error", "message": str(e)}, 500
            else:
                return {"status": "error", "message": "خدمة Home Assistant غير متاحة"}, 503

        @self.app.route('/ha/automation/rules', methods=['GET'])
        def handle_ha_automation_rules():
            """معالجة طلب قواعد الأتمتة"""
            if self.ha_service:
                try:
                    rules = self.ha_service.get_automation_rules()
                    return {"status": "success", "data": rules}
                except Exception as e:
                    return {"status": "error", "message": str(e)}, 500
            else:
                return {"status": "error", "message": "خدمة Home Assistant غير متاحة"}, 503
        # ... (بقية المسارات بنفس النمط)

    def on_connect(self, client, userdata, flags, rc, properties):
        """معالج الاتصال بـ MQTT"""
        if not hasattr(self, '_connected_once'):
            print("Connected with result code " + str(rc))
            self._connected_once = True

        client.subscribe("input/#")
        client.subscribe(st.device_nb)
        client.subscribe('connect')
        client.subscribe(st.device_nb + '/app/zain')
        client.subscribe('XX:XX:XX:XX:XX:XX')
        client.subscribe("phone/ask")
        client.subscribe("phone/join")
        client.subscribe("phone/edit")
        client.subscribe("ha/weather/request")
        client.subscribe("ha/entities/request")
        client.subscribe("ha/automation/trigger")

        if not hasattr(self, '_subscriptions_logged'):
            print("MQTT subscriptions completed")
            self._subscriptions_logged = True

        homebridge_thread = threading.Thread(
            target=self.homebridge_manager.setup_homebridge,
            args=(client,)
        )
        homebridge_thread.daemon = True
        homebridge_thread.start()

        print("Homebridge setup started")

        if HA_INTEGRATION_AVAILABLE:
            try:
                print("🚀 بدء خدمات Home Assistant...")
                self.ha_service = HAService(shared_mqtt_client=client)
                self.automation_engine = AutomationEngine(shared_mqtt_client=client)

                ha_thread = threading.Thread(
                    target=self.ha_service.start_service,
                    name="HAService",
                    daemon=True
                )
                ha_thread.start()

                automation_thread = threading.Thread(
                    target=self.automation_engine.start_engine,
                    name="AutomationEngine",
                    daemon=True
                )
                automation_thread.start()

                print("✅ تم بدء خدمات Home Assistant")
            except Exception as e:
                print(f"❌ فشل في بدء خدمات Home Assistant: {e}")
        else:
            print("⚠️ خدمات Home Assistant غير متاحة")

    def on_message(self, client, userdata, msg, properties):
        """معالج الرسائل الواردة"""
        self.mqtt_handlers.handle_message(client, msg)

    def signal_handler(self, signum, frame):
        """معالج إشارات النظام للإغلاق الآمن"""
        print(f"\nReceived signal {signum}. Shutting down gracefully...")

        if self.ha_service and hasattr(self.ha_service, 'stop_service'):
            try:
                self.ha_service.stop_service()
                print("✅ تم إيقاف خدمة Home Assistant")
            except Exception as e:
                print(f"❌ خطأ في إيقاف خدمة Home Assistant: {e}")

        if self.automation_engine and hasattr(self.automation_engine, 'stop_engine'):
            try:
                self.automation_engine.stop_engine()
                print("✅ تم إيقاف محرك الأتمتة")
            except Exception as e:
                print(f"❌ خطأ في إيقاف محرك الأتمتة: {e}")

        os._exit(0)

    def run(self):
        """تشغيل النظام"""
        print("Starting Zain Smart Home System...")
        print("=" * 50)
        
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            client = mqtt.Client(callback_api_version=mqtt.CallbackAPIVersion.VERSION2)
            print("1. Setting up MQTT client...")
            client.on_connect = self.on_connect
            client.on_message = self.on_message
            
            print("2. Connecting to MQTT broker...")
            client.connect('zain', 1883, 20)
            
            print("3. Starting MQTT loop...")
            client.loop_start()
            
            print("4. Starting Flask server...")
            print("=" * 50)
            print("System is ready!")
            self.app.run(debug=False, host='zain', port=5000, threaded=True)
            
        except KeyboardInterrupt:
            print("\nShutdown requested by user")
            self.signal_handler(signal.SIGINT, None)
        except Exception as e:
            print(f"Error starting system: {e}")
            sys.exit(1)

if __name__ == "__main__":
    system = SmartHomeSystem()
    system.run()