#!/usr/bin/env bashio
# vim: ft=bash
# ==============================================================================
# Take down the S6 supervision tree when service fails
# s6-overlay docs: https://github.com/just-containers/s6-overlay
# ==============================================================================

if [[ "$1" -ne 0 ]] && [[ "$1" -ne 256 ]]; then
  bashio::log.warning "Halt add-on"
  exec /run/s6/basedir/bin/halt
fi

bashio::log.info "Service restart after closing"
