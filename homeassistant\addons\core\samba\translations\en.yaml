---
configuration:
  username:
    name: <PERSON>rna<PERSON>
    description: >-
      The username you would like to use to authenticate with the Samba server.
  password:
    name: Password
    description: >-
      The password that goes with the username configured for authentication.
  workgroup:
    name: Workgroup
    description: Change WORKGROUP to reflect your network needs.
  local_master:
    name: Local master
    description: Enable to try and become a local master browser on a subnet.
  enabled_shares:
    name: >-
      Enabled Shares - allowed values are:
      addons, addon_configs, backup, config, media, share, or ssl.
    description: >-
      List of file shares to make available.
      Adding a share requires typing its name to add it.
      The listed values are the only allowed values.
      The configuration cannot be saved if any non-allowed value is in the list.
  compatibility_mode:
    name: Enable Compatibility Mode
    description: >-
      Enable this to use old legacy Samba protocols on the Samba add-on.
  apple_compatibility_mode:
    name: Enable Compatibility Settings for Apple Devices
    description: >-
      Enable Samba configurations to improve interoperability with Apple
      devices. May cause issues with file systems that do not support xattr
      such as exFAT.
  veto_files:
    name: Veto Files
    description: List of files that are neither visible nor accessible.
  allow_hosts:
    name: Allowed Hosts
    description: List of hosts/networks allowed to access the shared folders.
