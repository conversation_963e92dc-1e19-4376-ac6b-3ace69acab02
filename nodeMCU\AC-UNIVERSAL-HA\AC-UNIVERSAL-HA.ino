#include <ESP8266WiFi.h>
#include <PubSubClient.h>
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include <String.h>
#include <ESP8266mDNS.h>
#include <ESP8266WebServer.h>
//#include <WebServer.h>
#include <ArduinoJson.h>
#include <Preferences.h>


const uint16_t kIrLed = 4;
IRsend irsend(kIrLed);
int led = 14;
int PIN_RESET_BUTTON= 12;
int RESET = 0;
const char* mqttServer="zain.local";    // IP adress Raspberry Pi
const int mqttPort = 1883;
//const char* mqttUser = "username";      // if you don't have MQTT Username, no need input
//const char* mqttPassword = "12345678";  // if you don't have MQTT Password, no need input
int co = 55;
char* lasttopic = "input/AC";
String code = "";
IPAddress ipaddress;
String ip;
String clientId;
char* response;
String mytopic;
String message;
char* topic1;
char* topic2;
char* topic3;
char* topic4;
String Topic1;
String Topic2;
String Topic3;
String Topic4;
String Response;
String key = "False";
String mytemp = "RUN 25 VAN 3 AC";

// ========== Home Assistant Integration Variables ==========
String currentState = "OFF 24 AUTO 3";  // Current AC state
String stateTopic = "";                 // State topic for this device
bool deviceRegistered = false;          // Registration status

static PROGMEM const uint16_t c16v3[147] = { 9112, 4588, 624, 656, 540, 688, 548, 676, 548, 1800, 548, 684, 552, 668, 544, 680, 548, 1800, 628, 1740, 544, 680, 544, 684, 540, 1804, 576, 1772, 572, 656, 548, 680, 548, 676, 548, 1816, 572, 652, 544, 684, 552, 672, 552, 680, 544, 680, 544, 680, 548, 676, 548, 696, 572, 656, 548, 676, 568, 656, 552, 676, 548, 676, 548, 680, 548, 676, 548, 692, 544, 684, 572, 652, 544, 680, 544, 688, 548, 672, 544, 684, 548, 676, 572, 668, 548, 680, 564, 660, 568, 660, 544, 1804, 572, 656, 548, 680, 544, 684, 544, 1816, 572, 656, 548, 680, 544, 1800, 568, 1780, 576, 1776, 568, 660, 544, 680, 568, 676, 548, 680, 548, 676, 548, 680, 564, 664, 540, 684, 544, 684, 540, 684, 572, 672, 576, 652, 572, 1776, 572, 1776, 580, 648, 544, 1804, 572, 656, 572, 656, 548};
static PROGMEM const uint16_t c17v3[147] = { 9108, 4592, 624, 656, 572, 656, 568, 656, 568, 1784, 564, 660, 564, 660, 568, 664, 548, 1796, 572, 672, 544, 1804, 552, 676, 568, 1780, 568, 1784, 572, 652, 572, 656, 572, 656, 568, 1796, 572, 656, 568, 660, 544, 680, 568, 660, 544, 680, 544, 680, 548, 680, 576, 668, 548, 676, 548, 680, 544, 684, 540, 684, 544, 684, 540, 688, 548, 676, 548, 696, 540, 684, 544, 684, 552, 676, 548, 676, 548, 680, 548, 676, 548, 680, 544, 700, 548, 676, 548, 680, 544, 684, 544, 1804, 572, 656, 568, 656, 572, 660, 544, 696, 548, 1800, 568, 660, 564, 1784, 576, 1776, 568, 1780, 568, 660, 576, 656, 548, 692, 572, 656, 540, 688, 548, 676, 568, 660, 568, 660, 544, 680, 544, 684, 572, 672, 544, 684, 544, 1804, 572, 1780, 564, 660, 568, 1784, 572, 656, 568, 660, 564};
static PROGMEM const uint16_t c18v3[147] = { 9108, 4600, 616, 660, 564, 664, 572, 652, 572, 1776, 572, 656, 568, 656, 572, 656, 548, 1800, 564, 1800, 568, 1780, 568, 660, 544, 1800, 568, 1780, 564, 660, 564, 664, 572, 656, 552, 1808, 568, 656, 568, 660, 564, 660, 544, 684, 544, 680, 544, 684, 540, 684, 544, 696, 552, 676, 548, 676, 548, 680, 544, 680, 568, 656, 548, 680, 544, 680, 548, 692, 544, 688, 548, 672, 540, 684, 544, 684, 540, 684, 544, 680, 544, 688, 548, 1812, 544, 680, 652, 576, 628, 600, 624, 1724, 620, 604, 624, 600, 572, 660, 596, 644, 540, 684, 540, 684, 628, 600, 540, 688, 548, 676, 540, 688, 548, 676, 548, 696, 540, 684, 544, 680, 628, 600, 540, 688, 540, 684, 552, 676, 620, 604, 548, 696, 540, 684, 544, 1804, 624, 1732, 540, 684, 544, 1800, 628, 600, 628, 596, 628};
static PROGMEM const uint16_t c19v3[147] = { 9108, 4592, 624, 656, 568, 656, 568, 660, 568, 1780, 564, 660, 564, 664, 564, 664, 540, 1804, 572, 672, 564, 660, 564, 1784, 572, 1776, 572, 1776, 568, 660, 568, 656, 568, 660, 564, 1800, 568, 660, 564, 660, 568, 660, 564, 660, 544, 684, 540, 684, 576, 652, 572, 668, 548, 680, 544, 680, 568, 660, 544, 684, 552, 672, 540, 684, 544, 684, 540, 700, 620, 608, 544, 680, 544, 684, 544, 684, 540, 684, 624, 600, 628, 604, 548, 1812, 648, 580, 624, 600, 624, 604, 624, 1724, 620, 604, 624, 604, 620, 604, 620, 1748, 548, 676, 624, 604, 620, 608, 544, 680, 544, 680, 620, 608, 544, 680, 544, 700, 548, 676, 548, 680, 548, 680, 544, 684, 540, 684, 544, 684, 552, 676, 600, 640, 544, 684, 540, 1804, 544, 1804, 624, 604, 624, 1724, 620, 604, 620, 608, 620};
static PROGMEM const uint16_t c20v3[147] = { 9108, 4588, 628, 652, 572, 652, 544, 684, 540, 1808, 548, 676, 540, 684, 552, 676, 548, 1800, 568, 1796, 572, 656, 568, 1776, 568, 1780, 568, 1780, 576, 652, 572, 656, 572, 652, 552, 1812, 576, 652, 572, 652, 544, 684, 552, 676, 548, 676, 548, 680, 548, 676, 568, 676, 540, 684, 540, 688, 572, 652, 552, 676, 548, 680, 544, 680, 548, 676, 548, 696, 540, 684, 544, 684, 548, 676, 552, 676, 548, 680, 544, 684, 544, 680, 564, 1804, 564, 660, 564, 664, 540, 684, 544, 1808, 568, 656, 568, 660, 568, 660, 544, 696, 548, 1800, 568, 660, 564, 664, 564, 660, 576, 652, 540, 684, 544, 684, 540, 704, 544, 680, 564, 664, 540, 688, 568, 656, 540, 684, 572, 656, 548, 680, 548, 692, 544, 684, 540, 1808, 568, 1780, 568, 664, 540, 1804, 572, 656, 572, 652, 572};
static PROGMEM const uint16_t c21v3[147] = { 9104, 4596, 616, 660, 568, 656, 568, 660, 564, 1784, 572, 652, 576, 652, 572, 652, 572, 1776, 572, 672, 564, 1784, 572, 1780, 544, 1800, 568, 1784, 572, 656, 548, 676, 572, 656, 548, 1816, 572, 656, 568, 656, 568, 660, 568, 656, 548, 680, 544, 688, 548, 672, 544, 700, 548, 676, 568, 660, 564, 660, 544, 684, 564, 660, 544, 684, 540, 684, 544, 700, 568, 656, 548, 680, 544, 680, 568, 660, 544, 680, 544, 688, 548, 672, 544, 1820, 568, 660, 564, 660, 576, 652, 540, 1816, 540, 680, 568, 656, 568, 652, 576, 676, 548, 1800, 620, 608, 544, 680, 616, 612, 624, 604, 540, 684, 552, 676, 548, 692, 544, 684, 540, 684, 628, 600, 624, 604, 548, 680, 544, 684, 604, 616, 548, 696, 552, 676, 548, 1804, 624, 1720, 628, 604, 548, 1800, 620, 612, 540, 680, 628};
static PROGMEM const uint16_t c22v3[147] = { 9104, 4596, 620, 660, 564, 660, 564, 660, 568, 1780, 576, 652, 572, 656, 548, 676, 572, 1776, 568, 1796, 572, 1776, 568, 1780, 568, 1780, 564, 1784, 572, 656, 572, 652, 572, 656, 568, 1792, 564, 664, 572, 652, 576, 652, 572, 652, 544, 684, 548, 676, 552, 672, 572, 672, 544, 680, 544, 684, 544, 680, 544, 676, 572, 656, 548, 680, 564, 660, 544, 696, 552, 676, 548, 676, 552, 676, 548, 676, 540, 684, 540, 684, 572, 656, 568, 1792, 568, 660, 544, 680, 564, 660, 568, 1780, 564, 664, 552, 672, 572, 652, 576, 1788, 568, 1780, 576, 652, 572, 652, 572, 656, 572, 652, 552, 676, 548, 676, 552, 696, 548, 672, 544, 684, 540, 684, 544, 680, 544, 684, 552, 672, 540, 688, 540, 704, 540, 684, 544, 1808, 548, 1796, 624, 600, 624, 1724, 620, 608, 544, 680, 620};
static PROGMEM const uint16_t c23v3[147] = { 9112, 4588, 616, 664, 564, 660, 564, 664, 572, 1776, 572, 652, 572, 656, 572, 652, 572, 1780, 564, 676, 572, 656, 568, 656, 572, 656, 548, 676, 548, 1804, 544, 680, 564, 664, 540, 1820, 568, 660, 568, 656, 568, 660, 564, 664, 572, 656, 548, 676, 572, 652, 540, 700, 548, 680, 548, 676, 548, 676, 548, 676, 548, 684, 544, 676, 548, 676, 548, 696, 540, 684, 544, 680, 544, 684, 540, 684, 544, 680, 544, 688, 536, 684, 544, 1820, 568, 656, 568, 656, 568, 652, 576, 1780, 564, 660, 568, 660, 544, 680, 564, 1800, 568, 660, 564, 1788, 548, 672, 564, 664, 564, 660, 576, 652, 540, 684, 540, 704, 544, 680, 576, 648, 548, 680, 544, 684, 544, 684, 548, 676, 540, 684, 544, 704, 540, 680, 544, 1804, 628, 1720, 628, 600, 624, 1724, 620, 608, 620, 604, 620};
static PROGMEM const uint16_t c24v3[147] = { 9112, 4588, 596, 684, 572, 656, 572, 652, 572, 1776, 568, 660, 568, 660, 544, 680, 564, 1784, 576, 1792, 572, 652, 576, 652, 572, 652, 552, 676, 548, 1800, 568, 660, 568, 656, 568, 1796, 572, 652, 572, 656, 548, 680, 544, 680, 548, 676, 568, 656, 572, 652, 552, 692, 544, 680, 544, 680, 544, 684, 544, 680, 564, 660, 568, 656, 568, 660, 544, 696, 572, 652, 540, 684, 544, 688, 548, 672, 540, 684, 628, 600, 540, 684, 540, 1824, 648, 576, 652, 572, 620, 608, 544, 1800, 652, 576, 616, 608, 660, 568, 544, 696, 552, 1796, 652, 1696, 652, 572, 652, 572, 620, 608, 544, 680, 620, 608, 544, 696, 540, 684, 540, 688, 540, 684, 540, 684, 624, 604, 540, 684, 540, 684, 628, 612, 548, 680, 548, 1796, 620, 1728, 620, 604, 620, 1728, 620, 604, 620, 608, 616};
static PROGMEM const uint16_t c25v3[147] = { 9112, 4588, 616, 660, 564, 660, 568, 660, 564, 1784, 572, 652, 572, 652, 576, 652, 560, 1784, 564, 676, 572, 1776, 568, 660, 564, 660, 568, 656, 548, 1800, 568, 660, 564, 660, 564, 1800, 568, 660, 544, 680, 568, 656, 568, 656, 548, 680, 544, 684, 544, 676, 568, 676, 540, 684, 540, 688, 548, 676, 540, 684, 544, 688, 548, 672, 540, 688, 572, 668, 544, 680, 548, 680, 564, 660, 544, 684, 564, 660, 544, 680, 548, 680, 544, 1816, 572, 656, 568, 660, 544, 680, 568, 1780, 544, 680, 568, 660, 564, 660, 564, 1800, 568, 1780, 564, 1784, 576, 648, 564, 664, 564, 660, 564, 664, 540, 684, 544, 696, 548, 680, 548, 676, 568, 660, 544, 680, 544, 684, 564, 660, 564, 660, 544, 700, 540, 684, 540, 1808, 568, 1776, 572, 656, 548, 1800, 568, 656, 568, 664, 552};
static PROGMEM const uint16_t c26v3[147] = { 9104, 4568, 648, 660, 564, 660, 648, 580, 616, 1732, 644, 580, 644, 580, 628, 604, 628, 1716, 652, 1712, 572, 1776, 572, 656, 548, 676, 620, 608, 544, 1800, 652, 576, 648, 576, 620, 1752, 552, 668, 652, 576, 616, 608, 544, 684, 544, 676, 620, 616, 600, 596, 568, 700, 544, 680, 548, 684, 624, 544, 596, 680, 620, 608, 544, 680, 544, 680, 548, 696, 624, 604, 548, 676, 548, 676, 548, 680, 548, 676, 548, 680, 548, 676, 548, 1816, 644, 584, 548, 676, 624, 604, 548, 1800, 576, 652, 540, 684, 616, 612, 624, 616, 548, 680, 544, 680, 548, 1804, 624, 600, 624, 604, 624, 600, 552, 676, 548, 696, 540, 684, 540, 684, 628, 600, 604, 624, 620, 604, 540, 688, 548, 676, 548, 692, 544, 684, 552, 1792, 628, 1720, 624, 604, 624, 1724, 620, 604, 624, 600, 636};
static PROGMEM const uint16_t c27v3[147] = { 9108, 4588, 596, 684, 656, 568, 624, 600, 544, 1804, 624, 604, 624, 600, 552, 676, 548, 1796, 656, 584, 620, 612, 552, 1788, 620, 608, 628, 596, 648, 1700, 628, 596, 616, 612, 616, 1748, 648, 576, 628, 600, 616, 608, 628, 596, 544, 684, 628, 596, 544, 680, 544, 696, 552, 676, 540, 684, 540, 684, 540, 684, 544, 684, 540, 684, 540, 688, 548, 692, 628, 596, 568, 660, 628, 596, 544, 680, 548, 680, 544, 680, 544, 680, 568, 1796, 548, 676, 624, 604, 620, 604, 552, 1796, 652, 572, 620, 592, 624, 620, 608, 1752, 624, 604, 620, 604, 548, 1800, 620, 612, 540, 680, 620, 604, 548, 680, 544, 696, 624, 604, 548, 676, 548, 676, 552, 676, 548, 676, 552, 672, 572, 656, 548, 692, 544, 684, 544, 1800, 620, 1728, 616, 608, 628, 1720, 624, 608, 596, 624, 572};
static PROGMEM const uint16_t c28v3[147] = { 9104, 4592, 616, 660, 564, 660, 564, 664, 572, 1776, 572, 652, 572, 652, 572, 660, 544, 1796, 572, 1796, 540, 684, 572, 1776, 572, 652, 572, 656, 548, 1800, 568, 656, 568, 660, 564, 1800, 568, 656, 572, 656, 568, 656, 548, 680, 544, 680, 548, 676, 548, 680, 544, 696, 540, 688, 540, 684, 552, 672, 548, 680, 548, 676, 552, 676, 548, 680, 544, 696, 544, 680, 544, 680, 544, 684, 552, 676, 548, 676, 540, 684, 540, 688, 540, 1824, 564, 660, 544, 680, 576, 652, 544, 1804, 572, 656, 548, 676, 572, 652, 572, 672, 544, 1804, 572, 652, 572, 1776, 572, 656, 568, 656, 572, 656, 548, 676, 572, 676, 548, 676, 548, 672, 544, 684, 540, 684, 544, 680, 544, 684, 540, 684, 544, 696, 548, 680, 548, 1796, 620, 1732, 576, 648, 628, 1720, 624, 604, 624, 600, 572};
static PROGMEM const uint16_t c29v3[147] = { 9104, 4592, 624, 652, 572, 656, 572, 652, 572, 1776, 572, 656, 568, 656, 568, 664, 540, 1804, 564, 680, 544, 1800, 568, 1788, 548, 672, 564, 660, 564, 1784, 572, 656, 572, 656, 568, 1792, 576, 652, 572, 656, 548, 676, 572, 652, 544, 684, 572, 660, 544, 676, 536, 704, 544, 684, 540, 684, 544, 680, 544, 680, 548, 668, 628, 608, 564, 660, 548, 692, 540, 688, 548, 676, 540, 684, 552, 680, 596, 624, 604, 624, 548, 680, 544, 1816, 624, 600, 628, 600, 624, 604, 548, 1796, 624, 608, 544, 676, 548, 676, 552, 1812, 628, 1720, 624, 604, 620, 1728, 548, 676, 620, 604, 568, 664, 544, 676, 548, 696, 540, 684, 552, 680, 596, 624, 540, 684, 540, 688, 548, 680, 548, 676, 548, 692, 544, 680, 544, 1812, 544, 1796, 624, 608, 544, 1800, 620, 604, 620, 608, 544};
static PROGMEM const uint16_t c30v3[147] = { 9108, 4592, 624, 656, 568, 656, 572, 656, 568, 1784, 540, 684, 572, 652, 576, 652, 540, 1808, 568, 1796, 572, 1776, 572, 1776, 568, 656, 568, 660, 568, 1772, 572, 660, 564, 664, 564, 1800, 564, 664, 564, 660, 564, 664, 572, 652, 572, 660, 544, 676, 540, 688, 548, 692, 544, 684, 572, 656, 572, 652, 540, 688, 548, 676, 548, 680, 548, 676, 548, 692, 544, 684, 540, 684, 544, 684, 552, 676, 548, 676, 548, 680, 548, 676, 548, 1816, 572, 652, 572, 656, 548, 680, 548, 1800, 564, 660, 568, 660, 544, 684, 572, 668, 548, 684, 552, 1788, 568, 1784, 572, 652, 572, 656, 552, 672, 552, 676, 548, 696, 552, 672, 572, 652, 544, 680, 544, 680, 568, 660, 572, 652, 544, 684, 540, 700, 548, 676, 548, 1800, 568, 1780, 564, 660, 568, 1780, 576, 652, 572, 652, 544};
static PROGMEM const uint16_t c31v3[147] = { 9108, 4596, 628, 652, 572, 656, 572, 652, 572, 1780, 544, 680, 568, 660, 544, 684, 572, 1776, 572, 668, 568, 660, 564, 664, 540, 1808, 548, 676, 572, 1776, 568, 660, 568, 656, 548, 1820, 548, 672, 572, 656, 572, 656, 548, 680, 544, 680, 548, 676, 548, 680, 544, 696, 540, 684, 572, 656, 548, 676, 552, 676, 548, 676, 548, 680, 568, 656, 548, 696, 552, 672, 544, 680, 576, 652, 540, 684, 544, 684, 540, 684, 540, 688, 540, 1820, 568, 660, 576, 652, 540, 688, 540, 1804, 572, 652, 572, 656, 548, 676, 552, 692, 544, 680, 544, 684, 572, 652, 544, 1804, 572, 656, 572, 652, 552, 676, 548, 692, 544, 684, 540, 684, 544, 688, 548, 676, 548, 676, 540, 684, 540, 684, 544, 700, 544, 680, 548, 1800, 564, 1784, 572, 656, 572, 1776, 568, 660, 568, 656, 548};
static PROGMEM const uint16_t c32v3[147] = { 9108, 4584, 620, 664, 548, 676, 572, 648, 568, 1784, 572, 648, 568, 660, 544, 684, 540, 1804, 572, 1792, 564, 660, 568, 656, 568, 1784, 540, 680, 568, 1784, 540, 684, 564, 660, 564, 1800, 568, 656, 568, 656, 572, 656, 568, 656, 548, 680, 544, 680, 548, 676, 548, 696, 548, 676, 552, 676, 548, 676, 552, 676, 548, 676, 548, 680, 548, 676, 548, 696, 572, 652, 572, 652, 544, 684, 540, 684, 540, 692, 544, 676, 552, 676, 548, 1812, 576, 652, 572, 656, 552, 672, 572, 1776, 572, 652, 572, 656, 568, 656, 572, 672, 572, 1776, 572, 652, 572, 652, 572, 1776, 572, 652, 572, 652, 576, 652, 572, 668, 548, 680, 544, 680, 544, 684, 544, 680, 580, 648, 572, 652, 544, 684, 540, 700, 568, 656, 568, 1780, 568, 1780, 564, 668, 548, 1796, 572, 652, 572, 660, 544};

static PROGMEM const uint16_t c16v2[147] = { 9104, 4596, 596, 680, 548, 672, 656, 568, 648, 1700, 572, 656, 640, 584, 644, 580, 624, 1724, 652, 1712, 644, 580, 644, 584, 644, 1700, 656, 1692, 652, 576, 648, 576, 652, 576, 544, 700, 544, 1800, 652, 572, 620, 608, 576, 652, 540, 684, 544, 680, 544, 680, 544, 696, 552, 672, 552, 676, 540, 684, 540, 688, 620, 604, 548, 676, 548, 676, 552, 692, 544, 680, 628, 596, 544, 680, 544, 688, 600, 620, 616, 608, 544, 684, 540, 1820, 652, 576, 620, 604, 620, 604, 620, 1728, 648, 576, 620, 604, 620, 608, 648, 592, 548, 676, 552, 676, 548, 676, 548, 680, 548, 1796, 620, 608, 544, 676, 624, 620, 616, 608, 544, 680, 544, 684, 540, 684, 544, 680, 628, 600, 624, 600, 540, 700, 548, 676, 548, 1800, 576, 1772, 628, 596, 576, 1772, 624, 604, 624, 600, 624};
static PROGMEM const uint16_t c17v2[147] = { 9100, 4608, 596, 680, 544, 680, 568, 656, 568, 1780, 568, 656, 568, 660, 564, 660, 564, 1788, 568, 672, 564, 1784, 572, 652, 576, 1772, 572, 1780, 564, 660, 576, 652, 564, 660, 544, 704, 540, 1804, 576, 652, 572, 652, 572, 656, 548, 680, 544, 680, 548, 680, 544, 696, 540, 684, 572, 660, 564, 660, 564, 660, 544, 684, 544, 684, 548, 672, 576, 668, 548, 676, 548, 680, 544, 680, 544, 684, 544, 680, 544, 684, 540, 684, 544, 1820, 576, 652, 572, 652, 572, 660, 544, 1800, 568, 660, 564, 660, 564, 664, 540, 1824, 564, 664, 540, 684, 572, 656, 572, 656, 548, 1800, 564, 660, 568, 660, 576, 668, 544, 680, 548, 680, 544, 680, 544, 684, 552, 676, 540, 684, 548, 680, 548, 696, 548, 680, 548, 1788, 576, 1780, 564, 664, 572, 1780, 568, 656, 568, 660, 576};
static PROGMEM const uint16_t c18v2[147] = { 9108, 4592, 604, 676, 548, 672, 576, 648, 564, 1788, 568, 656, 568, 656, 572, 660, 544, 1800, 564, 1792, 576, 1780, 564, 660, 564, 1784, 564, 1780, 576, 652, 572, 652, 572, 652, 544, 700, 544, 1804, 564, 660, 564, 660, 564, 660, 544, 688, 548, 672, 544, 680, 544, 696, 552, 676, 548, 676, 652, 576, 544, 680, 620, 604, 548, 680, 544, 680, 544, 696, 552, 676, 620, 604, 548, 676, 552, 676, 620, 604, 620, 608, 544, 680, 548, 1816, 568, 656, 644, 584, 548, 676, 548, 1800, 652, 572, 620, 608, 544, 680, 544, 700, 548, 1800, 576, 648, 648, 1700, 624, 600, 628, 1728, 544, 676, 620, 604, 548, 696, 540, 688, 548, 676, 540, 684, 552, 676, 548, 676, 548, 684, 544, 680, 544, 692, 544, 684, 540, 1808, 620, 1728, 564, 664, 624, 1724, 624, 600, 624, 604, 568};
static PROGMEM const uint16_t c19v2[147] = { 9104, 4592, 620, 660, 564, 664, 572, 652, 544, 1800, 576, 652, 540, 684, 544, 684, 540, 1808, 568, 672, 544, 680, 544, 1804, 572, 1776, 548, 1800, 568, 656, 568, 660, 544, 684, 540, 700, 548, 1796, 568, 660, 544, 680, 568, 660, 544, 680, 544, 680, 544, 684, 544, 696, 548, 680, 544, 680, 568, 656, 548, 680, 544, 680, 544, 684, 544, 680, 544, 700, 544, 680, 548, 664, 568, 668, 548, 680, 544, 680, 548, 680, 544, 680, 544, 1816, 572, 656, 548, 676, 548, 680, 544, 1800, 568, 660, 544, 680, 544, 684, 540, 1820, 568, 1780, 568, 656, 548, 1800, 564, 660, 544, 1800, 568, 664, 548, 672, 544, 700, 544, 680, 548, 676, 548, 680, 568, 656, 568, 656, 548, 676, 548, 676, 548, 696, 540, 684, 540, 1808, 572, 1772, 572, 660, 544, 1796, 572, 656, 548, 676, 568};
static PROGMEM const uint16_t c20v2[147] = { 9104, 4592, 620, 660, 568, 656, 568, 656, 548, 1800, 564, 660, 568, 660, 544, 680, 564, 1784, 560, 1804, 544, 680, 544, 1800, 564, 1784, 572, 1776, 572, 656, 568, 656, 568, 660, 544, 696, 552, 1796, 568, 660, 568, 656, 548, 680, 544, 680, 544, 680, 568, 660, 540, 704, 544, 680, 544, 680, 544, 684, 544, 684, 540, 684, 540, 684, 540, 684, 544, 700, 544, 680, 544, 684, 544, 680, 544, 680, 628, 600, 540, 684, 544, 684, 540, 1820, 620, 608, 616, 612, 624, 600, 540, 1808, 620, 608, 620, 604, 568, 660, 544, 696, 548, 680, 548, 1796, 620, 1728, 628, 600, 624, 1724, 624, 604, 620, 604, 620, 620, 544, 684, 552, 676, 548, 676, 548, 680, 544, 684, 544, 680, 564, 660, 544, 700, 548, 684, 560, 1780, 616, 1732, 624, 604, 548, 1804, 544, 676, 620, 608, 616};
static PROGMEM const uint16_t c21v2[147] = { 9104, 4600, 604, 676, 632, 588, 624, 600, 544, 1804, 656, 572, 548, 676, 600, 624, 548, 1804, 616, 624, 540, 1804, 624, 1728, 576, 1768, 564, 1784, 648, 580, 624, 600, 552, 676, 600, 640, 544, 1800, 628, 600, 624, 600, 540, 688, 540, 684, 540, 684, 540, 688, 548, 692, 544, 688, 548, 676, 568, 656, 624, 600, 572, 652, 552, 676, 548, 676, 548, 696, 540, 684, 540, 684, 544, 684, 540, 684, 540, 684, 540, 688, 540, 688, 568, 1792, 616, 608, 628, 600, 572, 652, 552, 1796, 620, 608, 544, 680, 548, 684, 604, 632, 548, 1800, 620, 1728, 544, 1804, 624, 604, 568, 1780, 620, 604, 548, 680, 564, 676, 548, 680, 548, 676, 548, 680, 544, 680, 544, 684, 540, 684, 564, 664, 552, 688, 600, 624, 548, 1800, 564, 1784, 624, 600, 572, 1776, 548, 680, 548, 680, 544};
static PROGMEM const uint16_t c22v2[147] = { 9108, 4600, 604, 672, 636, 588, 624, 600, 544, 1804, 624, 604, 548, 676, 548, 680, 544, 1800, 620, 1744, 624, 1728, 616, 1732, 552, 1796, 652, 1696, 620, 604, 548, 684, 572, 652, 540, 700, 548, 1800, 616, 612, 540, 688, 548, 676, 548, 680, 548, 676, 548, 680, 564, 676, 552, 676, 548, 676, 548, 680, 544, 680, 548, 680, 544, 680, 544, 684, 540, 700, 548, 676, 548, 680, 544, 684, 596, 628, 544, 680, 544, 684, 540, 684, 572, 1792, 564, 664, 540, 684, 540, 688, 548, 1804, 544, 684, 604, 616, 544, 684, 544, 1820, 564, 1784, 624, 1644, 700, 1676, 672, 608, 544, 1808, 608, 620, 544, 680, 544, 696, 552, 676, 548, 680, 544, 676, 548, 680, 548, 680, 544, 668, 576, 664, 540, 700, 548, 680, 544, 1804, 540, 1808, 572, 656, 548, 1800, 564, 664, 540, 688, 548};
static PROGMEM const uint16_t c23v2[147] = { 9108, 4600, 604, 676, 568, 652, 576, 652, 540, 1780, 596, 656, 548, 680, 544, 680, 548, 1748, 628, 668, 544, 684, 544, 680, 544, 684, 540, 684, 540, 1752, 628, 652, 552, 672, 552, 692, 544, 1748, 628, 652, 552, 676, 548, 680, 544, 684, 544, 680, 564, 664, 540, 696, 552, 676, 548, 680, 544, 680, 568, 656, 548, 684, 540, 684, 552, 668, 548, 696, 548, 684, 540, 680, 568, 656, 548, 680, 544, 680, 544, 680, 568, 660, 544, 1764, 624, 660, 544, 684, 540, 684, 540, 1752, 624, 656, 548, 676, 552, 676, 568, 672, 576, 652, 540, 684, 540, 688, 548, 1744, 624, 1724, 620, 660, 544, 684, 544, 700, 544, 680, 544, 684, 572, 652, 576, 656, 548, 672, 540, 688, 548, 676, 552, 692, 540, 688, 548, 1744, 624, 1724, 620, 660, 544, 1752, 624, 656, 572, 656, 548};
static PROGMEM const uint16_t c24v2[147] = { 9112, 4592, 600, 680, 544, 676, 624, 600, 552, 1800, 648, 576, 616, 608, 628, 600, 544, 1804, 624, 1740, 624, 600, 628, 600, 540, 684, 624, 604, 548, 1800, 620, 612, 548, 672, 544, 700, 620, 1728, 616, 612, 540, 684, 540, 692, 596, 624, 540, 684, 540, 688, 548, 692, 564, 660, 544, 684, 624, 604, 540, 688, 548, 672, 552, 676, 548, 680, 544, 696, 540, 688, 540, 684, 552, 672, 552, 676, 548, 680, 544, 684, 604, 620, 544, 1820, 544, 684, 616, 608, 628, 600, 540, 1808, 624, 604, 620, 604, 548, 680, 544, 1820, 620, 1728, 616, 612, 624, 600, 624, 1728, 628, 1720, 628, 600, 624, 600, 624, 620, 564, 664, 540, 684, 552, 676, 548, 676, 624, 604, 548, 680, 544, 680, 544, 700, 548, 676, 576, 1772, 620, 1728, 624, 592, 624, 1736, 620, 604, 620, 608, 616};
static PROGMEM const uint16_t c25v2[147] = { 9104, 4592, 600, 676, 572, 656, 548, 676, 548, 1800, 564, 660, 544, 684, 544, 680, 544, 1804, 572, 668, 548, 1800, 564, 664, 540, 684, 544, 680, 544, 1800, 576, 656, 548, 672, 544, 696, 548, 1800, 568, 656, 548, 680, 544, 684, 552, 668, 544, 672, 628, 612, 604, 632, 552, 676, 548, 676, 548, 676, 548, 676, 624, 604, 548, 680, 544, 676, 548, 696, 544, 684, 600, 620, 544, 688, 600, 616, 544, 688, 548, 672, 544, 680, 544, 1816, 624, 600, 552, 676, 548, 676, 540, 1804, 624, 600, 544, 684, 540, 684, 540, 700, 600, 624, 548, 1796, 620, 608, 596, 1744, 624, 1724, 620, 604, 568, 656, 552, 696, 600, 620, 544, 680, 544, 680, 544, 684, 540, 684, 544, 680, 544, 680, 544, 696, 540, 692, 596, 1740, 572, 1780, 544, 676, 540, 1804, 624, 604, 548, 676, 552};
static PROGMEM const uint16_t c26v2[147] = { 9096, 4588, 628, 660, 616, 608, 544, 684, 540, 1804, 628, 600, 540, 684, 552, 676, 548, 1800, 620, 1740, 572, 1784, 540, 684, 628, 596, 544, 680, 564, 1780, 620, 608, 544, 680, 616, 624, 552, 1792, 624, 604, 548, 676, 552, 672, 552, 676, 548, 676, 568, 660, 596, 644, 540, 684, 540, 688, 548, 676, 540, 684, 540, 684, 544, 684, 548, 676, 540, 700, 544, 684, 544, 680, 544, 680, 544, 684, 540, 684, 544, 680, 544, 680, 544, 1820, 620, 608, 616, 608, 544, 680, 544, 1804, 616, 612, 624, 604, 548, 680, 596, 640, 544, 1804, 540, 1804, 628, 600, 548, 1800, 620, 1728, 616, 608, 544, 684, 544, 696, 548, 680, 544, 680, 548, 676, 568, 660, 576, 648, 544, 688, 548, 676, 548, 688, 548, 676, 552, 1796, 548, 1800, 564, 664, 624, 1720, 576, 656, 548, 676, 568};
static PROGMEM const uint16_t c27v2[147] = { 9108, 4588, 628, 652, 552, 676, 548, 676, 548, 1800, 548, 680, 544, 680, 544, 680, 548, 1800, 576, 664, 548, 680, 548, 1804, 552, 668, 544, 684, 604, 1740, 544, 684, 540, 684, 540, 704, 544, 1800, 544, 692, 544, 676, 548, 676, 548, 680, 548, 676, 548, 680, 544, 696, 540, 688, 548, 676, 572, 656, 544, 680, 568, 656, 548, 680, 544, 680, 544, 700, 548, 676, 548, 680, 548, 676, 568, 660, 544, 684, 552, 672, 540, 688, 548, 1812, 564, 664, 540, 684, 544, 684, 552, 1792, 552, 676, 548, 680, 568, 656, 568, 1744, 624, 1728, 604, 1712, 624, 684, 572, 1724, 620, 1728, 596, 684, 544, 684, 572, 668, 544, 688, 540, 676, 548, 680, 544, 680, 544, 684, 540, 684, 544, 680, 544, 700, 544, 680, 568, 1724, 620, 1728, 628, 652, 544, 1748, 628, 652, 572, 656, 548};
static PROGMEM const uint16_t c28v2[147] = { 9108, 4588, 628, 652, 552, 676, 548, 676, 548, 1800, 548, 680, 544, 680, 544, 680, 548, 1800, 576, 664, 548, 680, 548, 1804, 552, 668, 544, 684, 604, 1740, 544, 684, 540, 684, 540, 704, 544, 1800, 544, 692, 544, 676, 548, 676, 548, 680, 548, 676, 548, 680, 544, 696, 540, 688, 548, 676, 572, 656, 544, 680, 568, 656, 548, 680, 544, 680, 544, 700, 548, 676, 548, 680, 548, 676, 568, 660, 544, 684, 552, 672, 540, 688, 548, 1812, 564, 664, 540, 684, 544, 684, 552, 1792, 552, 676, 548, 680, 568, 656, 568, 1744, 624, 1728, 604, 1712, 624, 684, 572, 1724, 620, 1728, 596, 684, 544, 684, 572, 668, 544, 688, 540, 676, 548, 680, 544, 680, 544, 684, 540, 684, 544, 680, 544, 700, 544, 680, 568, 1724, 620, 1728, 628, 652, 544, 1748, 628, 652, 572, 656, 548};
static PROGMEM const uint16_t c29v2[147] = { 9100, 4588, 604, 676, 552, 672, 552, 676, 568, 1696, 628, 676, 572, 656, 548, 680, 544, 1744, 600, 700, 548, 1716, 628, 1740, 604, 676, 552, 676, 568, 1724, 600, 680, 544, 680, 548, 692, 544, 1748, 596, 688, 548, 672, 572, 656, 540, 684, 540, 684, 552, 676, 548, 692, 544, 680, 544, 680, 544, 684, 544, 680, 544, 680, 544, 684, 572, 652, 540, 700, 548, 680, 544, 680, 544, 680, 548, 680, 544, 680, 544, 680, 544, 684, 544, 1816, 548, 680, 544, 684, 544, 680, 544, 1800, 544, 684, 540, 684, 572, 656, 572, 1788, 544, 684, 628, 596, 564, 1784, 604, 1688, 604, 1716, 628, 680, 568, 660, 544, 696, 548, 676, 552, 660, 576, 664, 548, 680, 544, 680, 548, 676, 548, 680, 544, 700, 548, 676, 548, 1796, 548, 1748, 628, 652, 544, 1804, 572, 656, 568, 656, 548};
static PROGMEM const uint16_t c30v2[147] = { 9100, 4592, 624, 660, 544, 680, 544, 680, 576, 1772, 544, 688, 544, 676, 540, 684, 540, 1808, 656, 1708, 624, 1728, 544, 1724, 696, 608, 544, 684, 552, 1796, 620, 608, 544, 684, 540, 700, 548, 1800, 564, 660, 544, 684, 544, 680, 544, 684, 540, 684, 540, 684, 544, 700, 544, 680, 544, 684, 564, 660, 628, 596, 544, 680, 544, 684, 624, 604, 604, 632, 572, 656, 548, 676, 548, 676, 548, 680, 544, 680, 600, 624, 548, 680, 544, 1820, 632, 592, 548, 676, 548, 684, 572, 1768, 548, 680, 544, 680, 544, 684, 540, 700, 548, 1800, 544, 684, 540, 1804, 576, 1772, 624, 1724, 620, 608, 628, 596, 600, 644, 548, 680, 564, 660, 544, 680, 548, 680, 544, 680, 544, 684, 540, 688, 540, 700, 544, 684, 540, 1804, 628, 1720, 572, 656, 548, 1800, 568, 664, 540, 684, 548};
static PROGMEM const uint16_t c31v2[147] = { 9104, 4584, 620, 664, 572, 652, 572, 656, 568, 1780, 568, 656, 568, 660, 564, 660, 544, 1804, 572, 676, 552, 668, 564, 664, 572, 1776, 572, 652, 552, 1796, 568, 656, 572, 660, 552, 684, 624, 1728, 568, 652, 656, 572, 620, 604, 548, 680, 620, 608, 544, 680, 544, 696, 552, 676, 548, 676, 548, 676, 548, 680, 628, 596, 548, 676, 548, 680, 544, 696, 552, 676, 548, 680, 544, 680, 544, 680, 548, 676, 548, 680, 544, 680, 544, 1820, 652, 572, 652, 576, 620, 604, 548, 1800, 648, 576, 620, 608, 616, 608, 544, 1820, 576, 1772, 648, 576, 576, 1772, 644, 1708, 544, 1800, 620, 608, 656, 568, 544, 696, 624, 604, 548, 676, 620, 604, 548, 684, 604, 616, 568, 648, 628, 608, 564, 676, 552, 676, 548, 1800, 628, 1720, 628, 600, 548, 1800, 620, 604, 620, 608, 620};
static PROGMEM const uint16_t c32v2[147] = { 9100, 4592, 620, 660, 564, 660, 568, 660, 564, 1784, 572, 652, 572, 656, 568, 660, 564, 1780, 568, 1800, 576, 648, 576, 656, 568, 1776, 572, 656, 568, 1780, 576, 652, 572, 656, 548, 692, 564, 1784, 572, 652, 572, 660, 544, 676, 624, 604, 548, 680, 544, 680, 544, 696, 624, 604, 548, 680, 544, 680, 544, 684, 544, 680, 544, 680, 628, 600, 556, 684, 548, 680, 628, 596, 544, 684, 628, 600, 552, 672, 540, 684, 540, 688, 540, 700, 628, 1720, 624, 600, 624, 604, 624, 1724, 620, 604, 620, 608, 620, 608, 540, 700, 620, 608, 544, 684, 540, 688, 624, 604, 600, 624, 620, 604, 548, 680, 544, 700, 548, 676, 600, 628, 544, 680, 544, 680, 620, 608, 616, 608, 544, 680, 544, 700, 548, 680, 608, 1736, 616, 1732, 624, 600, 628, 1720, 624, 600, 624, 600, 624};

static PROGMEM const uint16_t c16v1[147] = { 9104, 4592, 600, 680, 544, 680, 544, 684, 540, 1752, 676, 604, 548, 676, 552, 676, 548, 1744, 600, 1764, 604, 676, 568, 656, 548, 1796, 552, 1716, 628, 680, 544, 680, 544, 684, 540, 1820, 548, 1800, 616, 612, 540, 684, 544, 684, 540, 684, 540, 684, 572, 652, 544, 700, 548, 664, 628, 608, 544, 680, 548, 680, 544, 680, 544, 680, 544, 684, 544, 696, 548, 676, 548, 680, 544, 680, 548, 676, 548, 676, 572, 656, 548, 676, 548, 692, 572, 1776, 624, 600, 552, 680, 596, 1744, 620, 608, 544, 680, 548, 676, 548, 1816, 624, 600, 624, 600, 552, 676, 548, 676, 548, 676, 572, 656, 548, 660, 628, 628, 544, 680, 544, 684, 564, 660, 544, 680, 544, 684, 540, 684, 540, 684, 572, 668, 568, 656, 548, 1796, 572, 1776, 620, 608, 544, 1804, 624, 600, 544, 684, 572};
static PROGMEM const uint16_t c17v1[147] = { 9096, 4592, 624, 652, 572, 656, 540, 684, 540, 1804, 572, 652, 540, 688, 540, 688, 544, 1804, 544, 692, 648, 1692, 652, 576, 616, 1728, 620, 1724, 620, 604, 620, 604, 548, 672, 628, 1736, 620, 1728, 648, 580, 540, 684, 540, 684, 540, 684, 544, 680, 576, 652, 540, 700, 544, 684, 596, 624, 620, 604, 548, 680, 544, 680, 548, 676, 548, 676, 572, 668, 544, 684, 540, 684, 544, 680, 544, 680, 564, 660, 544, 684, 544, 680, 544, 700, 596, 1744, 624, 604, 620, 604, 548, 1796, 620, 608, 620, 604, 548, 680, 544, 692, 544, 1804, 624, 600, 624, 600, 540, 684, 544, 684, 540, 684, 540, 684, 544, 696, 568, 656, 548, 680, 548, 676, 548, 680, 544, 680, 544, 680, 628, 600, 596, 640, 544, 680, 628, 1724, 548, 1792, 624, 604, 624, 1724, 548, 676, 624, 604, 544};
static PROGMEM const uint16_t c18v1[147] = { 9100, 4592, 624, 656, 568, 656, 568, 656, 572, 1772, 572, 656, 568, 656, 568, 648, 568, 1784, 572, 1792, 572, 1776, 572, 652, 572, 1776, 568, 1776, 572, 656, 568, 656, 548, 680, 564, 1796, 572, 1776, 568, 656, 568, 656, 572, 656, 548, 676, 548, 680, 544, 680, 548, 692, 540, 684, 544, 684, 540, 684, 540, 684, 540, 688, 548, 676, 552, 672, 552, 680, 576, 660, 544, 688, 632, 588, 540, 684, 544, 680, 544, 684, 624, 600, 624, 616, 600, 1752, 540, 684, 624, 596, 548, 1800, 628, 596, 648, 580, 616, 612, 548, 1808, 568, 1780, 568, 656, 620, 608, 544, 680, 544, 684, 540, 684, 540, 684, 628, 616, 544, 684, 544, 676, 620, 608, 544, 680, 544, 680, 620, 604, 548, 680, 544, 696, 548, 676, 552, 1796, 620, 1732, 624, 596, 628, 1720, 628, 600, 552, 672, 552};
static PROGMEM const uint16_t c19v1[147] = { 9100, 4596, 620, 660, 564, 660, 564, 660, 564, 1784, 572, 652, 572, 656, 572, 660, 544, 1796, 568, 672, 576, 652, 572, 1772, 572, 1780, 544, 1800, 568, 660, 564, 660, 564, 664, 572, 1792, 572, 1776, 572, 652, 572, 656, 568, 656, 572, 656, 568, 660, 544, 680, 544, 696, 548, 680, 548, 676, 568, 660, 544, 684, 540, 684, 540, 684, 544, 680, 576, 668, 568, 656, 548, 676, 548, 680, 544, 680, 544, 680, 544, 684, 544, 680, 544, 696, 572, 1776, 568, 656, 568, 660, 564, 1784, 572, 656, 548, 676, 572, 652, 572, 668, 544, 684, 564, 1784, 572, 652, 572, 656, 568, 656, 572, 652, 552, 676, 548, 692, 544, 684, 540, 684, 540, 684, 544, 684, 540, 684, 540, 688, 548, 676, 548, 692, 544, 684, 540, 1804, 572, 1780, 568, 660, 544, 1800, 576, 652, 572, 656, 568};
static PROGMEM const uint16_t c20v1[147] = { 9104, 4592, 600, 680, 548, 680, 544, 680, 544, 1748, 628, 652, 572, 656, 540, 684, 540, 1752, 628, 1736, 596, 684, 552, 1716, 628, 1720, 624, 1752, 592, 688, 540, 684, 552, 676, 548, 1812, 544, 1724, 652, 656, 548, 676, 548, 680, 544, 680, 548, 680, 544, 680, 544, 700, 568, 656, 548, 680, 544, 680, 544, 680, 544, 684, 544, 680, 576, 652, 540, 700, 544, 684, 544, 680, 544, 680, 544, 684, 540, 684, 572, 656, 540, 684, 540, 704, 540, 1804, 544, 684, 540, 684, 572, 1772, 572, 656, 548, 680, 548, 676, 548, 692, 544, 1800, 576, 1692, 632, 676, 568, 656, 568, 660, 544, 680, 548, 676, 548, 692, 544, 684, 572, 652, 540, 684, 540, 684, 544, 684, 540, 684, 572, 652, 544, 700, 544, 680, 544, 1800, 544, 1752, 596, 668, 576, 1784, 552, 676, 568, 656, 548};
static PROGMEM const uint16_t c21v1[147] = { 9104, 4592, 624, 652, 552, 676, 548, 676, 548, 1800, 544, 680, 548, 680, 544, 680, 544, 1800, 544, 700, 548, 1796, 568, 1780, 568, 1780, 624, 1640, 736, 572, 540, 684, 552, 676, 600, 1764, 548, 1792, 616, 612, 540, 684, 540, 684, 544, 680, 544, 680, 544, 684, 604, 636, 548, 676, 548, 676, 572, 656, 568, 656, 548, 676, 548, 676, 548, 680, 544, 696, 572, 652, 544, 688, 596, 624, 540, 684, 540, 684, 540, 688, 572, 652, 604, 636, 544, 1800, 632, 596, 544, 684, 540, 1808, 620, 600, 540, 688, 540, 684, 604, 1760, 564, 1784, 548, 1720, 700, 608, 608, 616, 628, 596, 544, 680, 544, 684, 544, 696, 548, 680, 544, 680, 568, 656, 548, 680, 544, 680, 544, 684, 540, 684, 544, 696, 548, 676, 548, 1808, 600, 1744, 540, 684, 540, 1808, 568, 656, 548, 680, 548};
static PROGMEM const uint16_t c22v1[147] = { 9108, 4588, 624, 656, 568, 656, 548, 680, 548, 1796, 568, 660, 564, 660, 544, 680, 548, 1800, 576, 1788, 544, 1808, 548, 1796, 572, 1776, 568, 1780, 564, 660, 576, 652, 564, 660, 544, 1820, 568, 1780, 564, 660, 564, 660, 544, 688, 548, 672, 572, 656, 548, 676, 540, 700, 544, 680, 568, 660, 544, 680, 544, 684, 552, 672, 540, 684, 544, 680, 544, 700, 544, 680, 548, 680, 544, 680, 544, 680, 564, 664, 540, 684, 544, 680, 544, 700, 564, 1784, 564, 660, 564, 660, 576, 1772, 572, 656, 548, 676, 548, 680, 548, 692, 540, 688, 572, 652, 552, 1796, 568, 656, 568, 660, 548, 676, 544, 684, 544, 696, 548, 684, 540, 680, 552, 676, 544, 680, 544, 684, 540, 684, 544, 684, 540, 700, 544, 680, 544, 1804, 576, 1772, 572, 656, 568, 1776, 568, 660, 568, 656, 548};
static PROGMEM const uint16_t c23v1[147] = { 9104, 4596, 616, 660, 576, 652, 572, 652, 544, 1808, 564, 660, 568, 660, 544, 680, 576, 1772, 572, 672, 564, 660, 544, 684, 540, 684, 540, 688, 548, 1800, 568, 656, 548, 680, 576, 1788, 564, 1784, 572, 656, 548, 680, 544, 680, 548, 680, 544, 680, 544, 684, 540, 700, 548, 684, 540, 684, 540, 684, 544, 684, 540, 684, 540, 688, 548, 676, 548, 692, 544, 684, 540, 684, 540, 688, 548, 676, 548, 680, 548, 676, 548, 680, 544, 696, 540, 1808, 568, 660, 564, 660, 544, 1804, 572, 652, 544, 684, 540, 688, 568, 1796, 572, 652, 540, 684, 572, 1776, 568, 660, 544, 680, 568, 660, 544, 680, 544, 696, 552, 676, 548, 676, 548, 680, 544, 680, 568, 656, 568, 660, 564, 660, 544, 696, 548, 680, 548, 1800, 564, 1784, 572, 652, 540, 1808, 568, 660, 544, 680, 548};
static PROGMEM const uint16_t c24v1[147] = { 9100, 4596, 624, 656, 572, 652, 572, 656, 568, 1780, 576, 648, 568, 660, 564, 668, 544, 1796, 572, 1792, 572, 656, 572, 660, 544, 676, 568, 656, 568, 1780, 568, 656, 568, 660, 568, 1792, 572, 1776, 568, 660, 564, 660, 564, 664, 540, 688, 540, 684, 540, 684, 540, 700, 548, 680, 564, 660, 544, 684, 540, 684, 540, 688, 572, 652, 572, 656, 568, 672, 576, 652, 540, 684, 540, 688, 548, 676, 568, 660, 548, 676, 548, 668, 576, 676, 548, 1804, 544, 680, 648, 576, 616, 1732, 648, 580, 624, 600, 624, 600, 552, 692, 544, 1800, 648, 580, 552, 1792, 644, 584, 620, 604, 624, 604, 548, 676, 600, 640, 544, 684, 540, 684, 624, 600, 540, 684, 544, 680, 544, 684, 540, 684, 540, 704, 544, 680, 544, 1800, 616, 1732, 624, 600, 624, 1724, 624, 600, 624, 604, 548};
static PROGMEM const uint16_t c25v1[147] = { 9104, 4596, 616, 664, 564, 660, 572, 652, 576, 1776, 568, 656, 568, 660, 564, 660, 568, 1780, 576, 668, 564, 1784, 572, 652, 576, 652, 572, 652, 552, 1796, 568, 660, 544, 680, 568, 1796, 568, 1784, 540, 680, 568, 656, 568, 660, 544, 684, 552, 672, 540, 684, 540, 700, 548, 676, 548, 684, 540, 680, 548, 676, 548, 676, 548, 680, 544, 680, 620, 620, 552, 676, 548, 676, 548, 680, 544, 680, 548, 676, 548, 680, 544, 684, 552, 688, 548, 1796, 652, 572, 652, 576, 620, 1728, 628, 596, 628, 600, 624, 600, 544, 700, 544, 668, 628, 1732, 624, 1724, 652, 572, 572, 656, 620, 604, 548, 680, 544, 704, 604, 616, 548, 680, 604, 620, 544, 680, 544, 680, 544, 684, 624, 600, 616, 628, 544, 680, 544, 1800, 632, 1716, 628, 600, 624, 1724, 620, 604, 620, 608, 620};
static PROGMEM const uint16_t c26v1[147] = { 9108, 4592, 600, 676, 568, 660, 568, 656, 568, 1780, 576, 652, 572, 652, 572, 656, 548, 1800, 568, 1796, 568, 1780, 576, 652, 572, 652, 572, 656, 548, 1800, 568, 656, 568, 660, 564, 1796, 572, 1776, 568, 660, 576, 648, 564, 660, 544, 684, 540, 688, 568, 656, 540, 700, 544, 680, 544, 680, 568, 656, 548, 680, 544, 680, 544, 688, 548, 668, 548, 696, 548, 676, 624, 604, 544, 680, 548, 676, 548, 676, 548, 680, 544, 680, 652, 592, 548, 1792, 648, 584, 544, 680, 548, 1796, 656, 568, 620, 608, 616, 608, 544, 1820, 652, 1696, 648, 564, 576, 664, 540, 1804, 628, 1720, 572, 656, 620, 604, 548, 696, 548, 676, 540, 688, 620, 604, 548, 676, 548, 676, 552, 676, 620, 604, 548, 696, 540, 684, 552, 1796, 620, 1728, 616, 608, 620, 1732, 620, 608, 544, 676, 552};
static PROGMEM const uint16_t c27v1[147] = { 9104, 4592, 600, 680, 544, 684, 540, 684, 544, 1804, 572, 656, 548, 676, 548, 680, 544, 1800, 576, 668, 568, 660, 544, 1800, 544, 684, 540, 688, 536, 1808, 548, 684, 544, 680, 564, 1800, 544, 1720, 628, 680, 544, 680, 544, 688, 548, 676, 548, 676, 548, 676, 548, 696, 540, 684, 540, 688, 548, 680, 544, 680, 544, 680, 548, 676, 548, 680, 544, 696, 552, 672, 552, 676, 548, 676, 548, 684, 540, 680, 568, 656, 548, 680, 544, 696, 540, 1804, 552, 676, 548, 676, 548, 1800, 544, 680, 548, 680, 544, 680, 544, 696, 548, 680, 548, 1796, 548, 684, 552, 1788, 544, 1804, 624, 604, 552, 672, 624, 624, 600, 624, 600, 624, 540, 684, 540, 684, 540, 688, 548, 676, 548, 680, 548, 692, 540, 684, 544, 1804, 540, 1804, 572, 656, 548, 1804, 540, 680, 544, 684, 544};
static PROGMEM const uint16_t c28v1[147] = { 9104, 4592, 600, 680, 548, 676, 548, 680, 544, 1748, 628, 652, 552, 676, 568, 648, 568, 1708, 624, 1768, 600, 680, 544, 1752, 604, 676, 548, 676, 548, 1748, 596, 684, 544, 684, 572, 1736, 628, 1728, 596, 676, 548, 680, 544, 684, 544, 680, 544, 684, 540, 684, 552, 692, 572, 652, 544, 684, 552, 672, 552, 676, 548, 680, 544, 680, 576, 652, 544, 700, 544, 680, 544, 684, 540, 688, 548, 676, 548, 680, 548, 680, 544, 680, 576, 668, 548, 1744, 596, 684, 576, 652, 552, 1744, 600, 680, 544, 684, 540, 688, 548, 1760, 596, 688, 548, 1744, 600, 680, 544, 1752, 604, 1748, 596, 680, 544, 684, 540, 700, 548, 680, 564, 660, 544, 684, 540, 688, 548, 676, 572, 652, 552, 676, 548, 696, 548, 676, 548, 1748, 600, 1748, 596, 684, 540, 1752, 604, 664, 572, 664, 548};
static PROGMEM const uint16_t c29v1[147] = { 9112, 4592, 600, 680, 544, 684, 540, 684, 544, 1752, 604, 676, 548, 676, 548, 680, 564, 1728, 596, 700, 548, 1744, 600, 1748, 596, 684, 540, 692, 544, 1744, 604, 676, 548, 676, 548, 1760, 604, 1748, 596, 684, 544, 680, 544, 680, 544, 684, 540, 684, 552, 676, 548, 696, 552, 672, 540, 688, 548, 676, 548, 676, 548, 680, 548, 676, 548, 680, 544, 688, 580, 656, 548, 680, 544, 680, 556, 668, 544, 680, 548, 628, 624, 652, 544, 620, 624, 1728, 680, 544, 628, 624, 600, 1748, 600, 680, 544, 680, 544, 684, 540, 1768, 596, 684, 544, 1752, 600, 684, 544, 1748, 596, 1752, 604, 676, 548, 676, 548, 692, 544, 684, 552, 680, 544, 672, 552, 676, 548, 676, 548, 680, 568, 656, 548, 692, 544, 684, 540, 1756, 600, 1744, 600, 680, 544, 1752, 604, 672, 552, 676, 548};
static PROGMEM const uint16_t c30v1[147] = { 9104, 4588, 676, 548, 624, 600, 624, 604, 624, 1720, 624, 604, 620, 604, 620, 604, 600, 1748, 620, 1740, 624, 1724, 620, 1728, 628, 600, 604, 620, 624, 1720, 624, 600, 628, 600, 624, 1740, 612, 1732, 624, 600, 624, 604, 624, 592, 620, 668, 568, 608, 596, 628, 596, 640, 628, 600, 624, 600, 624, 600, 628, 600, 620, 604, 624, 600, 624, 600, 624, 620, 668, 556, 616, 608, 628, 600, 624, 600, 624, 604, 600, 624, 620, 604, 624, 616, 616, 1732, 624, 604, 600, 676, 624, 1672, 672, 552, 620, 604, 620, 608, 596, 644, 624, 1724, 620, 1728, 616, 608, 628, 1724, 600, 1748, 616, 608, 596, 628, 620, 620, 624, 600, 624, 604, 624, 600, 624, 604, 620, 604, 620, 604, 620, 604, 620, 624, 624, 600, 624, 1724, 620, 1728, 620, 604, 620, 1728, 616, 608, 628, 596, 628};
static PROGMEM const uint16_t c31v1[147] = { 9112, 4592, 624, 656, 548, 676, 548, 680, 544, 1804, 552, 676, 548, 680, 544, 680, 544, 1808, 548, 692, 544, 680, 576, 652, 540, 1808, 548, 688, 536, 1804, 540, 688, 548, 676, 548, 1820, 548, 1796, 620, 608, 628, 600, 540, 688, 568, 656, 572, 656, 600, 624, 548, 696, 548, 680, 544, 680, 544, 688, 600, 620, 544, 680, 544, 684, 540, 684, 572, 672, 544, 680, 544, 680, 544, 684, 540, 688, 568, 656, 548, 676, 572, 656, 548, 692, 540, 1808, 548, 668, 632, 604, 544, 1804, 544, 684, 548, 680, 568, 664, 600, 636, 568, 1776, 548, 1748, 680, 604, 540, 1804, 624, 1724, 568, 664, 548, 676, 548, 696, 540, 688, 548, 676, 548, 676, 572, 656, 548, 676, 548, 680, 544, 684, 572, 668, 544, 684, 544, 1800, 544, 1804, 552, 680, 564, 1784, 572, 656, 548, 684, 552};
static PROGMEM const uint16_t c32v1[147] = { 9096, 4596, 616, 664, 540, 684, 540, 684, 572, 1748, 568, 684, 572, 652, 572, 656, 548, 1740, 624, 1740, 596, 684, 572, 652, 540, 1752, 604, 676, 548, 1744, 624, 656, 548, 680, 564, 1744, 600, 1748, 600, 680, 544, 684, 572, 652, 552, 672, 572, 656, 548, 676, 548, 696, 572, 652, 540, 688, 548, 676, 548, 676, 548, 680, 548, 676, 568, 660, 544, 696, 548, 680, 548, 680, 544, 680, 544, 680, 544, 684, 540, 684, 540, 688, 572, 668, 544, 1748, 596, 688, 548, 680, 544, 1716, 628, 680, 544, 680, 548, 680, 544, 1764, 620, 1728, 596, 1752, 604, 676, 548, 1744, 624, 1700, 624, 684, 548, 680, 544, 692, 576, 652, 544, 680, 544, 684, 540, 684, 552, 672, 552, 676, 548, 676, 548, 700, 548, 672, 552, 1744, 600, 1748, 596, 684, 540, 1752, 624, 656, 548, 680, 544};

static PROGMEM const uint16_t c16v4[147] = { 9100, 4596, 600, 680, 552, 672, 544, 680, 544, 1748, 596, 688, 548, 676, 548, 680, 544, 1748, 600, 1764, 600, 680, 564, 660, 544, 1752, 604, 1744, 600, 680, 548, 676, 548, 680, 564, 676, 572, 652, 552, 676, 548, 676, 548, 680, 544, 684, 552, 676, 548, 676, 540, 704, 540, 680, 544, 684, 544, 684, 548, 676, 552, 676, 568, 656, 548, 680, 544, 696, 552, 676, 548, 680, 544, 680, 544, 680, 544, 684, 544, 684, 540, 688, 548, 692, 544, 1748, 604, 680, 548, 676, 548, 1748, 596, 688, 548, 676, 548, 676, 548, 696, 552, 1740, 604, 680, 544, 1752, 592, 1756, 600, 1748, 596, 688, 548, 676, 572, 672, 540, 684, 552, 676, 548, 680, 544, 688, 548, 672, 544, 684, 540, 688, 548, 692, 544, 684, 572, 1724, 600, 1748, 596, 684, 552, 1744, 620, 664, 540, 684, 540};
static PROGMEM const uint16_t c17v4[147] = { 9108, 4592, 600, 684, 552, 672, 540, 684, 552, 1744, 624, 656, 548, 680, 544, 680, 544, 1752, 604, 692, 544, 1748, 596, 688, 548, 1744, 600, 1748, 596, 684, 540, 684, 552, 676, 548, 692, 576, 648, 544, 684, 572, 652, 572, 652, 544, 684, 552, 672, 552, 676, 548, 692, 564, 660, 544, 684, 540, 684, 552, 676, 548, 676, 552, 676, 548, 676, 548, 692, 544, 680, 544, 684, 540, 684, 552, 676, 572, 652, 548, 676, 552, 676, 548, 692, 544, 1748, 596, 684, 552, 672, 552, 1740, 604, 676, 568, 660, 544, 680, 548, 1760, 604, 1744, 600, 684, 540, 1752, 596, 1752, 600, 1744, 604, 680, 544, 680, 544, 696, 548, 680, 548, 676, 552, 672, 548, 680, 544, 684, 540, 680, 548, 680, 544, 696, 548, 676, 548, 1748, 600, 1748, 596, 684, 548, 1744, 624, 656, 548, 676, 568};
static PROGMEM const uint16_t c18v4[147] = { 9108, 4592, 604, 676, 548, 676, 548, 680, 564, 1728, 596, 684, 552, 672, 544, 684, 540, 1752, 604, 1760, 608, 1740, 604, 676, 548, 1744, 600, 1748, 600, 688, 544, 676, 552, 672, 552, 692, 544, 680, 544, 680, 544, 684, 540, 684, 540, 684, 544, 684, 552, 672, 552, 688, 548, 680, 544, 680, 544, 684, 540, 684, 552, 676, 548, 680, 548, 676, 548, 692, 544, 684, 548, 676, 552, 676, 548, 676, 548, 676, 548, 680, 548, 676, 548, 696, 552, 1740, 624, 656, 548, 680, 576, 1716, 596, 684, 540, 684, 544, 684, 548, 1764, 624, 1724, 600, 680, 544, 1752, 604, 1740, 596, 1752, 624, 656, 568, 660, 544, 696, 572, 656, 568, 656, 548, 680, 544, 680, 544, 684, 544, 684, 540, 680, 544, 704, 540, 680, 548, 1752, 604, 1740, 604, 676, 548, 1748, 596, 684, 540, 692, 544};
static PROGMEM const uint16_t c19v4[147] = { 9104, 4596, 596, 692, 544, 676, 552, 676, 548, 1744, 600, 680, 544, 684, 544, 680, 544, 1748, 608, 688, 544, 684, 544, 1748, 608, 1744, 600, 1748, 596, 684, 540, 684, 552, 676, 548, 692, 544, 684, 540, 684, 544, 684, 548, 676, 552, 672, 572, 656, 548, 684, 540, 696, 540, 684, 552, 660, 576, 664, 552, 676, 548, 676, 548, 676, 548, 680, 576, 668, 548, 676, 568, 664, 540, 680, 548, 680, 544, 688, 548, 672, 540, 688, 548, 1760, 596, 1752, 604, 676, 548, 680, 544, 1736, 632, 664, 548, 676, 552, 676, 568, 672, 544, 684, 552, 680, 544, 676, 548, 680, 544, 680, 544, 684, 544, 680, 544, 696, 548, 680, 548, 676, 548, 680, 544, 680, 544, 684, 540, 692, 544, 676, 552, 688, 544, 684, 544, 1748, 604, 1752, 596, 676, 548, 1748, 596, 684, 540, 688, 568};
static PROGMEM const uint16_t c20v4[147] = { 9104, 4592, 600, 680, 544, 684, 540, 684, 540, 1804, 544, 688, 544, 680, 548, 676, 548, 1796, 568, 1744, 624, 656, 568, 1700, 656, 1720, 624, 1724, 600, 676, 548, 680, 568, 656, 548, 692, 544, 680, 544, 680, 544, 680, 568, 656, 548, 680, 544, 684, 552, 668, 544, 696, 552, 676, 548, 680, 544, 676, 548, 676, 552, 676, 548, 676, 548, 676, 548, 692, 544, 684, 540, 684, 540, 684, 544, 680, 544, 684, 540, 684, 540, 688, 548, 1756, 620, 1728, 616, 664, 540, 684, 572, 1720, 628, 652, 552, 676, 548, 676, 568, 672, 564, 660, 544, 680, 544, 680, 548, 680, 544, 680, 544, 680, 564, 660, 544, 700, 568, 656, 548, 676, 548, 676, 572, 652, 552, 676, 568, 656, 548, 680, 544, 692, 544, 684, 540, 1752, 624, 1724, 624, 656, 548, 1744, 620, 660, 544, 680, 544};
static PROGMEM const uint16_t c21v4[147] = { 9108, 4588, 624, 656, 548, 680, 548, 676, 548, 1804, 552, 668, 544, 684, 540, 684, 544, 1800, 616, 628, 620, 1724, 620, 1728, 564, 1784, 624, 1720, 624, 604, 548, 680, 544, 680, 548, 696, 540, 684, 548, 676, 552, 676, 548, 676, 548, 676, 548, 680, 568, 656, 548, 692, 544, 684, 540, 684, 540, 684, 540, 688, 540, 684, 540, 684, 540, 684, 552, 692, 544, 680, 564, 664, 572, 652, 540, 684, 544, 684, 540, 684, 552, 672, 540, 1824, 544, 1804, 552, 676, 548, 676, 548, 1800, 544, 684, 544, 680, 544, 684, 540, 1820, 548, 680, 544, 684, 540, 688, 548, 680, 544, 676, 548, 676, 572, 656, 548, 692, 548, 680, 572, 652, 552, 676, 548, 676, 568, 660, 544, 680, 548, 676, 548, 696, 548, 676, 548, 1796, 552, 1796, 568, 664, 604, 1740, 540, 688, 540, 684, 548};
static PROGMEM const uint16_t c22v4[147] = { 9100, 4592, 620, 608, 596, 628, 620, 580, 644, 1728, 616, 584, 652, 572, 652, 576, 648, 1724, 624, 1740, 624, 1724, 620, 1720, 628, 1700, 652, 1724, 624, 576, 648, 576, 648, 580, 644, 596, 652, 572, 652, 576, 648, 580, 648, 576, 648, 576, 648, 580, 644, 612, 604, 604, 652, 576, 648, 576, 648, 580, 644, 580, 648, 580, 644, 584, 628, 624, 624, 588, 648, 580, 644, 580, 644, 612, 624, 600, 624, 576, 652, 604, 620, 604, 620, 1744, 620, 1728, 616, 612, 624, 572, 652, 1724, 624, 604, 620, 584, 620, 600, 644, 1748, 620, 580, 624, 600, 656, 572, 652, 572, 652, 576, 648, 576, 652, 576, 648, 596, 628, 596, 648, 580, 628, 592, 652, 576, 648, 576, 648, 584, 620, 600, 648, 596, 648, 580, 644, 1732, 624, 1724, 620, 580, 656, 1720, 624, 576, 652, 576, 648};
static PROGMEM const uint16_t c23v4[147] = { 9112, 4588, 604, 680, 544, 680, 544, 680, 544, 1804, 540, 688, 548, 676, 548, 680, 548, 1800, 544, 700, 544, 676, 572, 656, 548, 680, 544, 680, 544, 1804, 540, 688, 572, 660, 540, 700, 548, 676, 548, 676, 548, 680, 548, 676, 548, 680, 544, 684, 540, 684, 540, 700, 548, 680, 544, 684, 540, 684, 540, 684, 544, 684, 540, 684, 540, 688, 548, 692, 544, 684, 540, 684, 540, 684, 540, 688, 540, 688, 544, 680, 548, 676, 548, 1812, 576, 1720, 604, 680, 544, 680, 544, 1800, 544, 684, 544, 684, 572, 652, 540, 700, 544, 1800, 548, 680, 544, 680, 544, 684, 552, 672, 544, 680, 544, 684, 540, 700, 568, 660, 564, 664, 552, 668, 544, 680, 544, 684, 540, 688, 540, 680, 544, 700, 544, 680, 548, 1796, 548, 1748, 596, 688, 548, 1792, 544, 684, 548, 676, 572};
static PROGMEM const uint16_t c24v4[147] = { 9100, 4600, 592, 684, 540, 684, 556, 672, 552, 1744, 600, 676, 548, 680, 544, 680, 568, 1728, 596, 1768, 596, 684, 540, 684, 552, 676, 552, 672, 552, 1740, 624, 656, 548, 680, 544, 696, 552, 672, 552, 676, 548, 680, 544, 680, 548, 676, 548, 676, 548, 676, 548, 700, 548, 672, 540, 684, 552, 676, 548, 676, 552, 676, 548, 680, 544, 680, 544, 696, 552, 672, 552, 676, 548, 676, 548, 680, 548, 680, 544, 680, 544, 680, 544, 1764, 624, 1728, 628, 652, 540, 684, 552, 1744, 600, 680, 544, 680, 548, 684, 548, 688, 548, 1748, 600, 680, 544, 684, 540, 680, 544, 684, 540, 684, 544, 680, 544, 700, 568, 656, 544, 680, 548, 680, 544, 680, 544, 684, 572, 652, 552, 672, 552, 692, 544, 680, 544, 1748, 600, 1748, 604, 676, 548, 1748, 600, 680, 544, 680, 544};
static PROGMEM const uint16_t c25v4[147] = { 9108, 4592, 600, 680, 544, 680, 544, 684, 540, 1752, 596, 684, 572, 652, 552, 676, 548, 1744, 620, 680, 548, 1740, 604, 676, 548, 680, 544, 680, 544, 1752, 624, 656, 548, 672, 576, 672, 540, 688, 548, 676, 540, 684, 552, 676, 548, 676, 548, 680, 544, 680, 548, 696, 548, 676, 548, 680, 544, 680, 548, 680, 544, 684, 552, 672, 572, 656, 548, 692, 576, 648, 544, 684, 552, 676, 548, 676, 548, 676, 548, 676, 552, 676, 548, 1760, 624, 1724, 600, 684, 544, 680, 544, 1748, 596, 684, 552, 672, 552, 680, 544, 1760, 628, 1720, 604, 676, 548, 676, 552, 676, 548, 676, 568, 656, 548, 680, 544, 696, 552, 676, 568, 656, 548, 684, 552, 672, 552, 668, 548, 680, 544, 680, 544, 700, 544, 680, 548, 1748, 596, 1760, 604, 668, 548, 1748, 596, 688, 548, 672, 544};
static PROGMEM const uint16_t c26v4[147] = { 9104, 4592, 600, 684, 540, 680, 548, 676, 548, 1744, 600, 680, 544, 680, 544, 684, 544, 1748, 596, 1768, 600, 1744, 600, 680, 544, 680, 544, 680, 568, 1724, 596, 684, 544, 684, 540, 700, 544, 680, 544, 684, 544, 680, 576, 648, 544, 684, 572, 652, 540, 688, 572, 668, 564, 660, 544, 680, 544, 684, 544, 680, 544, 680, 544, 684, 552, 672, 572, 668, 548, 676, 568, 660, 544, 680, 568, 660, 544, 680, 544, 680, 544, 684, 572, 1736, 600, 1748, 596, 684, 540, 684, 540, 1752, 604, 676, 552, 676, 548, 676, 548, 1760, 596, 1752, 604, 680, 544, 680, 544, 680, 568, 656, 548, 624, 600, 600, 624, 644, 600, 676, 572, 576, 628, 596, 628, 624, 600, 680, 548, 596, 628, 628, 596, 644, 600, 624, 604, 1744, 600, 1748, 596, 680, 544, 1728, 628, 676, 572, 652, 552};
static PROGMEM const uint16_t c27v4[147] = { 9108, 4592, 624, 604, 600, 624, 672, 552, 620, 1732, 592, 628, 620, 608, 616, 608, 616, 1732, 624, 620, 616, 608, 680, 1676, 668, 552, 620, 604, 620, 1732, 592, 632, 596, 628, 628, 616, 616, 608, 620, 608, 616, 608, 628, 600, 624, 600, 624, 600, 624, 604, 624, 616, 628, 600, 624, 600, 624, 592, 624, 608, 628, 600, 624, 600, 624, 604, 620, 620, 596, 628, 620, 604, 620, 608, 596, 600, 656, 572, 652, 576, 648, 576, 648, 1712, 656, 1692, 652, 576, 648, 576, 652, 1696, 648, 576, 648, 580, 644, 580, 648, 596, 648, 576, 648, 1700, 648, 580, 652, 572, 656, 568, 656, 572, 652, 572, 652, 592, 644, 580, 624, 600, 648, 580, 644, 580, 644, 580, 656, 572, 652, 572, 652, 588, 648, 580, 644, 1704, 644, 1704, 648, 580, 624, 1720, 628, 600, 624, 600, 644};
static PROGMEM const uint16_t c28v4[147] = { 9104, 4564, 628, 628, 596, 628, 600, 600, 624, 1752, 604, 672, 540, 684, 552, 676, 548, 1748, 600, 1764, 600, 676, 548, 1748, 600, 684, 548, 672, 544, 1752, 604, 676, 548, 676, 548, 696, 552, 672, 552, 676, 548, 680, 564, 660, 544, 684, 544, 668, 576, 664, 540, 700, 568, 660, 544, 680, 544, 684, 540, 684, 540, 688, 548, 676, 548, 680, 548, 692, 544, 684, 552, 672, 552, 676, 568, 660, 544, 680, 544, 684, 544, 680, 544, 1768, 596, 1752, 604, 676, 548, 680, 548, 1744, 600, 680, 544, 680, 544, 684, 572, 1736, 632, 648, 544, 1752, 604, 676, 548, 676, 548, 680, 544, 680, 548, 676, 548, 696, 548, 676, 552, 676, 568, 660, 544, 676, 548, 680, 544, 680, 548, 680, 544, 696, 548, 680, 544, 1748, 600, 1748, 608, 672, 552, 1744, 600, 680, 544, 680, 544};
static PROGMEM const uint16_t c29v4[147] = { 9108, 4592, 600, 680, 544, 680, 544, 680, 544, 1752, 592, 688, 548, 676, 552, 676, 548, 1744, 600, 700, 544, 1748, 620, 1728, 596, 684, 540, 684, 552, 1744, 600, 680, 544, 680, 548, 692, 544, 684, 552, 676, 548, 676, 548, 676, 548, 680, 544, 680, 548, 676, 548, 696, 548, 676, 552, 672, 572, 656, 548, 680, 544, 680, 568, 656, 548, 680, 576, 664, 548, 676, 552, 676, 568, 656, 548, 676, 568, 664, 540, 680, 548, 680, 544, 1764, 600, 1752, 604, 676, 548, 680, 544, 1748, 600, 680, 544, 684, 552, 672, 552, 1760, 628, 652, 552, 1744, 600, 680, 564, 664, 540, 688, 548, 672, 544, 684, 552, 692, 544, 680, 564, 660, 544, 684, 540, 684, 572, 656, 548, 676, 552, 680, 544, 696, 548, 676, 552, 1740, 604, 1744, 600, 684, 540, 1752, 604, 684, 540, 676, 548};
static PROGMEM const uint16_t c30v4[147] = { 9112, 4588, 604, 676, 548, 676, 548, 676, 552, 1744, 600, 676, 568, 660, 544, 684, 544, 1744, 632, 1732, 600, 1748, 596, 1752, 596, 684, 548, 680, 548, 1740, 604, 676, 548, 676, 548, 696, 572, 652, 552, 676, 548, 684, 544, 672, 572, 656, 548, 676, 548, 676, 548, 700, 548, 672, 552, 676, 548, 676, 572, 656, 548, 680, 544, 676, 572, 656, 548, 692, 540, 684, 544, 684, 572, 652, 552, 672, 552, 676, 548, 676, 552, 672, 552, 1760, 592, 1756, 600, 676, 552, 676, 548, 1748, 596, 684, 540, 684, 572, 652, 544, 696, 548, 1748, 596, 1752, 596, 684, 552, 672, 552, 676, 568, 660, 544, 680, 544, 696, 540, 684, 552, 672, 544, 680, 544, 684, 540, 684, 540, 684, 572, 652, 552, 692, 544, 680, 544, 1748, 600, 1748, 628, 652, 552, 1740, 604, 680, 544, 680, 544};
static PROGMEM const uint16_t c31v4[147] = { 9104, 4596, 672, 556, 668, 552, 620, 608, 616, 1732, 624, 600, 624, 604, 624, 600, 676, 1672, 684, 556, 624, 604, 624, 600, 624, 1724, 620, 604, 620, 1728, 628, 600, 624, 600, 624, 616, 620, 608, 616, 616, 600, 620, 616, 608, 616, 608, 628, 596, 628, 600, 624, 592, 656, 564, 648, 608, 628, 568, 648, 568, 656, 576, 648, 580, 656, 568, 644, 600, 628, 600, 624, 596, 648, 576, 652, 572, 652, 572, 652, 572, 652, 576, 648, 1716, 652, 1696, 648, 580, 624, 596, 648, 1700, 648, 580, 624, 600, 656, 568, 624, 620, 628, 1724, 628, 1716, 632, 592, 632, 592, 652, 576, 628, 596, 628, 596, 632, 612, 620, 604, 652, 576, 628, 604, 624, 596, 628, 596, 628, 596, 648, 580, 628, 612, 632, 596, 628, 1720, 624, 1724, 620, 684, 544, 1752, 604, 676, 548, 624, 600};
static PROGMEM const uint16_t c32v4[147] = { 9112, 4560, 632, 680, 556, 668, 544, 680, 544, 1756, 600, 676, 548, 680, 548, 676, 548, 1748, 596, 1772, 604, 672, 576, 652, 552, 1744, 600, 680, 544, 1748, 596, 684, 544, 684, 540, 700, 544, 680, 548, 680, 564, 664, 540, 684, 540, 688, 548, 676, 548, 676, 552, 688, 544, 684, 544, 680, 544, 684, 552, 676, 548, 676, 548, 680, 568, 656, 548, 692, 544, 684, 540, 684, 552, 672, 552, 676, 548, 684, 540, 680, 548, 664, 572, 1724, 652, 1724, 620, 664, 548, 676, 552, 1712, 652, 656, 548, 680, 544, 684, 544, 1764, 600, 1752, 604, 1744, 600, 676, 552, 680, 544, 680, 544, 680, 544, 680, 568, 676, 548, 676, 548, 680, 544, 680, 548, 684, 540, 680, 544, 680, 544, 684, 540, 700, 548, 676, 568, 1780, 544, 1748, 600, 680, 544, 1804, 540, 688, 548, 676, 548};

static PROGMEM const uint16_t h15v3[147] = { 9088, 4548, 644, 1728, 616, 1696, 648, 576, 648, 1696, 652, 568, 644, 580, 644, 580, 644, 1696, 652, 588, 644, 584, 620, 596, 652, 1696, 624, 1712, 644, 588, 628, 588, 644, 580, 648, 1708, 644, 580, 648, 576, 648, 580, 644, 576, 648, 572, 652, 572, 644, 580, 644, 592, 652, 572, 644, 580, 644, 576, 648, 576, 648, 576, 648, 572, 656, 572, 652, 584, 648, 572, 644, 580, 644, 580, 644, 576, 648, 576, 648, 576, 640, 580, 644, 1712, 644, 1700, 644, 1696, 648, 576, 648, 576, 648, 572, 644, 584, 620, 600, 644, 1716, 640, 580, 644, 1700, 644, 1696, 648, 1700, 624, 596, 640, 580, 644, 580, 644, 596, 652, 1688, 656, 568, 648, 572, 652, 576, 628, 588, 644, 584, 640, 584, 644, 592, 620, 600, 644, 1700, 648, 1696, 648, 572, 640, 1704, 640, 584, 620, 600, 648};
static PROGMEM const uint16_t h16v3[147] = { 9088, 4548, 724, 1696, 652, 1688, 656, 568, 624, 1716, 648, 576, 620, 600, 624, 600, 624, 1716, 616, 1740, 648, 576, 616, 608, 544, 1792, 624, 1720, 572, 648, 620, 604, 620, 604, 620, 1736, 652, 572, 620, 600, 624, 600, 540, 684, 540, 680, 568, 656, 548, 676, 548, 688, 548, 676, 548, 676, 548, 672, 540, 684, 572, 652, 544, 676, 548, 676, 548, 688, 544, 680, 548, 676, 548, 676, 548, 672, 540, 684, 544, 676, 548, 676, 548, 1808, 632, 1708, 624, 1720, 624, 600, 624, 604, 548, 668, 544, 680, 548, 676, 548, 1808, 620, 1724, 628, 1716, 544, 1800, 548, 672, 612, 612, 540, 680, 568, 656, 548, 688, 576, 1768, 620, 600, 548, 676, 552, 672, 540, 680, 544, 684, 540, 680, 568, 672, 540, 680, 544, 1796, 624, 1720, 624, 600, 560, 1780, 616, 608, 616, 604, 568};
static PROGMEM const uint16_t h17v3[147] = { 9088, 4556, 624, 1748, 628, 1712, 600, 680, 576, 1712, 600, 676, 572, 652, 540, 684, 540, 1748, 596, 700, 544, 1744, 592, 684, 572, 1716, 596, 1748, 628, 648, 544, 680, 544, 680, 548, 1756, 596, 680, 544, 680, 548, 672, 552, 672, 540, 684, 544, 680, 544, 680, 544, 692, 544, 680, 540, 684, 544, 676, 548, 676, 548, 676, 548, 676, 548, 676, 548, 688, 548, 676, 548, 676, 548, 672, 544, 680, 544, 680, 544, 676, 548, 676, 548, 1760, 604, 1708, 628, 1740, 604, 676, 548, 672, 544, 684, 540, 676, 548, 676, 548, 688, 548, 676, 548, 680, 544, 672, 572, 1716, 600, 676, 548, 676, 548, 676, 568, 672, 564, 1720, 604, 676, 548, 680, 544, 672, 540, 684, 544, 676, 548, 676, 548, 692, 544, 676, 548, 1740, 604, 1740, 604, 672, 540, 1748, 628, 652, 544, 676, 548};
static PROGMEM const uint16_t h18v3[147] = { 9096, 4584, 616, 1724, 620, 1724, 624, 572, 640, 1728, 616, 580, 648, 576, 648, 576, 648, 1724, 620, 1740, 616, 1728, 616, 584, 620, 1720, 648, 1720, 624, 576, 648, 572, 652, 572, 644, 1744, 620, 576, 652, 572, 652, 572, 640, 588, 628, 592, 652, 568, 648, 580, 624, 612, 644, 576, 648, 576, 648, 576, 652, 596, 628, 572, 644, 604, 620, 604, 620, 592, 644, 580, 644, 580, 644, 576, 648, 576, 648, 576, 652, 572, 640, 580, 648, 1740, 624, 1716, 616, 1736, 600, 588, 648, 576, 648, 576, 648, 576, 652, 568, 644, 1744, 624, 572, 652, 572, 640, 580, 648, 1724, 600, 596, 648, 576, 648, 600, 616, 600, 644, 1720, 628, 596, 616, 608, 616, 612, 592, 596, 652, 572, 652, 572, 652, 588, 628, 592, 644, 1728, 592, 1748, 600, 596, 648, 1720, 624, 572, 644, 604, 620};
static PROGMEM const uint16_t h19v3[147] = {9084, 4552, 728, 1696, 648, 1696, 648, 576, 620, 1720, 624, 600, 624, 600, 540, 684, 616, 1724, 620, 620, 616, 608, 544, 1800, 548, 1792, 624, 1716, 620, 608, 544, 676, 548, 676, 620, 1736, 652, 572, 624, 604, 628, 592, 540, 684, 540, 680, 548, 680, 544, 676, 548, 692, 544, 680, 596, 624, 572, 652, 540, 684, 540, 680, 548, 676, 548, 680, 596, 640, 544, 676, 548, 676, 572, 652, 572, 652, 572, 652, 544, 676, 568, 656, 568, 1792, 544, 1796, 624, 1720, 552, 672, 540, 684, 540, 684, 544, 676, 548, 676, 548, 692, 544, 1796, 624, 600, 540, 684, 540, 1800, 632, 592, 548, 676, 548, 672, 544, 696, 548, 1792, 628, 596, 544, 680, 544, 680, 548, 676, 548, 672, 544, 680, 544, 692, 544, 680, 544, 1796, 632, 1712, 624, 600, 560, 1780, 620, 604, 632, 592, 568};
static PROGMEM const uint16_t h20v3[147] = { 9088, 4552, 684, 1684, 596, 1748, 600, 676, 548, 1740, 604, 672, 544, 680, 544, 680, 548, 1740, 604, 1756, 600, 676, 548, 1740, 628, 1716, 628, 1712, 604, 676, 568, 656, 548, 672, 576, 1732, 604, 672, 552, 672, 540, 684, 544, 676, 548, 676, 548, 676, 572, 656, 548, 688, 548, 676, 548, 672, 544, 680, 544, 680, 544, 684, 540, 680, 548, 672, 572, 668, 548, 676, 548, 676, 540, 680, 544, 684, 544, 676, 568, 656, 548, 676, 548, 1756, 600, 1744, 604, 1736, 628, 652, 544, 676, 548, 676, 568, 656, 548, 676, 552, 684, 552, 1712, 620, 684, 544, 680, 544, 1748, 596, 676, 548, 676, 572, 652, 540, 696, 552, 1736, 600, 688, 548, 668, 544, 680, 544, 680, 548, 672, 552, 676, 540, 696, 548, 676, 548, 1740, 628, 1716, 600, 676, 548, 1740, 608, 672, 592, 632, 572};
static PROGMEM const uint16_t h21v3[147] = { 9084, 4584, 620, 1724, 620, 1720, 616, 608, 620, 1720, 624, 600, 624, 600, 616, 608, 596, 1744, 620, 616, 620, 1724, 624, 1716, 616, 1728, 620, 1724, 600, 620, 628, 596, 616, 608, 616, 1740, 616, 608, 620, 604, 620, 604, 592, 632, 592, 628, 616, 604, 624, 600, 624, 668, 620, 548, 624, 600, 624, 600, 668, 448, 776, 552, 676, 552, 596, 620, 616, 624, 624, 600, 616, 604, 620, 608, 608, 556, 732, 544, 616, 608, 616, 608, 620, 1708, 648, 1696, 648, 1696, 648, 600, 596, 628, 616, 604, 624, 652, 624, 544, 620, 1712, 644, 1700, 644, 608, 596, 596, 620, 1720, 644, 608, 620, 600, 604, 592, 644, 620, 624, 1692, 644, 604, 620, 576, 704, 548, 624, 572, 620, 600, 648, 576, 648, 588, 648, 576, 648, 1696, 652, 1688, 648, 576, 648, 1692, 652, 572, 644, 656, 620};
static PROGMEM const uint16_t h22v3[147] = { 9096, 4552, 652, 1688, 656, 1688, 644, 580, 648, 1696, 648, 576, 648, 576, 648, 580, 676, 1692, 604, 608, 648, 572, 652, 572, 652, 572, 656, 568, 624, 1720, 624, 600, 648, 576, 648, 1708, 648, 576, 648, 576, 648, 576, 652, 568, 656, 568, 648, 576, 648, 576, 648, 592, 644, 580, 644, 604, 620, 576, 648, 576, 652, 600, 592, 600, 624, 604, 624, 612, 644, 584, 620, 600, 644, 580, 648, 572, 652, 572, 652, 604, 600, 580, 656, 1716, 628, 1712, 656, 1688, 644, 580, 656, 568, 648, 576, 648, 572, 652, 572, 656, 1704, 652, 572, 652, 1692, 652, 568, 648, 1696, 648, 576, 648, 572, 652, 572, 656, 584, 648, 1696, 652, 568, 644, 580, 648, 576, 648, 576, 648, 572, 652, 576, 620, 616, 648, 564, 652, 1700, 644, 1700, 648, 576, 648, 1692, 652, 572, 656, 568, 644};
static PROGMEM const uint16_t h23v3[147] = { 9148, 4496, 652, 1720, 616, 1700, 648, 576, 648, 1696, 648, 600, 616, 580, 644, 580, 648, 1720, 624, 588, 648, 576, 648, 604, 624, 572, 652, 568, 648, 1696, 648, 576, 648, 576, 648, 1712, 624, 596, 652, 596, 616, 608, 620, 580, 624, 596, 648, 576, 652, 568, 644, 596, 652, 572, 652, 568, 648, 576, 648, 576, 648, 576, 652, 572, 640, 580, 648, 592, 652, 572, 644, 576, 648, 576, 648, 576, 652, 572, 640, 584, 644, 576, 648, 1740, 596, 1720, 624, 1744, 624, 572, 620, 604, 624, 596, 628, 596, 628, 592, 624, 616, 620, 600, 624, 1720, 628, 676, 548, 1736, 600, 676, 548, 624, 600, 592, 624, 644, 600, 1744, 592, 680, 548, 672, 572, 600, 596, 628, 596, 676, 568, 656, 572, 588, 624, 600, 628, 1736, 596, 1748, 600, 676, 568, 1720, 596, 680, 544, 676, 552};
static PROGMEM const uint16_t h24v3[147] = {9084, 4580, 624, 1720, 624, 1720, 616, 604, 672, 1672, 620, 604, 620, 604, 620, 604, 600, 1744, 592, 1764, 624, 600, 612, 608, 620, 604, 620, 604, 620, 1728, 596, 592, 652, 600, 616, 1744, 600, 624, 592, 632, 592, 628, 616, 608, 616, 604, 624, 600, 624, 600, 624, 616, 620, 600, 604, 592, 652, 600, 624, 600, 616, 656, 568, 604, 676, 548, 624, 612, 624, 600, 624, 596, 616, 608, 620, 604, 620, 600, 624, 600, 624, 600, 616, 1748, 596, 1736, 620, 1724, 620, 604, 620, 600, 628, 568, 644, 580, 644, 608, 596, 1756, 620, 1720, 628, 1716, 616, 580, 644, 1724, 624, 600, 612, 584, 620, 600, 648, 616, 620, 1724, 620, 572, 652, 600, 616, 580, 624, 596, 648, 604, 672, 548, 596, 616, 648, 576, 652, 1716, 616, 1724, 620, 580, 648, 1720, 624, 600, 592, 628, 620};
static PROGMEM const uint16_t h25v3[147] = { 9084, 4556, 648, 1724, 620, 1692, 644, 580, 644, 1700, 644, 576, 648, 576, 652, 572, 652, 1692, 644, 592, 652, 1716, 620, 576, 648, 572, 652, 572, 644, 1724, 620, 576, 648, 576, 648, 1736, 620, 576, 648, 576, 648, 572, 644, 580, 644, 576, 648, 580, 644, 580, 624, 612, 624, 596, 652, 576, 616, 604, 644, 580, 620, 596, 652, 572, 640, 580, 648, 592, 652, 572, 644, 576, 648, 580, 624, 596, 648, 572, 644, 580, 644, 576, 648, 1740, 616, 1724, 624, 1716, 628, 568, 644, 580, 644, 576, 652, 572, 652, 572, 640, 596, 652, 572, 640, 580, 648, 1720, 624, 1720, 592, 604, 644, 604, 620, 604, 620, 592, 644, 1724, 620, 580, 644, 576, 652, 568, 644, 580, 644, 580, 648, 572, 652, 588, 648, 576, 648, 1720, 624, 1720, 616, 576, 648, 1720, 624, 572, 644, 584, 628};
static PROGMEM const uint16_t h26v3[147] = { 9092, 4552, 648, 1724, 620, 1696, 652, 572, 652, 1688, 648, 576, 648, 576, 648, 576, 648, 1692, 656, 1704, 648, 1692, 652, 600, 616, 608, 616, 608, 616, 1724, 624, 600, 624, 600, 616, 1740, 624, 572, 652, 572, 652, 572, 644, 580, 644, 576, 652, 572, 652, 572, 640, 600, 628, 592, 652, 572, 644, 580, 644, 576, 648, 576, 648, 576, 648, 572, 644, 596, 648, 576, 640, 608, 616, 580, 644, 580, 648, 572, 652, 572, 652, 572, 644, 1716, 648, 1692, 644, 1700, 644, 580, 644, 576, 648, 576, 652, 564, 648, 580, 644, 596, 652, 572, 640, 580, 648, 1696, 648, 1696, 648, 576, 648, 572, 652, 572, 644, 596, 648, 1696, 640, 580, 644, 580, 648, 576, 648, 580, 624, 592, 640, 584, 644, 580, 656, 608, 624, 1720, 616, 1700, 644, 580, 644, 1700, 648, 576, 648, 576, 648};
static PROGMEM const uint16_t h27v3[147] = { 9084, 4556, 644, 1728, 620, 1696, 648, 576, 648, 1696, 648, 572, 644, 580, 644, 580, 644, 1696, 652, 588, 644, 580, 648, 1684, 648, 584, 652, 572, 640, 1704, 644, 576, 648, 604, 620, 1740, 616, 584, 620, 596, 648, 576, 652, 572, 652, 572, 640, 580, 648, 576, 648, 592, 652, 572, 644, 576, 648, 576, 648, 576, 648, 576, 640, 580, 644, 584, 640, 596, 652, 568, 644, 580, 648, 580, 644, 576, 648, 576, 648, 572, 652, 572, 644, 1716, 648, 1696, 652, 1692, 620, 600, 644, 580, 648, 572, 652, 572, 652, 572, 652, 1708, 648, 572, 652, 572, 644, 1700, 644, 1696, 648, 576, 652, 572, 640, 580, 644, 596, 652, 1696, 616, 604, 652, 568, 648, 576, 648, 572, 652, 572, 644, 580, 644, 596, 648, 576, 620, 1720, 644, 1696, 648, 576, 652, 1692, 640, 580, 648, 580, 620};
static PROGMEM const uint16_t h28v3[147] = { 9092, 4556, 648, 1724, 620, 1696, 648, 580, 624, 1740, 616, 580, 656, 568, 648, 576, 648, 1696, 648, 1712, 644, 580, 644, 1696, 648, 576, 648, 576, 648, 1696, 640, 580, 644, 580, 644, 1712, 644, 580, 644, 580, 648, 576, 648, 576, 648, 580, 644, 572, 644, 580, 644, 596, 648, 576, 652, 572, 640, 580, 648, 580, 644, 576, 648, 576, 648, 572, 652, 588, 648, 576, 648, 576, 648, 576, 640, 580, 644, 580, 644, 608, 620, 576, 648, 1708, 648, 1700, 624, 1716, 648, 576, 648, 576, 652, 572, 640, 580, 644, 580, 644, 1716, 640, 580, 644, 580, 648, 1696, 648, 1696, 648, 572, 652, 572, 644, 580, 644, 596, 652, 1688, 656, 568, 644, 580, 644, 576, 652, 576, 648, 572, 652, 576, 616, 616, 652, 572, 652, 1692, 644, 1700, 644, 576, 648, 1696, 648, 576, 648, 572, 644};
static PROGMEM const uint16_t h29v3[147] = {9092, 4556, 648, 1724, 620, 1696, 648, 576, 648, 1696, 652, 572, 652, 572, 652, 572, 652, 1696, 648, 588, 648, 1700, 644, 1696, 652, 572, 652, 604, 600, 1712, 644, 580, 644, 576, 648, 1712, 652, 572, 644, 580, 644, 580, 648, 560, 652, 584, 652, 572, 652, 572, 652, 588, 648, 576, 648, 572, 652, 572, 644, 580, 644, 580, 644, 580, 648, 576, 648, 592, 644, 576, 648, 576, 648, 576, 648, 576, 648, 572, 644, 580, 644, 584, 640, 1716, 652, 1692, 652, 1692, 652, 572, 644, 580, 644, 580, 644, 580, 644, 580, 648, 616, 616, 1728, 620, 604, 620, 1696, 648, 1700, 624, 596, 648, 572, 644, 608, 616, 596, 648, 1696, 652, 572, 652, 572, 652, 572, 644, 576, 648, 576, 648, 576, 648, 592, 644, 580, 644, 1700, 644, 1696, 652, 572, 652, 1692, 640, 584, 644, 576, 648};
static PROGMEM const uint16_t h30v3[147] = {9084, 4564, 648, 1724, 620, 1696, 652, 568, 656, 1688, 644, 584, 652, 568, 648, 572, 652, 1692, 644, 1716, 648, 1692, 652, 1692, 644, 580, 644, 580, 644, 1724, 624, 600, 624, 576, 648, 1708, 648, 572, 652, 572, 652, 572, 644, 580, 644, 576, 648, 576, 648, 576, 648, 592, 644, 580, 644, 576, 652, 572, 652, 572, 652, 572, 640, 584, 644, 580, 644, 596, 648, 572, 644, 580, 644, 580, 644, 584, 620, 604, 620, 596, 652, 572, 652, 1708, 648, 1720, 624, 1696, 648, 572, 652, 572, 644, 584, 620, 600, 644, 576, 652, 588, 644, 1700, 648, 576, 648, 1692, 652, 1692, 652, 572, 644, 580, 644, 580, 644, 596, 620, 1720, 648, 576, 648, 576, 648, 576, 648, 576, 648, 572, 644, 580, 644, 596, 648, 572, 644, 1700, 644, 1700, 644, 580, 644, 1696, 652, 576, 648, 572, 640};

static PROGMEM const uint16_t h15v2[147] = { 9084, 4560, 640, 1732, 592, 1720, 648, 580, 644, 1696, 648, 576, 648, 576, 648, 572, 652, 1692, 644, 596, 648, 576, 648, 576, 652, 1696, 624, 1712, 644, 580, 644, 580, 644, 580, 648, 592, 640, 1700, 648, 580, 644, 576, 648, 576, 648, 572, 652, 576, 648, 576, 640, 596, 648, 572, 644, 580, 644, 580, 644, 576, 648, 576, 648, 576, 648, 576, 640, 596, 648, 576, 652, 572, 640, 584, 620, 600, 644, 584, 620, 596, 652, 572, 652, 1704, 652, 1692, 652, 1692, 652, 568, 644, 580, 648, 576, 648, 576, 648, 576, 648, 588, 648, 1696, 648, 1696, 648, 1692, 652, 1692, 652, 572, 644, 584, 628, 588, 648, 592, 644, 1704, 640, 580, 644, 580, 644, 580, 644, 576, 652, 572, 652, 576, 648, 588, 648, 576, 648, 1696, 648, 1720, 624, 572, 652, 1720, 616, 580, 644, 576, 648};
static PROGMEM const uint16_t h16v2[147] = {9092, 4552, 648, 1724, 620, 1696, 648, 572, 652, 1692, 644, 580, 644, 576, 648, 576, 648, 1696, 652, 1704, 648, 580, 644, 576, 652, 1692, 640, 1700, 644, 580, 644, 580, 644, 580, 648, 592, 640, 1704, 644, 576, 648, 576, 648, 576, 648, 576, 648, 576, 648, 576, 652, 588, 644, 580, 644, 580, 648, 576, 648, 576, 648, 572, 652, 572, 652, 572, 644, 596, 648, 580, 644, 600, 624, 572, 644, 580, 644, 580, 644, 580, 644, 580, 648, 1708, 644, 1700, 644, 1700, 644, 584, 620, 596, 652, 572, 652, 572, 644, 580, 644, 592, 652, 1692, 652, 1692, 644, 1684, 648, 1708, 648, 576, 648, 576, 628, 592, 644, 596, 648, 1692, 652, 572, 652, 572, 644, 580, 644, 580, 644, 576, 648, 576, 648, 592, 644, 576, 648, 1696, 648, 1692, 644, 584, 640, 1700, 644, 576, 648, 580, 648};
static PROGMEM const uint16_t h17v2[147] = {9084, 4560, 644, 1732, 592, 1720, 644, 580, 644, 1700, 648, 576, 648, 576, 648, 572, 652, 1696, 648, 588, 648, 1696, 648, 576, 628, 1716, 652, 1696, 648, 576, 648, 572, 652, 572, 656, 584, 652, 1720, 624, 576, 648, 576, 648, 576, 648, 580, 644, 576, 640, 580, 644, 596, 652, 576, 648, 572, 652, 572, 652, 572, 644, 580, 644, 580, 644, 576, 648, 624, 612, 580, 644, 580, 648, 576, 648, 576, 648, 580, 624, 600, 624, 596, 648, 1712, 644, 1700, 648, 1692, 652, 572, 652, 572, 652, 572, 644, 580, 644, 576, 648, 1712, 644, 1696, 648, 1696, 648, 1696, 648, 1724, 624, 568, 644, 580, 644, 580, 644, 592, 644, 1700, 648, 576, 648, 572, 652, 572, 652, 572, 644, 580, 644, 584, 620, 612, 644, 580, 644, 1704, 620, 1716, 648, 580, 624, 1712, 656, 568, 648, 576, 648};
static PROGMEM const uint16_t h18v2[147] = {9088, 4552, 632, 1740, 604, 1712, 624, 680, 576, 1712, 600, 676, 548, 676, 540, 680, 544, 1720, 624, 1760, 596, 1752, 604, 660, 576, 1692, 624, 1744, 600, 680, 544, 680, 544, 676, 548, 692, 544, 1744, 604, 672, 552, 672, 540, 684, 544, 676, 548, 676, 548, 676, 540, 696, 548, 676, 552, 672, 540, 684, 540, 680, 548, 676, 548, 676, 548, 672, 544, 696, 548, 676, 540, 688, 548, 668, 544, 680, 548, 672, 552, 672, 544, 684, 552, 1748, 604, 1736, 620, 1724, 600, 676, 552, 672, 540, 684, 540, 680, 548, 676, 548, 1756, 600, 1740, 604, 1740, 596, 1748, 596, 1744, 624, 652, 552, 672, 540, 680, 548, 692, 540, 1748, 600, 676, 548, 676, 548, 676, 540, 680, 544, 680, 544, 680, 544, 692, 544, 680, 548, 1740, 604, 1740, 604, 672, 544, 1744, 600, 676, 548, 676, 540};
static PROGMEM const uint16_t h19v2[147] = { 9092, 4552, 628, 1744, 604, 1740, 604, 672, 572, 1716, 600, 680, 544, 676, 548, 680, 548, 1736, 596, 704, 544, 680, 544, 1708, 648, 1700, 624, 1748, 596, 684, 544, 676, 548, 676, 548, 692, 544, 1744, 620, 660, 544, 680, 548, 672, 552, 672, 540, 684, 544, 680, 544, 692, 544, 680, 544, 680, 544, 680, 544, 676, 540, 684, 540, 680, 548, 676, 548, 692, 544, 680, 544, 676, 568, 656, 548, 672, 544, 680, 544, 680, 548, 672, 552, 1752, 604, 1740, 592, 1748, 600, 676, 548, 680, 544, 676, 572, 648, 544, 680, 544, 696, 540, 680, 544, 680, 548, 672, 552, 672, 540, 1748, 600, 676, 548, 672, 544, 696, 548, 1740, 596, 684, 540, 676, 548, 676, 540, 680, 544, 680, 544, 684, 544, 692, 540, 680, 548, 1744, 600, 1736, 600, 676, 548, 1740, 596, 680, 544, 680, 544};
static PROGMEM const uint16_t h20v2[147] = {9088, 4552, 632, 1740, 624, 1720, 596, 680, 544, 1748, 600, 672, 548, 676, 552, 672, 540, 1748, 620, 1736, 600, 680, 544, 1744, 624, 1720, 592, 1748, 596, 684, 544, 676, 548, 676, 548, 692, 544, 1740, 604, 676, 540, 680, 544, 680, 548, 676, 548, 676, 548, 672, 544, 696, 552, 672, 572, 648, 544, 680, 548, 676, 548, 672, 544, 680, 544, 680, 544, 692, 544, 684, 540, 684, 544, 672, 552, 672, 540, 684, 544, 676, 548, 676, 548, 1756, 624, 1716, 596, 1748, 596, 680, 548, 676, 548, 676, 540, 680, 544, 680, 544, 692, 544, 680, 544, 684, 544, 676, 548, 672, 544, 1744, 600, 676, 548, 672, 544, 696, 548, 1740, 596, 680, 568, 652, 552, 672, 540, 680, 548, 676, 548, 676, 540, 684, 572, 664, 548, 1740, 596, 1744, 600, 680, 548, 1736, 596, 680, 568, 656, 548};
static PROGMEM const uint16_t h21v2[147] = {9084, 4556, 628, 1744, 600, 1740, 608, 676, 548, 1736, 596, 680, 548, 680, 544, 676, 548, 1740, 596, 696, 552, 1736, 596, 1748, 620, 1724, 600, 1744, 600, 676, 540, 680, 548, 676, 548, 692, 544, 1744, 620, 660, 544, 676, 552, 672, 552, 672, 544, 680, 544, 680, 568, 668, 544, 680, 544, 680, 552, 672, 548, 676, 548, 676, 540, 680, 544, 680, 544, 696, 552, 672, 544, 680, 544, 676, 548, 676, 548, 676, 540, 684, 540, 680, 548, 1756, 600, 1744, 600, 1744, 624, 652, 552, 672, 544, 680, 544, 680, 544, 676, 548, 1756, 620, 656, 552, 676, 548, 672, 540, 684, 544, 1740, 596, 680, 576, 648, 544, 696, 540, 1720, 648, 656, 548, 676, 548, 672, 544, 680, 544, 676, 548, 676, 552, 688, 548, 672, 552, 1740, 596, 1744, 600, 676, 548, 1740, 628, 652, 540, 680, 548};
static PROGMEM const uint16_t h22v2[147] = { 9084, 4548, 656, 1720, 624, 1740, 576, 676, 568, 1724, 600, 672, 544, 680, 544, 680, 544, 1744, 604, 1752, 604, 1748, 596, 1712, 656, 1712, 604, 1740, 592, 684, 544, 680, 544, 676, 548, 692, 576, 1712, 624, 652, 572, 652, 540, 684, 544, 680, 544, 676, 548, 676, 548, 692, 548, 676, 548, 676, 548, 672, 576, 648, 564, 660, 544, 676, 548, 680, 548, 688, 548, 676, 548, 676, 540, 680, 544, 680, 564, 660, 568, 656, 548, 672, 544, 1760, 596, 1748, 608, 1732, 600, 680, 548, 672, 540, 684, 544, 680, 544, 676, 548, 1756, 624, 652, 572, 652, 540, 684, 544, 676, 548, 1740, 624, 652, 544, 680, 544, 696, 540, 1744, 604, 680, 544, 676, 540, 680, 544, 680, 544, 680, 544, 676, 552, 688, 548, 676, 548, 1736, 600, 1744, 620, 660, 548, 1736, 596, 684, 540, 680, 548};
static PROGMEM const uint16_t h23v2[147] = { 9084, 4556, 628, 1748, 596, 1744, 600, 676, 552, 1736, 596, 680, 548, 676, 548, 676, 548, 1740, 628, 664, 552, 672, 540, 688, 548, 668, 548, 676, 548, 1740, 596, 680, 544, 680, 544, 692, 544, 1744, 604, 672, 552, 672, 544, 680, 544, 676, 548, 676, 552, 672, 540, 696, 552, 672, 540, 684, 544, 680, 544, 676, 548, 676, 540, 680, 544, 680, 548, 692, 572, 652, 544, 676, 548, 676, 548, 680, 548, 676, 548, 668, 548, 676, 548, 1756, 600, 1744, 600, 1740, 596, 680, 544, 680, 548, 676, 548, 676, 548, 672, 544, 700, 544, 1744, 604, 672, 540, 684, 576, 648, 544, 1744, 600, 680, 548, 672, 540, 696, 552, 1740, 604, 672, 544, 676, 548, 676, 552, 672, 540, 680, 548, 676, 568, 672, 544, 676, 548, 1740, 628, 1716, 596, 688, 548, 1732, 604, 672, 544, 680, 544};
static PROGMEM const uint16_t h24v2[147] = { 9088, 4556, 628, 1744, 604, 1736, 596, 680, 548, 1740, 604, 676, 572, 648, 544, 680, 548, 1740, 592, 1764, 604, 676, 540, 680, 544, 680, 544, 680, 548, 1740, 596, 680, 544, 680, 544, 696, 540, 1732, 624, 668, 548, 676, 548, 676, 540, 680, 564, 656, 552, 672, 540, 696, 552, 672, 540, 684, 564, 656, 548, 676, 540, 668, 568, 668, 544, 680, 548, 688, 568, 656, 548, 680, 544, 676, 552, 668, 544, 676, 552, 672, 544, 680, 544, 1764, 604, 1704, 652, 1720, 592, 684, 544, 680, 544, 676, 548, 676, 552, 672, 572, 664, 540, 1748, 600, 680, 544, 676, 548, 672, 544, 1744, 600, 680, 548, 672, 540, 696, 552, 1736, 600, 676, 548, 676, 552, 672, 540, 680, 548, 676, 548, 672, 544, 696, 548, 672, 544, 1744, 600, 1744, 604, 672, 540, 1748, 600, 676, 548, 676, 552};
static PROGMEM const uint16_t h25v2[147] = { 9088, 4548, 624, 1744, 600, 1744, 604, 672, 552, 1736, 620, 656, 548, 676, 548, 676, 540, 1748, 596, 696, 540, 1748, 600, 676, 548, 676, 548, 672, 576, 1712, 624, 652, 552, 672, 540, 700, 548, 1740, 596, 680, 544, 680, 544, 676, 552, 672, 540, 688, 548, 668, 548, 692, 544, 676, 548, 676, 572, 656, 548, 668, 548, 676, 548, 676, 548, 672, 544, 696, 548, 672, 576, 648, 544, 676, 552, 672, 540, 680, 548, 676, 548, 680, 544, 1724, 632, 1740, 596, 1744, 604, 672, 540, 684, 544, 676, 548, 676, 548, 672, 544, 1760, 628, 1720, 604, 668, 548, 676, 548, 672, 544, 1744, 600, 676, 548, 676, 552, 684, 540, 1752, 596, 676, 548, 672, 544, 680, 544, 680, 544, 676, 552, 668, 544, 696, 540, 684, 540, 1744, 624, 1716, 632, 648, 544, 1740, 608, 668, 548, 676, 544};
static PROGMEM const uint16_t h26v2[147] = { 9096, 4548, 624, 1752, 604, 1736, 596, 680, 548, 1744, 600, 676, 572, 652, 540, 684, 540, 1720, 628, 1760, 604, 1720, 624, 676, 540, 684, 540, 680, 548, 1732, 624, 664, 548, 676, 552, 688, 548, 1740, 604, 676, 568, 656, 548, 676, 572, 652, 540, 684, 544, 680, 564, 676, 548, 676, 540, 684, 540, 684, 544, 676, 548, 676, 548, 676, 548, 684, 544, 692, 544, 676, 548, 676, 548, 676, 572, 648, 544, 680, 544, 680, 548, 680, 544, 1732, 624, 1720, 624, 1748, 600, 680, 544, 676, 548, 680, 544, 676, 572, 652, 572, 1732, 604, 1744, 600, 676, 552, 672, 540, 680, 548, 1744, 620, 656, 548, 676, 548, 692, 544, 1748, 600, 676, 548, 676, 548, 676, 540, 684, 540, 684, 544, 680, 544, 692, 544, 680, 576, 1712, 600, 1748, 600, 672, 552, 1736, 600, 680, 544, 676, 572};
static PROGMEM const uint16_t h27v2[147] = { 9096, 4548, 624, 1748, 596, 1748, 596, 680, 568, 1696, 628, 676, 568, 656, 540, 684, 572, 1716, 600, 696, 548, 672, 544, 1716, 628, 684, 540, 676, 552, 1740, 604, 672, 544, 680, 544, 692, 544, 1736, 628, 660, 548, 672, 552, 672, 540, 684, 544, 680, 544, 680, 544, 692, 544, 680, 544, 680, 548, 672, 540, 684, 572, 652, 564, 656, 548, 676, 548, 692, 544, 676, 548, 680, 548, 672, 540, 680, 548, 676, 548, 676, 572, 648, 544, 1760, 596, 1748, 596, 1744, 604, 672, 540, 688, 540, 680, 544, 676, 548, 676, 548, 1756, 600, 1744, 592, 684, 552, 672, 544, 676, 548, 1716, 628, 676, 548, 676, 540, 696, 552, 1712, 620, 684, 544, 676, 548, 676, 548, 680, 544, 676, 540, 684, 540, 696, 540, 684, 540, 1748, 600, 1716, 628, 680, 544, 1740, 596, 680, 576, 648, 548};
static PROGMEM const uint16_t h28v2[147] = { 9084, 4552, 632, 1736, 596, 1748, 600, 676, 548, 1740, 596, 680, 576, 648, 544, 676, 552, 1736, 628, 1732, 592, 684, 540, 1748, 600, 676, 548, 676, 548, 1744, 604, 668, 544, 680, 544, 692, 544, 1744, 604, 680, 544, 672, 544, 680, 544, 680, 564, 656, 548, 672, 544, 696, 548, 676, 540, 680, 544, 680, 548, 672, 552, 672, 540, 680, 548, 676, 548, 692, 544, 676, 548, 676, 548, 676, 540, 680, 544, 680, 544, 676, 552, 672, 540, 1764, 592, 1752, 624, 1716, 600, 676, 548, 676, 540, 680, 544, 680, 544, 680, 548, 688, 548, 676, 548, 1740, 624, 656, 548, 672, 544, 1748, 596, 676, 540, 680, 544, 696, 540, 1748, 596, 680, 568, 652, 552, 672, 544, 680, 544, 676, 548, 676, 540, 696, 548, 676, 540, 1748, 596, 1744, 604, 672, 552, 1736, 600, 680, 564, 660, 544};
static PROGMEM const uint16_t h29v2[147] = { 9088, 4548, 632, 1744, 604, 1704, 628, 680, 544, 1744, 604, 672, 540, 668, 568, 668, 548, 1740, 592, 700, 548, 1736, 600, 1744, 620, 660, 544, 676, 540, 1744, 600, 676, 548, 676, 552, 692, 540, 1740, 596, 680, 544, 684, 540, 676, 552, 672, 572, 652, 544, 676, 548, 692, 544, 676, 548, 676, 548, 676, 540, 680, 544, 676, 548, 676, 540, 680, 544, 696, 540, 680, 544, 680, 568, 652, 552, 672, 540, 680, 548, 676, 548, 672, 540, 1764, 592, 1748, 620, 1720, 604, 676, 548, 672, 544, 680, 544, 676, 548, 672, 544, 1760, 608, 668, 544, 1740, 604, 672, 544, 680, 544, 1748, 596, 676, 552, 668, 544, 692, 544, 1744, 600, 676, 540, 680, 544, 680, 544, 676, 552, 668, 544, 680, 544, 692, 544, 680, 548, 1736, 596, 1744, 600, 676, 552, 1740, 592, 680, 548, 672, 552};
static PROGMEM const uint16_t h30v2[147] = { 9088, 4548, 636, 1736, 596, 1744, 600, 680, 548, 1740, 592, 684, 540, 680, 548, 676, 548, 1740, 624, 1732, 592, 1728, 628, 1736, 620, 656, 548, 672, 544, 1716, 628, 676, 548, 680, 548, 688, 544, 1740, 596, 684, 572, 648, 568, 656, 548, 672, 540, 684, 544, 680, 544, 692, 544, 680, 544, 680, 544, 676, 572, 652, 540, 680, 544, 680, 544, 680, 568, 668, 568, 656, 548, 672, 540, 684, 572, 648, 548, 684, 540, 676, 540, 680, 544, 1760, 596, 1720, 644, 1696, 628, 676, 540, 680, 544, 680, 544, 676, 552, 676, 548, 684, 552, 1740, 604, 1708, 624, 680, 548, 672, 540, 1748, 620, 656, 568, 660, 544, 688, 568, 1708, 628, 664, 548, 676, 540, 676, 548, 676, 572, 652, 540, 680, 544, 696, 540, 684, 552, 1736, 600, 1740, 624, 652, 540, 1748, 600, 680, 544, 676, 548};

static PROGMEM const uint16_t h15v1[147] = { 9084, 4548, 624, 1744, 600, 1740, 596, 684, 540, 1744, 604, 676, 548, 672, 540, 684, 544, 1716, 628, 692, 544, 676, 548, 676, 548, 1740, 596, 1744, 600, 676, 548, 676, 548, 676, 540, 1760, 596, 1748, 596, 676, 548, 676, 548, 672, 544, 680, 564, 660, 544, 676, 572, 668, 544, 676, 540, 684, 540, 680, 548, 676, 548, 672, 572, 648, 568, 656, 548, 688, 548, 676, 548, 672, 540, 684, 544, 676, 548, 676, 548, 672, 544, 680, 564, 1736, 600, 1740, 604, 1736, 600, 676, 548, 676, 548, 676, 540, 680, 544, 676, 548, 1756, 632, 1684, 628, 1712, 624, 680, 544, 660, 576, 1700, 624, 680, 544, 676, 548, 692, 544, 1740, 604, 672, 544, 680, 544, 680, 544, 676, 572, 652, 540, 680, 568, 668, 564, 656, 548, 1744, 604, 1732, 600, 676, 548, 1740, 628, 648, 564, 656, 548};
static PROGMEM const uint16_t h16v1[147] = { 9084, 4556, 648, 1724, 600, 1716, 628, 680, 544, 1712, 624, 680, 544, 680, 544, 680, 548, 1740, 592, 1740, 648, 656, 548, 676, 540, 1720, 624, 1724, 652, 648, 544, 604, 620, 596, 628, 1732, 624, 1720, 648, 572, 652, 600, 592, 604, 684, 540, 624, 596, 628, 596, 628, 612, 624, 596, 628, 596, 628, 596, 628, 592, 624, 600, 644, 580, 624, 604, 624, 612, 676, 544, 628, 596, 680, 540, 620, 604, 656, 568, 624, 596, 628, 596, 628, 1760, 596, 1720, 624, 1720, 624, 596, 652, 568, 624, 600, 628, 596, 628, 592, 620, 1740, 628, 1716, 628, 1712, 620, 604, 624, 596, 628, 1716, 628, 596, 620, 600, 624, 616, 620, 1720, 624, 600, 624, 600, 624, 596, 652, 572, 620, 604, 620, 600, 628, 612, 620, 604, 624, 1744, 600, 1716, 628, 676, 548, 1744, 600, 672, 544, 680, 544};
static PROGMEM const uint16_t h17v1[147] = { 9084, 4556, 628, 1744, 600, 1740, 604, 676, 572, 1716, 596, 680, 564, 608, 596, 604, 620, 1744, 604, 688, 544, 1748, 600, 676, 548, 1712, 624, 1720, 624, 680, 544, 680, 544, 676, 548, 1760, 596, 1724, 620, 676, 552, 672, 540, 604, 620, 680, 568, 656, 548, 676, 548, 688, 548, 624, 620, 656, 548, 596, 620, 604, 640, 608, 616, 580, 648, 656, 548, 692, 544, 676, 548, 676, 568, 652, 576, 648, 544, 680, 544, 680, 544, 604, 620, 1760, 596, 1720, 624, 1720, 628, 676, 548, 620, 592, 684, 544, 680, 544, 600, 624, 648, 596, 592, 624, 680, 544, 1748, 596, 676, 552, 1736, 628, 648, 544, 632, 596, 692, 544, 1744, 600, 676, 548, 676, 548, 676, 540, 680, 544, 632, 604, 612, 600, 640, 596, 680, 544, 1744, 600, 1716, 620, 684, 540, 1748, 600, 676, 548, 676, 548};
static PROGMEM const uint16_t h18v1[147] = { 9088, 4556, 624, 1748, 600, 1740, 604, 672, 552, 1740, 624, 652, 544, 680, 544, 680, 544, 1716, 628, 1756, 600, 1744, 600, 680, 544, 1748, 600, 1716, 628, 672, 540, 688, 548, 672, 544, 1756, 620, 1728, 596, 680, 564, 660, 544, 684, 544, 676, 548, 676, 548, 676, 548, 688, 548, 676, 548, 676, 548, 676, 540, 684, 540, 680, 544, 680, 548, 676, 548, 692, 544, 680, 544, 680, 544, 680, 544, 680, 544, 684, 540, 680, 548, 672, 552, 1756, 600, 1748, 596, 1748, 596, 680, 544, 680, 568, 656, 548, 672, 552, 676, 548, 688, 548, 676, 548, 676, 548, 1720, 628, 676, 568, 1720, 604, 672, 544, 680, 544, 692, 552, 1740, 596, 684, 540, 680, 544, 684, 552, 668, 544, 680, 548, 676, 568, 672, 540, 684, 544, 1744, 600, 1744, 620, 660, 544, 1716, 628, 680, 548, 672, 552};
static PROGMEM const uint16_t h19v1[147] = {9084, 4552, 628, 1744, 604, 1736, 596, 684, 572, 1716, 596, 680, 544, 600, 648, 656, 568, 1716, 596, 696, 552, 592, 620, 1752, 592, 1752, 596, 1716, 648, 656, 568, 600, 596, 628, 596, 1760, 596, 1744, 620, 660, 544, 600, 624, 596, 620, 636, 600, 588, 624, 680, 544, 640, 596, 600, 624, 600, 624, 596, 620, 604, 652, 652, 564, 660, 564, 600, 604, 612, 624, 624, 600, 624, 620, 600, 596, 632, 600, 592, 624, 600, 624, 584, 652, 1720, 624, 1744, 600, 1744, 592, 680, 544, 604, 652, 596, 596, 600, 628, 592, 652, 588, 648, 600, 592, 632, 604, 1736, 596, 680, 548, 1740, 592, 684, 540, 628, 600, 640, 624, 1716, 596, 680, 548, 620, 604, 592, 652, 600, 596, 676, 580, 644, 548, 688, 568, 604, 600, 1740, 596, 1720, 624, 680, 544, 1740, 596, 680, 544, 676, 548};
static PROGMEM const uint16_t h20v1[147] = {9084, 4552, 652, 1720, 624, 1716, 600, 680, 576, 1712, 600, 676, 548, 676, 548, 672, 544, 1748, 596, 1760, 596, 680, 544, 1748, 596, 1748, 600, 1740, 604, 672, 540, 684, 544, 628, 596, 1760, 596, 1748, 596, 680, 544, 628, 596, 680, 544, 628, 600, 624, 600, 620, 592, 620, 628, 620, 604, 596, 620, 628, 596, 600, 624, 600, 624, 600, 624, 596, 620, 644, 600, 600, 628, 596, 628, 592, 620, 604, 624, 596, 628, 596, 620, 600, 624, 1760, 596, 1752, 600, 1708, 628, 676, 548, 596, 652, 600, 592, 604, 620, 628, 596, 1760, 596, 684, 540, 608, 620, 1728, 624, 668, 548, 1744, 600, 676, 548, 600, 628, 632, 600, 1740, 596, 680, 544, 628, 596, 624, 604, 596, 628, 596, 616, 632, 596, 616, 628, 596, 620, 1752, 600, 1736, 600, 680, 544, 1744, 600, 676, 540, 632, 592};
static PROGMEM const uint16_t h21v1[147] = {9084, 4556, 624, 1744, 624, 1720, 592, 684, 540, 1748, 620, 656, 548, 596, 628, 592, 652, 1716, 596, 696, 540, 1748, 596, 1720, 628, 1740, 604, 1740, 592, 684, 544, 684, 548, 592, 624, 1760, 596, 1716, 628, 664, 572, 584, 628, 676, 540, 600, 624, 628, 596, 680, 548, 692, 540, 604, 624, 596, 628, 620, 592, 632, 596, 600, 624, 676, 548, 596, 628, 692, 544, 600, 624, 600, 624, 676, 540, 628, 596, 600, 624, 676, 552, 592, 652, 1732, 624, 1692, 620, 1748, 596, 680, 544, 680, 548, 672, 572, 576, 648, 572, 624, 1760, 624, 652, 544, 676, 548, 1748, 596, 676, 548, 1740, 596, 680, 544, 600, 624, 692, 564, 1728, 596, 676, 548, 624, 592, 628, 596, 684, 552, 616, 600, 620, 624, 616, 596, 628, 600, 1736, 596, 1720, 624, 676, 552, 1740, 624, 648, 544, 600, 628};
static PROGMEM const uint16_t h22v1[147] = {9088, 4552, 632, 1740, 592, 1748, 600, 680, 572, 1688, 628, 676, 568, 656, 548, 676, 540, 1720, 656, 1732, 600, 1744, 604, 1736, 596, 1748, 596, 1748, 600, 676, 548, 676, 548, 676, 548, 1752, 604, 1736, 600, 680, 544, 676, 548, 676, 548, 676, 540, 680, 544, 688, 548, 684, 548, 672, 544, 680, 544, 676, 548, 680, 548, 672, 540, 680, 544, 676, 552, 688, 568, 656, 548, 672, 540, 684, 540, 680, 544, 680, 548, 676, 548, 672, 540, 1764, 604, 1712, 644, 1724, 620, 656, 548, 672, 572, 652, 544, 676, 548, 676, 548, 1756, 620, 656, 548, 672, 576, 1712, 600, 676, 548, 1740, 596, 680, 544, 680, 564, 672, 544, 1716, 628, 676, 572, 648, 564, 664, 552, 668, 544, 680, 544, 676, 552, 688, 548, 676, 548, 1736, 596, 1748, 596, 680, 548, 1736, 596, 684, 544, 676, 548};
static PROGMEM const uint16_t h23v1[147] = {9092, 4552, 628, 1744, 600, 1740, 596, 684, 572, 1716, 616, 668, 548, 588, 648, 656, 568, 1720, 592, 700, 548, 596, 628, 596, 628, 596, 620, 684, 540, 1748, 596, 680, 568, 656, 548, 1764, 600, 1708, 628, 676, 548, 596, 648, 576, 620, 604, 620, 628, 596, 628, 596, 696, 540, 604, 644, 576, 628, 596, 628, 676, 548, 676, 548, 596, 620, 604, 620, 624, 620, 596, 632, 676, 548, 592, 620, 604, 624, 624, 600, 624, 600, 628, 596, 1760, 596, 1744, 600, 1748, 596, 680, 548, 676, 548, 676, 540, 680, 544, 684, 552, 1752, 604, 672, 552, 672, 540, 1748, 596, 684, 544, 1744, 600, 684, 540, 676, 548, 692, 544, 1744, 600, 680, 548, 672, 540, 684, 540, 684, 544, 676, 548, 676, 548, 692, 544, 680, 544, 1744, 600, 1744, 624, 652, 572, 1716, 596, 680, 548, 676, 548};
static PROGMEM const uint16_t h24v1[147] = {9080, 4552, 632, 1740, 604, 1712, 620, 684, 576, 1712, 620, 660, 544, 672, 544, 684, 540, 1720, 624, 1760, 596, 680, 544, 680, 544, 628, 600, 676, 548, 1736, 596, 680, 568, 604, 600, 1760, 596, 1744, 600, 676, 548, 596, 620, 628, 628, 596, 596, 676, 548, 596, 628, 612, 624, 624, 604, 620, 624, 572, 632, 616, 600, 596, 628, 624, 600, 680, 544, 696, 552, 584, 628, 620, 596, 604, 652, 568, 624, 628, 596, 600, 628, 596, 628, 1756, 600, 1740, 604, 1744, 600, 672, 544, 628, 596, 600, 624, 632, 604, 612, 600, 640, 596, 1748, 596, 680, 548, 1740, 624, 656, 540, 1748, 616, 660, 564, 656, 548, 612, 656, 1712, 600, 676, 548, 676, 552, 672, 540, 680, 544, 680, 544, 628, 620, 672, 564, 660, 552, 1736, 600, 1716, 628, 672, 544, 1748, 596, 680, 544, 680, 544};
static PROGMEM const uint16_t h25v1[147] = {9084, 4552, 628, 1744, 600, 1712, 624, 604, 652, 1688, 624, 596, 632, 592, 652, 572, 652, 1688, 628, 612, 620, 1720, 628, 596, 628, 592, 620, 604, 652, 1688, 628, 596, 628, 596, 648, 1708, 648, 1692, 620, 608, 628, 596, 628, 588, 628, 596, 620, 600, 624, 600, 644, 592, 624, 600, 624, 600, 624, 596, 628, 596, 620, 600, 624, 600, 624, 604, 620, 616, 632, 584, 628, 596, 620, 600, 656, 568, 624, 596, 632, 592, 620, 600, 624, 1732, 624, 1716, 628, 1720, 628, 588, 624, 600, 624, 600, 624, 600, 628, 588, 624, 616, 620, 1720, 624, 600, 624, 1716, 652, 572, 620, 1720, 648, 576, 648, 572, 632, 608, 648, 1692, 620, 604, 620, 600, 624, 600, 628, 620, 604, 592, 620, 604, 624, 612, 652, 572, 624, 1716, 628, 1740, 592, 604, 624, 1716, 628, 596, 628, 592, 624};
static PROGMEM const uint16_t h26v1[147] = {9084, 4552, 628, 1740, 628, 1688, 624, 600, 624, 1716, 628, 596, 620, 600, 624, 600, 624, 1720, 628, 1728, 624, 1720, 624, 596, 620, 604, 632, 588, 624, 1720, 628, 592, 620, 604, 624, 1736, 628, 1712, 624, 596, 628, 624, 600, 592, 620, 604, 624, 596, 628, 596, 628, 608, 628, 596, 628, 592, 624, 600, 624, 600, 624, 596, 628, 596, 620, 600, 624, 616, 652, 572, 620, 604, 632, 588, 628, 596, 680, 540, 632, 592, 620, 600, 628, 1760, 592, 1724, 624, 1716, 648, 656, 548, 592, 652, 572, 624, 600, 624, 600, 624, 612, 624, 1716, 628, 596, 620, 1720, 624, 600, 624, 1716, 628, 596, 620, 600, 624, 616, 620, 1724, 652, 568, 648, 660, 544, 672, 552, 592, 620, 604, 620, 600, 628, 612, 620, 604, 644, 1724, 600, 1716, 628, 596, 620, 1720, 624, 600, 644, 576, 628};
static PROGMEM const uint16_t h27v1[147] = {9088, 4548, 696, 1676, 616, 1696, 648, 576, 652, 1688, 656, 572, 620, 600, 648, 604, 596, 1716, 652, 588, 648, 572, 652, 1696, 616, 604, 644, 576, 648, 1696, 648, 576, 648, 576, 648, 1708, 648, 1692, 652, 572, 644, 608, 616, 580, 644, 576, 652, 572, 652, 572, 652, 588, 648, 572, 652, 572, 652, 572, 620, 600, 648, 576, 648, 576, 648, 576, 648, 592, 644, 576, 648, 576, 648, 576, 704, 544, 628, 568, 644, 580, 644, 580, 648, 1712, 640, 1700, 648, 1696, 648, 576, 648, 572, 652, 572, 644, 576, 648, 576, 648, 592, 644, 1700, 624, 596, 648, 1692, 656, 568, 644, 1696, 648, 576, 652, 572, 620, 616, 648, 1696, 652, 568, 644, 580, 644, 576, 652, 572, 652, 568, 644, 580, 656, 584, 652, 568, 648, 1696, 648, 1696, 628, 596, 628, 1708, 648, 576, 648, 576, 628};
static PROGMEM const uint16_t h28v1[147] = {9088, 4556, 644, 1700, 708, 1632, 652, 572, 652, 1720, 624, 572, 652, 572, 644, 580, 624, 1720, 644, 1716, 652, 572, 652, 1688, 644, 580, 648, 576, 648, 1696, 648, 580, 624, 596, 648, 1712, 644, 1700, 644, 580, 648, 572, 652, 572, 652, 572, 652, 572, 644, 580, 624, 628, 628, 580, 656, 572, 620, 600, 624, 600, 644, 580, 648, 572, 652, 572, 652, 588, 648, 576, 648, 572, 652, 572, 704, 504, 712, 528, 644, 580, 644, 576, 648, 1712, 644, 1700, 644, 1700, 648, 576, 624, 596, 652, 600, 624, 572, 644, 580, 652, 1708, 648, 1692, 656, 568, 644, 1700, 644, 580, 624, 1720, 624, 596, 652, 568, 644, 624, 624, 1692, 652, 572, 644, 576, 648, 576, 648, 576, 648, 572, 652, 572, 644, 624, 600, 596, 628, 1712, 656, 1688, 644, 580, 644, 1696, 652, 572, 652, 572, 640};
static PROGMEM const uint16_t h29v1[147] = {9088, 4552, 632, 1740, 604, 1716, 628, 596, 648, 1696, 628, 676, 552, 672, 572, 656, 568, 1720, 592, 700, 548, 1716, 628, 1716, 628, 676, 548, 676, 552, 1744, 600, 672, 540, 684, 540, 1736, 652, 1696, 628, 676, 548, 672, 544, 600, 624, 600, 624, 600, 624, 600, 624, 640, 596, 604, 620, 684, 552, 672, 544, 596, 628, 676, 548, 596, 620, 684, 540, 644, 624, 572, 620, 680, 544, 680, 564, 580, 624, 680, 544, 676, 552, 672, 540, 1764, 604, 1712, 632, 1712, 620, 684, 544, 628, 596, 628, 596, 600, 644, 580, 624, 1760, 596, 1716, 632, 676, 544, 1744, 604, 672, 540, 1748, 596, 680, 544, 604, 624, 640, 592, 1748, 600, 676, 548, 624, 620, 604, 600, 624, 600, 620, 596, 604, 620, 644, 600, 676, 540, 1748, 596, 1720, 624, 680, 568, 1720, 624, 652, 540, 604, 632};
static PROGMEM const uint16_t h30v1[147] = {9080, 4584, 616, 1700, 648, 1696, 648, 576, 648, 1692, 652, 572, 644, 580, 644, 576, 648, 1696, 648, 1708, 648, 1696, 648, 1692, 652, 572, 644, 580, 644, 1700, 644, 576, 648, 576, 652, 1704, 648, 1696, 652, 596, 616, 580, 644, 584, 632, 584, 648, 576, 652, 556, 656, 596, 652, 576, 624, 596, 620, 600, 644, 580, 648, 576, 628, 592, 652, 576, 636, 596, 652, 572, 652, 568, 648, 604, 620, 576, 628, 592, 652, 572, 620, 600, 648, 1716, 628, 1712, 624, 1716, 628, 596, 628, 592, 620, 608, 628, 592, 656, 564, 628, 1720, 656, 1700, 624, 600, 624, 1716, 628, 596, 652, 1688, 624, 600, 624, 600, 624, 616, 620, 1724, 620, 600, 624, 596, 632, 592, 652, 572, 620, 604, 620, 600, 628, 612, 620, 604, 624, 1716, 648, 1696, 648, 572, 624, 1720, 624, 596, 628, 596, 628};

static PROGMEM const uint16_t h15v4[147] = { 9092, 4548, 632, 1740, 592, 1748, 600, 684, 548, 1708, 628, 676, 568, 656, 572, 648, 576, 1712, 600, 696, 572, 648, 564, 660, 564, 1724, 600, 1748, 600, 672, 540, 684, 540, 680, 544, 696, 552, 672, 540, 680, 548, 676, 548, 680, 544, 680, 544, 672, 572, 652, 544, 696, 548, 676, 548, 672, 544, 680, 544, 680, 544, 676, 548, 676, 572, 648, 544, 696, 548, 680, 548, 672, 540, 680, 548, 676, 548, 676, 568, 652, 540, 684, 540, 1764, 604, 1712, 620, 1724, 624, 676, 548, 676, 548, 676, 548, 672, 544, 680, 564, 676, 548, 672, 544, 1720, 624, 1744, 600, 676, 572, 1716, 596, 684, 540, 684, 540, 696, 552, 1736, 628, 648, 544, 680, 548, 676, 548, 672, 552, 672, 540, 684, 544, 700, 544, 660, 576, 1696, 628, 1740, 604, 672, 552, 1708, 628, 676, 548, 676, 548};
static PROGMEM const uint16_t h16v4[147] = { 9088, 4552, 680, 1692, 620, 1724, 624, 652, 540, 1748, 596, 680, 544, 680, 544, 680, 548, 1744, 600, 1724, 632, 672, 572, 652, 544, 1744, 600, 1716, 628, 680, 544, 676, 548, 676, 548, 692, 544, 676, 548, 676, 572, 652, 540, 684, 540, 684, 544, 676, 548, 676, 548, 692, 544, 680, 544, 680, 544, 676, 548, 676, 552, 672, 540, 684, 540, 684, 544, 692, 552, 672, 540, 684, 544, 676, 548, 676, 548, 676, 548, 676, 548, 672, 544, 1760, 604, 1744, 604, 1736, 596, 676, 548, 676, 552, 672, 540, 684, 540, 680, 548, 1756, 596, 680, 548, 1740, 592, 1748, 596, 680, 544, 1744, 604, 672, 540, 684, 552, 684, 540, 1720, 648, 656, 548, 676, 548, 672, 540, 684, 544, 680, 544, 676, 548, 692, 544, 676, 548, 1740, 604, 1736, 600, 676, 548, 1716, 628, 676, 540, 680, 544};
static PROGMEM const uint16_t h17v4[147] = {9088, 4548, 624, 1748, 628, 1712, 620, 656, 548, 1740, 604, 672, 544, 680, 544, 676, 548, 1744, 600, 692, 544, 1744, 624, 652, 540, 1748, 596, 1716, 628, 676, 552, 672, 552, 672, 540, 696, 552, 672, 552, 672, 540, 684, 544, 676, 548, 684, 540, 676, 548, 672, 544, 700, 544, 676, 548, 676, 540, 680, 544, 680, 544, 676, 548, 676, 548, 676, 540, 696, 548, 676, 548, 676, 540, 680, 544, 680, 544, 684, 540, 676, 552, 672, 572, 1736, 600, 1740, 604, 1740, 604, 672, 540, 684, 544, 676, 548, 676, 548, 676, 548, 1728, 628, 676, 548, 1744, 600, 1712, 652, 652, 544, 1744, 600, 676, 548, 672, 544, 696, 548, 1740, 604, 672, 544, 680, 544, 680, 544, 676, 548, 676, 548, 676, 540, 700, 544, 676, 552, 1736, 596, 1748, 596, 680, 544, 1744, 624, 652, 540, 680, 544};
static PROGMEM const uint16_t h18v4[147] = { 9088, 4548, 632, 1740, 624, 1720, 624, 652, 564, 1724, 600, 676, 548, 676, 548, 672, 544, 1744, 600, 1760, 628, 1712, 620, 660, 544, 1740, 604, 1712, 624, 680, 544, 676, 568, 656, 548, 692, 544, 676, 548, 676, 572, 648, 544, 680, 544, 680, 544, 676, 552, 672, 540, 700, 548, 676, 548, 672, 572, 648, 548, 676, 568, 656, 548, 672, 540, 684, 572, 668, 548, 672, 544, 680, 544, 676, 548, 680, 544, 676, 540, 680, 544, 680, 552, 1724, 656, 1712, 620, 1728, 596, 676, 548, 676, 540, 680, 544, 680, 544, 680, 544, 1756, 600, 680, 544, 1744, 624, 1692, 632, 668, 544, 1744, 600, 680, 544, 676, 540, 700, 568, 1720, 604, 672, 540, 680, 548, 676, 548, 676, 548, 672, 540, 684, 544, 692, 540, 684, 540, 1752, 596, 1740, 604, 676, 568, 1692, 624, 680, 544, 676, 548};
static PROGMEM const uint16_t h19v4[147] = { 9084, 4552, 628, 1740, 628, 1716, 628, 648, 544, 1744, 600, 676, 548, 676, 540, 680, 576, 1712, 620, 676, 540, 680, 544, 1744, 600, 1744, 604, 1736, 596, 684, 540, 680, 544, 680, 544, 692, 564, 660, 568, 652, 548, 676, 552, 672, 540, 680, 544, 680, 548, 680, 544, 692, 544, 676, 548, 676, 568, 652, 552, 672, 576, 648, 544, 680, 544, 676, 548, 692, 544, 680, 544, 680, 544, 680, 544, 676, 552, 672, 552, 672, 540, 680, 544, 1760, 628, 1720, 592, 1720, 648, 656, 548, 672, 552, 672, 540, 680, 568, 656, 548, 692, 540, 1720, 648, 1720, 604, 1736, 596, 680, 576, 1712, 604, 676, 548, 672, 540, 700, 548, 1740, 604, 672, 540, 684, 544, 676, 548, 676, 548, 676, 548, 676, 548, 688, 548, 676, 548, 1740, 596, 1744, 600, 676, 548, 1740, 624, 656, 540, 680, 544};
static PROGMEM const uint16_t h20v4[147] = { 9088, 4548, 632, 1740, 624, 1716, 600, 676, 548, 1740, 604, 672, 544, 680, 544, 680, 564, 1720, 604, 1756, 600, 676, 572, 1720, 600, 1740, 596, 1748, 596, 676, 548, 676, 552, 672, 540, 696, 552, 672, 540, 684, 540, 680, 548, 676, 548, 676, 548, 672, 540, 684, 544, 692, 540, 684, 544, 676, 548, 676, 548, 672, 544, 680, 544, 684, 540, 676, 548, 692, 544, 676, 548, 676, 548, 672, 544, 680, 544, 684, 540, 680, 544, 676, 540, 1764, 624, 1716, 596, 1748, 596, 680, 544, 676, 548, 676, 540, 684, 552, 668, 544, 696, 540, 1744, 600, 1740, 596, 1744, 600, 680, 544, 1740, 604, 672, 576, 640, 572, 676, 548, 1740, 596, 676, 548, 676, 548, 672, 544, 680, 544, 676, 548, 676, 548, 692, 544, 676, 548, 1740, 596, 1744, 600, 680, 544, 1740, 604, 676, 540, 680, 544};
static PROGMEM const uint16_t h21v4[147] = {9084, 4544, 628, 1744, 620, 1720, 604, 672, 544, 1744, 600, 676, 548, 672, 544, 684, 540, 1740, 624, 668, 548, 1740, 604, 1736, 600, 1752, 600, 1704, 632, 680, 544, 672, 544, 680, 544, 692, 544, 680, 544, 676, 548, 676, 548, 672, 544, 680, 544, 676, 548, 676, 548, 692, 544, 676, 548, 676, 552, 668, 544, 680, 544, 680, 544, 676, 552, 672, 540, 696, 552, 672, 572, 648, 544, 680, 568, 652, 552, 672, 552, 672, 540, 684, 544, 1756, 596, 1748, 596, 1744, 604, 672, 552, 672, 540, 684, 540, 680, 548, 676, 548, 692, 544, 1740, 604, 1712, 652, 1716, 620, 656, 548, 1740, 604, 672, 540, 684, 544, 692, 552, 1736, 600, 676, 548, 676, 548, 676, 548, 672, 544, 680, 544, 680, 544, 692, 576, 648, 544, 1744, 600, 1748, 596, 676, 552, 1732, 600, 676, 572, 652, 540};
static PROGMEM const uint16_t h22v4[147] = { 9084, 4556, 680, 1688, 624, 1720, 604, 672, 540, 1720, 628, 676, 568, 656, 548, 676, 548, 1736, 600, 1764, 600, 1740, 604, 1740, 628, 1688, 624, 1744, 620, 660, 544, 676, 548, 676, 552, 688, 544, 680, 548, 676, 548, 676, 548, 672, 540, 684, 544, 680, 544, 680, 544, 692, 544, 680, 544, 680, 544, 680, 544, 680, 544, 680, 548, 672, 552, 672, 540, 696, 552, 672, 540, 684, 540, 684, 544, 676, 548, 676, 548, 672, 540, 684, 544, 1760, 604, 1736, 620, 1724, 600, 676, 548, 676, 548, 676, 548, 672, 544, 676, 548, 692, 544, 1744, 600, 1744, 600, 1740, 596, 688, 544, 1708, 628, 680, 544, 676, 548, 692, 544, 1744, 600, 680, 544, 676, 540, 680, 544, 680, 544, 680, 544, 680, 548, 688, 548, 680, 544, 1740, 604, 1740, 604, 672, 596, 1692, 600, 676, 548, 684, 540};
static PROGMEM const uint16_t h23v4[147] = {9092, 4556, 676, 1692, 600, 1748, 616, 664, 540, 1712, 632, 676, 540, 680, 544, 680, 576, 1712, 604, 696, 548, 672, 544, 680, 544, 680, 544, 668, 568, 1704, 628, 676, 548, 676, 548, 692, 544, 676, 548, 680, 548, 672, 540, 684, 544, 680, 544, 680, 544, 676, 548, 692, 544, 680, 544, 680, 544, 680, 544, 676, 548, 676, 572, 652, 540, 684, 540, 704, 544, 672, 552, 676, 548, 672, 544, 680, 544, 676, 568, 656, 548, 676, 548, 1728, 648, 1724, 600, 1716, 628, 676, 572, 652, 540, 684, 540, 680, 544, 680, 548, 1728, 624, 1748, 600, 1748, 596, 1740, 604, 676, 548, 1744, 600, 676, 592, 628, 544, 692, 576, 1716, 596, 676, 548, 676, 548, 672, 544, 680, 544, 680, 544, 676, 548, 692, 544, 680, 544, 1744, 624, 1716, 628, 648, 544, 1748, 596, 680, 548, 676, 548};
static PROGMEM const uint16_t h24v4[147] = { 9092, 4552, 628, 1744, 600, 1716, 652, 652, 540, 1752, 604, 672, 540, 684, 544, 676, 548, 1716, 628, 1760, 596, 680, 544, 684, 552, 668, 544, 680, 544, 1716, 628, 676, 552, 676, 548, 688, 548, 676, 548, 676, 548, 672, 596, 628, 544, 680, 544, 680, 544, 680, 544, 696, 540, 684, 592, 632, 540, 684, 540, 680, 548, 676, 548, 676, 600, 624, 548, 696, 552, 668, 564, 656, 548, 676, 548, 676, 552, 672, 540, 684, 540, 684, 544, 1760, 604, 1736, 600, 1744, 600, 680, 544, 676, 548, 676, 548, 676, 548, 680, 544, 1732, 624, 1720, 624, 1748, 596, 1716, 632, 672, 552, 1740, 604, 672, 540, 684, 544, 696, 548, 1740, 604, 676, 548, 676, 548, 676, 592, 632, 540, 684, 592, 628, 544, 696, 552, 672, 540, 1752, 596, 1744, 600, 680, 544, 1716, 628, 676, 548, 676, 548};
static PROGMEM const uint16_t h25v4[147] = { 9096, 4548, 652, 1720, 596, 1720, 624, 680, 544, 1744, 600, 676, 548, 676, 540, 680, 544, 1744, 600, 696, 548, 1744, 604, 668, 544, 680, 544, 680, 568, 1720, 604, 672, 540, 684, 540, 700, 548, 676, 548, 672, 544, 680, 544, 680, 544, 680, 544, 676, 548, 676, 548, 692, 544, 680, 544, 676, 548, 676, 552, 672, 540, 684, 540, 680, 544, 680, 548, 692, 540, 680, 544, 680, 548, 676, 548, 676, 548, 672, 544, 680, 544, 680, 576, 1728, 596, 1744, 600, 1716, 628, 676, 600, 624, 548, 676, 548, 672, 544, 680, 544, 1760, 604, 1712, 624, 1744, 600, 1740, 604, 676, 548, 1736, 600, 680, 544, 676, 548, 692, 544, 1744, 600, 676, 548, 676, 548, 676, 540, 680, 544, 680, 596, 624, 548, 692, 596, 628, 544, 1744, 600, 1744, 600, 676, 548, 1712, 624, 680, 596, 624, 548};
static PROGMEM const uint16_t h26v4[147] = {9092, 4548, 632, 1736, 596, 1748, 620, 656, 548, 1740, 604, 672, 544, 680, 544, 680, 544, 1744, 600, 1756, 600, 1744, 600, 676, 548, 676, 548, 676, 548, 1740, 624, 652, 544, 680, 544, 696, 572, 660, 540, 680, 548, 668, 544, 680, 544, 680, 544, 676, 552, 672, 552, 688, 544, 680, 568, 652, 552, 672, 572, 652, 572, 652, 572, 648, 548, 676, 548, 692, 544, 680, 544, 676, 548, 676, 548, 676, 548, 676, 548, 676, 540, 680, 576, 1704, 628, 1740, 596, 1724, 628, 672, 576, 648, 544, 680, 568, 652, 548, 676, 552, 688, 544, 680, 548, 676, 548, 672, 552, 1712, 620, 1748, 600, 680, 544, 680, 576, 664, 548, 1716, 628, 676, 540, 684, 544, 676, 544, 680, 544, 680, 600, 624, 548, 692, 540, 684, 544, 1716, 680, 1664, 628, 676, 548, 1716, 628, 676, 548, 676, 540};
static PROGMEM const uint16_t h27v4[147] = { 9084, 4552, 628, 1744, 620, 1716, 632, 644, 548, 1744, 600, 672, 540, 684, 544, 680, 544, 1740, 604, 688, 548, 676, 548, 1708, 624, 680, 544, 680, 544, 1728, 628, 664, 540, 680, 544, 696, 552, 668, 544, 680, 544, 680, 544, 676, 548, 676, 572, 648, 544, 680, 564, 672, 544, 680, 544, 680, 544, 676, 548, 676, 540, 680, 544, 680, 544, 676, 548, 692, 544, 676, 548, 676, 548, 676, 540, 680, 544, 680, 544, 680, 544, 676, 552, 1752, 600, 1740, 596, 1744, 600, 680, 544, 676, 548, 672, 544, 680, 544, 676, 548, 692, 544, 676, 548, 676, 548, 676, 548, 1740, 624, 1716, 600, 680, 544, 676, 548, 692, 544, 1740, 604, 672, 552, 672, 540, 684, 544, 676, 548, 676, 548, 672, 540, 700, 548, 672, 552, 1712, 620, 1744, 624, 656, 548, 1740, 604, 672, 540, 684, 540};
static PROGMEM const uint16_t h28v4[147] = {9080, 4552, 628, 1740, 628, 1712, 632, 648, 544, 1744, 600, 672, 544, 680, 544, 676, 548, 1740, 596, 1764, 600, 672, 544, 1744, 600, 676, 548, 676, 568, 1720, 604, 672, 544, 676, 548, 692, 540, 680, 548, 676, 548, 672, 544, 680, 544, 676, 548, 676, 548, 672, 540, 700, 548, 672, 544, 680, 544, 676, 548, 676, 548, 672, 540, 680, 548, 676, 548, 688, 548, 660, 572, 664, 552, 676, 548, 668, 548, 676, 548, 676, 548, 672, 540, 1764, 604, 1736, 620, 1720, 600, 680, 548, 672, 572, 652, 544, 676, 548, 680, 544, 688, 548, 676, 548, 676, 548, 660, 572, 1696, 632, 1740, 604, 672, 540, 684, 552, 684, 552, 1736, 596, 680, 544, 676, 548, 676, 540, 684, 540, 680, 544, 680, 544, 692, 544, 680, 544, 1740, 604, 1740, 628, 648, 544, 1744, 600, 676, 548, 676, 540};
static PROGMEM const uint16_t h29v4[147] = { 9088, 4552, 648, 1724, 600, 1748, 596, 676, 548, 1744, 600, 676, 540, 684, 540, 680, 544, 1748, 596, 696, 552, 1740, 592, 1752, 592, 684, 540, 684, 544, 1748, 596, 680, 544, 680, 544, 696, 548, 676, 540, 680, 544, 680, 544, 680, 544, 680, 544, 680, 548, 676, 548, 692, 540, 684, 544, 680, 544, 680, 544, 676, 548, 676, 548, 676, 540, 680, 544, 696, 548, 676, 548, 680, 552, 672, 548, 672, 552, 668, 544, 680, 568, 656, 548, 1756, 596, 1748, 596, 1748, 600, 676, 568, 656, 548, 676, 548, 676, 548, 676, 540, 696, 548, 676, 548, 676, 548, 676, 540, 1748, 628, 1716, 596, 684, 544, 680, 544, 700, 544, 1708, 624, 688, 548, 672, 544, 676, 548, 680, 544, 676, 548, 676, 548, 692, 544, 676, 568, 1720, 604, 1740, 604, 676, 540, 1748, 596, 680, 544, 680, 544};
static PROGMEM const uint16_t h30v4[147] = { 9088, 4548, 632, 1740, 596, 1744, 600, 684, 552, 1708, 624, 676, 548, 676, 548, 672, 544, 1744, 620, 1740, 596, 1748, 628, 1716, 596, 688, 548, 668, 544, 1744, 600, 676, 552, 672, 540, 700, 548, 676, 548, 672, 552, 672, 540, 684, 544, 680, 544, 676, 548, 676, 548, 692, 544, 680, 544, 676, 568, 656, 548, 676, 548, 676, 540, 684, 540, 680, 544, 696, 540, 680, 544, 680, 544, 680, 544, 680, 548, 672, 552, 672, 540, 684, 544, 1764, 600, 1712, 620, 1748, 596, 680, 548, 676, 568, 656, 548, 672, 552, 672, 540, 1736, 652, 652, 552, 672, 544, 680, 544, 1744, 600, 1740, 604, 676, 568, 652, 544, 700, 544, 1740, 604, 672, 572, 652, 544, 676, 548, 676, 548, 672, 540, 684, 544, 692, 540, 684, 544, 1732, 620, 1708, 628, 676, 548, 1740, 604, 672, 572, 656, 540};

static PROGMEM const uint16_t VAN1[147] = {9084, 4556, 676, 628, 544, 1744, 600, 676, 548, 1740, 608, 672, 540, 680, 544, 680, 576, 1720, 592, 692, 544, 1744, 600, 676, 556, 668, 552, 672, 540, 1748, 596, 680, 544, 680, 548, 1752, 600, 1744, 624, 652, 552, 672, 540, 680, 544, 680, 548, 676, 548, 672, 572, 668, 544, 680, 548, 672, 552, 672, 540, 684, 540, 680, 548, 676, 548, 676, 548, 688, 548, 676, 548, 676, 548, 672, 544, 680, 576, 648, 544, 680, 544, 676, 548, 1756, 600, 1740, 604, 1736, 628, 652, 544, 676, 548, 676, 548, 676, 548, 676, 548, 1752, 604, 1740, 604, 672, 540, 684, 544, 1744, 600, 1740, 604, 676, 548, 672, 544, 696, 548, 672, 540, 684, 572, 652, 544, 676, 548, 676, 548, 672, 544, 680, 544, 696, 540, 680, 544, 1728, 628, 1700, 652, 656, 548, 1736, 600, 684, 548, 668, 548};
static PROGMEM const uint16_t VAN2[147] = {9084, 4556, 624, 684, 544, 1744, 600, 676, 548, 1740, 604, 672, 552, 680, 544, 672, 544, 1744, 600, 696, 540, 1748, 596, 680, 544, 680, 544, 680, 544, 1748, 596, 676, 548, 676, 552, 688, 544, 1744, 600, 676, 552, 672, 552, 676, 548, 676, 548, 672, 540, 680, 548, 692, 540, 684, 544, 680, 544, 676, 548, 676, 548, 676, 548, 672, 544, 680, 544, 696, 548, 676, 548, 672, 544, 680, 544, 680, 544, 680, 544, 676, 548, 676, 548, 1756, 620, 1724, 604, 1736, 604, 676, 548, 672, 544, 680, 544, 680, 544, 680, 544, 696, 540, 688, 548, 1736, 596, 680, 544, 1748, 600, 1744, 600, 676, 548, 676, 548, 692, 544, 684, 548, 672, 544, 676, 548, 676, 548, 680, 544, 676, 552, 672, 540, 700, 544, 676, 552, 1712, 652, 1716, 608, 672, 552, 1736, 596, 684, 544, 676, 548};
static PROGMEM const uint16_t VAN3[147] = { 9096, 4548, 632, 676, 548, 1740, 596, 680, 544, 1744, 600, 680, 544, 676, 548, 676, 552, 1740, 624, 664, 552, 1736, 596, 684, 540, 684, 540, 684, 544, 1744, 600, 676, 548, 676, 548, 1764, 600, 672, 544, 680, 544, 676, 548, 676, 548, 676, 548, 676, 540, 684, 540, 696, 552, 672, 552, 672, 572, 652, 572, 648, 568, 656, 548, 676, 548, 672, 552, 688, 548, 676, 548, 676, 548, 676, 548, 676, 540, 684, 540, 680, 544, 680, 544, 1760, 596, 1748, 596, 1744, 600, 684, 540, 680, 548, 676, 548, 672, 540, 684, 544, 696, 548, 1712, 620, 684, 544, 664, 568, 1712, 632, 1736, 600, 676, 548, 680, 544, 692, 544, 680, 544, 676, 548, 676, 552, 672, 552, 672, 540, 684, 540, 680, 544, 696, 540, 684, 552, 1736, 620, 1720, 604, 676, 548, 1740, 604, 672, 540, 684, 544};
static PROGMEM const uint16_t VAN4[147] = { 9084, 4556, 624, 684, 552, 1736, 628, 648, 548, 1740, 604, 680, 544, 676, 548, 676, 548, 1736, 600, 696, 548, 1744, 600, 672, 544, 680, 544, 680, 544, 1744, 600, 676, 548, 676, 540, 696, 548, 676, 552, 672, 540, 684, 552, 668, 544, 684, 540, 680, 548, 676, 568, 672, 544, 680, 544, 676, 548, 664, 572, 664, 568, 652, 576, 648, 544, 680, 576, 664, 548, 676, 552, 672, 540, 680, 544, 680, 544, 680, 548, 676, 548, 676, 548, 1752, 604, 1740, 604, 1740, 604, 672, 552, 672, 544, 680, 544, 680, 544, 676, 548, 1760, 596, 1744, 600, 676, 548, 676, 548, 1740, 604, 1740, 596, 680, 576, 648, 544, 696, 552, 672, 552, 672, 540, 684, 540, 684, 544, 684, 548, 668, 548, 676, 548, 692, 544, 680, 544, 1744, 632, 1712, 600, 676, 548, 1744, 604, 672, 552, 672, 552};

static PROGMEM const uint16_t OFF[147] = {9076, 4556, 644, 576, 648, 576, 648, 576, 628, 592, 644, 580, 644, 576, 648, 580, 688, 1648, 652, 1732, 624, 572, 652, 572, 644, 576, 648, 576, 648, 1696, 628, 592, 620, 600, 644, 1712, 644, 580, 644, 580, 624, 596, 648, 572, 652, 572, 644, 580, 644, 576, 648, 596, 628, 588, 648, 572, 652, 568, 648, 576, 648, 576, 648, 572, 652, 572, 644, 596, 624, 596, 640, 580, 644, 580, 644, 576, 652, 576, 624, 592, 644, 580, 644, 1712, 644, 580, 644, 576, 648, 576, 628, 592, 652, 572, 644, 576, 648, 576, 648, 1708, 648, 1692, 652, 572, 652, 572, 620, 600, 644, 576, 648, 576, 628, 592, 644, 592, 652, 572, 644, 576, 648, 576, 648, 572, 644, 576, 648, 576, 648, 572, 652, 588, 648, 572, 620, 1720, 644, 1696, 648, 580, 624, 1708, 648, 576, 648, 572, 652};
WiFiClient espClient;
PubSubClient client(espClient);
ESP8266WebServer server(5000);
String macAddress;
Preferences preferences;
// دالة للحصول على عنوان MAC
String getMacAddress() {
  uint8_t baseMac[6];
  WiFi.softAPmacAddress(baseMac);
  char baseMacChr[18] = {0};
  sprintf(baseMacChr, "%02X:%02X:%02X:%02X:%02X:%02X", baseMac[0], baseMac[1], baseMac[2], baseMac[3], baseMac[4], baseMac[5]);
  return String(baseMacChr);
}
void AP(String addressWiFi){
  IPAddress local_IP(192, 144, 4, 1);
  IPAddress gateway(192, 144, 4, 1);
  IPAddress subnet(255, 255, 255, 0);
  WiFi.softAPConfig(local_IP, gateway, subnet);
  Serial.println("Access Point Started");
  Serial.print("IP Address: ");
  Serial.println(WiFi.softAPIP());
  WiFi.softAP(addressWiFi.c_str(),"");
  server.on("/wifi", HTTP_POST, handlePost);

  // بدء الخادم
  server.begin();
  Serial.println("Server started");
  Serial.println("تشغيل نقطه الوصول مده دقيقة واحده");
  
  }
void handlePost() {
  Serial.println("sssss");
  if (server.hasArg("plain") == false) {
    server.send(400, "text/plain", "Body not received");
    return;
  }

  String body = server.arg("plain");
  DynamicJsonDocument doc(1024);
  deserializeJson(doc, body);

  const char* ssid = doc["ssid"];
  const char* password = doc["password"];
  Serial.println(ssid);
  WiFi.begin(ssid, password);

  int timeout = 10; // 10 ثواني كحد أقصى للاتصال
  while (WiFi.status() != WL_CONNECTED && timeout > 0) {
    delay(1000);
    timeout--;
  }

  if (WiFi.status() == WL_CONNECTED) {
    server.send(200, "text/plain", "Configuration successful");
    preferences.putString("ssid", ssid);
    preferences.putString("password", password);
    ESP.reset();
  } else {
    server.send(500, "text/plain", "Configuration failed");
  }
}

String getValue(String data, char separator, int index)
{
  int found = 0;
  int strIndex[] = {0, -1};
  int maxIndex = data.length()-1;

  for(int i=0; i<=maxIndex && found<=index; i++){
    if(data.charAt(i)==separator || i==maxIndex){
        found++;
        strIndex[0] = strIndex[1]+1;
        strIndex[1] = (i == maxIndex) ? i+1 : i;
    }
  }

  return found>index ? data.substring(strIndex[0], strIndex[1]) : "";
}

void setup_topic(){
  client.publish(lasttopic, code.c_str());
  client.subscribe(code.c_str());
  delay(500);
}

void send_signal(const uint16_t *signal) {
    static uint16_t signal_buffer[199];
    memcpy_P(signal_buffer, signal, 199 * sizeof *signal);
    irsend.sendRaw(signal_buffer, 199, 38);
}

// ========== Home Assistant Integration Functions ==========
void registerDevice() {
  // Send device registration to Home Assistant
  String payload = "{\"device_id\":\"" + code + "\",\"device_name\":\"مكيف " + macAddress + "\",\"device_type\":\"AC\",\"room\":\"unknown\"}";
  client.publish("zain/devices/register", payload.c_str());
  Serial.println("Device registered: " + payload);
  deviceRegistered = true;
}

void publishState() {
  // Send current state to Home Assistant
  client.publish(stateTopic.c_str(), (code + " response " + currentState).c_str());
  Serial.println("State published: " + currentState);
}

void setup() {
  irsend.begin();
  Serial.begin(115200);
  pinMode(led, OUTPUT);
  digitalWrite(led,LOW);
  pinMode(PIN_RESET_BUTTON, INPUT);
  delay(2000);
  preferences.begin("wifi-creds", false);
  
  delay(5000);                                                                
  String savedSSID = preferences.getString("ssid", "");
  String savedPassword = preferences.getString("password", "");
  Serial.println("ddddddddddddddddd");
  Serial.println(savedSSID);
  if(savedSSID == ""){
    WiFi.mode(WIFI_AP_STA);
    macAddress = getMacAddress();
    Serial.print("MAC Address: ");
    Serial.println(macAddress);
    String addressWiFi="مكيف/" +macAddress;
  
    Serial.println("fffffffffffffff");
    AP(addressWiFi);
    delay(1000);
    }
  else if(savedSSID != "" and WiFi.status() != WL_CONNECTED){
    WiFi.mode(WIFI_STA);
    WiFi.softAPdisconnect(true);
    WiFi.begin(savedSSID.c_str(), savedPassword.c_str());
    delay(5000);
    }                                                                    
  if(savedSSID != "" and WiFi.status() == WL_CONNECTED) {
    unsigned char mac[6];
    WiFi.macAddress(mac);
    code+=String(mac[0],16)+String(":")+String(mac[1],16)+String(":")+String(mac[2],16)+String(":")+String(mac[3],16)+String(":")+String(mac[4],16)+String(":")+String(mac[5],16);
    String codeAP = code;
    Serial.println(code);
    
    code.replace(":","_a_");
    
    Serial.println("connected"); //A-عند الانتهاء من الاتصال يظهر هذة الاسم علي السريال

    clientId="zain.local/"+String(code);
    client.setServer(mqttServer, mqttPort);
    client.setCallback(callback);
  
    while (!client.connected()) {
      Serial.println("Connecting to MQTT...");
      if (client.connect(clientId.c_str())) {

        Serial.println("connected");

        // Setup Home Assistant topics
        stateTopic = "zain/" + code + "/response";

        // Register device with Home Assistant
        registerDevice();

        // Publish initial state
        publishState();

      } else {
  
        Serial.print("failed with state ");
        //Serial.print(client.state());
        delay(2000);
       
      }
      resetESP();
    }
//  client.publish("esp8266", "Hello Raspberry Pi");
    //  client.subscribe("esp8266");
  }
}
void resetESP(){
  RESET = digitalRead(PIN_RESET_BUTTON);
  if( RESET == 1) {                                 
    Serial.println("Erase settings and restart ...");
    const char* ssid = "";                   // wifi ssid
    const char* password =  "";
    WiFi.begin(ssid, password);
    preferences.putString("ssid", "");
    preferences.putString("password", "");
    delay(1000);
    //wifiManager.resetSettings();  
    ESP.reset();
  }
}
void callback(char* topic, byte* payload, unsigned int length) {

    Serial.print("Message arrived in topic: ");
    Serial.println(topic);

    Serial.print("Message:");


    // ========== Legacy Command Processing ==========
    if ((String)topic == (String)code){
      mytopic = "";
      for (int i=0;i< length; i++){
         mytopic = mytopic + (char)payload[i];
       //  Serial.println(newtopic);
         Serial.println(mytopic);
      }
      if (mytopic=="remove"){
        Serial.println("Erase settings and restart ...");
        const char* ssid = "";                   // wifi ssid
        const char* password =  "";
        WiFi.begin(ssid, password);
        preferences.putString("ssid", "");
        preferences.putString("password", "");
        delay(1000);
        //wifiManager.resetSettings();  
        ESP.reset();
        delay(5000);
      }
      else if (mytopic=="re"){
        delay(1000);
        //wifiManager.resetSettings();  
        ESP.reset();
      }
      else if (mytopic.indexOf("state") != -1){
        mytopic.replace("state RUN","RUN");
        mytemp= mytopic;
        Serial.println(mytemp);
      }
      Serial.println(message);
    }
    else if ((String)topic == (String)topic1 or (String)topic == (String)topic2 or (String)topic == (String)topic3 or (String)topic == (String)topic4){
      message = "";
      for (int i=0;i< length; i++){
         message = message + (char)payload[i];
       //  Serial.println(newtopic);
         Serial.println((char)payload[i]);
      }
      
      if (message.indexOf("+")== 0 or message.indexOf("-")== 0){
        if (getValue(mytemp, ' ', 1)!="X" and getValue(message, ' ', 4)!="VAN"){
          Serial.println("11");
          if (message.indexOf("+")== 0){
            message.replace(getValue(message, ' ', 1),String(getValue(message, ' ', 1).toInt()+getValue(mytemp, ' ', 1).toInt()) );
          }
          else{
            message.replace(getValue(message, ' ', 1),String(getValue(mytemp, ' ', 1).toInt()-getValue(message, ' ', 1).toInt()) );
          }
        }
        else if (getValue(mytemp, ' ', 1)=="X" and (getValue(message, ' ', 4)=="AC" or getValue(message, ' ', 4)=="HEAT")){
          Serial.println("22");
          if (message.indexOf("+")== 0){
            message.replace(getValue(message, ' ', 1),String(getValue(message, ' ', 1).toInt()+25) );
          }
          else{
            message.replace(getValue(message, ' ', 1),String(25-getValue(message, ' ', 1).toInt()) );
          }
        }
        else{
          Serial.println("33");
          message.replace(getValue(message, ' ', 0)+" "+getValue(message, ' ', 1),getValue(message, ' ', 0)+" X");
        }
        message.replace(getValue(message, ' ', 0), "RUN");
        Serial.println(message);
      }
      if (message.indexOf("RUN")== 0 and getValue(message, ' ', 1) == "0"){
        message.replace("0 VAN", getValue(mytemp, ' ', 1)+ " VAN");
    //    if (getValue(mytemp, ' ', 1)=="X"){
   //       message.replace("X VAN", "24 VAN");
  //      }
      }
      if (message.indexOf("VAN") != -1 and getValue(message, ' ', 3) == "0"){
        message.replace("VAN 0", "VAN "+getValue(mytemp, ' ', 3));
      }
      if(message.indexOf("OFF")!= -1){
        Response =  getValue(message, ' ', 1);
        message = getValue(message, ' ', 0);
      }
      else{
        Response =  getValue(message, ' ', 5);
        message = getValue(message, ' ', 0)+" "+getValue(message, ' ', 1)+" "+getValue(message, ' ', 2)+" "+getValue(message, ' ', 3)+" "+getValue(message, ' ', 4);
        if (getValue(message, ' ', 4)=="XX"){
          message.replace("XX", getValue(mytemp, ' ', 4));
        }
        if ((getValue(message, ' ', 4)=="AC" or getValue(message, ' ', 4)=="HEAT") and getValue(message, ' ', 1)=="X"){
          message.replace("RUN X", "RUN 24");
        }
      }

      response=const_cast<char*>(Response.c_str());
      Serial.println("!!!!!!");
      Serial.println(response);
      Serial.println("!!!!!!");
      Serial.println(message);
      //uint16_t Send[199];
      //for (int i = 0; i < 199;i++) {
        //Send[i] = c16v1[i];
      //}
      if (message == "RUN 16 VAN 1 AC") {
          send_signal(c16v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 16 COOL 1";
          publishState();
      } 
      else if (message == "RUN 17 VAN 1 AC") {
          send_signal(c17v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 17 COOL 1";
          publishState();
      } 
      else if (message == "RUN 18 VAN 1 AC") {
          send_signal(c18v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 18 COOL 1";
          publishState();
      } 
      else if (message == "RUN 19 VAN 1 AC") {
          send_signal(c19v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 19 COOL 1";
          publishState();
      } 
      else if (message == "RUN 20 VAN 1 AC") {
          send_signal(c20v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 20 COOL 1";
          publishState();
      } 
      else if (message == "RUN 21 VAN 1 AC") {
          send_signal(c21v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 21 COOL 1";
          publishState();
      } 
      else if (message == "RUN 22 VAN 1 AC") {
          send_signal(c22v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 22 COOL 1";
          publishState();
      } 
      else if (message == "RUN 23 VAN 1 AC") {
          send_signal(c23v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 23 COOL 1";
          publishState();
      } 
      else if (message == "RUN 24 VAN 1 AC") {
          send_signal(c24v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 24 COOL 1";
          publishState();
      } 
      else if (message == "RUN 25 VAN 1 AC") {
          send_signal(c25v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 25 COOL 1";
          publishState();
      } 
      else if (message == "RUN 26 VAN 1 AC") {
          send_signal(c26v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 26 COOL 1";
          publishState();
      } 
      else if (message == "RUN 27 VAN 1 AC") {
          send_signal(c27v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 27 COOL 1";
          publishState();
      } 
      else if (message == "RUN 28 VAN 1 AC") {
          send_signal(c28v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 28 COOL 1";
          publishState();
      } 
      else if (message == "RUN 29 VAN 1 AC") {
          send_signal(c29v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 29 COOL 1";
          publishState();
      } 
      else if (message == "RUN 30 VAN 1 AC") {
          send_signal(c30v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 30 COOL 1";
          publishState();
      } 
      else if (message == "RUN 31 VAN 1 AC") {
          send_signal(c31v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 31 COOL 1";
          publishState();
      } 
      else if (message == "RUN 32 VAN 1 AC") {
          send_signal(c32v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 32 COOL 1";
          publishState();
      } 
      else if (message == "RUN 16 VAN 2 AC") {
          send_signal(c16v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 16 COOL 2";
          publishState();
      } 
      else if (message == "RUN 17 VAN 2 AC") {
          send_signal(c17v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 17 COOL 2";
          publishState();
      } 
      else if (message == "RUN 18 VAN 2 AC") {
          send_signal(c18v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 18 COOL 2";
          publishState();
      } 
      else if (message == "RUN 19 VAN 2 AC") {
          send_signal(c19v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 19 COOL 2";
          publishState();
      } 
      else if (message == "RUN 20 VAN 2 AC") {
          send_signal(c20v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 20 COOL 2";
          publishState();
      } 
      else if (message == "RUN 21 VAN 2 AC") {
          send_signal(c21v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 21 COOL 2";
          publishState();
      } 
      else if (message == "RUN 22 VAN 2 AC") {
          send_signal(c22v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 22 COOL 2";
          publishState();
      } 
      else if (message == "RUN 23 VAN 2 AC") {
          send_signal(c23v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 23 COOL 2";
          publishState();
      } 
      else if (message == "RUN 24 VAN 2 AC") {
          send_signal(c24v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 24 COOL 2";
          publishState();
      } 
      else if (message == "RUN 25 VAN 2 AC") {
          send_signal(c25v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 25 COOL 2";
          publishState();
      } 
      else if (message == "RUN 26 VAN 2 AC") {
          send_signal(c26v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 26 COOL 2";
          publishState();
      } 
      else if (message == "RUN 27 VAN 2 AC") {
          send_signal(c27v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 27 COOL 2";
          publishState();
      } 
      else if (message == "RUN 28 VAN 2 AC") {
          send_signal(c28v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 28 COOL 2";
          publishState();
      } 
      else if (message == "RUN 29 VAN 2 AC") {
          send_signal(c29v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 29 COOL 2";
          publishState();
      } 
      else if (message == "RUN 30 VAN 2 AC") {
          send_signal(c30v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 30 COOL 2";
          publishState();
      } 
      else if (message == "RUN 31 VAN 2 AC") {
          send_signal(c31v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 31 COOL 2";
          publishState();
      } 
      else if (message == "RUN 32 VAN 2 AC") {
          send_signal(c32v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 32 COOL 2";
          publishState();
      } 
      else if (message == "RUN 16 VAN 3 AC") {
          send_signal(c16v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 16 COOL 3";
          publishState();
      } 
      else if (message == "RUN 17 VAN 3 AC") {
          send_signal(c17v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 17 COOL 3";
          publishState();
      } 
      else if (message == "RUN 18 VAN 3 AC") {
          send_signal(c18v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 18 COOL 3";
          publishState();
      } 
      else if (message == "RUN 19 VAN 3 AC") {
          send_signal(c19v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 19 COOL 3";
          publishState();
      } 
      else if (message == "RUN 20 VAN 3 AC") {
          send_signal(c20v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 20 COOL 3";
          publishState();
      } 
      else if (message == "RUN 21 VAN 3 AC") {
          send_signal(c21v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 21 COOL 3";
          publishState();
      } 
      else if (message == "RUN 22 VAN 3 AC") {
          send_signal(c22v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 22 COOL 3";
          publishState();
      } 
      else if (message == "RUN 23 VAN 3 AC") {
          send_signal(c23v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 23 COOL 3";
          publishState();
      } 
      else if (message == "RUN 24 VAN 3 AC") {
          send_signal(c24v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 24 COOL 3";
          publishState();
      } 
      else if (message == "RUN 25 VAN 3 AC") {
          send_signal(c25v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 25 COOL 3";
          publishState();
      } 
      else if (message == "RUN 26 VAN 3 AC") {
          send_signal(c26v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 26 COOL 3";
          publishState();
      } 
      else if (message == "RUN 27 VAN 3 AC") {
          send_signal(c27v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 27 COOL 3";
          publishState();
      } 
      else if (message == "RUN 28 VAN 3 AC") {
          send_signal(c28v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 28 COOL 3";
          publishState();
      } 
      else if (message == "RUN 29 VAN 3 AC") {
          send_signal(c29v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 29 COOL 3";
          publishState();
      } 
      else if (message == "RUN 30 VAN 3 AC") {
          send_signal(c30v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 30 COOL 3";
          publishState();
      } 
      else if (message == "RUN 31 VAN 3 AC") {
          send_signal(c31v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 31 COOL 3";
          publishState();
      } 
      else if (message == "RUN 32 VAN 3 AC") {
          send_signal(c32v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 32 COOL 3";
          publishState();
      } 
      else if (message == "RUN 16 VAN 4 AC") {
          send_signal(c16v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 16 COOL 4";
          publishState();
      } 
      else if (message == "RUN 17 VAN 4 AC") {
          send_signal(c17v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 17 COOL 4";
          publishState();
      } 
      else if (message == "RUN 18 VAN 4 AC") {
          send_signal(c18v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 18 COOL 4";
          publishState();
      } 
      else if (message == "RUN 19 VAN 4 AC") {
          send_signal(c19v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 19 COOL 4";
          publishState();
      } 
      else if (message == "RUN 20 VAN 4 AC") {
          send_signal(c20v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 20 COOL 4";
          publishState();
      } 
      else if (message == "RUN 21 VAN 4 AC") {
          send_signal(c21v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 21 COOL 4";
          publishState();
      } 
      else if (message == "RUN 22 VAN 4 AC") {
          send_signal(c22v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 22 COOL 4";
          publishState();
      } 
      else if (message == "RUN 23 VAN 4 AC") {
          send_signal(c23v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 23 COOL 4";
          publishState();
      } 
      else if (message == "RUN 24 VAN 4 AC") {
          send_signal(c24v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 24 COOL 4";
          publishState();
      } 
      else if (message == "RUN 25 VAN 4 AC") {
          send_signal(c25v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 25 COOL 4";
          publishState();
      } 
      else if (message == "RUN 26 VAN 4 AC") {
          send_signal(c26v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 26 COOL 4";
          publishState();
      } 
      else if (message == "RUN 27 VAN 4 AC") {
          send_signal(c27v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 27 COOL 4";
          publishState();
      } 
      else if (message == "RUN 28 VAN 4 AC") {
          send_signal(c28v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 28 COOL 4";
          publishState();
      } 
      else if (message == "RUN 29 VAN 4 AC") {
          send_signal(c29v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 29 COOL 4";
          publishState();
      } 
      else if (message == "RUN 30 VAN 4 AC") {
          send_signal(c30v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 30 COOL 4";
          publishState();
      } 
      else if (message == "RUN 31 VAN 4 AC") {
          send_signal(c31v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 31 COOL 4";
          publishState();
      } 
      else if (message == "RUN 32 VAN 4 AC") {
          send_signal(c32v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 32 COOL 4";
          publishState();
      } 
      // الآن نبدأ بالتدفئة (HEAT) بنفس الطريقة
      else if (message == "RUN 15 VAN 1 HEAT") {
          send_signal(h15v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 15 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 16 VAN 1 HEAT") {
          send_signal(h16v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 16 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 17 VAN 1 HEAT") {
          send_signal(h17v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 17 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 18 VAN 1 HEAT") {
          send_signal(h18v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 18 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 19 VAN 1 HEAT") {
          send_signal(h19v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 19 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 20 VAN 1 HEAT") {
          send_signal(h20v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 20 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 21 VAN 1 HEAT") {
          send_signal(h21v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 21 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 22 VAN 1 HEAT") {
          send_signal(h22v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 22 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 23 VAN 1 HEAT") {
          send_signal(h23v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 23 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 24 VAN 1 HEAT") {
          send_signal(h24v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 24 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 25 VAN 1 HEAT") {
          send_signal(h25v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 25 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 26 VAN 1 HEAT") {
          send_signal(h26v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 26 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 27 VAN 1 HEAT") {
          send_signal(h27v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 27 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 28 VAN 1 HEAT") {
          send_signal(h28v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 28 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 29 VAN 1 HEAT") {
          send_signal(h29v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 29 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 30 VAN 1 HEAT") {
          send_signal(h30v1);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 30 HEAT 1";
          publishState();
      } 
      else if (message == "RUN 15 VAN 2 HEAT") {
          send_signal(h15v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 15 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 16 VAN 2 HEAT") {
          send_signal(h16v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 16 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 17 VAN 2 HEAT") {
          send_signal(h17v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 17 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 18 VAN 2 HEAT") {
          send_signal(h18v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 18 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 19 VAN 2 HEAT") {
          send_signal(h19v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 19 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 20 VAN 2 HEAT") {
          send_signal(h20v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 20 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 21 VAN 2 HEAT") {
          send_signal(h21v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 21 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 22 VAN 2 HEAT") {
          send_signal(h22v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 22 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 23 VAN 2 HEAT") {
          send_signal(h23v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 23 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 24 VAN 2 HEAT") {
          send_signal(h24v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 24 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 25 VAN 2 HEAT") {
          send_signal(h25v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 25 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 26 VAN 2 HEAT") {
          send_signal(h26v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 26 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 27 VAN 2 HEAT") {
          send_signal(h27v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 27 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 28 VAN 2 HEAT") {
          send_signal(h28v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 28 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 29 VAN 2 HEAT") {
          send_signal(h29v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 29 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 30 VAN 2 HEAT") {
          send_signal(h30v2);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 30 HEAT 2";
          publishState();
      } 
      else if (message == "RUN 15 VAN 3 HEAT") {
          send_signal(h15v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 15 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 16 VAN 3 HEAT") {
          send_signal(h16v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 16 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 17 VAN 3 HEAT") {
          send_signal(h17v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 17 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 18 VAN 3 HEAT") {
          send_signal(h18v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 18 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 19 VAN 3 HEAT") {
          send_signal(h19v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 19 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 20 VAN 3 HEAT") {
          send_signal(h20v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 20 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 21 VAN 3 HEAT") {
          send_signal(h21v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 21 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 22 VAN 3 HEAT") {
          send_signal(h22v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 22 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 23 VAN 3 HEAT") {
          send_signal(h23v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 23 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 24 VAN 3 HEAT") {
          send_signal(h24v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 24 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 25 VAN 3 HEAT") {
          send_signal(h25v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 25 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 26 VAN 3 HEAT") {
          send_signal(h26v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 26 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 27 VAN 3 HEAT") {
          send_signal(h27v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 27 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 28 VAN 3 HEAT") {
          send_signal(h28v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 28 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 29 VAN 3 HEAT") {
          send_signal(h29v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 29 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 30 VAN 3 HEAT") {
          send_signal(h30v3);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 30 HEAT 3";
          publishState();
      } 
      else if (message == "RUN 15 VAN 4 HEAT") {
          send_signal(h15v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 15 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 16 VAN 4 HEAT") {
          send_signal(h16v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 16 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 17 VAN 4 HEAT") {
          send_signal(h17v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 17 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 18 VAN 4 HEAT") {
          send_signal(h18v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 18 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 19 VAN 4 HEAT") {
          send_signal(h19v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 19 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 20 VAN 4 HEAT") {
          send_signal(h20v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 20 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 21 VAN 4 HEAT") {
          send_signal(h21v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 21 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 22 VAN 4 HEAT") {
          send_signal(h22v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 22 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 23 VAN 4 HEAT") {
          send_signal(h23v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 23 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 24 VAN 4 HEAT") {
          send_signal(h24v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 24 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 25 VAN 4 HEAT") {
          send_signal(h25v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 25 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 26 VAN 4 HEAT") {
          send_signal(h26v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 26 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 27 VAN 4 HEAT") {
          send_signal(h27v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 27 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 28 VAN 4 HEAT") {
          send_signal(h28v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 28 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 29 VAN 4 HEAT") {
          send_signal(h29v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 29 HEAT 4";
          publishState();
      } 
      else if (message == "RUN 30 VAN 4 HEAT") {
          send_signal(h30v4);
          mytemp = message;
          client.publish(response, (code + " response " + mytemp).c_str());
          currentState = "ON 30 HEAT 4";
          publishState();
      }
            
      else if (message.indexOf("RUN X VAN 1")!= -1){
          send_signal(VAN1);

          mytemp=message;
          client.publish(response, (code+" response "+mytemp).c_str());
          currentState = "ON 25 FAN 1";
          publishState();
      }
      else if (message.indexOf("RUN X VAN 2")!= -1){
          send_signal(VAN2);

          mytemp=message;
          client.publish(response, (code+" response "+mytemp).c_str());
          currentState = "ON 25 FAN 2";
          publishState();
      }
      else if (message.indexOf("RUN X VAN 3")!= -1){
          send_signal(VAN3);

          mytemp=message;
          client.publish(response, (code+" response "+mytemp).c_str());
          currentState = "ON 25 FAN 3";
          publishState();
      }
      
      else if (message == "OFF"){
          send_signal(OFF);
          Serial.println("Power off for air con");
          mytemp.replace("RUN","OFF");
          client.publish(response, (code+" response "+mytemp).c_str());
          mytemp.replace("OFF","RUN");
          String power = "OFF";
          String temp = getValue(currentState, ' ', 1);
          String mode = getValue(currentState, ' ', 2);
          String fan = getValue(currentState, ' ', 3);
          currentState = power + " " + String(temp) + " " + mode + " " + fan;
          publishState();
      }
      else {
        client.publish("RESPONSE", "ERROR");
          delay(3000);
      }
      message = "";  
      
     }
    else {
      Serial.println("xxx");
     }

    Serial.println();
    Serial.println("-----------------------");
    Serial.println(mytemp);
    
    

}
char* string2char(String command){
     if(command.length()==0){
        Serial.println("ccc");
        setup_topic();
     }
     if(command.length()!=0){
        delay(200);
        Topic1 = "ROOMS/"+command+"/AC%/"+String(code);
        Topic2 = "ROOMS/*/AC%/"+String(code);
        Topic3 = "ROOMS/*/AC%/*";
        Topic4 = "ROOMS/"+command+"/AC%/*";
        //Serial.println((String)p);
      //client.publish("199", "Hello Raspberry Pi");
        topic1 = const_cast<char*>(Topic1.c_str());
        topic2 = const_cast<char*>(Topic2.c_str());
        topic3 = const_cast<char*>(Topic3.c_str());
        topic4 = const_cast<char*>(Topic4.c_str());
        digitalWrite(led,HIGH);
        client.subscribe(topic1);
        client.subscribe(topic2);
        client.subscribe(topic3);
        client.subscribe(topic4);
        key = "True";
    } 
  }


void reconnect() {
  if (!client.connected()) {
    //if (WiFi.status() != WL_CONNECTED) return;
    if (client.connect(clientId.c_str())) {
      Serial.println("connected");
      mytopic="";
      key = "False";
      string2char(mytopic);

      // Re-subscribe to Home Assistant command topic
      
      // Re-register device if needed
      if (!deviceRegistered) {
        registerDevice();
      }

      }
    else {
      Serial.print("failed, rc=");
      Serial.print(client.state());
      Serial.println(" try again in 5 seconds");
      // Wait 5 seconds before retrying
 //     delay(5000);
    }
  }
}



void loop() {
  String ssid = preferences.getString("ssid", "");
  String password = preferences.getString("password", "");
//  Serial.println(ssid);
//  Serial.println(password);
  if (ssid != "" and WiFi.status() != WL_CONNECTED){
    Serial.println("11111111");
    Serial.println(WiFi.status());
    digitalWrite(led,LOW);
    WiFi.begin(ssid.c_str(), password.c_str());
    delay(5000);
    if (WiFi.status() == WL_CONNECTED){
      ESP.reset();
    }
  }
  else if (ssid != "" and WiFi.status() == WL_CONNECTED and !client.connected()) {
    digitalWrite(led,LOW);
    reconnect();
    delay(200);
  }
  else if (client.connected()){
    client.loop();
    if (key != "True") {
      string2char(mytopic);
    }
    if (co == 60){
      // Legacy heartbeat
      client.publish("connect", code.c_str());

      // Home Assistant heartbeat
      client.publish("zain/heartbeat", code.c_str());

      // Publish current state periodically
      if (deviceRegistered) {
        publishState();
      }

      co=0;
    }
    else{
      co=co+1;
    }
  }
  else{
    delay(200);
    digitalWrite(led,LOW);
  }
  //Serial.println(WiFi.status());
  server.handleClient();
  resetESP();
  delay(200);
}
