import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/themes/app_colors.dart';

/// Widget لعرض رسالة عدم وجود صلاحية
Widget NoPermissionWidget({required String message}) {
  final controller = Get.find<HomeController>();

  return containerPageOption(
    content: Padding(
      padding: EdgeInsets.all(controller.sized * 0.02),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          iconStyle(
            icon: Icons.lock_outline,
            color: AppColors.errorColor,
            size: controller.sized * 0.04,
          ),
          SizedBox(height: controller.sizedHight * 0.02),
          txtStyle(
            txt: message,
            color: AppColors.errorColor,
            align: TextAlign.center,
            size: controller.sized * 0.016,
          ),
          Si<PERSON><PERSON><PERSON>(height: controller.sizedHight * 0.02),
          txtStyle(
            txt: 'تواصل مع مدير النظام للحصول على الصلاحيات المطلوبة',
            color: AppColors.textColor2,
            align: TextAlign.center,
            size: controller.sized * 0.012,
          ),
        ],
      ),
    ),
  );
}

/// عرض نافذة تنبيه عدم وجود صلاحية
void showNoPermissionDialog({String? customMessage}) {
  AwesomeDialog(
    context: Get.context!,
    dialogType: DialogType.warning,
    animType: AnimType.rightSlide,
    title: 'ليس لديك صلاحية',
    desc: customMessage ?? 'تحتاج إلى صلاحيات إضافية للقيام بهذا الإجراء',
    btnOkText: 'موافق',
    btnOkColor: AppColors.warningColor,
    btnOkOnPress: () {},
  ).show();
}

/// Widget wrapper للتحقق من الصلاحيات
Widget PermissionWrapper({
  required String permission,
  required Widget child,
  String? noPermissionMessage,
  bool showNoPermissionWidget = true,
}) {
  final controller = Get.find<HomeController>();

  if (controller.hasPermission(permission)) {
    return child;
  } else {
    if (showNoPermissionWidget) {
      return NoPermissionWidget(
          message: noPermissionMessage ?? 'ليس لديك صلاحية للوصول لهذه الميزة');
    } else {
      return Container(); // إخفاء العنصر تماماً
    }
  }
}

/// Widget للأزرار المحمية بالصلاحيات
Widget PermissionButton({
  required String permission,
  required Widget button,
  String? noPermissionMessage,
  VoidCallback? onNoPermission,
}) {
  final controller = Get.find<HomeController>();

  if (controller.hasPermission(permission)) {
    return button;
  } else {
    return GestureDetector(
      onTap: () {
        if (onNoPermission != null) {
          onNoPermission();
        } else {
          showNoPermissionDialog(customMessage: noPermissionMessage);
        }
      },
      child: Opacity(
        opacity: 0.5,
        child: button,
      ),
    );
  }
}

/// دالة للتحقق من الصلاحية قبل تنفيذ عملية
bool checkPermissionAndShowDialog(String permission, {String? customMessage}) {
  final controller = Get.find<HomeController>();

  if (controller.hasPermission(permission)) {
    return true;
  } else {
    showNoPermissionDialog(customMessage: customMessage);
    return false;
  }
}

/// Widget لعرض حالة الصلاحيات في الإعدادات
Widget PermissionStatusWidget() {
  final controller = Get.find<HomeController>();

  String statusText;
  Color statusColor;
  IconData statusIcon;

  switch (controller.access) {
    case 'full':
      statusText = 'وصول كامل';
      statusColor = AppColors.successColor;
      statusIcon = Icons.admin_panel_settings;
      break;
    case 'never':
      statusText = 'عدم الوصول';
      statusColor = AppColors.errorColor;
      statusIcon = Icons.block;
      break;
    default:
      statusText = 'وصول محدود';
      statusColor = AppColors.primaryColor;
      statusIcon = Icons.security;
      break;
  }

  return containerPageOption(
      ver: controller.sizedHight * 0.00001,
      content: Container(
        height: controller.sizedHight * 0.06,
        width: controller.sizedWidth * 0.5,
        padding: EdgeInsets.only(
          right: controller.sizedWidth * 0.01,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                txtStyle(
                  txt: 'مستوى الصلاحيات',
                  color: AppColors.textColor,
                  size: controller.sized * 0.014,
                ),
                SizedBox(height: controller.sizedHight * 0.005),
                txtStyle(
                  txt: statusText,
                  color: statusColor,
                  size: controller.sized * 0.012,
                ),
              ],
            ),
            iconStyle(
              icon: statusIcon,
              color: statusColor,
              size: controller.sized * 0.02,
            ),
          ],
        ),
      ));
}

/// دالة لعرض تفاصيل الصلاحيات الحالية
void showCurrentPermissionsDialog() {
  final controller = Get.find<HomeController>();

  List<String> permissions = [];

  if (controller.access == 'full') {
    permissions = [
      'بيانات النظام',
      'الملحقات والغرف',
      'الكلمات الروتينية',
      'المهام المجدولة',
      'الخريطة',
      'الأشخاص',
      'إدارة الشبكة والمستخدمين'
    ];
  } else if (controller.access != 'never') {
    Map<String, String> accessCodeToPermission = {
      'data': 'بيانات النظام',
      'dev&rooms': 'الملحقات والغرف',
      'routinesWords': 'الكلمات الروتينية',
      'tasks': 'المهام المجدولة',
      'map': 'الخريطة',
      'people': 'الأشخاص',
      'wifi': 'إدارة الشبكة والمستخدمين'
    };

    List<String> accessCodes =
        controller.access.split(',').map((e) => e.trim()).toList();

    for (String code in accessCodes) {
      if (accessCodeToPermission.containsKey(code)) {
        permissions.add(accessCodeToPermission[code]!);
      }
    }
  }

  AwesomeDialog(
    context: Get.context!,
    dialogType: DialogType.info,
    animType: AnimType.scale,
    title: 'صلاحياتك الحالية',
    body: Container(
      width: controller.sizedWidth * 0.8,
      child: Column(
        children: [
          if (permissions.isEmpty)
            txtStyle(
              txt: 'ليس لديك أي صلاحيات حالياً',
              color: AppColors.errorColor,
              align: TextAlign.center,
            )
          else
            ...permissions
                .map((permission) => Padding(
                      padding: EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        children: [
                          iconStyle(
                            icon: Icons.check_circle,
                            color: AppColors.successColor,
                            size: controller.sized * 0.015,
                          ),
                          SizedBox(width: 8),
                          Expanded(
                            child: txtStyle(
                              txt: permission,
                              color: AppColors.textColor,
                              size: controller.sized * 0.013,
                            ),
                          ),
                        ],
                      ),
                    ))
                .toList(),
        ],
      ),
    ),
    btnOkText: 'موافق',
    btnOkOnPress: () {},
  ).show();
}
