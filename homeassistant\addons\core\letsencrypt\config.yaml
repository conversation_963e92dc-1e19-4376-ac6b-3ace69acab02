---
version: 5.4.9
breaking_versions: [5.3.0]
slug: letsencrypt
name: Let's Encrypt
description: Manage certificate from Let's Encrypt
url: https://github.com/home-assistant/addons/tree/master/letsencrypt
arch:
  - armhf
  - armv7
  - aarch64
  - amd64
  - i386
boot: manual
image: homeassistant/{arch}-addon-letsencrypt
init: false
map:
  - ssl:rw
  - share
options:
  keyfile: privkey.pem
  certfile: fullchain.pem
  challenge: http
  dns: {}
ports:
  80/tcp: 80
schema:
  domains:
    - str
  email: email
  keyfile: str
  certfile: str
  challenge: list(dns|http)
  dns:
    # Developer note: please add a new plugin alphabetically into all lists
    aws_access_key_id: str?
    aws_secret_access_key: str?
    azure_config: str?
    cloudflare_api_key: str?
    cloudflare_api_token: str?
    cloudflare_email: email?
    cloudns_auth_id: int?
    cloudns_auth_password: str?
    cloudns_sub_auth_id: int?
    cloudns_sub_auth_user: str?
    desec_token: str?
    digitalocean_token: str?
    directadmin_password: str?
    directadmin_url: str?
    directadmin_username: str?
    dnsimple_token: str?
    dnsmadeeasy_api_key: str?
    dnsmadeeasy_secret_key: str?
    domainoffensive_token: str?
    dreamhost_api_key: str?
    dreamhost_baseurl: str?
    duckdns_token: str?
    dynu_auth_token: str?
    easydns_endpoint: str?
    easydns_key: str?
    easydns_token: str?
    eurodns_apiKey: str?
    eurodns_applicationId: str?
    gandi_api_key: str?
    gandi_sharing_id: str?
    gandi_token: str?
    gehirn_api_secret: str?
    gehirn_api_token: str?
    godaddy_key: str?
    godaddy_secret: str?
    google_creds: str?
    he_pass: str?
    he_user: str?
    hetzner_api_token: str?
    infomaniak_api_token: str?
    inwx_password: str?
    inwx_shared_secret: str?
    inwx_username: str?
    ionos_endpoint: str?
    ionos_prefix: str?
    ionos_secret: str?
    joker_domain: str?
    joker_password: str?
    joker_username: str?
    linode_key: str?
    linode_version: str?
    loopia_password: str?
    loopia_user: str?
    luadns_email: email?
    luadns_token: str?
    mijn_host_api_key: str?
    namecheap_api_key: str?
    namecheap_username: str?
    netcup_api_key: str?
    netcup_api_password: str?
    netcup_customer_id: str?
    njalla_token: str?
    noris_token: str?
    nsone_api_key: str?
    ovh_application_key: str?
    ovh_application_secret: str?
    ovh_consumer_key: str?
    ovh_endpoint: str?
    plesk_api_url: str?
    plesk_password: str?
    plesk_username: str?
    porkbun_key: str?
    porkbun_secret: str?
    propagation_seconds: int(60,3600)?
    provider: "list(\
        dns-azure|\
        dns-cloudflare|\
        dns-cloudns|\
        dns-desec|\
        dns-digitalocean|\
        dns-directadmin|\
        dns-dnsimple|\
        dns-dnsmadeeasy|\
        dns-domainoffensive|\
        dns-dreamhost|\
        dns-duckdns|\
        dns-dynu|\
        dns-easydns|\
        dns-eurodns|\
        dns-gandi|\
        dns-gehirn|\
        dns-godaddy|\
        dns-google|\
        dns-he|\
        dns-hetzner|\
        dns-infomaniak|\
        dns-inwx|\
        dns-ionos|\
        dns-joker|\
        dns-linode|\
        dns-loopia|\
        dns-luadns|\
        dns-mijn-host|\
        dns-namecheap|\
        dns-netcup|\
        dns-njalla|\
        dns-noris|\
        dns-nsone|\
        dns-ovh|\
        dns-plesk|\
        dns-porkbun|\
        dns-rfc2136|\
        dns-route53|\
        dns-sakuracloud|\
        dns-simply|\
        dns-transip|\
        dns-websupport)?"
    rfc2136_algorithm: str?
    rfc2136_name: str?
    rfc2136_port: str?
    rfc2136_secret: str?
    rfc2136_server: str?
    rfc2136_sign_query: bool?
    sakuracloud_api_secret: str?
    sakuracloud_api_token: str?
    simply_account_name: str?
    simply_api_key: str?
    transip_api_key: str?
    transip_global_key: list(yes|no)?
    transip_username: str?
    websupport_identifier: str?
    websupport_secret_key: str?
  key_type: list(ecdsa|rsa)?
  elliptic_curve: list(secp256r1|secp384r1)?
  acme_server: url?
  acme_root_ca_cert: str?
  verbose: bool?
  dry_run: bool?
  test_cert: bool?
  eab_kid: str?
  eab_hmac_key: str?
startup: once
