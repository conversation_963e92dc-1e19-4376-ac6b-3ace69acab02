# C/C++ build system timings
generate_cxx_metadata
  [gap of 59ms]
  create-invalidation-state 158ms
  [gap of 56ms]
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 292ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 213ms]
  create-invalidation-state 201ms
  [gap of 78ms]
  write-metadata-json-to-file 20ms
generate_cxx_metadata completed in 514ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 45ms]
  create-invalidation-state 167ms
  [gap of 74ms]
  write-metadata-json-to-file 22ms
generate_cxx_metadata completed in 310ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 36ms]
  create-invalidation-state 91ms
  [gap of 40ms]
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 180ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 34ms]
  create-invalidation-state 123ms
  [gap of 42ms]
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 213ms

