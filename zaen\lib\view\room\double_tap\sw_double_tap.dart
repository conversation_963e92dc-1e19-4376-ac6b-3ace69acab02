import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/cupertino.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:get/get.dart';
import 'package:mysql1/mysql1.dart';
import 'package:sqflite/sqflite.dart';
import 'package:flutter/material.dart';
import 'package:zaen/models/pages/pages_index.dart';
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/view/room/double_tap/del.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/view/room/double_tap/edit_room.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Future<dynamic> swDoubleTap({
  required var context,
  required var i,
}) async {
  final conn = await MySqlConnection.connect(ConnectionSettings(
      host: controller.hostZain.value,
      // port: 80,
      user: 'root',
      db: 'zain',
      password: 'zain',
      characterSet: CharacterSet.UTF8));
  var appDB = await openDatabase('${controller.system}.db', version: 3);

  Map rooms = {};
  print(11111111111111111);
  pageHor = 0;
  pageVer = 0;

  var selectroom = 0;
  var selectname = 0;

  for (var r = 0; r < controller.rooms.keys.toList().length; r++) {
    rooms[controller.rooms[controller.rooms.keys.toList()[r]]['id']] =
        controller.rooms[controller.rooms.keys.toList()[r]]['privName'];
    if (roomId == controller.rooms[controller.rooms.keys.toList()[r]]['id']) {
      selectroom = r;
    }
  }
  print(i);
  showBottomSheet(
      enableDrag: true,
      backgroundColor: Colors.transparent,
      context: context,
      builder: (context) {
        return GetBuilder<HomeController>(
          builder: (controller) => SWPage(
            id: i['id'],
            sizedWidth: controller.sizedWidth,
            sizedHeight: controller.sizedHight,
            sized: controller.sized,
            connect: controller.devices[i['id']],
            swList: controller.rooms[rooms.keys.toList()[selectroom]]['devices']
                [i['id']],
            roomN: rooms[rooms.keys.toList()[selectroom]],
            del: () {
              del(appDB: appDB, i: i, context: context, conn: conn);
            },
            Dfavorite: () {
              Dfavorite(
                  context: context, device: i['id'], appDB: appDB, state: true);
            },
            swType: (type, sw) {
              if (!controller.canManageData()) {
                showNoPermissionDialog(
                    customMessage: 'ليس لديك صلاحية لتعديل انواع المفاتيح');
                return;
              }
              if (client.connectionStatus!.state.name == 'connected') {
                AwesomeDialog(
                  context: context,
                  dialogType: DialogType.noHeader,
                  headerAnimationLoop: true,
                  animType: AnimType.topSlide,
                  dialogBackgroundColor: AppColors.surfaceElevated,
                  body: SizedBox(
                    height: controller.sizedHight * 0.25,
                    child: CupertinoPicker(
                      squeeze: 1.2,
                      // looping: true,
                      useMagnifier: true,
                      magnification: 1.25,
                      scrollController:
                          FixedExtentScrollController(initialItem: type!),
                      itemExtent:
                          controller.sizedHight * 0.05, //height of each item

                      backgroundColor: Colors.transparent,
                      children: <Widget>[
                        for (var c in ['مفتاح', 'ضوء', 'مروحة/شفاط'])
                          Center(
                            child: Text(
                              c,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  color: AppColors.textColor2.withOpacity(0.8),
                                  fontSize: controller.sized * 0.017,
                                  fontWeight: FontWeight.bold),
                            ),
                          ),
                      ],
                      onSelectedItemChanged: (int index) {
                        type = index;
                      },
                    ),
                  ),
                  btnOkText: 'موافق',
                  btnOkOnPress: () async {
                    final conn =
                        await MySqlConnection.connect(ConnectionSettings(
                            host: controller.hostZain.value,
                            // port: 80,
                            user: 'root',
                            db: 'zain',
                            password: 'zain',
                            characterSet: CharacterSet.UTF8));
                    // Devices = await conn.query('select * from Devices');
                    // print(Devices.toList());
                    await conn.query(
                        "UPDATE ${i['id']}_SWITCH SET $sw = '${type == 0 ? 'SWITCH' : type == 1 ? 'LIGHT' : 'VAN'}' WHERE id = 1");
                    await getDevices();
                  },
                  // btnCancelOnPress:
                  //     () {},
                ).show();
              }
            },
            editRoom: () {
              x() async {
                var s = await editRoom(
                    context: context,
                    selectroom: selectroom,
                    rooms: rooms,
                    i: i);
                s = await s.toString();
                if (selectroom != int.parse(s)) {
                  selectroom = await int.parse(s);
                  final builder = MqttClientPayloadBuilder();
                  builder.addString('1');
                  await client.publishMessage(
                      'edit', MqttQos.atLeastOnce, builder.payload!);
                  await builder.clear();
                  await builder.addString('re');
                  await client.publishMessage(
                      i['id'], MqttQos.atLeastOnce, builder.payload!);
                  Navigator.of(context).pop();
                }
              }

              x();
            },
            editNames: (string) async {
              if (client.connectionStatus!.state.name == 'connected') {
                Results getNames =
                    await conn.query('select ${i['id']} from NDevice');

                List names = [];

                for (var n in getNames) {
                  if (n.fields.values.toList()[0] != null) {
                    names.add(n.fields.values.toList()[0]);
                  }
                }
                if (names.length == 0) {
                  string = 'add';
                }
                TextEditingController name = TextEditingController(
                  text: string == 'edit' ? names[0] : '',
                );
                bool edit = false;

                print(names);

                AwesomeDialog(
                  context: context,
                  dialogType: DialogType.noHeader,
                  headerAnimationLoop: true,
                  animType: AnimType.topSlide,
                  dialogBackgroundColor: AppColors.surfaceElevated,
                  btnOkText: string == 'add'
                      ? 'اضافة'
                      : string == 'del'
                          ? 'حذف'
                          : string == 'edit'
                              ? 'تعديل'
                              : 'موافق',

                  body: Column(
                    children: [
                      SizedBox(
                        height: controller.sizedHight * 0.25,
                        child: CupertinoPicker(
                          squeeze: 1.2,
                          // looping: true,
                          useMagnifier: true,
                          magnification: 1.25,
                          // scrollController: FixedExtentScrollController(initialItem: selectitem),
                          itemExtent: controller.sizedHight *
                              0.05, //height of each item

                          backgroundColor: Colors.transparent,
                          children: <Widget>[
                            for (var c in names)
                              if (c != null)
                                Center(
                                  child: Text(
                                    c.toString(),
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                        color: AppColors.textColor
                                            .withOpacity(0.85),
                                        fontSize: controller.sized * 0.015,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                          ],
                          onSelectedItemChanged: (int index) {
                            selectname = index;
                            if (string == 'edit') {
                              name.text = names[selectname];
                            }
                          },
                        ),
                      ),
                      string == 'edit' || string == 'add'
                          ? Material(
                              color: Colors.transparent,
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Column(
                                  children: [
                                    Container(
                                      width: controller.sizedWidth * 0.85,
                                      child: TextFormField(
                                        controller: name,
                                        maxLength: 25,
                                        autofocus: true,
                                        showCursor: true,
                                        cursorColor: AppColors.primary,
                                        textDirection: TextDirection.rtl,
                                        style: TextStyle(
                                          color: AppColors.textPrimary,
                                          fontSize: controller.sized * 0.015,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        onEditingComplete: () {
                                          FocusManager.instance.primaryFocus
                                              ?.unfocus();
                                        },
                                        decoration: InputDecoration(
                                          hintText: 'اسم المفتاح',
                                          hintStyle: TextStyle(
                                            color: AppColors.textHint,
                                            fontSize: controller.sized * 0.014,
                                            fontWeight: FontWeight.normal,
                                          ),
                                          filled: true,
                                          fillColor: AppColors.surface,
                                          contentPadding: EdgeInsets.symmetric(
                                            horizontal:
                                                controller.sizedWidth * 0.04,
                                            vertical:
                                                controller.sizedHight * 0.015,
                                          ),
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            borderSide: BorderSide(
                                              color: AppColors.border,
                                              width: 1.0,
                                            ),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            borderSide: BorderSide(
                                              color: AppColors.border,
                                              width: 1.0,
                                            ),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            borderSide: BorderSide(
                                              color: AppColors.primary,
                                              width: 2.0,
                                            ),
                                          ),
                                          suffixIcon: Icon(
                                            Icons.edit_rounded,
                                            color: AppColors.textSecondary,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            )
                          : Container()
                    ],
                  ),

                  btnOkOnPress: () async {
                    if (string != 'show') {
                      final conn =
                          await MySqlConnection.connect(ConnectionSettings(
                              host: controller.hostZain.value,
                              // port: 80,
                              user: 'root',
                              db: 'zain',
                              password: 'zain',
                              characterSet: CharacterSet.UTF8));
                      if (string == 'edit' && edit) {
                        await conn.query(
                            'update NDevice set ${i['id']}=? where ${i['id']}=?',
                            [name.text.toString(), names[selectname]]);
                        // getDevices();
                      } else if (string == 'add' && edit) {
                        await conn.query(
                            'insert INTO NDevice(${i['id']}) values(?)', [
                          name.text.toString(),
                        ]);
                        // getDevices();
                      } else if (string == 'del') {
                        print(names[selectname]);
                        await conn
                            .query('DELETE FROM NDevice WHERE ${i['id']} = ?', [
                          names[selectname],
                        ]);
                        // getDevices();
                      }
                    }
                  },
                  // btnCancelOnPress:
                  //     () {},
                ).show();
              }
            },
            editSwNames: (string, sw) async {
              // if (!controller.canManageData()) {
              //   showNoPermissionDialog(
              //       customMessage: 'ليس لديك صلاحية لتعديل اسماء المفاتيح');
              //   return null;
              // }
              if (client.connectionStatus!.state.name == 'connected') {
                Results getNames = await conn
                    .query('select ${sw} from ${i['id']}_SWITCH WHERE id >1');
                List names = [];
                bool finish = true;
                for (var n in getNames) {
                  if (n.fields.values.toList()[0] != null) {
                    names.add(n.fields.values.toList()[0]);
                  }
                }
                if (names.length == 0) {
                  string = 'add';
                }
                TextEditingController name = TextEditingController(
                  text: string == 'edit' ? names[0] : '',
                );
                bool edit = false;

                print(names);

                AwesomeDialog(
                    context: context,
                    dialogType: DialogType.noHeader,
                    headerAnimationLoop: true,
                    animType: AnimType.topSlide,
                    dialogBackgroundColor: AppColors.surfaceElevated,
                    btnOkText: string == 'add'
                        ? 'اضافة'
                        : string == 'del'
                            ? 'حذف'
                            : string == 'edit'
                                ? 'تعديل'
                                : 'موافق',
                    body: StatefulBuilder(builder: (context, setState) {
                      return Padding(
                        padding: EdgeInsets.all(controller.sized * 0.01),
                        child: Column(
                          children: [
                            SizedBox(
                              height: controller.sizedHight * 0.25,
                              child: CupertinoPicker(
                                squeeze: 1.2,
                                // looping: true,
                                useMagnifier: true,
                                magnification: 1.25,
                                // scrollController: FixedExtentScrollController(initialItem: selectitem),
                                itemExtent: controller.sizedHight *
                                    0.05, //height of each item

                                backgroundColor: Colors.transparent,
                                children: <Widget>[
                                  for (var c in names)
                                    if (c != null)
                                      Center(
                                        child: Text(
                                          c.toString(),
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                              color: AppColors.textColor2,
                                              fontSize:
                                                  controller.sized * 0.015,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                ],
                                onSelectedItemChanged: (int index) {
                                  selectname = index;
                                  if (string == 'edit') {
                                    name.text = names[selectname];
                                  }
                                },
                              ),
                            ),
                            string == 'edit' || string == 'add'
                                ? Material(
                                    color: Colors.transparent,
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Column(
                                        children: [
                                          Container(
                                            width: controller.sizedWidth * 0.85,
                                            child: TextFormField(
                                              controller: name,
                                              maxLength: 25,
                                              autofocus: true,
                                              showCursor: true,
                                              cursorColor: AppColors.primary,
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                color: AppColors.textPrimary,
                                                fontSize:
                                                    controller.sized * 0.015,
                                                fontWeight: FontWeight.w500,
                                              ),
                                              onEditingComplete: () {
                                                FocusManager
                                                    .instance.primaryFocus
                                                    ?.unfocus();
                                              },
                                              decoration: InputDecoration(
                                                hintText: 'اسم المفتاح',
                                                hintStyle: TextStyle(
                                                  color: AppColors.textHint,
                                                  fontSize:
                                                      controller.sized * 0.014,
                                                  fontWeight: FontWeight.normal,
                                                ),
                                                filled: true,
                                                fillColor: AppColors.surface,
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                  horizontal:
                                                      controller.sizedWidth *
                                                          0.04,
                                                  vertical:
                                                      controller.sizedHight *
                                                          0.015,
                                                ),
                                                border: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.border,
                                                    width: 1.0,
                                                  ),
                                                ),
                                                enabledBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.border,
                                                    width: 1.0,
                                                  ),
                                                ),
                                                focusedBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.primary,
                                                    width: 2.0,
                                                  ),
                                                ),
                                                suffixIcon: Icon(
                                                  Icons.edit_rounded,
                                                  color:
                                                      AppColors.textSecondary,
                                                  size: controller.sized * 0.02,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                : Container(),
                            string == 'edit' || string == 'add'
                                ? Container(
                                    width: double.infinity,
                                    child: submitButtom(
                                      onPressed: () async {
                                        FocusManager.instance.primaryFocus
                                            ?.unfocus();
                                        if (name.text == '' ||
                                            name.text == null) {
                                          name.text =
                                              string == 'edit' ? names[0] : '';
                                          edit = false;
                                        } else if (names.contains(name.text) ==
                                            false) {
                                          for (var i = 0;
                                              i < name.text.length;
                                              i++) {
                                            if (arabic.contains(name.text[i]) ||
                                                name.text[i].isNumericOnly) {
                                              edit = true;
                                            } else {
                                              name.text = string == 'edit'
                                                  ? names[0]
                                                  : '';
                                              break;
                                            }
                                          }
                                        }
                                        if (string != 'show') {
                                          final conn =
                                              await MySqlConnection.connect(
                                                  ConnectionSettings(
                                                      host: controller
                                                          .hostZain.value,
                                                      // port: 80,
                                                      user: 'root',
                                                      db: 'zain',
                                                      password: 'zain',
                                                      characterSet:
                                                          CharacterSet.UTF8));
                                          if (string == 'edit' && edit) {
                                            setState(
                                              () {
                                                finish = false;
                                              },
                                            );
                                            await conn.query(
                                                'update ${i['id']}_SWITCH set ${sw}=? where ${sw}=?',
                                                [
                                                  name.text.toString(),
                                                  names[selectname]
                                                ]);

                                            getNames = await conn.query(
                                                'select ${sw} from ${i['id']}_SWITCH WHERE id >1');

                                            setState(() {
                                              names = [];

                                              for (var n in getNames) {
                                                if (n.fields.values
                                                        .toList()[0] !=
                                                    null) {
                                                  names.add(n.fields.values
                                                      .toList()[0]);
                                                }
                                              }
                                            });

                                            // getDevices();
                                          } else if (string == 'add' && edit) {
                                            setState(
                                              () {
                                                finish = false;
                                              },
                                            );
                                            var newName = await conn.query(
                                                'update ${i['id']}_SWITCH set ${sw}=? where ${sw} IS NULL LIMIT 1',
                                                [
                                                  name.text.toString(),
                                                ]);

                                            if (newName.affectedRows! > 0) {
                                              print('تم تعديل البيانات بنجاح.');
                                            } else {
                                              print(
                                                  'لم يتم تنفيذ أي تعديل . سيتم اضافة سطر جديد');
                                              await conn.query(
                                                  'insert INTO ${i['id']}_SWITCH(${sw}) values(?)',
                                                  [
                                                    name.text.toString(),
                                                  ]);
                                            }
                                            getNames = await conn.query(
                                                'select ${sw} from ${i['id']}_SWITCH WHERE id >1');

                                            setState(() {
                                              names = [];

                                              for (var n in getNames) {
                                                if (n.fields.values
                                                        .toList()[0] !=
                                                    null) {
                                                  names.add(n.fields.values
                                                      .toList()[0]);
                                                }
                                              }
                                            });

                                            // getDevices();
                                          }
                                        }
                                      },
                                    ),
                                  )
                                : string == 'del'
                                    ? Container(
                                        width: double.infinity,
                                        child: ConditionalBuilder(
                                          condition:
                                              controller.home.isNotEmpty &&
                                                  client.connectionStatus!.state
                                                          .name ==
                                                      'connected' &&
                                                  finish,
                                          fallback: (context) => const Center(
                                              child:
                                                  CircularProgressIndicator()),
                                          builder: (context) => delButtom(
                                            onPressed: () async {
                                              FocusManager.instance.primaryFocus
                                                  ?.unfocus();
                                              if (name.text == '' ||
                                                  name.text == null) {
                                                name.text = string == 'edit'
                                                    ? names[0]
                                                    : '';
                                                edit = false;
                                              } else if (names
                                                      .contains(name.text) ==
                                                  false) {
                                                for (var i = 0;
                                                    i < name.text.length;
                                                    i++) {
                                                  if (arabic.contains(
                                                          name.text[i]) ||
                                                      name.text[i]
                                                          .isNumericOnly) {
                                                    edit = true;
                                                  } else {
                                                    name.text = string == 'edit'
                                                        ? names[0]
                                                        : '';
                                                    break;
                                                  }
                                                }
                                              }
                                              if (string != 'show') {
                                                final conn =
                                                    await MySqlConnection
                                                        .connect(
                                                            ConnectionSettings(
                                                                host: controller
                                                                    .hostZain
                                                                    .value,
                                                                // port: 80,
                                                                user: 'root',
                                                                db: 'zain',
                                                                password:
                                                                    'zain',
                                                                characterSet:
                                                                    CharacterSet
                                                                        .UTF8));
                                                if (string == 'del') {
                                                  if (names.length != 1) {
                                                    var del = await conn.query(
                                                        'update ${i['id']}_SWITCH set ${sw}=NULL where ${sw}=?',
                                                        [names[selectname]]);
                                                    setState(
                                                      () {
                                                        finish = false;
                                                      },
                                                    );
                                                    if (await del
                                                            .affectedRows! >
                                                        0) {
                                                      getNames = await conn.query(
                                                          'select ${sw} from ${i['id']}_SWITCH WHERE id >1');
                                                      setState(() {
                                                        if (selectname !=
                                                                names.length &&
                                                            selectname != 0) {
                                                          selectname =
                                                              selectname + 1;
                                                        } else if (selectname ==
                                                                names.length &&
                                                            selectname != 0) {
                                                          selectname =
                                                              selectname - 1;
                                                        }
                                                        names = [];

                                                        for (var n
                                                            in getNames) {
                                                          if (n.fields.values
                                                                      .toList()[
                                                                  0] !=
                                                              null) {
                                                            names.add(n
                                                                .fields.values
                                                                .toList()[0]);
                                                          }
                                                        }
                                                      });
                                                      await getDevices();
                                                      setState(
                                                        () {
                                                          finish = true;
                                                        },
                                                      );
                                                    }
                                                  }
                                                }
                                              }
                                            },
                                          ),
                                        ),
                                      )
                                    : Container(
                                        width: double.infinity,
                                        height: controller.sizedHight * 0.03,
                                      )
                          ],
                        ),
                      );
                    })).show();
                return selectname;
              }
            },
            editPrivName: (privN, priv, page) async {
              print(privN!);
              if (privN) {
                print(123);
                String name = '';
                s() async {
                  var n = await appDB.rawQuery(
                      'SELECT name FROM devices WHERE id = ?', [i['id']]);

                  print(n);
                  List names = n[0]['name'].toString().split('_');
                  names[page!] = priv;

                  for (var i in names) {
                    name += i + '_';
                  }
                  name = name.substring(0, name.length - 1);
                  print(name);

                  await appDB.transaction((txn) async {
                    print(12345);
                    await txn.rawUpdate(
                        'UPDATE devices SET name = ? WHERE id = ?',
                        [name, i['id']]);
                    print(123456);
                  });

                  var myHome = await appDB.rawQuery('SELECT * FROM devices');
                  print(myHome);

                  print(name);
                  controller.rooms[rooms.keys.toList()[selectroom]]['devices']
                      [i['id']]['priv'] = name;
                  controller.update();
                }

                await s();
              }
            },
            switchState: (val) {
              if (!controller.canControlDevices()) {
                showNoPermissionDialog(
                    customMessage: 'ليس لديك صلاحية للتحكم في المفاتيح');
                return;
              }
              if (client.connectionStatus!.state.name == 'connected') {
                switchTap('state', i['state'], i['id']);

                final builder = MqttClientPayloadBuilder();

                String pubMassege = i['id'] + ' SWITCH';

                if (val == false) {
                  for (var j in controller
                      .rooms[rooms.keys.toList()[selectroom]]['devices']
                          [i['id']]
                      .keys
                      .toList()) {
                    if (j != 'id' &&
                        j != 'device' &&
                        j != 'state' &&
                        j != 'pub' &&
                        j != 'priv' &&
                        j != 'pubName' &&
                        j != 'privName') {
                      pubMassege += ' ' + j + '_OFF';
                      // setState(() {
                      controller.rooms[rooms.keys.toList()[selectroom]]
                          ['devices'][i['id']][j]['state'] = val;
                      // });
                    }
                  }

                  roomState = false;
                  for (var j in controller
                      .rooms[rooms.keys.toList()[selectroom]]['devices']
                      .values) {
                    if (j['state'] == true && j['type'] != 'ZAIN') {
                      roomState = true;
                      print(1111111112222222);
                    }
                    // setState(() {

                    // });
                  }
                } else if (val == true) {
                  // في هذه الحاله يجب ان يذهب الى قاعده البيانات و استخراج اخر حاله مخزنه في المساعد
                  roomState = true;
                  for (var j in controller
                      .rooms[rooms.keys.toList()[selectroom]]['devices']
                          [i['id']]
                      .keys
                      .toList()) {
                    if (j != 'id' &&
                        j != 'device' &&
                        j != 'state' &&
                        j != 'pub' &&
                        j != 'priv' &&
                        j != 'pubName' &&
                        j != 'privName') {
                      pubMassege += ' ' + j + '_RUN';
                      // setState(() {
                      controller.rooms[rooms.keys.toList()[selectroom]]
                          ['devices'][i['id']][j]['state'] = val;
                      // });
                    }
                  }
                }
                controller.rooms[rooms.keys.toList()[selectroom]]['state'] =
                    roomState;
                if (val == true) {
                  controller.homeState = true;
                } else {
                  controller.homeState = false;
                  for (var i in controller.rooms.values) {
                    if (i['state'] == true) {
                      controller.homeState = true;
                    }
                  }
                }
                builder.addString(pubMassege);

                client.publishMessage(controller.homeId + "/app/zain",
                    MqttQos.atLeastOnce, builder.payload!);

                controller.update();
              }
            },
            switchTap: (sw, val) {
              if (!controller.canControlDevices()) {
                showNoPermissionDialog(
                    customMessage: 'ليس لديك صلاحية للتحكم في المفاتيح');
                return;
              }
              if (client.connectionStatus!.state.name == 'connected') {
                final builder = MqttClientPayloadBuilder();

                String pubMassege = i['id'] + ' SWITCH';

                // setState(( {
                controller.rooms[rooms.keys.toList()[selectroom]]['devices']
                    [i['id']][sw]['state'] = !val!;
                pubMassege += ' ' + sw! + (!val == true ? '_RUN' : '_OFF');
                //

                var F = false;
                for (var j in controller
                    .rooms[rooms.keys.toList()[selectroom]]['devices'][i['id']]
                    .keys
                    .toList()) {
                  if (j != 'id' &&
                      j != 'device' &&
                      j != 'state' &&
                      j != 'pub' &&
                      j != 'priv' &&
                      j != 'pubName' &&
                      j != 'privName') {
                    if (controller.rooms[rooms.keys.toList()[selectroom]]
                            ['devices'][i['id']][j]['state'] ==
                        true) {
                      F = true;
                    }
                  }
                }
                if (F && i['state'] == false) {
                  switchTap('state', i['state'], i['id']);
                  // setState(() {
                  roomState = true;
                  // });
                } else if (!F && i['state'] == true) {
                  switchTap('state', i['state'], i['id']);
                  roomState = false;
                  for (var j in controller
                      .rooms[rooms.keys.toList()[selectroom]]['devices']
                      .values) {
                    if (j['state'] == true) {
                      roomState = true;
                    }
                    // setState(() {

                    // });
                  }
                }

                controller.rooms[rooms.keys.toList()[selectroom]]['state'] =
                    roomState;
                if (roomState == true) {
                  controller.homeState = true;
                } else {
                  controller.homeState = false;
                  for (var i in controller.rooms.values) {
                    if (i['state'] == true) {
                      controller.homeState = true;
                    }
                  }
                }
                builder.addString(pubMassege);

                client.publishMessage(controller.homeId + "/app/zain",
                    MqttQos.atLeastOnce, builder.payload!);
                print(roomState);
                print(
                    '777777777777777777777777777777777700000000000000000000000000');
                controller.update();
              }
            },
            SwPrivName: controller.rooms[rooms.keys.toList()[selectroom]]
                ['devices'][i['id']]['priv'],
            // image: AssetImage("assets/images/sw.jpg"),
            deviceState: controller.rooms[rooms.keys.toList()[selectroom]]
                ['devices'][i['id']]['state'],
          ),
        );
      });
}
