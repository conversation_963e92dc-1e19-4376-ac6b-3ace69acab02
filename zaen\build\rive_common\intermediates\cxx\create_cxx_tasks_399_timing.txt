# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 81ms
create_cxx_tasks completed in 84ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 10ms
    create-X86-model 12ms
    create-X86_64-model 15ms
    create-module-model 14ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 48ms
    create-X86-model 29ms
    create-X86_64-model 17ms
    create-module-model
      [gap of 19ms]
      create-ndk-meta-abi-list 21ms
    create-module-model completed in 43ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 22ms
    create-X86-model 14ms
    create-X86_64-model 21ms
  create-initial-cxx-model completed in 349ms
  [gap of 13ms]
create_cxx_tasks completed in 363ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 112ms
create_cxx_tasks completed in 118ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 12ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 15ms
    create-X86-model 12ms
    create-X86_64-model 12ms
    create-module-model 11ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 231ms
create_cxx_tasks completed in 236ms

