---
version: 1.19.0
slug: duckdns
name: Duck DNS
description: >-
  Free Dynamic DNS (DynDNS or DDNS) service with Let's Encrypt support
url: https://github.com/home-assistant/addons/tree/master/duckdns
arch:
  - armhf
  - armv7
  - aarch64
  - amd64
  - i386
hassio_api: true
init: false
image: homeassistant/{arch}-addon-duckdns
map:
  - ssl:rw
options:
  domains:
    - null
  token: null
  aliases: []
  lets_encrypt:
    accept_terms: false
    algo: secp384r1
    certfile: fullchain.pem
    keyfile: privkey.pem
  seconds: 300
schema:
  domains:
    - match(.+\.duckdns\.org)
  token: str
  aliases:
    - alias: str
      domain: str
  lets_encrypt:
    accept_terms: bool
    algo: list(rsa|prime256v1|secp384r1)
    certfile: str
    keyfile: str
  seconds: int
  ipv4: str?
  ipv6: str?
startup: services
