#!/usr/bin/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Download available firmware update for OSRAM Ledvance
# ==============================================================================
readonly URL_OSRAM="https://api.update.ledvance.com/v1/zigbee/firmwares"

# Ensure otau folder exists
mkdir -p "/data/otau"

bashio::log.info "Running the OSRAM LEdvance OTA updater..."

# fetch data
if ! OSRAM_DATA="$(curl -sL ${URL_OSRAM})"; then
    bashio::log.info "Can't fetch data from osram!"
    exec sleep 18000
fi

OSRAM_DATA_SIZE="$(echo "${OSRAM_DATA}" | jq --raw-output '.firmwares | length')"
DL_DONE=0
for (( i=0; i < "${OSRAM_DATA_SIZE}"; i++ )); do
    OSRAM_COMPANY=$( echo "${OSRAM_DATA}" | jq --raw-output ".firmwares[$i].identity.company  // empty" 2>/dev/null)
    OSRAM_PRODUCT=$( echo "${OSRAM_DATA}" | jq --raw-output ".firmwares[$i].identity.product  // empty" 2>/dev/null)
    OTAU_FILENAME=$( echo "${OSRAM_DATA}" | jq --raw-output ".firmwares[$i].name  // empty" 2>/dev/null)
    OTAU_URL="$URL_OSRAM/download/${OSRAM_COMPANY}/${OSRAM_PRODUCT}/latest"    
            
    OTAU_FILE="/data/otau/${OTAU_FILENAME}"
    if [ -e "${OTAU_FILE}" ]; then
        continue
    fi
    curl -s -L -o "${OTAU_FILE}" "${OTAU_URL}" || true
    ((DL_DONE+1))
    if [ "$((DL_DONE % 10))" == "0" ]; then
        # LEDVANCE/OSRAM API RateLimits : The rate limit 10 calls per 60 seconds or quota 100 MB per month.
        DL_DONE=0
        sleep 65
    fi
done

exec sleep 259200
