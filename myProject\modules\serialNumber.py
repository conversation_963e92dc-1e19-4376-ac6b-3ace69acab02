import subprocess
import netifaces as ni
import sys

sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/nlt')
sys.path.append('/home/<USER>/myProject/modules')
import static as st
'''
هذا البرنامج يقوم بحذف تطبيقات الراسبيري باي عند نسخ الذاكره و تشغيلها على جهاز راسبيري باي اخر

'''

def getSerial():
    try:
        file=open('/proc/cpuinfo','r')
        for i in file:
            if i[0:6] == 'Serial':
                serial=i[10:]
        file.close()
    except:
        serial='Err'
    return serial

def serialN():


    p=open(st.pathFiles+'serial.txt','r')
    serial=p.readline()
    p.close()
    device=ni.ifaddresses('wlan0')[17][0]['addr']
    while 1:
        rpiSerial=getSerial()
        if rpiSerial != 'Err':
            if serial != getSerial():
                print('fail to match serialNumber')
            #     try:
            #         subprocess.run("sudo rm /", shell=True)
            #         time.sleep(1)
                subprocess.run("sudo reboot", shell=True)
            #     except:
            #         subprocess.run("sudo reboot", shell=True)
                break
            else:
                break
            
