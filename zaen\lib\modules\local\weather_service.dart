import 'dart:convert';
import 'dart:async';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/modules/local/mysql_helper.dart';

/// خدمة إدارة بيانات الطقس
class WeatherService extends GetxController {
  static WeatherService get instance => Get.find();

  // البيانات التفاعلية
  final Rx<WeatherData?> currentWeather = Rx<WeatherData?>(null);
  final RxList<WeatherForecast> forecast = <WeatherForecast>[].obs;
  final RxBool isLoading = false.obs;
  final RxString lastUpdated = ''.obs;
  final RxString errorMessage = ''.obs;

  // إعدادات التحديث
  Timer? _updateTimer;
  final int updateInterval = 300; // 5 دقائق

  @override
  void onInit() {
    super.onInit();
    setupMqttListeners();
    startPeriodicUpdate();
    requestWeatherUpdate();
  }

  @override
  void onClose() {
    _updateTimer?.cancel();
    super.onClose();
  }

  /// إعداد مستمعي MQTT
  void setupMqttListeners() {
    try {
      // الاشتراك في موضوع استجابة الطقس
      // client.subscribe('homeassistant/weather/response', MqttQos.atLeastOnce);

      // إضافة مستمع للرسائل
      client.updates?.listen((List<MqttReceivedMessage<MqttMessage>> messages) {
        for (var message in messages) {
          final topic = message.topic;
          final payload = MqttPublishPayload.bytesToStringAsString(
              (message.payload as MqttPublishMessage).payload.message);

          if (topic == 'homeassistant/weather/response') {
            handleWeatherResponse(payload);
          }
        }
      });
    } catch (e) {
      print('خطأ في إعداد مستمعي MQTT للطقس: $e');
    }
  }

  /// معالجة استجابة بيانات الطقس
  void handleWeatherResponse(String payload) {
    try {
      final response = json.decode(payload);

      if (response['status'] == 'success') {
        final data = response['data'];
        currentWeather.value = WeatherData.fromJson(data);
        lastUpdated.value = DateTime.now().toString();
        errorMessage.value = '';
        isLoading.value = false;
      } else {
        errorMessage.value = response['message'] ?? 'خطأ في جلب بيانات الطقس';
        isLoading.value = false;
      }
    } catch (e) {
      print('خطأ في معالجة استجابة الطقس: $e');
      errorMessage.value = 'خطأ في معالجة البيانات';
      isLoading.value = false;
    }
  }

  /// طلب تحديث بيانات الطقس
  void requestWeatherUpdate({String? location}) {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final request = {
        'location': location ?? 'here',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      final builder = MqttClientPayloadBuilder();
      builder.addString(json.encode(request));

      client.publishMessage(
        'phone/weather/request',
        MqttQos.atLeastOnce,
        builder.payload!,
      );

      print('تم إرسال طلب تحديث الطقس');
    } catch (e) {
      print('خطأ في طلب تحديث الطقس: $e');
      isLoading.value = false;
      errorMessage.value = 'خطأ في إرسال الطلب';
    }
  }

  /// بدء التحديث الدوري
  void startPeriodicUpdate() {
    _updateTimer = Timer.periodic(
      Duration(seconds: updateInterval),
      (timer) => requestWeatherUpdate(),
    );
  }

  /// إيقاف التحديث الدوري
  void stopPeriodicUpdate() {
    _updateTimer?.cancel();
    _updateTimer = null;
  }

  /// الحصول على بيانات الطقس من قاعدة البيانات المحلية
  Future<WeatherData?> getWeatherFromDatabase() async {
    try {
      final result = await executeSafeQuery(
          'SELECT * FROM weather_data ORDER BY last_updated DESC LIMIT 1');

      if (result.isNotEmpty) {
        final row = result.first;
        return WeatherData(
          temperature: row['temperature']?.toDouble(),
          humidity: row['humidity']?.toInt(),
          pressure: row['pressure']?.toDouble(),
          windSpeed: row['wind_speed']?.toDouble(),
          windDirection: row['wind_direction']?.toString(),
          condition: row['weather_condition']?.toString() ?? '',
          description: row['weather_description']?.toString() ?? '',
          visibility: row['visibility']?.toDouble(),
          uvIndex: row['uv_index']?.toDouble(),
          location: row['location']?.toString() ?? '',
          lastUpdated: DateTime.parse(row['last_updated'].toString()),
        );
      }
    } catch (e) {
      print('خطأ في جلب بيانات الطقس من قاعدة البيانات: $e');
    }

    return null;
  }

  /// حفظ بيانات الطقس في قاعدة البيانات المحلية
  Future<bool> saveWeatherToDatabase(WeatherData weather) async {
    try {
      await executeSafeQuery('''
        INSERT OR REPLACE INTO weather_cache 
        (temperature, humidity, pressure, wind_speed, wind_direction, 
         condition, description, visibility, uv_index, location, last_updated)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ''', [
        weather.temperature,
        weather.humidity,
        weather.pressure,
        weather.windSpeed,
        weather.windDirection,
        weather.condition,
        weather.description,
        weather.visibility,
        weather.uvIndex,
        weather.location,
        weather.lastUpdated.toIso8601String(),
      ]);

      return true;
    } catch (e) {
      print('خطأ في حفظ بيانات الطقس: $e');
      return false;
    }
  }

  /// الحصول على وصف الطقس بالعربية
  String getWeatherDescriptionArabic(String condition) {
    const Map<String, String> weatherTranslations = {
      'clear': 'صافي',
      'clear-night': 'مساء صافي',
      'sunny': 'مشمس',
      'cloudy': 'غائم',
      'partlycloudy': 'غائم جزئياً',
      'overcast': 'غائم كلياً',
      'rainy': 'ممطر',
      'snowy': 'ثلجي',
      'stormy': 'عاصف',
      'foggy': 'ضبابي',
      'windy': 'عاصف',
      'hot': 'حار',
      'cold': 'بارد',
      'unavailable': '-'
    };

    return weatherTranslations[condition.toLowerCase()] ?? condition;
  }

  /// الحصول على أيقونة الطقس
  String getWeatherIcon(String condition) {
    const Map<String, String> weatherIcons = {
      'clear': '☀️',
      'sunny': '🌞',
      'cloudy': '☁️',
      'partly_cloudy': '⛅',
      'overcast': '☁️',
      'rainy': '🌧️',
      'snowy': '❄️',
      'stormy': '⛈️',
      'foggy': '🌫️',
      'windy': '💨',
    };

    return weatherIcons[condition.toLowerCase()] ?? '🌤️';
  }
}

/// نموذج بيانات الطقس
class WeatherData {
  final double? temperature;
  final int? humidity;
  final double? pressure;
  final double? windSpeed;
  final String? windDirection;
  final String condition;
  final String description;
  final double? visibility;
  final double? uvIndex;
  final String location;
  final DateTime lastUpdated;

  WeatherData({
    this.temperature,
    this.humidity,
    this.pressure,
    this.windSpeed,
    this.windDirection,
    required this.condition,
    required this.description,
    this.visibility,
    this.uvIndex,
    required this.location,
    required this.lastUpdated,
  });

  factory WeatherData.fromJson(Map<String, dynamic> json) {
    return WeatherData(
      temperature: json['temperature']?.toDouble(),
      humidity: json['humidity']?.toInt(),
      pressure: json['pressure']?.toDouble(),
      windSpeed: json['wind_speed']?.toDouble(),
      windDirection: json['wind_direction']?.toString(),
      condition: json['condition']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      visibility: json['visibility']?.toDouble(),
      uvIndex: json['uv_index']?.toDouble(),
      location: json['location']?.toString() ?? '',
      lastUpdated: DateTime.parse(
          json['last_updated'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'temperature': temperature,
      'humidity': humidity,
      'pressure': pressure,
      'wind_speed': windSpeed,
      'wind_direction': windDirection,
      'condition': condition,
      'description': description,
      'visibility': visibility,
      'uv_index': uvIndex,
      'location': location,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }
}

/// نموذج توقعات الطقس
class WeatherForecast {
  final DateTime date;
  final double? maxTemp;
  final double? minTemp;
  final String condition;
  final String description;
  final int? humidity;

  WeatherForecast({
    required this.date,
    this.maxTemp,
    this.minTemp,
    required this.condition,
    required this.description,
    this.humidity,
  });

  factory WeatherForecast.fromJson(Map<String, dynamic> json) {
    return WeatherForecast(
      date: DateTime.parse(json['date']),
      maxTemp: json['max_temp']?.toDouble(),
      minTemp: json['min_temp']?.toDouble(),
      condition: json['condition'] ?? '',
      description: json['description'] ?? '',
      humidity: json['humidity']?.toInt(),
    );
  }
}
