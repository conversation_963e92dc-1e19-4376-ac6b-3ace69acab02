import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import 'package:mysql1/mysql1.dart';
import 'package:zaen/modules/local/database.dart';
import 'package:zaen/modules/local/ip.dart';
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/modules/local/location.dart';
import 'package:zaen/shared/settings/settings.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:image_picker/image_picker.dart';
import 'package:zaen/shared/components/components.dart' hide controller;
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/services/notification_service.dart';

const _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
Random _rnd = Random();

String getRandomString(int length) => String.fromCharCodes(Iterable.generate(
    length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));

// دالة تحويل أنواع الأنظمة من العربية إلى الإنجليزية
String convertSystemTypeToEnglish(String arabicType) {
  Map<String, String> typeMapping = {
    'منزل': 'home',
    'مكتب': 'office',
    'متجر': 'store',
    'مطعم': 'res',
    'مستشفى': 'hospital',
    'مدرسة': 'school',
    'فندق': 'hotel',
    'منشأه': 'facility'
  };

  return typeMapping[arabicType] ?? 'home'; // القيمة الافتراضية هي 'home'
}

MqttServerClient client = MqttServerClient.withPort(
  controller.hostZain.value,
  getRandomString(10),
  1883,
);

var conn;
late Results getData;

Future<MqttServerClient> connect() async {
  try {
    client.logging(on: false);
    client.setProtocolV311();
    client.keepAlivePeriod = 6;
    client.disconnectOnNoResponsePeriod = 10;

    client.autoReconnect = false;
    client.resubscribeOnAutoReconnect = false;

    // client.onAutoReconnect = onAutoReconnect;

    // client.onAutoReconnected = onAutoReconnected;

    client.onConnected = onConnected;
    client.onDisconnected = onDisconnected;
    // client.onUnsubscribed = onUnsubscribed;
    client.onSubscribed = onSubscribed;
    client.onSubscribeFail = onSubscribeFail;
    client.pongCallback = pong;

    final connMessage = MqttConnectMessage()
        // .authenticateAs('username', 'password')
        .keepAliveFor(40)
        .withWillTopic('willtopic')
        .withWillMessage('Will message')
        .startClean()
        .withWillQos(MqttQos.atLeastOnce);
    client.connectionMessage = connMessage;
    try {
      if (client.connectionStatus!.state.name != 'connected' &&
          controller.hostZain.value.isNotEmpty) {
        try {
          await client.connect();
        } on SocketException catch (e) {
          print('الخلل: $e');
        } catch (e) {
          print('Exception: $e');
        }
      }
    } on SocketException catch (e) {
      print('الخلل: $e');
    } catch (e) {
      print('Exception: $e');
    }
    client.updates?.listen(onUpdate);
  } catch (e) {
    print('44444444444444444444444444');
  }

  return client;
}

// connection succeeded

Future<void> onUpdate(List<MqttReceivedMessage<MqttMessage>> c) async {
  final message = c[0].payload as MqttPublishMessage;
  final payload =
      MqttPublishPayload.bytesToStringAsString(message.payload.message);

  print('Received message:$payload from topic: ${c[0].topic}');

  if (c[0].topic == 'edit') {
    print('7777777777');
    getDevices();
  } else if (c[0].topic == controller.uuid) {
    if (payload == 'false') {
      controller.sysMAC = payload;
      controller.update();
    } else {
      controller.access = payload.split('/')[2];
      print(controller.access);
      if (controller.access == 'never') {
        controller.sysMAC = 'never';
      } else {
        for (var system in controller.systems) {
          print(system);
          if (system['name'].contains(payload.split('/')[1])) {
            print(payload +
                '11111111111111111111111111111111111111111111111111111111111111111111');
            controller.system = payload.split('/')[1];
            controller.update();
            await getDevices();
            controller.update();
            Get.toNamed('home');
            return;
          }
        }
        controller.system = payload.split('/')[1];
        print('444444');
        if (payload.split('/')[0] == 'True') {
          // عرض نافذة إضافة النظام الجديد
          showAddSystemDialog(
            Get.context!,
            payload.split('/')[1],
            controller.access,
          );
        } else if (payload.split('/')[0] == 'true') {
          // عرض نافذة اختيار النظام الموجود
          showSelectExistingSystemDialog(
            Get.context!,
            payload.split('/')[1],
            controller.access,
          );
        }
      }
    }
  } else if (c[0].topic == 'homeassistant/weather/response') {
    // معالجة استجابة الطقس من Home Assistant
    try {
      final response = json.decode(payload);
      if (response != null && response is Map<String, dynamic>) {
        if (response['status'] == 'success' && response['data'] != null) {
          // تحديث بيانات الطقس في الكنترولر
          final weatherData = response['data'] as Map<String, dynamic>;
          controller.weatherData = weatherData;
          controller.update();
          print('تم تحديث بيانات الطقس بنجاح');

          // تحليل بيانات الطقس وإرسال الإشعارات الذكية
          try {
            print('🔍 MQTT: فحص تسجيل خدمة الإشعارات...');
            if (Get.isRegistered<NotificationService>()) {
              print('✅ MQTT: خدمة الإشعارات مسجلة، بدء التحليل...');
              await NotificationService.instance
                  .analyzeWeatherAndNotify(weatherData);
              print('✅ MQTT: تم تحليل بيانات الطقس بنجاح');
            } else {
              print('❌ MQTT: خدمة الإشعارات غير مسجلة!');
            }
          } catch (e) {
            print('خطأ في تحليل الطقس للإشعارات: $e');
          }
        } else if (response['status'] == 'error') {
          print('خطأ في بيانات الطقس: ${response['message']}');
          // يمكن إضافة معالجة خاصة للأخطاء هنا
        }
      }
    } catch (e) {
      print('خطأ في معالجة استجابة الطقس: $e');
    }
  } else {
    List m = payload.split(' ');
    String roomId = m[0];
    String type = m[1];
    String id = m[2];
    List massege = m.sublist(3);
    print(controller.rooms);
    if (type == 'AC') {
      controller.rooms[roomId]['devices'][id]['degree'] =
          massege[1] == 'X' ? 16 : int.parse(massege[1]);

      controller.rooms[roomId]['devices'][id]['speed'] = int.parse(massege[3]);

      controller.rooms[roomId]['devices'][id]['type'] = massege[4] == 'AC'
          ? 'تبريد'
          : massege[4] == 'HEAT'
              ? 'تدفئه'
              : 'مروحه';

      controller.rooms[roomId]['devices'][id]['state'] =
          massege[0] == 'OFF' ? false : true;
    } else if (type == 'TV') {
      controller.rooms[roomId]['devices'][id]['state'] =
          massege[0] == 'POWER-ON' ? true : false;

      controller.rooms[roomId]['devices'][id]['sil'] =
          massege[1] == 'SIL-ON' ? false : true;
    } else if (type == 'SWITCH') {
      controller.rooms[roomId]['devices'][id]['state'] = false;
      for (var j in controller.rooms[roomId]['devices'][id].keys.toList()) {
        if (j != 'id' &&
            j != 'device' &&
            j != 'state' &&
            j != 'pub' &&
            j != 'priv' &&
            j != 'pubName' &&
            j != 'privName') {
          controller.rooms[roomId]['devices'][id][j]['state'] = massege[massege
                          .indexWhere((element) => element.split('_')[0] == j)]
                      .split('_')[1] ==
                  'OFF'
              ? false
              : true;
          if (massege[massege
                      .indexWhere((element) => element.split('_')[0] == j)]
                  .split('_')[1] ==
              'RUN') {
            controller.rooms[roomId]['devices'][id]['state'] = true;
          }
        }
      }
    } else if (type.contains('ZAIN')) {}

    // التحقق من وجود الغرفة قبل الوصول إليها
    if (controller.rooms.containsKey(roomId) &&
        controller.rooms[roomId] != null) {
      var roomState = false;
      if (controller.rooms[roomId]['devices'] != null) {
        for (var j in controller.rooms[roomId]['devices'].values) {
          if (j['state'] == true) {
            roomState = true;
          }
        }
      }
      controller.rooms[roomId]['state'] = roomState;
      if (roomState == true) {
        controller.homeState = true;
      } else {
        controller.homeState = false;
        for (var i in controller.rooms.values) {
          if (i != null && i['state'] == true) {
            controller.homeState = true;
          }
        }
      }
    }
    controller.update();
  }
}

void onConnected() async {
  print('Connected');

  // الاشتراك في موضوعات الطقس عند الاتصال

  // while (true) {
  //   try {
  //     conn = await MySqlConnection.connect(ConnectionSettings(
  //         host: controller.hostZain.value,
  //         // port: 80,
  //         user: 'root',
  //         db: 'zain',
  //         password: 'zain',
  //         characterSet: CharacterSet.UTF8));
  //     break;
  //   } catch (e) {}
  // }
}

// unconnected
void onDisconnected() async {
  print('Disconnected 111111111111111111111111111111111111111111112223');

  controller.hostZain.value = '';
  controller.system = '';
  controller.sysMAC = '';
  controller.update();
  // controller.onInit();
}

// subscribe to topic succeeded
void onSubscribed(String topic) {
  print('Subscribed topic: $topic');
}

// subscribe to topic failed
void onSubscribeFail(String topic) {
  print('Failed to subscribe $topic');
}

// unsubscribe succeeded
void onUnsubscribed(String topic) {
  print('Unsubscribed topic: $topic');
}

// PING response received

void onAutoReconnect() {
  print(
      'EXAMPLE::onAutoReconnect client callback - Client auto reconnection sequence will start');

  // controller.update();
}

/// The post auto re connect callback
void onAutoReconnected() async {
  // await findIp();

  // await getDevices();
  print(
      'EXAMPLE::onAutoReconnected client callback - Client auto reconnection sequence has completed');
  // controller.onInit();

  // conn = await MySqlConnection.connect(ConnectionSettings(
  //     host: controller.hostZain.value,
  //     // port: 80,
  //     user: 'root',
  //     db: 'zain',
  //     password: 'zain',
  //     characterSet: CharacterSet.UTF8));

  controller.update();
}

Future<void> pong() async {
  // print('Ping response client callback invoked');
  // var Devices = await conn.query('select * from Devices');
  // print(Devices.toList());
  while (client.connectionStatus!.state.name == 'connected' &&
      controller.system.isNotEmpty) {
    try {
      conn = await MySqlConnection.connect(ConnectionSettings(
          host: controller.hostZain.value,
          // port: 80,
          user: 'root',
          db: 'zain',
          password: 'zain',
          characterSet: CharacterSet.UTF8));

      getData = await conn.query("SELECT id,type,connect FROM Devices");
      // print('1111111');

      List date;
      for (var i in getData) {
        if (i.fields['connect'] != null &&
            i.fields['connect'].contains('-') &&
            i.fields['type'].contains('ZAIN') == false) {
          // print('222222');
          date = i.fields['connect'].split('-');
          if (date.length == 5) {
            date.add('0');
          }
          // print((DateTime.now().minute + (DateTime.now().second / 100)) -
          //     (int.parse(date[4]) + (int.parse(date[5]) / 100)));
          if (int.parse(date[0]) == DateTime.now().year &&
              int.parse(date[1]) == DateTime.now().month &&
              int.parse(date[2]) == DateTime.now().day &&
              (int.parse(date[3]) == DateTime.now().hour ||
                  (DateTime.now().hour - int.parse(date[3]) == 1 &&
                      DateTime.now().minute == 0 &&
                      int.parse(date[4]) == 59)) &&
              ((DateTime.now().minute + (DateTime.now().second / 100)) -
                      (int.parse(date[4]) + (int.parse(date[5]) / 100))) <
                  0.45) {
            if (controller.devices[i.fields['id']] == false) {
              controller.devices[i.fields['id']] = true;
              print('33333333333333333333333');
              getDevices();
            }
          } else {
            if (controller.devices[i.fields['id']] == true) {
              controller.devices[i.fields['id']] = false;
              print('4444444444444444444444');
              getDevices();
            }
          }
        }
      }
      var homeConnect = false;
      for (var i in controller.rooms.values) {
        var room = false;
        // print(i['devices']);
        // print('4444444444444444444422222222222');
        for (var j in i['devices'].values) {
          // print('44444444444');
          if (controller.devices[j['id']] == true) {
            room = true;
            homeConnect = true;
          }
        }
        controller.devices[i['id']] = room;
      }

      controller.devices['home'] = homeConnect;

      // print('55555555555555555');
      await conn.close();
      break;
    } on MySqlException catch (e) {
      // التحقق من رمز الخطأ ورسالة الخطأ لتحديد سبب الفشل
      await Future.delayed(Duration(seconds: 5));
    } catch (e) {
      print('حدث خطأ غير متوقع: $e');
      break;
    }
  }

  // إغلاق الاتصال بعد الانتهاء
}

// متغير لمنع الضغط المتكرر
bool _isProcessing = false;

// دالة لعرض نافذة إضافة النظام الجديد
Future<void> showAddSystemDialog(BuildContext context, name, type) async {
  if (_isProcessing) return; // منع الضغط المتكرر

  GlobalKey<FormState> systemNameKey = GlobalKey<FormState>();
  TextEditingController systemNameController = TextEditingController();

  // قائمة أنواع الأنظمة المحددة
  List<String> systemTypes = [
    'منزل',
    'مكتب',
    'متجر',
    'مطعم',
    'مستشفى',
    'مدرسة',
    'فندق',
    'منشأه'
  ];
  String selectedSystemType = 'منزل';

  PageController pageController = PageController();
  String selectedImage = 'assets/images/places/home/<USER>';

  // متحكمات المعلومات الشخصية
  TextEditingController personalNameController =
      TextEditingController(text: controller.deviceName);
  String selectedDeviceType = 'هاتف ذكي';
  List<String> deviceTypes = [
    'هاتف ذكي',
    'تابلت',
    'حاسوب محمول',
    'حاسوب مكتبي'
  ];

  TextEditingController editPriv = TextEditingController(
    text: controller.deviceName,
  );
  bool privN = false;
  bool isLoading = false;
  int currentPage = 0; // 0 = معلومات النظام، 1 = اختيار الصورة
  AwesomeDialog(
    context: context,
    dialogType: DialogType.noHeader,
    headerAnimationLoop: true,
    animType: AnimType.topSlide,
    dialogBackgroundColor: AppColors.backgroundColor2,
    body: Center(
      child: Material(
        color: Colors.transparent,
        child: StatefulBuilder(
          builder: (context, setState) {
            return SizedBox(
              height: controller.sizedHight * 0.6,
              child: PageView(
                controller: pageController,
                physics: NeverScrollableScrollPhysics(),
                onPageChanged: (index) {
                  setState(() {
                    currentPage = index;
                  });
                },
                children: [
                  // الصفحة الأولى: المعلومات الشخصية
                  StatefulBuilder(
                    builder: (context, setState) {
                      return Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // ترويسة الصفحة
                          Text(
                            'معلومات النظام الجديد',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: AppColors.textColor.withOpacity(0.7),
                              fontSize: controller.sized * 0.018,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: controller.sizedHight * 0.02),
                          // حقل تعديل اسم الجهاز
                          Row(
                            children: [
                              if (privN)
                                IconButton(
                                  onPressed: () async {
                                    // حفظ الاسم الجديد
                                    final prefs =
                                        await SharedPreferences.getInstance();
                                    await prefs.setString(
                                        'device_name', editPriv.text);

                                    // تحديث الكنترولر
                                    controller.deviceName = editPriv.text;
                                    controller.update();

                                    // إخفاء زر التأكيد
                                    setState(() {
                                      privN = false;
                                    });

                                    // إظهار رسالة نجاح
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text('تم حفظ الاسم بنجاح'),
                                        backgroundColor: AppColors.primaryColor,
                                        duration: Duration(seconds: 2),
                                      ),
                                    );
                                  },
                                  icon: Icon(
                                    Icons.done,
                                    color: AppColors.primaryColor,
                                  ),
                                ),
                              Expanded(
                                child: TextFormField(
                                  controller: editPriv,
                                  maxLength: 15,
                                  showCursor: true,
                                  cursorColor: AppColors.primary,
                                  textDirection: TextDirection.rtl,
                                  style: TextStyle(
                                    color: AppColors.textPrimary,
                                    fontSize: controller.sized * 0.015,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  onChanged: (i) {
                                    setState(() {
                                      privN = true;
                                    });
                                  },
                                  decoration: InputDecoration(
                                    hintText: 'الاسم',
                                    hintStyle: TextStyle(
                                      color: AppColors.textHint,
                                      fontSize: controller.sized * 0.014,
                                      fontWeight: FontWeight.normal,
                                    ),
                                    filled: true,
                                    fillColor: AppColors.surface,
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: controller.sizedWidth * 0.04,
                                      vertical: controller.sizedHight * 0.015,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                        color: AppColors.border,
                                        width: 1.0,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                        color: AppColors.border,
                                        width: 1.0,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                        color: AppColors.primary,
                                        width: 2.0,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: controller.sizedHight * 0.02),

                          // نوع الجهاز
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              txtStyle(
                                  txt: controller.deviceModel,
                                  color: AppColors.textColor2,
                                  align: TextAlign.right),
                              txtStyle(
                                  txt: 'نوع الجهاز :       ',
                                  color: AppColors.textColor2),
                            ],
                          ),
                          SizedBox(
                            height: controller.sizedHight * 0.02,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              txtStyle(
                                  align: TextAlign.right,
                                  color: AppColors.textColor2,
                                  txt: controller.uuid
                                      .toString()
                                      .replaceAll('-', '-\n'),
                                  maxLines: 5),
                              txtStyle(
                                  txt: 'رمز الجهاز :       ',
                                  color: AppColors.textColor2),
                            ],
                          ),
                          submitButtom(
                            text: 'التالي',
                            onPressed: () {
                              FocusManager.instance.primaryFocus?.unfocus();
                              pageController.nextPage(
                                duration: Duration(milliseconds: 650),
                                curve: Curves.ease,
                              );
                            },
                          ),
                        ],
                      );
                    },
                  ),

                  // الصفحة الثانية: إدخال اسم النظام
                  Column(
                    children: [
                      Text(
                        'ادخل اسم النظام',
                        style: TextStyle(
                          color: AppColors.textColor.withOpacity(0.7),
                          fontSize: controller.sized * 0.015,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Form(
                        key: systemNameKey,
                        child: Container(
                          width: controller.sizedWidth * 0.85,
                          child: TextFormField(
                            controller: systemNameController,
                            validator: (val) {
                              if (systemNameController.text.isEmpty) {
                                return 'قم بادخال اسم النظام';
                              }
                              return null;
                            },
                            maxLength: 25,
                            autofocus: true,
                            showCursor: true,
                            cursorColor: AppColors.primary,
                            textDirection: TextDirection.rtl,
                            style: TextStyle(
                              color: AppColors.textPrimary,
                              fontSize: controller.sized * 0.015,
                              fontWeight: FontWeight.w500,
                            ),
                            decoration: InputDecoration(
                              hintText: 'اسم النظام',
                              hintStyle: TextStyle(
                                color: AppColors.textHint,
                                fontSize: controller.sized * 0.014,
                                fontWeight: FontWeight.normal,
                              ),
                              filled: true,
                              fillColor: AppColors.surface,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: controller.sizedWidth * 0.04,
                                vertical: controller.sizedHight * 0.015,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                    color: AppColors.border, width: 1.0),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                    color: AppColors.border, width: 1.0),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                    color: AppColors.primary, width: 2.0),
                              ),
                              suffixIcon: Icon(Icons.edit_rounded,
                                  color: AppColors.textSecondary),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: controller.sizedHight * 0.075),
                      submitButtom(
                        text: 'التالي',
                        onPressed: () {
                          var formdata = systemNameKey.currentState;
                          if (formdata!.validate()) {
                            FocusManager.instance.primaryFocus?.unfocus();
                            pageController.nextPage(
                              duration: Duration(milliseconds: 650),
                              curve: Curves.ease,
                            );
                          }
                        },
                      ),
                    ],
                  ),

                  // الصفحة الثانية: اختيار نوع النظام
                  StatefulBuilder(
                    builder: (context, setState) {
                      return Column(
                        children: [
                          Row(
                            children: [
                              MaterialButton(
                                minWidth: controller.sizedWidth * 0.001,
                                padding: EdgeInsets.only(
                                    right: controller.sizedWidth * 0.09),
                                onPressed: () {
                                  FocusManager.instance.primaryFocus?.unfocus();
                                  pageController.previousPage(
                                    duration: Duration(milliseconds: 650),
                                    curve: Curves.ease,
                                  );
                                },
                                child: Icon(Icons.arrow_forward_ios,
                                    color: AppColors.textSecondary),
                              ),
                              Expanded(
                                child: Text(
                                  'اختر نوع النظام',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: AppColors.textColor.withOpacity(0.7),
                                    fontSize: controller.sized * 0.015,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              SizedBox(width: controller.sizedWidth * 0.09),
                            ],
                          ),
                          SizedBox(height: controller.sizedHight * 0.03),

                          // قائمة أنواع الأنظمة
                          Container(
                            height: controller.sizedHight * 0.25,
                            child: GridView.builder(
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 2,
                                childAspectRatio: 3,
                                crossAxisSpacing: 10,
                                mainAxisSpacing: 10,
                              ),
                              itemCount: systemTypes.length,
                              itemBuilder: (context, index) {
                                bool isSelected =
                                    systemTypes[index] == selectedSystemType;
                                return GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      selectedSystemType = systemTypes[index];
                                    });
                                  },
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? AppColors.primary.withOpacity(0.1)
                                          : AppColors.surface,
                                      border: Border.all(
                                        color: isSelected
                                            ? AppColors.primary
                                            : AppColors.border,
                                        width: isSelected ? 2.0 : 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Center(
                                      child: Text(
                                        systemTypes[index],
                                        style: TextStyle(
                                          color: isSelected
                                              ? AppColors.primary
                                              : AppColors.textPrimary,
                                          fontSize: controller.sized * 0.014,
                                          fontWeight: isSelected
                                              ? FontWeight.bold
                                              : FontWeight.normal,
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),

                          SizedBox(height: controller.sizedHight * 0.03),
                          submitButtom(
                            text: 'التالي',
                            onPressed: () {
                              FocusManager.instance.primaryFocus?.unfocus();
                              pageController.nextPage(
                                duration: Duration(milliseconds: 650),
                                curve: Curves.ease,
                              );
                            },
                          ),
                        ],
                      );
                    },
                  ),

                  // الصفحة الثانية: اختيار الصورة
                  Column(
                    children: [
                      // عنوان الصفحة مع زر الرجوع
                      Row(
                        children: [
                          MaterialButton(
                            minWidth: controller.sizedWidth * 0.001,
                            padding: EdgeInsets.only(
                                right: controller.sizedWidth * 0.09),
                            onPressed: () {
                              pageController.previousPage(
                                duration: Duration(milliseconds: 650),
                                curve: Curves.ease,
                              );
                            },
                            child: Icon(
                              Icons.arrow_back_ios_rounded,
                              size: controller.sized * 0.02,
                              color: Colors.blue,
                            ),
                          ),
                          Expanded(
                            child: Text(
                              'اختر صورة للنظام',
                              textAlign: TextAlign.center,
                              textDirection: TextDirection.rtl,
                              style: TextStyle(
                                color: AppColors.textColor.withOpacity(0.7),
                                fontSize: controller.sized * 0.015,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          Container(
                            width: controller.sizedWidth * 0.1,
                          ),
                        ],
                      ),

                      // عرض الصورة المختارة
                      Container(
                        height: controller.sizedHight * 0.093,
                        width: controller.sizedWidth * 0.4,
                        child: selectedImage.contains('com.example.zaen')
                            ? Image.file(
                                File(selectedImage),
                                color: AppColors.subtitleColor.withOpacity(0.2),
                                colorBlendMode: BlendMode.darken,
                                fit: BoxFit.cover,
                                filterQuality: FilterQuality.high,
                              )
                            : Image.asset(
                                selectedImage,
                                color: AppColors.subtitleColor.withOpacity(0.2),
                                colorBlendMode: BlendMode.darken,
                                fit: BoxFit.cover,
                                filterQuality: FilterQuality.high,
                              ),
                      ),

                      // زر اختيار من الصور الموجودة
                      MaterialButton(
                        padding: EdgeInsets.symmetric(
                            horizontal: controller.sizedWidth * 0.01),
                        onPressed: () async {
                          final manifestContent =
                              await rootBundle.loadString('AssetManifest.json');
                          Map<String, dynamic> manifestMap =
                              json.decode(manifestContent);
                          List imagePaths = manifestMap.keys
                              .where((String key) =>
                                  key.contains('images/places/home/'))
                              .toList();

                          var item = imagePaths.indexOf(selectedImage);
                          if (item == -1) item = 0;

                          await showCupertinoModalPopup(
                            context: context,
                            barrierColor:
                                AppColors.textColor2.withOpacity(0.35),
                            builder: (builder) {
                              return Center(
                                child: Container(
                                  width: controller.sizedWidth,
                                  height: controller.sizedHight * 0.3,
                                  decoration: BoxDecoration(
                                    color: AppColors.backgroundColor2
                                        .withOpacity(0.975),
                                  ),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      StatefulBuilder(
                                        builder: (context, setStateModal) =>
                                            Expanded(
                                          child: RotatedBox(
                                            quarterTurns: 1,
                                            child: Center(
                                              child: ListWheelScrollView(
                                                magnification: 5,
                                                controller:
                                                    FixedExtentScrollController(
                                                        initialItem: item),
                                                itemExtent:
                                                    controller.sizedWidth *
                                                        0.55,
                                                diameterRatio: 20,
                                                squeeze: 0.88,
                                                onSelectedItemChanged: (x) {
                                                  setStateModal(() {
                                                    item = x;
                                                  });
                                                },
                                                children: List.generate(
                                                  imagePaths.length,
                                                  (index) => RotatedBox(
                                                    quarterTurns: -1,
                                                    child: Center(
                                                      child: AnimatedContainer(
                                                        duration: Duration(
                                                            milliseconds: 450),
                                                        width: item == index
                                                            ? controller
                                                                    .sizedWidth *
                                                                0.7
                                                            : controller
                                                                    .sizedWidth *
                                                                0.5,
                                                        height: item == index
                                                            ? controller
                                                                    .sizedHight *
                                                                0.16
                                                            : controller
                                                                    .sizedHight *
                                                                0.13,
                                                        decoration:
                                                            BoxDecoration(
                                                          image:
                                                              DecorationImage(
                                                            image: AssetImage(
                                                                imagePaths[
                                                                        index]
                                                                    .toString()),
                                                            onError: (context,
                                                                stackTrace) {},
                                                            colorFilter:
                                                                ColorFilter
                                                                    .mode(
                                                              item == index
                                                                  ? AppColors
                                                                      .textColor
                                                                      .withOpacity(
                                                                          0.1)
                                                                  : AppColors
                                                                      .subtitleColor
                                                                      .withOpacity(
                                                                          0.6),
                                                              BlendMode.darken,
                                                            ),
                                                            fit: BoxFit.cover,
                                                            filterQuality:
                                                                FilterQuality
                                                                    .high,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      submitButtom(
                                        onPressed: () async {
                                          setState(() {
                                            if (Navigator.of(context)
                                                .canPop()) {
                                              Navigator.of(context).pop();
                                            }
                                            selectedImage = imagePaths[item];
                                          });
                                        },
                                      ),
                                      SizedBox(
                                          height: controller.sizedHight * 0.01),
                                    ],
                                  ),
                                ),
                              );
                            },
                          );
                        },
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.arrow_back_ios,
                              size: controller.sized * 0.015,
                              color: AppColors.textColor.withOpacity(0.6),
                            ),
                            Expanded(child: SizedBox(width: double.infinity)),
                            Text(
                              'اختيار صوره من الموجود',
                              textDirection: TextDirection.rtl,
                              style: TextStyle(
                                fontSize: controller.sized * 0.0125,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textColor.withOpacity(0.6),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // زر اختيار من ألبوم الصور
                      MaterialButton(
                        padding: EdgeInsets.symmetric(
                            horizontal: controller.sizedWidth * 0.01),
                        onPressed: () async {
                          XFile? ximage = await ImagePicker()
                              .pickImage(source: ImageSource.gallery);
                          if (ximage == null) {
                            return;
                          }
                          final imageTemp = File(ximage.path);
                          setState(() {
                            selectedImage = imageTemp.path;
                          });
                        },
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.camera_rounded,
                              size: controller.sized * 0.015,
                              color: AppColors.textColor.withOpacity(0.6),
                            ),
                            Expanded(child: SizedBox(width: double.infinity)),
                            Text(
                              'اختيار صوره من البوم الصور',
                              textDirection: TextDirection.rtl,
                              style: TextStyle(
                                fontSize: controller.sized * 0.0125,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textColor.withOpacity(0.6),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // زر التقاط صورة
                      MaterialButton(
                        padding: EdgeInsets.symmetric(
                            horizontal: controller.sizedWidth * 0.01),
                        onPressed: () async {
                          XFile? ximage = await ImagePicker()
                              .pickImage(source: ImageSource.camera);
                          if (ximage == null) {
                            return;
                          }
                          final imageTemp = File(ximage.path);
                          setState(() {
                            selectedImage = imageTemp.path;
                          });
                        },
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.camera_alt_rounded,
                              size: controller.sized * 0.015,
                              color: AppColors.textColor.withOpacity(0.6),
                            ),
                            Expanded(child: SizedBox(width: double.infinity)),
                            Text(
                              'التقاط صورة',
                              textDirection: TextDirection.rtl,
                              style: TextStyle(
                                fontSize: controller.sized * 0.0125,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textColor.withOpacity(0.6),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // زر حفظ النظام
                      submitButtom(
                        text: isLoading ? 'جاري الحفظ...' : 'حفظ النظام',
                        onPressed: () async {
                          if (_isProcessing || isLoading) return;

                          setState(() {
                            isLoading = true;
                          });

                          _isProcessing = true;

                          try {
                            await _saveNewSystem(
                              context,
                              name,
                              type,
                              systemNameController.text,
                              selectedSystemType,
                              selectedImage,
                              personalNameController.text,
                              selectedDeviceType,
                            );
                          } finally {
                            _isProcessing = false;
                            setState(() {
                              isLoading = false;
                            });
                          }
                        },
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    ),
  ).show();
}

// دالة لحفظ النظام الجديد
Future<void> _saveNewSystem(
    context,
    String name,
    String type,
    String systemName,
    String systemType,
    String systemImage,
    String deviceName,
    String deviceType) async {
  try {
    // حفظ في قاعدة بيانات MySQL
    final conn = await MySqlConnection.connect(ConnectionSettings(
      host: controller.hostZain.value,
      user: 'root',
      db: 'zain',
      password: 'zain',
      characterSet: CharacterSet.UTF8,
    ));

    // إنشاء جدول system إذا لم يكن موجوداً
    await conn.query('''
      CREATE TABLE IF NOT EXISTS system (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(255) NOT NULL
      )
    ''');

    // حذف جميع البيانات السابقة من جدول system
    await conn.query('DELETE FROM system');

    // إدخال النظام الجديد (تحويل النوع إلى الإنجليزية)
    String englishSystemType = convertSystemTypeToEnglish(systemType);
    await conn.query(
      'INSERT INTO system (name, type) VALUES (?, ?)',
      [systemName, englishSystemType],
    );

    await conn.close();

    // تحديث معلومات الهاتف في جدول phones

    // حفظ في قاعدة بيانات التطبيق المحلية
    await getCurrentLocation();
    await addSystem(name, type, systemImage, currentPosition!.latitude,
        currentPosition!.longitude, systemName, systemType);
    await getDevices();

    // تحديث قائمة الأنظمة
    await getSystems();
    controller.update();
    Navigator.of(context).pop();
    Get.toNamed('home');
    print('تم حفظ النظام الجديد بنجاح: $systemName');
  } catch (e) {
    print('خطأ في حفظ النظام الجديد: $e');
  }
}

// دالة لعرض نافذة اختيار النظام الموجود
Future<void> showSelectExistingSystemDialog(
    BuildContext context, name, type) async {
  if (_isProcessing) return; // منع الضغط المتكرر

  try {
    // جلب الأنظمة من MySQL
    final conn = await MySqlConnection.connect(ConnectionSettings(
      host: controller.hostZain.value,
      user: 'root',
      db: 'zain',
      password: 'zain',
      characterSet: CharacterSet.UTF8,
    ));

    var results = await conn.query('SELECT name, type FROM system');
    await conn.close();

    if (results.isEmpty) {
      // لا توجد أنظمة محفوظة
      showAddSystemDialog(Get.context!, name, type);
      return;
    }

    List<Map<String, dynamic>> systems = [];
    for (var row in results) {
      systems.add({
        'name': row['name'],
        'type': row['type'],
      });
    }

    String selectedSystemName = systems[0]['name'];
    String selectedSystemType = systems[0]['type'];
    String selectedImage = 'assets/images/places/home/<USER>';
    TextEditingController nameController =
        TextEditingController(text: selectedSystemName);

    // متحكمات المعلومات الشخصية
    TextEditingController personalNameController2 =
        TextEditingController(text: controller.deviceName);
    String selectedDeviceType2 = 'هاتف ذكي';
    List<String> deviceTypes2 = [
      'هاتف ذكي',
      'تابلت',
      'حاسوب محمول',
      'حاسوب مكتبي'
    ];

    PageController pageController2 = PageController();
    bool isLoading = false;
    int currentPage = 0; // 0 = معلومات النظام، 1 = اختيار الصورة

    AwesomeDialog(
      context: context,
      dialogType: DialogType.noHeader,
      headerAnimationLoop: true,
      animType: AnimType.topSlide,
      dialogBackgroundColor: AppColors.backgroundColor2,
      body: Center(
        child: Material(
          color: Colors.transparent,
          child: StatefulBuilder(
            builder: (context, setState) {
              return SizedBox(
                height: controller.sizedHight * 0.6,
                child: PageView(
                  controller: pageController2,
                  physics: NeverScrollableScrollPhysics(),
                  onPageChanged: (index) {
                    setState(() {
                      currentPage = index;
                    });
                  },
                  children: [
                    // الصفحة الأولى: معلومات النظام
                    Column(
                      children: [
                        Text(
                          'معلومات النظام',
                          style: TextStyle(
                            color: AppColors.textColor.withOpacity(0.7),
                            fontSize: controller.sized * 0.018,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: controller.sizedHight * 0.03),

                        // معلومات النظام الحالي
                        Directionality(
                          textDirection: TextDirection.rtl,
                          child: Container(
                            width: controller.sizedWidth * 0.7,
                            padding: EdgeInsets.all(controller.sized * 0.015),
                            decoration: BoxDecoration(
                              color: AppColors.surface,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: AppColors.border),
                            ),
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      'اسم النظام :     ',
                                      style: TextStyle(
                                        color: AppColors.textSecondary,
                                        fontSize: controller.sized * 0.014,
                                      ),
                                    ),
                                    Text(
                                      selectedSystemName,
                                      style: TextStyle(
                                        color: AppColors.textPrimary,
                                        fontSize: controller.sized * 0.015,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: controller.sizedHight * 0.01),
                                Row(
                                  children: [
                                    Text(
                                      'نوع النظام :     ',
                                      style: TextStyle(
                                        color: AppColors.textSecondary,
                                        fontSize: controller.sized * 0.014,
                                      ),
                                    ),
                                    Text(
                                      selectedSystemType,
                                      style: TextStyle(
                                        color: AppColors.textPrimary,
                                        fontSize: controller.sized * 0.015,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),

                        SizedBox(height: controller.sizedHight * 0.03),

                        // نص توضيحي لتعديل الاسم
                        Text(
                          'اختر اسماً خاصاً يناسبك للنظام',
                          style: TextStyle(
                            color: AppColors.textHint,
                            fontSize: controller.sized * 0.013,
                          ),
                        ),

                        SizedBox(height: controller.sizedHight * 0.01),

                        // تعديل اسم النظام
                        Container(
                          width: controller.sizedWidth * 0.7,
                          child: TextFormField(
                            controller: nameController,
                            textDirection: TextDirection.rtl,
                            style: TextStyle(
                              color: AppColors.textPrimary,
                              fontSize: controller.sized * 0.015,
                              fontWeight: FontWeight.w500,
                            ),
                            decoration: InputDecoration(
                              hintText: 'اسم النظام الخاص بك',
                              hintStyle: TextStyle(
                                color: AppColors.textHint,
                                fontSize: controller.sized * 0.014,
                              ),
                              filled: true,
                              fillColor: AppColors.surface,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: controller.sizedWidth * 0.04,
                                vertical: controller.sizedHight * 0.015,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                    color: AppColors.border, width: 1.0),
                              ),
                            ),
                          ),
                        ),

                        SizedBox(height: controller.sizedHight * 0.03),

                        // أزرار التنقل والحفظ
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            // زر اختيار الصورة
                            MaterialButton(
                              onPressed: () {
                                pageController2.nextPage(
                                  duration: Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                              },
                              color: AppColors.primary.withOpacity(0.1),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                                side: BorderSide(color: AppColors.primary),
                              ),
                              child: Text(
                                'اختيار الصورة',
                                style: TextStyle(
                                  color: AppColors.primary,
                                  fontSize: controller.sized * 0.014,
                                ),
                              ),
                            ),

                            // زر حفظ النظام
                            MaterialButton(
                              onPressed: isLoading
                                  ? null
                                  : () async {
                                      if (_isProcessing) return;

                                      setState(() {
                                        isLoading = true;
                                      });

                                      _isProcessing = true;

                                      try {
                                        await _saveSelectedSystem(
                                          context,
                                          name,
                                          type,
                                          nameController.text,
                                          selectedSystemType,
                                          selectedImage,
                                          personalNameController2.text,
                                          selectedDeviceType2,
                                        );
                                      } finally {
                                        _isProcessing = false;
                                        setState(() {
                                          isLoading = false;
                                        });
                                      }
                                    },
                              color: AppColors.primary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: isLoading
                                  ? SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                      ),
                                    )
                                  : Text(
                                      'حفظ النظام',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: controller.sized * 0.014,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                            ),
                          ],
                        ),
                      ],
                    ),

                    // الصفحة الثانية: اختيار الصورة
                    Column(
                      children: [
                        // عنوان الصفحة مع زر الرجوع
                        Row(
                          children: [
                            MaterialButton(
                              minWidth: controller.sizedWidth * 0.001,
                              padding: EdgeInsets.only(
                                  right: controller.sizedWidth * 0.09),
                              onPressed: () {
                                pageController2.previousPage(
                                  duration: Duration(milliseconds: 650),
                                  curve: Curves.ease,
                                );
                              },
                              child: Icon(
                                Icons.arrow_back_ios_rounded,
                                size: controller.sized * 0.02,
                                color: Colors.blue,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                'اختر صورة للنظام',
                                textAlign: TextAlign.center,
                                textDirection: TextDirection.rtl,
                                style: TextStyle(
                                  color: AppColors.textColor.withOpacity(0.7),
                                  fontSize: controller.sized * 0.015,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Container(
                              width: controller.sizedWidth * 0.1,
                            ),
                          ],
                        ),

                        // عرض الصورة المختارة
                        Container(
                          height: controller.sizedHight * 0.093,
                          width: controller.sizedWidth * 0.4,
                          child: selectedImage.contains('com.example.zaen')
                              ? Image.file(
                                  File(selectedImage),
                                  color:
                                      AppColors.subtitleColor.withOpacity(0.2),
                                  colorBlendMode: BlendMode.darken,
                                  fit: BoxFit.cover,
                                  filterQuality: FilterQuality.high,
                                )
                              : Image.asset(
                                  selectedImage,
                                  color:
                                      AppColors.subtitleColor.withOpacity(0.2),
                                  colorBlendMode: BlendMode.darken,
                                  fit: BoxFit.cover,
                                  filterQuality: FilterQuality.high,
                                ),
                        ),

                        // زر اختيار من الصور الموجودة
                        MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.01),
                          onPressed: () async {
                            final manifestContent = await rootBundle
                                .loadString('AssetManifest.json');
                            Map<String, dynamic> manifestMap =
                                json.decode(manifestContent);
                            List imagePaths = manifestMap.keys
                                .where((String key) =>
                                    key.contains('images/places/home/'))
                                .toList();

                            var item = imagePaths.indexOf(selectedImage);
                            if (item == -1) item = 0;

                            await showCupertinoModalPopup(
                              context: context,
                              barrierColor:
                                  AppColors.textColor2.withOpacity(0.35),
                              builder: (builder) {
                                return Center(
                                  child: Container(
                                    width: controller.sizedWidth,
                                    height: controller.sizedHight * 0.3,
                                    decoration: BoxDecoration(
                                      color: AppColors.backgroundColor2
                                          .withOpacity(0.975),
                                    ),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        StatefulBuilder(
                                          builder: (context, setStateModal) =>
                                              Expanded(
                                            child: RotatedBox(
                                              quarterTurns: 1,
                                              child: Center(
                                                child: ListWheelScrollView(
                                                  magnification: 5,
                                                  controller:
                                                      FixedExtentScrollController(
                                                          initialItem: item),
                                                  itemExtent:
                                                      controller.sizedWidth *
                                                          0.55,
                                                  diameterRatio: 20,
                                                  squeeze: 0.88,
                                                  onSelectedItemChanged: (x) {
                                                    setStateModal(() {
                                                      item = x;
                                                    });
                                                  },
                                                  children: List.generate(
                                                    imagePaths.length,
                                                    (index) => RotatedBox(
                                                      quarterTurns: -1,
                                                      child: Center(
                                                        child:
                                                            AnimatedContainer(
                                                          duration: Duration(
                                                              milliseconds:
                                                                  450),
                                                          width: item == index
                                                              ? controller
                                                                      .sizedWidth *
                                                                  0.7
                                                              : controller
                                                                      .sizedWidth *
                                                                  0.5,
                                                          height: item == index
                                                              ? controller
                                                                      .sizedHight *
                                                                  0.16
                                                              : controller
                                                                      .sizedHight *
                                                                  0.13,
                                                          decoration:
                                                              BoxDecoration(
                                                            image:
                                                                DecorationImage(
                                                              image: AssetImage(
                                                                  imagePaths[
                                                                          index]
                                                                      .toString()),
                                                              onError: (context,
                                                                  stackTrace) {},
                                                              colorFilter:
                                                                  ColorFilter
                                                                      .mode(
                                                                item == index
                                                                    ? AppColors
                                                                        .textColor
                                                                        .withOpacity(
                                                                            0.1)
                                                                    : AppColors
                                                                        .subtitleColor
                                                                        .withOpacity(
                                                                            0.6),
                                                                BlendMode
                                                                    .darken,
                                                              ),
                                                              fit: BoxFit.cover,
                                                              filterQuality:
                                                                  FilterQuality
                                                                      .high,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        submitButtom(
                                          onPressed: () async {
                                            setState(() {
                                              if (Navigator.of(context)
                                                  .canPop()) {
                                                Navigator.of(context).pop();
                                              }
                                              selectedImage = imagePaths[item];
                                            });
                                          },
                                        ),
                                        SizedBox(
                                            height:
                                                controller.sizedHight * 0.01),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.arrow_back_ios,
                                size: controller.sized * 0.015,
                                color: AppColors.textColor.withOpacity(0.6),
                              ),
                              Expanded(child: SizedBox(width: double.infinity)),
                              Text(
                                'اختيار صوره من الموجود',
                                textDirection: TextDirection.rtl,
                                style: TextStyle(
                                  fontSize: controller.sized * 0.0125,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textColor.withOpacity(0.6),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // زر اختيار من ألبوم الصور
                        MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.01),
                          onPressed: () async {
                            XFile? ximage = await ImagePicker()
                                .pickImage(source: ImageSource.gallery);
                            if (ximage == null) {
                              return;
                            }
                            final imageTemp = File(ximage.path);
                            setState(() {
                              selectedImage = imageTemp.path;
                            });
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.camera_rounded,
                                size: controller.sized * 0.015,
                                color: AppColors.textColor.withOpacity(0.6),
                              ),
                              Expanded(child: SizedBox(width: double.infinity)),
                              Text(
                                'اختيار صوره من البوم الصور',
                                textDirection: TextDirection.rtl,
                                style: TextStyle(
                                  fontSize: controller.sized * 0.0125,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textColor.withOpacity(0.6),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // زر التقاط صورة
                        MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.01),
                          onPressed: () async {
                            XFile? ximage = await ImagePicker()
                                .pickImage(source: ImageSource.camera);
                            if (ximage == null) {
                              return;
                            }
                            final imageTemp = File(ximage.path);
                            setState(() {
                              selectedImage = imageTemp.path;
                            });
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.camera_alt_rounded,
                                size: controller.sized * 0.015,
                                color: AppColors.textColor.withOpacity(0.6),
                              ),
                              Expanded(child: SizedBox(width: double.infinity)),
                              Text(
                                'التقاط صورة',
                                textDirection: TextDirection.rtl,
                                style: TextStyle(
                                  fontSize: controller.sized * 0.0125,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textColor.withOpacity(0.6),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // زر حفظ النظام
                        submitButtom(
                          text: isLoading ? 'جاري الحفظ...' : 'حفظ النظام',
                          onPressed: () async {
                            if (_isProcessing || isLoading) return;

                            setState(() {
                              isLoading = true;
                            });

                            _isProcessing = true;

                            try {
                              await _saveSelectedSystem(
                                context,
                                name,
                                type,
                                nameController.text,
                                selectedSystemType,
                                selectedImage,
                                personalNameController2.text,
                                selectedDeviceType2,
                              );
                            } finally {
                              _isProcessing = false;
                              setState(() {
                                isLoading = false;
                              });
                            }
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    ).show();
  } catch (e) {
    print('خطأ في جلب الأنظمة: $e');
    AwesomeDialog(
      context: context,
      dialogType: DialogType.error,
      animType: AnimType.topSlide,
      title: 'خطأ',
      desc: 'حدث خطأ في جلب الأنظمة من قاعدة البيانات',
      btnOkOnPress: () {},
    ).show();
  }
}

// دالة لحفظ النظام المختار
Future<void> _saveSelectedSystem(
    context,
    String name,
    String type,
    String systemName,
    String systemType,
    String systemImage,
    String deviceName,
    String deviceType) async {
  try {
    // تحديث معلومات الهاتف في جدول phones

    // حفظ في قاعدة بيانات التطبيق المحلية
    await getCurrentLocation();
    await addSystem(name, type, systemImage, currentPosition!.latitude,
        currentPosition!.longitude, systemName, systemType);

    // تحديث قائمة الأنظمة
    await getDevices();
    await getSystems();
    controller.update();
    Navigator.of(context).pop();
    Get.toNamed('home');
    print('تم حفظ النظام المختار بنجاح: $systemName');
  } catch (e) {
    print('خطأ في حفظ النظام المختار: $e');
  }
}

// دالة لطلب بيانات الطقس من Home Assistant
Future<void> requestWeatherData() async {
  if (client.connectionStatus!.state == MqttConnectionState.connected) {
    final builder = MqttClientPayloadBuilder();
    builder.addString('get_weather');

    // إرسال الطلب إلى موضوعات متعددة للتأكد من الوصول
    final topics = [
      'phone/weather/request',
      // 'weather/request',
      // 'ha/weather/request'
    ];

    for (String topic in topics) {
      client.publishMessage(topic, MqttQos.atLeastOnce, builder.payload!);
      print('تم إرسال طلب بيانات الطقس إلى: $topic');
    }
  } else {
    print('MQTT غير متصل - لا يمكن طلب بيانات الطقس');
  }
}

// دالة للاشتراك في موضوعات الطقس
void subscribeToWeatherTopics() {
  if (client.connectionStatus!.state == MqttConnectionState.connected) {
    // client.subscribe('weather/data', MqttQos.atLeastOnce);
    // client.subscribe('weather/request', MqttQos.atLeastOnce);
    client.subscribe('homeassistant/weather/response', MqttQos.atLeastOnce);
    print('تم الاشتراك في موضوعات الطقس');

    // بدء التحديث الدوري لبيانات الطقس
    // startPeriodicWeatherUpdates();
  }
}

// دالة لبدء التحديث الدوري لبيانات الطقس
void startPeriodicWeatherUpdates() {
  Timer.periodic(Duration(minutes: 5), (timer) {
    if (client.connectionStatus!.state == MqttConnectionState.connected) {
      requestWeatherData();
      print('🔄 طلب تحديث دوري لبيانات الطقس');
    } else {
      print('⚠️ MQTT غير متصل - تم تخطي التحديث الدوري للطقس');
    }
  });
}
