{"name": "Home Assistant Add-ons", "image": "ghcr.io/home-assistant/devcontainer:addons", "appPort": ["7123:8123", "7357:4357"], "postStartCommand": "bash devcontainer_bootstrap", "runArgs": ["-e", "GIT_EDITOR=code --wait", "--privileged"], "remoteUser": "root", "containerEnv": {"WORKSPACE_DIRECTORY": "${containerWorkspaceFolder}"}, "extensions": ["timonwong.shellcheck", "esbenp.prettier-vscode"], "mounts": ["type=volume,target=/var/lib/docker"], "settings": {"terminal.integrated.profiles.linux": {"zsh": {"path": "/usr/bin/zsh"}}, "terminal.integrated.defaultProfile.linux": "zsh", "editor.formatOnPaste": false, "editor.formatOnSave": true, "editor.formatOnType": true, "files.trimTrailingWhitespace": true}}