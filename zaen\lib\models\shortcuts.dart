import 'package:expansion_tile_card/expansion_tile_card.dart';
import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Widget shortcutAc({
  var deviceState = null,
  bool swingState = false,
  var speedState = 2,
  var typeState = 1,
  var degree = 25.0,
  required bool connect,
  String? acPrivName,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
  required Function() doubleTap,
  required Function() tapOn_Ac_Icon,
  required Function() acRun,
  required Function(bool?) switchState,
  required Function(double?) sliderState,
  required Function(int?) acTypeState,
  required Function() acSwingState,
  required Function() acSpeedsStateLeft,
  required Function() acSpeedsStateRight,
  // required var image,
}) =>
    shortCutStyle(
        connect: connect,
        type: 'مكيف',
        doubleTap: doubleTap,
        tapOnIcon: tapOn_Ac_Icon,
        PrivName: acPrivName!,
        content: [
          // Temperature Control - Professional Style from Favorite
          Container(
            padding: EdgeInsets.only(
                left: sizedWidth * 0.04,
                right: sizedWidth * 0.04,
                top: sizedHeight * 0.005,
                bottom: sizedHeight * 0.01),
            margin: EdgeInsets.only(
              left: sizedWidth * 0.04,
              right: sizedWidth * 0.04,
            ),
            decoration: BoxDecoration(
              color: AppColors.surface.withOpacity(0.5),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                  color: AppColors.border.withOpacity(0.2), width: 1),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadow.withOpacity(0.08),
                  blurRadius: 12,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Icon(Icons.thermostat_rounded,
                        color: typeState == 1
                            ? AppColors.primary
                            : typeState == 0
                                ? Colors.orange
                                : Colors.green,
                        size: sized * 0.02),
                    SizedBox(width: sizedWidth * 0.02),
                    Expanded(
                      child: Text(
                        'درجة الحرارة',
                        style: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: sized * 0.012,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: sizedWidth * 0.006,
                        vertical: sizedHeight * 0.001,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: typeState == 1
                              ? [
                                  AppColors.primary.withOpacity(0.1),
                                  AppColors.primary.withOpacity(0.05)
                                ]
                              : typeState == 0
                                  ? [
                                      Colors.orange.withOpacity(0.1),
                                      Colors.orange.withOpacity(0.05)
                                    ]
                                  : [
                                      Colors.green.withOpacity(0.1),
                                      Colors.green.withOpacity(0.05)
                                    ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                            color: typeState == 1
                                ? AppColors.primary.withOpacity(0.2)
                                : typeState == 0
                                    ? Colors.orange.withOpacity(0.2)
                                    : Colors.green.withOpacity(0.2),
                            width: 1.5),
                      ),
                      child: Text(
                        '${degree.toInt()}°C',
                        style: TextStyle(
                          fontSize: sized * 0.012,
                          color: typeState == 1
                              ? AppColors.primary
                              : typeState == 0
                                  ? Colors.orange
                                  : Colors.green,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 1.2,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: sizedHeight * 0.006),
                Container(
                  height: sizedHeight * 0.04,
                  child: Row(
                    children: [
                      Expanded(
                        child: SliderTheme(
                          data: SliderThemeData(
                            activeTrackColor: typeState == 1
                                ? AppColors.primary
                                : typeState == 0
                                    ? Colors.orange
                                    : Colors.green,
                            inactiveTrackColor:
                                AppColors.border.withOpacity(0.3),
                            thumbColor: typeState == 1
                                ? AppColors.primary
                                : typeState == 0
                                    ? Colors.orange
                                    : Colors.green,
                            thumbShape: RoundSliderThumbShape(
                                enabledThumbRadius: sized * 0.005),
                            overlayColor: AppColors.primary.withOpacity(0.2),
                            overlayShape: RoundSliderOverlayShape(
                                overlayRadius: sized * 0.001),
                            trackHeight: sized * 0.002,
                            valueIndicatorColor: typeState == 1
                                ? AppColors.primary
                                : typeState == 0
                                    ? Colors.orange
                                    : Colors.green,
                            valueIndicatorTextStyle: TextStyle(
                              color: AppColors.surface,
                              fontSize: sized * 0.012,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          child: Slider(
                            min: 16,
                            max: 30,
                            divisions: 14,
                            value: degree.toDouble(),
                            onChanged: sliderState,
                            label: '${degree.toInt()}°C',
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Fan Speed Control with Swing - Professional Style from Favorite
          Container(
            padding: EdgeInsets.only(
                left: sizedWidth * 0.04,
                right: sizedWidth * 0.04,
                top: sizedHeight * 0.005,
                bottom: sizedHeight * 0.01),
            margin: EdgeInsets.only(
              left: sizedWidth * 0.04,
              right: sizedWidth * 0.04,
              top: sizedHeight * 0.005,
            ),
            decoration: BoxDecoration(
              color: AppColors.surface.withOpacity(0.5),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                  color: AppColors.border.withOpacity(0.2), width: 1),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadow.withOpacity(0.08),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // Fan Speed Controls with Header and Swing Icon
                Row(
                  children: [
                    // Fan Speed Header
                    Icon(Icons.air_rounded,
                        color: typeState == 1
                            ? AppColors.primary
                            : typeState == 0
                                ? Colors.orange
                                : Colors.green,
                        size: sized * 0.02),
                    SizedBox(width: sizedWidth * 0.015),
                    Text(
                      'المروحة',
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: sized * 0.012,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(width: sizedWidth * 0.02),

                    // Fan Speed Controls
                    Expanded(
                      flex: 3,
                      child: Container(
                        height: sizedHeight * 0.04,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                              colors: [
                                AppColors.surfaceElevated,
                                AppColors.surface
                              ],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                              color: AppColors.border.withOpacity(0.3),
                              width: 1),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: GestureDetector(
                                onTap: acSpeedsStateLeft,
                                child: Container(
                                  child: Icon(
                                    Icons.remove_rounded,
                                    color: speedState > 0
                                        ? typeState == 1
                                            ? AppColors.primary
                                            : typeState == 0
                                                ? Colors.orange
                                                : Colors.green
                                        : AppColors.textHint,
                                    size: sized * 0.015,
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: typeState == 1
                                      ? AppColors.primary.withOpacity(0.1)
                                      : typeState == 0
                                          ? Colors.orange.withOpacity(0.1)
                                          : Colors.green.withOpacity(0.1),
                                  border: Border.symmetric(
                                    vertical: BorderSide(
                                        color:
                                            AppColors.border.withOpacity(0.3),
                                        width: 1),
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    acFanSpeed[speedState],
                                    style: TextStyle(
                                      fontSize: sized * 0.01,
                                      color: typeState == 1
                                          ? AppColors.primary
                                          : typeState == 0
                                              ? Colors.orange
                                              : Colors.green,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              child: GestureDetector(
                                onTap: acSpeedsStateRight,
                                child: Container(
                                  child: Icon(
                                    Icons.add_rounded,
                                    color: speedState < 3
                                        ? typeState == 1
                                            ? AppColors.primary
                                            : typeState == 0
                                                ? Colors.orange
                                                : Colors.green
                                        : AppColors.textHint,
                                    size: sized * 0.015,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    SizedBox(width: sizedWidth * 0.02),

                    // Swing Icon Button
                    GestureDetector(
                      onTap: acSwingState,
                      child: Container(
                        width: sizedWidth * 0.1,
                        height: sizedHeight * 0.04,
                        decoration: BoxDecoration(
                          gradient: swingState
                              ? LinearGradient(
                                  colors: [
                                    AppColors.success,
                                    AppColors.success.withOpacity(0.8),
                                  ],
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                )
                              : LinearGradient(
                                  colors: [
                                    AppColors.surfaceElevated,
                                    AppColors.surface,
                                  ],
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                ),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: swingState
                                ? AppColors.success.withOpacity(0.3)
                                : AppColors.border.withOpacity(0.3),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: swingState
                                  ? AppColors.success.withOpacity(0.2)
                                  : AppColors.shadow.withOpacity(0.05),
                              blurRadius: 6,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.swap_vert_rounded,
                          color: swingState
                              ? AppColors.white
                              : AppColors.textSecondary,
                          size: sized * 0.018,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(height: sizedHeight * 0.0005),
          // Mode Control Buttons - Professional Style from Favorite
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: sizedHeight * 0.07,
                width: sizedWidth * 0.6,
                margin: EdgeInsets.only(
                    right: sizedWidth * 0.04, top: sizedHeight * 0.005),
                decoration: BoxDecoration(
                  color: AppColors.surface.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(16),
                  // border: Border.all(
                  //     color: AppColors.border.withOpacity(0.3), width: 1),
                ),
                child: Row(
                  children: [
                    _buildModeButton(
                      label: 'تدفئة',
                      icon: Icons.local_fire_department_rounded,
                      isSelected: typeState == 0,
                      onTap: () => acTypeState(0),
                      color: Colors.orange,
                      sized: sized,
                      sizedHeight: sizedHeight,
                      sizedWidth: sizedWidth,
                    ),
                    _buildModeButton(
                      label: 'تبريد',
                      icon: Icons.ac_unit_rounded,
                      isSelected: typeState == 1,
                      onTap: () => acTypeState(1),
                      color: AppColors.primary,
                      sized: sized,
                      sizedHeight: sizedHeight,
                      sizedWidth: sizedWidth,
                    ),
                    _buildModeButton(
                      label: 'مروحة',
                      icon: Icons.air_rounded,
                      isSelected: typeState == 2,
                      onTap: () => acTypeState(2),
                      color: Colors.green,
                      sized: sized,
                      sizedHeight: sizedHeight,
                      sizedWidth: sizedWidth,
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    IconButton(
                      padding: EdgeInsets.zero,
                      onPressed: acRun,
                      icon: Transform.rotate(
                        angle: 3.14159, // 180 degrees in radians (π)
                        child: Icon(
                          Icons.play_circle_rounded,
                          color: AppColors.success,
                          size: sized * 0.065,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          // Green Play Button - Professional Style from Favorite
        ],
        deviceState: deviceState,
        switchState: switchState);

Widget shortcutTv({
  var deviceState = null,
  required Function() doubleTap,
  required Function(bool?) switchState,
  required Function() tapOn_Tv_Icon,
  required Function() tapOn_VolumeUp,
  required Function() tapOn_VolumeDown,
  required Function() tapOn_ChUp,
  required Function() tapOn_ChDown,
  required Function() tapOn_VolumeMute,
  required Function() tapOn_123,
  required Function() tapOn_menu,
  required Function() tapOn_star,
  required bool connect,
  String? tvPrivName,
  required bool sil,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
  // required var image,
}) =>
    shortCutStyle(
        connect: connect,
        type: 'تلفاز',
        doubleTap: doubleTap,
        tapOnIcon: tapOn_Tv_Icon,
        PrivName: tvPrivName!,
        content: [
          SizedBox(
            height: sizedHeight * 0.015,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              containerIconsOption(
                content: Column(
                  children: [
                    GestureDetector(
                      onTap: tapOn_ChUp,
                      child: iconStyle(
                        icon: Icons.arrow_drop_up_rounded,
                        size: sized * 0.035,
                        color: AppColors.textColor3,
                      ),
                    ),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                    IconButton(
                      padding: EdgeInsets.zero,
                      onPressed: tapOn_menu,
                      iconSize: sized * 0.035,
                      icon: iconStyle(
                        icon: Icons.swap_horiz_rounded,
                        size: sized * 0.035,
                        color: AppColors.textColor.withOpacity(0.8),
                      ),
                    ),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                    GestureDetector(
                      onTap: tapOn_ChDown,
                      child: iconStyle(
                        icon: Icons.arrow_drop_down_rounded,
                        size: sized * 0.035,
                        color: AppColors.textColor3,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: sizedWidth * 0.1,
              ),
              containerIconsOption(
                radius: 25,
                content: Column(
                  children: [
                    SizedBox(
                      height: sizedHeight * 0.006,
                    ),
                    IconButton(
                      padding: EdgeInsets.zero,
                      onPressed: tapOn_star,
                      icon: iconStyle(
                        icon: Icons.important_devices_rounded,
                        size: sized * 0.035,
                        color: AppColors.textColor2,
                      ),
                    ),
                    SizedBox(
                      width: sizedWidth * 0.15,
                      height: sized * 0.027,
                    ),
                    IconButton(
                      padding: EdgeInsets.only(bottom: 5),
                      onPressed: tapOn_123,
                      icon: iconStyle(
                        icon: Icons.pin_rounded,
                        size: sized * 0.035,
                        color: AppColors.textColor2,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: sizedWidth * 0.1,
              ),
              containerIconsOption(
                content: Column(
                  children: [
                    GestureDetector(
                      onTap: tapOn_VolumeUp,
                      child: iconStyle(
                        icon: Icons.arrow_drop_up_rounded,
                        size: sized * 0.035,
                        color: AppColors.textColor3,
                      ),
                    ),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                    IconButton(
                      padding: EdgeInsets.zero,
                      onPressed: tapOn_VolumeMute,
                      iconSize: sized * 0.032,
                      icon: iconStyle(
                        icon: sil
                            ? Icons.volume_up_rounded
                            : Icons.volume_off_rounded,
                        size: sized * 0.032,
                        color: AppColors.textColor.withOpacity(0.8),
                      ),
                    ),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                    GestureDetector(
                      onTap: tapOn_VolumeDown,
                      child: iconStyle(
                        icon: Icons.arrow_drop_down_rounded,
                        size: sized * 0.035,
                        color: AppColors.textColor3,
                      ),
                    ),
                  ],
                ),
              ),
              // SizedBox(
              //   width: 20,
              // ),
            ],
          ),
          SizedBox(
            height: 10,
          ),
        ],
        deviceState: deviceState,
        switchState: switchState);

Widget shortcutSwitch({
  var deviceState = null,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
  required bool connect,
  String? SwPrivName,
  required Map swList,
  required Function() doubleTap,
  required Function(bool?) switchState,
  required Function(String?, bool?) switchTap,
  // required var image,
}) =>
    shortCutStyle(
        connect: connect,
        type: 'مفاتيح',
        doubleTap: doubleTap,
        tapOnIcon: () {},
        PrivName: SwPrivName!.split('_')[0],
        content: connect
            ? <Widget>[
                Directionality(
                  textDirection: TextDirection.rtl,
                  child: Container(
                    // height: 160,
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: GridView(
                      padding: const EdgeInsets.only(top: 5),
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                        childAspectRatio: 0.75,
                      ),
                      children: [
                        for (String i in swList.keys
                            .toList()
                            .getRange(0, swList.length - 4))
                          Container(
                              // height: 160,
                              // margin: const EdgeInsets.symmetric(horizontal: 5),
                              // color: AppColors.textColor,
                              child: CircularPercentIndicator(
                                  radius: sized * 0.03,
                                  lineWidth: sized * 0.006,
                                  percent: swList[i]['state'] ? 0.965 : 0.0,
                                  // startAngle: 1,
                                  backgroundWidth: sized * 0.007,
                                  backgroundColor: swList[i]['state']
                                      ? Colors.transparent
                                      : AppColors.backgroundSecondary
                                          .withOpacity(0.3),
                                  footer: Container(
                                    // height: 20,
                                    child: Text(
                                      SwPrivName.split('_')[int.parse(
                                          i.replaceFirst(RegExp(r'v'), ''))],
                                      // 'ssss',
                                      textDirection: TextDirection.rtl,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: swList[i]['state']
                                            ? AppColors.textColor2
                                            : AppColors.backgroundColor2,
                                        fontSize: swList[i]['state']
                                            ? sized * 0.011
                                            : sized * 0.011,
                                      ),
                                    ),
                                  ),
                                  center: IconButton(
                                    onPressed: () {
                                      switchTap(i, swList[i]['state']);
                                    },
                                    splashColor: Colors.transparent,
                                    icon: Icon(
                                      swList[i]['type'] == 'LIGHT'
                                          ? Icons.lightbulb_outline
                                          : swList[i]['type'] == 'VAN'
                                              ? Icons.storm_outlined
                                              : Icons.power_outlined,
                                      color: swList[i]['state']
                                          ? AppColors.textPrimary
                                              .withOpacity(0.8)
                                          : AppColors.textPrimary
                                              .withOpacity(0.4),
                                      size: sized * 0.03,
                                    ),
                                  ),
                                  linearGradient: const LinearGradient(
                                      begin: Alignment.topRight,
                                      end: Alignment.bottomLeft,
                                      colors: <Color>[
                                        Color(0xFF6DD400),
                                        Color(0xFF1AB600),
                                      ]),
                                  rotateLinearGradient: true,
                                  circularStrokeCap: CircularStrokeCap.round)),
                      ],
                    ),
                  ),
                ),
              ]
            : [],
        deviceState: deviceState,
        switchState: switchState);

Widget shortcutZain({
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
  var volume = 20,
  var hearing = 15,
  bool sil = true,
  bool lestin = true,
  bool play = true,
  required bool connect,
  required String ZName,
  bool Zmain = false,
  required Function() TapPlay,
  required Function() Play,
  required Function() tapOn_VolumeMute,
  required Function() tapOn_hearingMute,
  required Function() doubleTap,
  required Function() tapOn_Switch_Icon,
  required Function(double?) vState,
  required Function(double?) hState,
}) =>
    GestureDetector(
        onDoubleTap: doubleTap,
        child: Container(
            margin: EdgeInsets.symmetric(
                horizontal: controller.sizedWidth * 0.055,
                vertical: controller.sizedHight * 0.0075),
            padding:
                EdgeInsets.symmetric(horizontal: controller.sizedWidth * 0.02),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(30)),
              color: AppColors.surfaceColor.withOpacity(0.75),
            ),
            child: Directionality(
              textDirection: TextDirection.rtl,
              child: ExpansionTileCard(
                  duration: Duration(milliseconds: 400),
                  baseColor: Colors.transparent,
                  expandedColor: Colors.transparent,
                  shadowColor: Colors.transparent,
                  borderRadius: BorderRadius.all(Radius.circular(30)),
                  contentPadding: EdgeInsets.symmetric(
                      horizontal: controller.sizedWidth * 0.01),
                  trailing: connect != true
                      ? Padding(
                          padding: EdgeInsets.all(sized * 0.013),
                          child: txtStyle(
                              txt: 'غير متصل', color: AppColors.errorColor))
                      : Zmain
                          ? Padding(
                              padding:
                                  EdgeInsets.only(right: sizedWidth * 0.01),
                              child: IconButton(
                                onPressed: tapOn_Switch_Icon,
                                icon: iconStyle(
                                    icon: Icons.star_rounded,
                                    color: AppColors.warningColor),
                              ))
                          : Container(),
                  title: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      IconButton(
                        onPressed: tapOn_Switch_Icon,
                        icon: iconStyle(
                            icon: Icons.flutter_dash_outlined,
                            color: AppColors.warningColor),
                      ),
                      Expanded(
                        child: Container(
                          margin: EdgeInsets.zero,
                          // padding: EdgeInsets.symmetric(horizontal: 5),
                          // color: Colors.blueGrey.shade600,
                          alignment: Alignment.bottomRight,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                padding:
                                    EdgeInsets.only(left: sizedWidth * 0.01),
                                decoration: BoxDecoration(
                                    border: Border(
                                        left: BorderSide(
                                            color: AppColors.textColor2
                                                .withOpacity(0.45),
                                            width: 1.5))),
                                child: txtStyle(
                                  txt: Zmain
                                      ? 'مساعد صوتي : رئيسي'
                                      : 'مساعد صوتي',
                                ),
                              ),
                              Container(
                                padding: EdgeInsets.only(right: sized * 0.01),
                                child: txtStyle(
                                  txt: ZName,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  children: connect == true
                      ? [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              IconButton(
                                onPressed: Play,
                                padding: EdgeInsets.zero,
                                icon: iconStyle(
                                    icon: Icons.fast_forward_rounded,
                                    size: controller.sized * 0.04),
                              ),
                              SizedBox(
                                width: controller.sizedWidth * 0.07,
                              ),
                              IconButton(
                                padding: EdgeInsets.zero,
                                onPressed: TapPlay,
                                icon: iconStyle(
                                    icon: play
                                        ? Icons.play_arrow_rounded
                                        : Icons.pause_rounded,
                                    size: controller.sized * 0.04),
                              ),
                              SizedBox(
                                width: controller.sizedWidth * 0.07,
                              ),
                              IconButton(
                                onPressed: Play,
                                padding: EdgeInsets.zero,
                                icon: iconStyle(
                                    icon: Icons.fast_rewind_rounded,
                                    size: controller.sized * 0.04),
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Expanded(
                                child: Padding(
                                  padding:
                                      EdgeInsets.only(right: sizedWidth * 0.06),
                                  child: Slider(
                                      min: 0,
                                      max: 30,
                                      activeColor:
                                          AppColors.textColor.withOpacity(0.7),
                                      inactiveColor:
                                          AppColors.textColor.withOpacity(0.15),
                                      thumbColor:
                                          AppColors.textColor.withOpacity(0.95),
                                      value: volume.toDouble(),
                                      onChanged: vState),
                                ),
                              ),
                              Padding(
                                padding:
                                    EdgeInsets.only(left: sizedWidth * 0.025),
                                child: IconButton(
                                  padding: EdgeInsets.zero,
                                  onPressed: tapOn_VolumeMute,
                                  iconSize: sized * 0.032,
                                  icon: iconStyle(
                                    icon: sil
                                        ? Icons.volume_up_rounded
                                        : Icons.volume_off_rounded,
                                    color:
                                        AppColors.textColor.withOpacity(0.75),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Expanded(
                                  child: Padding(
                                    padding:
                                        EdgeInsets.only(right: sized * 0.02),
                                    child: Slider(
                                        min: 0,
                                        max: 30,
                                        activeColor: AppColors.textColor
                                            .withOpacity(0.7),
                                        inactiveColor: AppColors.textColor
                                            .withOpacity(0.15),
                                        thumbColor: AppColors.textColor
                                            .withOpacity(0.95),
                                        value: hearing.toDouble(),
                                        onChanged: hState),
                                  ),
                                ),
                                Padding(
                                  padding:
                                      EdgeInsets.only(left: sizedWidth * 0.025),
                                  child: IconButton(
                                    padding: EdgeInsets.zero,
                                    onPressed: tapOn_hearingMute,
                                    iconSize: sized * 0.032,
                                    icon: iconStyle(
                                      icon: lestin
                                          ? Icons.hearing_rounded
                                          : Icons.hearing_disabled_rounded,
                                      color:
                                          AppColors.textColor.withOpacity(0.75),
                                    ),
                                  ),
                                ),
                              ])
                        ]
                      : []),
            )));

Widget shortcutRoom(
        {required Function() onTap,
        required double sizedWidth,
        required double sizedHeight,
        required double sized,
        var roomState,
        required bool connect,
        required String roomName,
        String? roomPrivName,
        required Function(bool?) switchState,
        required var image,
        required}) =>
    GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(
            horizontal: sizedWidth * 0.045, vertical: sizedHeight * 0.015),
        height: controller.sizedHight * 0.175,
        decoration: BoxDecoration(
          image: DecorationImage(
              image: image,
              fit: BoxFit.cover,
              colorFilter: ColorFilter.mode(
                  AppColors.black.withOpacity(0.25), BlendMode.darken)),

          borderRadius: const BorderRadius.all(Radius.circular(25)),
          // boxShadow: [
          //   BoxShadow(
          //     color: AppColors.textColor.withOpacity(0.7),
          //     spreadRadius: 2,
          //     blurRadius: 10,
          //     // offset: Offset(0, 0)
          //   )
          // ],
          // border: Border.all(width: 0.5, color: Colors.blueGrey.shade800)
        ),
        child: Column(
          children: [
            Row(
              children: [
                Padding(
                    padding: EdgeInsets.all(sized * 0.01),
                    child: connect == false
                        ? Padding(
                            padding: EdgeInsets.all(sized * 0.004),
                            child: Text(
                              'غير متصل',
                              textDirection: TextDirection.rtl,
                              style: TextStyle(
                                  color: AppColors.errorColor,
                                  fontSize: sized * 0.015,
                                  fontWeight: FontWeight.bold),
                            ))
                        : Directionality(
                            textDirection: TextDirection.rtl,
                            child: switchStyle(
                                value: roomState, onChanged: switchState),
                          )),
                Expanded(
                  child: Container(
                    padding: EdgeInsets.only(
                        right: sizedWidth * 0.03, top: sizedHeight * 0.02),
                    height: controller.sizedHight * 0.15,
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Container(
                          width: controller.sizedWidth * 0.6,
                          child: txtStyle(
                            align: TextAlign.right,
                            maxLines: 2,
                            txt: roomPrivName! == 'x'
                                ? 'لا يوجد اسم'
                                : roomPrivName.length > 12
                                    ? roomPrivName.substring(0, 14) + '...'
                                    : roomPrivName,
                            color: Color.fromARGB(255, 239, 235, 235),
                            size: roomPrivName.length > 12
                                ? sized * 0.023
                                : sized * 0.025,
                          ),
                        ),
                        SizedBox(
                          height: controller.sizedHight * 0.01,
                        ),
                        txtStyle(
                          txt: roomName,
                          color: Color.fromARGB(255, 200, 200, 200),
                          size: sized * 0.013,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );

// Helper function to build mode toggle buttons for AC shortcuts
Widget _buildModeButton({
  required String label,
  required IconData icon,
  required bool isSelected,
  required VoidCallback onTap,
  required Color color,
  required double sized,
  required double sizedHeight,
  required double sizedWidth,
}) {
  return Expanded(
    child: GestureDetector(
      onTap: onTap,
      child: Container(
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [
                    color,
                    color.withOpacity(0.8),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                )
              : null,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected
                  ? AppColors.surface.withOpacity(0.8)
                  : AppColors.textHint,
              size: isSelected
                  ? controller.sized * 0.017
                  : controller.sized * 0.015,
            ),
            SizedBox(height: sizedHeight * 0.003),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? AppColors.surface : AppColors.textHint,
                fontSize: isSelected
                    ? controller.sized * 0.01
                    : controller.sized * 0.009,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
