---
configuration:
  domains:
    name: Domains
    description: >-
      A list of DuckDNS subdomains registered under your account. An acceptable
      naming convention is `my-domain.duckdns.org`.
  token:
    name: Token
    description: >-
      The DuckDNS authentication token found at the top of the DuckDNS account
      landing page. The token is required to make any changes to the subdomains
      registered to your account.
  aliases:
    name: Alias
    description: A list aliases of domains configured on the `domains` option.
  lets_encrypt:
    name: Let's Encrypt
    description: Configure Let's Encrypt options
  seconds:
    name: Seconds
    description: >-
      The number of seconds to wait before updating DuckDNS subdomains and
      renewing Let's Encrypt certificates.
  ipv4:
    name: IPv4
    description: >-
      By default, DuckD<PERSON> will auto detect your IPv4 address and use that.
      This option allows you to override the auto-detection and specify an
      IPv4 address manually. The value 'none' disables IPv4 updates.
  ipv6:
    name: IPv6
    description: >-
      <PERSON><PERSON><PERSON> currently cannot auto-detect your IPv6 address. This option
      allows to specify either 'default' or an interface name to use an IPv6
      address of the host, alternatively you can input an address manually.
