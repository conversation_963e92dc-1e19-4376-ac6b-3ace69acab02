import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Widget TVPage({
  String? id,
  var deviceState = null,
  String? roomN,
  required Function(bool?) switchState,
  required Function() del,
  required Function() Dfavorite,
  required Function() editRoom,
  required Function(String?) editNames,
  required Function(bool?, String?) editPrivName,
  required Function() tapOn_Tv_Icon,
  required Function() tapOn_VolumeUp,
  required Function() tapOn_VolumeDown,
  required Function() tapOn_ChUp,
  required Function() tapOn_ChDown,
  required Function() tapOn_VolumeMute,
  required Function() tapOn_123,
  required Function() tapOn_menu,
  required Function(String?) tapOn_star,
  required List<Widget> tv_task,
  required bool connect,
  String? tvPrivName,
  required bool sil,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  TextEditingController editPriv = TextEditingController(
    text: tvPrivName != 'x' ? tvPrivName : 'X',
  );
  bool privN = false;
  PageController pageController = PageController();

  return pageSlide(
    content: GestureDetector(
      onTap: () {
        if (privN) {
          if (editPriv.text == '' ||
              editPriv.text == null ||
              editPriv.text == 'X' ||
              editPriv.text == 'x') {
            editPriv.text = tvPrivName! != 'x' ? tvPrivName : 'X';
            privN = false;
          } else {
            for (var i = 0; i < editPriv.text.length; i++) {
              if (arabic.contains(editPriv.text[i]) ||
                  editPriv.text[i].isNumericOnly) {
                privN = true;
              } else {
                editPriv.text = tvPrivName! != 'x' ? tvPrivName : 'X';
                privN = false;
                break;
              }
            }

            if (privN) {
              editPrivName(privN, editPriv.text);
              privN = false;
            }
          }
        }
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: PageView(
        scrollDirection: Axis.vertical,
        // shrinkWrap: true,
        onPageChanged: (i) {
          FocusManager.instance.primaryFocus?.unfocus();
          editPriv.text = tvPrivName! != 'x' ? tvPrivName : 'X';
        },
        physics: BouncingScrollPhysics(),
        children: connect == true &&
                client.connectionStatus!.state.name == 'connected'
            ? [
                Container(
                  child: SingleChildScrollView(
                    physics: BouncingScrollPhysics(
                        parent: NeverScrollableScrollPhysics()),
                    child: Column(
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: sizedWidth * 0.035),
                          child: Row(mainAxisSize: MainAxisSize.min, children: [
                            Directionality(
                              textDirection: TextDirection.rtl,
                              child: switchStyle(
                                  value: deviceState, onChanged: switchState),
                            ),
                            Expanded(
                              child: Container(
                                alignment: Alignment.bottomRight,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Container(
                                        width: controller.sizedWidth * 0.44,
                                        padding: EdgeInsets.only(
                                            right: controller.sized * 0.01),
                                        child: FittedBox(
                                          alignment: Alignment.centerRight,
                                          fit: BoxFit.scaleDown,
                                          child: txtStyle(
                                              align: TextAlign.right,
                                              txt: tvPrivName != 'x'
                                                  ? tvPrivName!
                                                  : 'لا يوجد اسم'),
                                        )),
                                    Container(
                                        padding: EdgeInsets.only(
                                            left: controller.sizedWidth * 0.01),
                                        decoration: BoxDecoration(
                                            border: Border(
                                                left: BorderSide(
                                                    color: AppColors.textColor
                                                        .withOpacity(0.25),
                                                    width: 1.5))),
                                        child: txtStyle(
                                            align: TextAlign.right,
                                            txt: 'تلفاز',
                                            color: AppColors.textColor3)),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(
                              width: sizedWidth * 0.01,
                            ),
                            iconStyle(
                              icon: Icons.tv_rounded,
                              color: AppColors.warningColor,
                              size: sized * 0.035,
                            ),
                          ]),
                        ),
                        SizedBox(
                          height: sizedHeight * 0.85,
                          child: PageView(
                              scrollDirection: Axis.horizontal,
                              physics: BouncingScrollPhysics(),
                              controller: pageController,
                              children: [
                                Column(
                                  children: [
                                    SizedBox(
                                      height: sizedHeight * 0.05,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        containerIconsOption(
                                          content: Column(
                                            children: [
                                              GestureDetector(
                                                onTap: tapOn_ChUp,
                                                child: iconStyle(
                                                  icon: Icons
                                                      .arrow_drop_up_rounded,
                                                  size: sized * 0.06,
                                                  color: AppColors.textColor3,
                                                ),
                                              ),
                                              SizedBox(
                                                height: sizedHeight * 0.025,
                                              ),
                                              IconButton(
                                                padding: EdgeInsets.zero,
                                                onPressed: tapOn_menu,
                                                iconSize: sized * 0.050,
                                                icon: iconStyle(
                                                    icon: Icons
                                                        .swap_horiz_rounded,
                                                    size: controller.sized *
                                                        0.04),
                                              ),
                                              SizedBox(
                                                height: sizedHeight * 0.025,
                                              ),
                                              GestureDetector(
                                                onTap: tapOn_ChDown,
                                                child: iconStyle(
                                                  icon: Icons
                                                      .arrow_drop_down_rounded,
                                                  size: sized * 0.06,
                                                  color: AppColors.textColor3,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          width: sizedWidth * 0.1,
                                        ),
                                        containerIconsOption(
                                          // padding: EdgeInsets.o,
                                          content: Column(
                                            children: [
                                              SizedBox(
                                                height: sizedHeight * 0.015,
                                              ),
                                              IconButton(
                                                padding: EdgeInsets.zero,
                                                onPressed: () {
                                                  tapOn_star('show');
                                                },
                                                icon: iconStyle(
                                                    icon: Icons
                                                        .important_devices_rounded,
                                                    size: controller.sized *
                                                        0.04),
                                              ),
                                              SizedBox(
                                                width: sizedWidth * 0.2,
                                                height: sized * 0.027,
                                              ),
                                              IconButton(
                                                padding:
                                                    EdgeInsets.only(bottom: 5),
                                                onPressed: tapOn_123,
                                                icon: iconStyle(
                                                    icon: Icons.pin_rounded,
                                                    size: controller.sized *
                                                        0.04),
                                              ),
                                              SizedBox(
                                                height: sized * 0.027,
                                              ),
                                              IconButton(
                                                padding:
                                                    EdgeInsets.only(bottom: 5),
                                                onPressed: () {},
                                                icon: iconStyle(
                                                    icon: Icons
                                                        .subdirectory_arrow_left_rounded,
                                                    size: controller.sized *
                                                        0.04),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          width: sizedWidth * 0.1,
                                        ),
                                        containerIconsOption(
                                          content: Column(
                                            children: [
                                              GestureDetector(
                                                onTap: tapOn_VolumeUp,
                                                child: iconStyle(
                                                    icon: Icons
                                                        .arrow_drop_up_rounded,
                                                    size: sized * 0.06,
                                                    color:
                                                        AppColors.textColor3),
                                              ),
                                              SizedBox(
                                                height: sizedHeight * 0.03,
                                              ),
                                              IconButton(
                                                padding: EdgeInsets.zero,
                                                onPressed: tapOn_VolumeMute,
                                                iconSize: sized * 0.045,
                                                icon: iconStyle(
                                                    icon: sil
                                                        ? Icons
                                                            .volume_up_rounded
                                                        : Icons
                                                            .volume_off_rounded,
                                                    size: controller.sized *
                                                        0.04),
                                              ),
                                              SizedBox(
                                                height: sizedHeight * 0.03,
                                              ),
                                              GestureDetector(
                                                onTap: tapOn_VolumeDown,
                                                child: iconStyle(
                                                    icon: Icons
                                                        .arrow_drop_down_rounded,
                                                    size: sized * 0.06,
                                                    color:
                                                        AppColors.textColor3),
                                              ),
                                            ],
                                          ),
                                        ),
                                        // SizedBox(
                                        //   width: 20,
                                        // ),
                                      ],
                                    ),
                                    SizedBox(
                                      height: sizedHeight * 0.05,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        SizedBox(
                                          width: sizedWidth * 0.1,
                                        ),
                                        IconButton(
                                          padding: EdgeInsets.zero,
                                          onPressed: () {},
                                          icon: iconStyle(
                                              icon: Icons.exit_to_app_rounded,
                                              size: controller.sized * 0.04),
                                        ),
                                        SizedBox(
                                          width: sizedWidth * 0.15,
                                        ),
                                        IconButton(
                                          padding: EdgeInsets.only(bottom: 5),
                                          onPressed: () {},
                                          icon: iconStyle(
                                              icon: Icons.info_outline_rounded,
                                              size: controller.sized * 0.04),
                                        ),
                                        SizedBox(
                                          width: sizedWidth * 0.15,
                                        ),
                                        IconButton(
                                            padding: EdgeInsets.only(bottom: 5),
                                            onPressed: () {},
                                            icon: iconStyle(
                                                icon: Icons
                                                    .video_settings_rounded,
                                                size: controller.sized * 0.04)),
                                        SizedBox(
                                          width: sizedWidth * 0.1,
                                        ),
                                      ],
                                    ),
                                    SizedBox(
                                      height: sizedHeight * 0.05,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        SizedBox(
                                          width: sizedWidth * 0.1,
                                        ),
                                        IconButton(
                                          padding: EdgeInsets.zero,
                                          onPressed: () {
                                            pageController.nextPage(
                                                duration:
                                                    Duration(milliseconds: 650),
                                                curve: Curves.ease);
                                          },
                                          icon: iconStyle(
                                              icon: Icons.fast_rewind_rounded,
                                              size: controller.sized * 0.04),
                                        ),
                                        SizedBox(
                                          width: sizedWidth * 0.15,
                                        ),
                                        IconButton(
                                            padding: EdgeInsets.only(bottom: 5),
                                            onPressed: () {},
                                            icon: iconStyle(
                                                icon: Icons.play_arrow_rounded,
                                                size: controller.sized * 0.04)),
                                        SizedBox(
                                          width: sizedWidth * 0.15,
                                        ),
                                        IconButton(
                                            padding: EdgeInsets.only(bottom: 5),
                                            onPressed: () {},
                                            icon: iconStyle(
                                              icon: Icons.fast_forward_rounded,
                                              size: controller.sized * 0.04,
                                            )),
                                        SizedBox(
                                          width: sizedWidth * 0.1,
                                        ),
                                      ],
                                    ),
                                    SizedBox(
                                      height: sizedHeight * 0.05,
                                    ),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        IconButton(
                                          onPressed: () {
                                            FocusManager.instance.primaryFocus
                                                ?.unfocus();
                                            pageController.nextPage(
                                                duration:
                                                    Duration(milliseconds: 650),
                                                curve: Curves.ease);
                                          },
                                          icon: Icon(Icons.more_time_rounded),
                                          iconSize: sized * 0.045,
                                          color: AppColors.textColor
                                              .withOpacity(0.7),
                                        )
                                      ],
                                    )
                                  ],
                                ),
                                Column(
                                  children: tv_task,
                                )
                              ]),
                        )
                      ],
                    ),
                  ),
                ),
                pageSetting(
                    del: del,
                    id: id,
                    Dfavorite: Dfavorite,
                    editRoom: editRoom,
                    editNames: editNames,
                    editPrivName: editPrivName,
                    roomN: roomN,
                    privName: tvPrivName,
                    tapOn_star: tapOn_star,
                    isTv: true,
                    type: 'تلفاز')
              ]
            : [
                pageSetting(
                    del: del,
                    id: id,
                    Dfavorite: Dfavorite,
                    editRoom: editRoom,
                    editNames: editNames,
                    editPrivName: editPrivName,
                    roomN: roomN,
                    privName: tvPrivName,
                    tapOn_star: tapOn_star,
                    isTv: true,
                    type: 'تلفاز',
                    connect: false)
              ],
      ),
    ),
  );
}
