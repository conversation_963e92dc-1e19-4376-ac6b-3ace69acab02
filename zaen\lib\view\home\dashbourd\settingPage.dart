import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/settings/settings.dart';
import 'package:zaen/controller/controller.dart';
import 'settingFolder/people.dart';
import 'package:zaen/pages/notifications_settings_page.dart';
import 'package:zaen/pages/advanced_automation_page.dart';

Widget HomePage({
  required BuildContext context,
  String? roomPrivName,
  var image,
  String? homeType,
  Function()? routinWords,
  Function()? tasks,
  Function()? addRoom,
  Function()? nunDevices,
  required Function() del,
  required Function() asset,
  required Function() roll,
  required Function() camera,
  required Function(String?) editNames,
  required Function(bool?, String?) editPrivName,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  TextEditingController editPriv = TextEditingController(
    text: roomPrivName != 'x' ? roomPrivName : 'X',
  );
  bool privN = false;

  return GetBuilder<SettingsController>(
    builder: (settingsController) => GestureDetector(
      onTap: () {
        if (privN) {
          if (editPriv.text == '' ||
              editPriv.text == null ||
              editPriv.text == 'X' ||
              editPriv.text == 'x') {
            editPriv.text = roomPrivName != null ? roomPrivName : 'X';
            privN = false;
          } else {
            for (var i = 0; i < editPriv.text.length; i++) {
              if (arabic.contains(editPriv.text[i]) ||
                  editPriv.text[i].isNumericOnly) {
                privN = true;
              } else {
                editPriv.text = roomPrivName != null ? roomPrivName : 'X';
                privN = false;
                break;
              }
            }

            if (privN) {
              editPrivName(privN, editPriv.text);
              privN = false;
            }
          }
        }
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: PageView(
          scrollDirection: Axis.vertical,
          // shrinkWrap: true,
          onPageChanged: (i) {
            FocusManager.instance.primaryFocus?.unfocus();
            editPriv.text = roomPrivName != null ? roomPrivName : 'X';
          },
          physics: BouncingScrollPhysics(),
          children: [
            SingleChildScrollView(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: sizedWidth * 0.03),
                child: Directionality(
                  textDirection: TextDirection.rtl,
                  child: Column(
                    children: [
                      // اسم النظام
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            width: sizedWidth * 0.75,
                            child: TextFormField(
                              controller: editPriv,
                              maxLength: 15,
                              showCursor: true,
                              cursorColor: AppColors.primary,
                              textDirection: TextDirection.rtl,
                              style: TextStyle(
                                color: AppColors.textPrimary,
                                fontSize: sized * 0.015,
                                fontWeight: FontWeight.w500,
                              ),
                              onChanged: (i) {
                                privN = true;
                              },
                              onEditingComplete: () {
                                FocusManager.instance.primaryFocus?.unfocus();
                                if (editPriv.text == '' ||
                                    editPriv.text == null ||
                                    editPriv.text == 'X' ||
                                    editPriv.text == 'x') {
                                  editPriv.text =
                                      roomPrivName != null ? roomPrivName : 'X';
                                  privN = false;
                                } else if (editPriv.text != roomPrivName) {
                                  for (var i = 0;
                                      i < editPriv.text.length;
                                      i++) {
                                    if (arabic.contains(editPriv.text[i]) ||
                                        editPriv.text[i].isNumericOnly) {
                                      privN = true;
                                    } else {
                                      editPriv.text = roomPrivName != null
                                          ? roomPrivName
                                          : 'X';
                                      privN = false;
                                      break;
                                    }
                                  }
                                  if (privN) {
                                    editPrivName(privN, editPriv.text);
                                    privN = false;
                                  }
                                }
                              },
                              decoration: InputDecoration(
                                hintText: 'اسم النظام الخاص',
                                hintStyle: TextStyle(
                                  color: AppColors.textHint,
                                  fontSize: sized * 0.014,
                                  fontWeight: FontWeight.normal,
                                ),
                                filled: true,
                                fillColor: AppColors.surface,
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: sizedWidth * 0.04,
                                  vertical: sizedHeight * 0.015,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(
                                    color: AppColors.border,
                                    width: 1.0,
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(
                                    color: AppColors.border,
                                    width: 1.0,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(
                                    color: AppColors.primary,
                                    width: 2.0,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(
                                bottom: controller.sizedHight * 0.025),
                            child: iconStyle(
                              icon: controller.access == 'full'
                                  ? Icons.admin_panel_settings
                                  : controller.access == 'never'
                                      ? Icons.block
                                      : Icons.security,
                              color: controller.access == 'full'
                                  ? AppColors.successColor
                                  : controller.access == 'never'
                                      ? AppColors.errorColor
                                      : AppColors.primaryColor,
                              size: controller.sized * 0.025,
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: sizedHeight * 0.02),

                      // عنوان أدوات النظام
                      Container(
                        width: sizedWidth * 0.85,
                        padding:
                            EdgeInsets.symmetric(horizontal: sizedWidth * 0.02),
                        child: Text(
                          'أدوات النظام',
                          textDirection: TextDirection.rtl,
                          style: TextStyle(
                            color: AppColors.textPrimary,
                            fontSize: sized * 0.018,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),

                      SizedBox(height: sizedHeight * 0.015),

                      // الكلمات الروتينية
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02),
                          onPressed: routinWords,
                          child: Row(children: [
                            Expanded(
                                child: txtStyle(
                                    txt: 'الكلمات الروتينية',
                                    align: TextAlign.start)),
                            iconStyle(
                                icon: Icons.menu_open_rounded,
                                size: controller.sized * 0.02),
                          ]),
                        ),
                      ),
                      SizedBox(height: sizedHeight * 0.015),

                      // المهام المجدولة
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02),
                          onPressed: tasks,
                          child: Row(children: [
                            Expanded(
                                child: txtStyle(
                                    txt: 'المهام المجدولة',
                                    align: TextAlign.start)),
                            iconStyle(
                                icon: Icons.more_time_rounded,
                                color: AppColors.warningColor,
                                size: controller.sized * 0.02),
                          ]),
                        ),
                      ),

                      SizedBox(height: sizedHeight * 0.015),

                      // الخريطة (سيتم إضافتها لاحقاً)
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02),
                          onPressed: () {
                            // سيتم إضافة الوظيفة لاحقاً
                            if (!controller.canAccessMap()) {
                              // سيتم إضافة الوظيفة لاحق
                              showNoPermissionDialog(
                                  customMessage:
                                      'هذه الميزة غير متوفرة حالياً');
                              return;
                            }
                          },
                          child: Row(children: [
                            Expanded(
                                child: txtStyle(
                                    txt: 'الخريطة (قريباً)',
                                    align: TextAlign.start)),
                            iconStyle(
                                icon: Icons.map_rounded,
                                color: AppColors.textHint,
                                size: controller.sized * 0.02),
                          ]),
                        ),
                      ),
                      SizedBox(height: sizedHeight * 0.015),

                      // إعدادات الإشعارات
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02),
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const NotificationsSettingsPage(),
                              ),
                            );
                          },
                          child: Row(children: [
                            Expanded(
                                child: txtStyle(
                                    txt: 'إعدادات الإشعارات',
                                    align: TextAlign.start)),
                            iconStyle(
                                icon: Icons.notifications_active,
                                color: AppColors.primaryColor,
                                size: controller.sized * 0.02),
                          ]),
                        ),
                      ),

                      SizedBox(height: sizedHeight * 0.015),

                      // الأتمتة المتقدمة
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02),
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const AdvancedAutomationPage(),
                              ),
                            );
                          },
                          child: Row(children: [
                            Expanded(
                                child: txtStyle(
                                    txt: 'الأتمتة المتقدمة',
                                    align: TextAlign.start)),
                            iconStyle(
                                icon: Icons.smart_toy,
                                color: AppColors.primaryColor,
                                size: controller.sized * 0.02),
                          ]),
                        ),
                      ),

                      SizedBox(height: sizedHeight * 0.015),

                      // الأشخاص
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02),
                          onPressed: () {
                            showUsersManagementDialog(context);
                          },
                          child: Row(children: [
                            Expanded(
                                child: txtStyle(
                                    txt: 'الأشخاص', align: TextAlign.start)),
                            iconStyle(
                                icon: Icons.people_alt_rounded,
                                color: AppColors.warningColor,
                                size: controller.sized * 0.02),
                          ]),
                        ),
                      ),

                      SizedBox(height: sizedHeight * 0.015),

                      // إضافة غرفة
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: sizedWidth * 0.025),
                          onPressed: () {
                            if (!controller.canManageData()) {
                              showNoPermissionDialog(
                                  customMessage: 'ليس لديك صلاحية لإضافة غرفة');
                              return;
                            }
                            addRoom!();
                          },
                          child: Row(mainAxisSize: MainAxisSize.min, children: [
                            txtStyle(txt: 'إضافة غرفة', align: TextAlign.start),
                            Expanded(child: SizedBox(width: double.infinity)),
                            Icon(
                              Icons.add_rounded,
                              size: sized * 0.02,
                              color: AppColors.primaryColor.withOpacity(0.6),
                            ),
                          ]),
                        ),
                      ),

                      SizedBox(height: sizedHeight * 0.015),

                      // الملحقات المتصلة
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: sizedWidth * 0.025),
                          onPressed: nunDevices,
                          child: Row(mainAxisSize: MainAxisSize.min, children: [
                            txtStyle(
                                txt: 'الملحقات المتصلة',
                                align: TextAlign.start),
                            Expanded(child: SizedBox(width: double.infinity)),
                            Icon(
                              Icons.dns_rounded,
                              size: sized * 0.02,
                              color: AppColors.textColor.withOpacity(0.6),
                            ),
                          ]),
                        ),
                      ),

                      SizedBox(height: sizedHeight * 0.03),

                      // عنوان معلومات النظام
                      Container(
                        width: sizedWidth * 0.85,
                        padding:
                            EdgeInsets.symmetric(horizontal: sizedWidth * 0.02),
                        child: Text(
                          'معلومات النظام',
                          textDirection: TextDirection.rtl,
                          style: TextStyle(
                            color: AppColors.textPrimary,
                            fontSize: sized * 0.018,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),

                      SizedBox(height: sizedHeight * 0.015),

                      // المنطقة (سيتم إضافتها لاحقاً)
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02),
                          onPressed: (() {}),
                          child: Row(children: [
                            Expanded(
                                child: txtStyle(
                                    txt: 'المنطقة: قلقيلية (قريباً)',
                                    align: TextAlign.start)),
                            iconStyle(
                                icon: Icons.location_on_rounded,
                                color: AppColors.textHint,
                                size: controller.sized * 0.02),
                          ]),
                        ),
                      ),
                      SizedBox(height: sizedHeight * 0.015),

                      // التوقيت الصيفي والشتوي
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.only(
                              right: controller.sizedWidth * 0.01),
                          onPressed: (() {}),
                          child: Row(children: [
                            Expanded(
                                child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                  iconStyle(
                                      icon: Icons.bedtime,
                                      color: AppColors.warningColor,
                                      size: controller.sized * 0.017),
                                  SizedBox(
                                    width: controller.sizedWidth * 0.02,
                                  ),
                                  txtStyle(
                                      txt: 'التوقيت الصيفي',
                                      align: TextAlign.start),
                                ])),
                            switchStyle(
                                onChanged: (val) {},
                                value: false,
                                size: controller.sized * 0.0007),
                          ]),
                        ),
                      ),
                      SizedBox(height: sizedHeight * 0.015),

                      // إدارة الشبكة والمستخدمين (سيتم إضافتها لاحقاً)
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: sizedWidth * 0.025),
                          onPressed: () {
                            if (!controller.canManageWifi()) {
                              showNoPermissionDialog(
                                  customMessage:
                                      'ليس لديك صلاحية لتعديل إعدادات الشبكة');
                              return;
                            }
                          },
                          child: Row(mainAxisSize: MainAxisSize.min, children: [
                            txtStyle(
                                txt: 'إدارة الشبكة والمستخدمين (قريباً)',
                                align: TextAlign.start),
                            Expanded(child: SizedBox(width: double.infinity)),
                            Icon(
                              Icons.wifi_rounded,
                              size: sized * 0.02,
                              color: AppColors.textHint,
                            ),
                          ]),
                        ),
                      ),
                      SizedBox(height: sizedHeight * 0.015),

                      // الويدجت الخاص بصورة النظام
                      containerPageOption(
                          ver: 0.015,
                          content: Column(
                            children: [
                              Container(
                                height: sizedHeight * 0.2,
                                width: sizedWidth * 0.85,
                                child: image!.contains('com.example.zaen')
                                    ? Image.file(
                                        File(image),
                                        color: AppColors.subtitleColor
                                            .withOpacity(0.2),
                                        colorBlendMode: BlendMode.darken,
                                        fit: BoxFit.cover,
                                        filterQuality: FilterQuality.high,
                                      )
                                    : Image.asset(
                                        image.isNotEmpty
                                            ? image
                                            : "assets/images/default_bg.png",
                                        color: AppColors.subtitleColor
                                            .withOpacity(0.2),
                                        colorBlendMode: BlendMode.darken,
                                        fit: BoxFit.cover,
                                        filterQuality: FilterQuality.high,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          return Container(
                                            color: AppColors.backgroundColor,
                                            child: Icon(
                                              Icons.image_not_supported,
                                              color: AppColors.textHint,
                                              size: 50,
                                            ),
                                          );
                                        },
                                      ),
                              ),
                              SizedBox(height: sizedHeight * 0.01),
                              MaterialButton(
                                padding: EdgeInsets.symmetric(
                                    horizontal: sizedWidth * 0.01),
                                onPressed: asset,
                                child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      txtStyle(
                                          txt: 'اختيار صورة من الموجود',
                                          align: TextAlign.start),
                                      Expanded(
                                          child:
                                              SizedBox(width: double.infinity)),
                                      Icon(
                                        Icons.arrow_forward_ios_rounded,
                                        size: sized * 0.017,
                                        color: AppColors.textColor
                                            .withOpacity(0.6),
                                      ),
                                    ]),
                              ),
                              SizedBox(height: sizedHeight * 0.01),
                              MaterialButton(
                                padding: EdgeInsets.symmetric(
                                    horizontal: sizedWidth * 0.01),
                                onPressed: roll,
                                child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      txtStyle(
                                          txt: 'اختيار صورة من ألبوم الصور',
                                          align: TextAlign.start),
                                      Expanded(
                                          child:
                                              SizedBox(width: double.infinity)),
                                      Icon(
                                        Icons.camera_rounded,
                                        size: sized * 0.017,
                                        color: AppColors.textColor
                                            .withOpacity(0.6),
                                      ),
                                    ]),
                              ),
                              SizedBox(height: sizedHeight * 0.01),
                              MaterialButton(
                                padding: EdgeInsets.symmetric(
                                    horizontal: sizedWidth * 0.01),
                                onPressed: camera,
                                child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      txtStyle(
                                          txt: 'التقاط صورة',
                                          align: TextAlign.start),
                                      Expanded(
                                          child:
                                              SizedBox(width: double.infinity)),
                                      Icon(
                                        Icons.camera_alt_rounded,
                                        size: sized * 0.017,
                                        color: AppColors.textColor
                                            .withOpacity(0.6),
                                      ),
                                    ]),
                              ),
                            ],
                          )),
                      SizedBox(height: sizedHeight * 0.015),

                      // إزالة النظام
                      containerPageOption(
                        content: MaterialButton(
                            padding: EdgeInsets.zero,
                            onPressed: del,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                txtStyle(
                                    txt: 'إزالة النظام',
                                    color: AppColors.errorColor),
                              ],
                            )),
                      ),

                      SizedBox(height: sizedHeight * 0.03),

                      // عنوان المعلومات الشخصية
                      Container(
                        width: sizedWidth * 0.85,
                        padding:
                            EdgeInsets.symmetric(horizontal: sizedWidth * 0.02),
                        child: Text(
                          'المعلومات الشخصية',
                          textDirection: TextDirection.rtl,
                          style: TextStyle(
                            color: AppColors.textPrimary,
                            fontSize: sized * 0.018,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),

                      SizedBox(height: sizedHeight * 0.015),

                      // حقل تعديل اسم الجهاز
                      GetBuilder<HomeController>(
                        builder: (controller) {
                          TextEditingController editPriv =
                              TextEditingController(
                            text: controller.deviceName,
                          );
                          bool privN = false;

                          return StatefulBuilder(
                            builder:
                                (BuildContext context, StateSetter setState) {
                              return containerPageOption(
                                content: Padding(
                                  padding: EdgeInsets.all(sizedWidth * 0.02),
                                  child: Column(
                                    children: [
                                      // حقل تعديل اسم الجهاز
                                      Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child: TextFormField(
                                              controller: editPriv,
                                              maxLength: 15,
                                              showCursor: true,
                                              cursorColor: AppColors.primary,
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                color: AppColors.textPrimary,
                                                fontSize: sized * 0.015,
                                                fontWeight: FontWeight.w500,
                                              ),
                                              onChanged: (i) {
                                                setState(() {
                                                  privN = true;
                                                });
                                              },
                                              decoration: InputDecoration(
                                                hintText: 'الاسم الشخصي',
                                                hintStyle: TextStyle(
                                                  color: AppColors.textHint,
                                                  fontSize: sized * 0.014,
                                                  fontWeight: FontWeight.normal,
                                                ),
                                                filled: true,
                                                fillColor: AppColors.surface,
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                  horizontal: sizedWidth * 0.04,
                                                  vertical: sizedHeight * 0.015,
                                                ),
                                                border: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.border,
                                                    width: 1.0,
                                                  ),
                                                ),
                                                enabledBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.border,
                                                    width: 1.0,
                                                  ),
                                                ),
                                                focusedBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.primary,
                                                    width: 2.0,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          if (privN)
                                            IconButton(
                                              onPressed: () async {
                                                // التحقق من صلاحية تعديل اسم الجهاز
                                                if (!controller
                                                    .canManageData()) {
                                                  showNoPermissionDialog(
                                                      customMessage:
                                                          'ليس لديك صلاحية لتعديل اسم الجهاز');
                                                  return;
                                                }

                                                // حفظ الاسم الجديد
                                                final prefs =
                                                    await SharedPreferences
                                                        .getInstance();
                                                await prefs.setString(
                                                    'device_name',
                                                    editPriv.text);

                                                // تحديث الكنترولر
                                                controller.deviceName =
                                                    editPriv.text;
                                                controller.update();

                                                // إخفاء زر التأكيد
                                                setState(() {
                                                  privN = false;
                                                });

                                                // إظهار رسالة نجاح
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                    content: Text(
                                                        'تم حفظ الاسم بنجاح'),
                                                    backgroundColor:
                                                        AppColors.primaryColor,
                                                    duration:
                                                        Duration(seconds: 2),
                                                  ),
                                                );
                                              },
                                              icon: Icon(
                                                Icons.done,
                                                color: AppColors.primaryColor,
                                              ),
                                            ),
                                        ],
                                      ),
                                      SizedBox(height: sizedHeight * 0.015),

                                      // نوع الجهاز
                                      Row(
                                        children: [
                                          txtStyle(
                                              txt: 'نوع الجهاز :       ',
                                              color: AppColors.textColor2),
                                          txtStyle(
                                              txt: controller.deviceModel,
                                              color: AppColors.textColor2,
                                              align: TextAlign.right),
                                        ],
                                      ),
                                      SizedBox(
                                        height: controller.sizedHight * 0.02,
                                      ),
                                      Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          txtStyle(
                                              txt: 'رمز الجهاز :       ',
                                              color: AppColors.textColor2),
                                          txtStyle(
                                              align: TextAlign.right,
                                              color: AppColors.textColor2,
                                              txt: controller.uuid
                                                  .toString()
                                                  .replaceAll('-', '-\n'),
                                              maxLines: 5),
                                        ],
                                      ),
                                      SizedBox(height: sizedHeight * 0.015),

                                      // رمز الجهاز
                                    ],
                                  ),
                                ),
                              );
                            },
                          );
                        },
                      ),
                      SizedBox(height: sizedHeight * 0.015),

                      // الوضع الداكن/الفاتح مع تحسين منطق الحفظ
                      containerPageOption(
                        content: Container(
                          height: controller.sizedHight * 0.06,
                          padding: EdgeInsets.only(
                              right: controller.sizedWidth * 0.01),
                          child: Row(children: [
                            Expanded(
                                child: Row(children: [
                              Obx(() {
                                final settingsController =
                                    Get.find<SettingsController>();
                                return iconStyle(
                                    icon: settingsController.isDarkMode.value
                                        ? Icons.dark_mode
                                        : Icons.light_mode,
                                    color: AppColors.warningColor,
                                    size: controller.sized * 0.017);
                              }),
                              SizedBox(
                                width: controller.sizedWidth * 0.02,
                              ),
                              Obx(() {
                                final settingsController =
                                    Get.find<SettingsController>();
                                return txtStyle(
                                    txt: settingsController.isDarkMode.value
                                        ? 'الوضع الداكن'
                                        : 'الوضع الفاتح',
                                    align: TextAlign.start);
                              }),
                            ])),
                            Obx(() {
                              final settingsController =
                                  Get.find<SettingsController>();
                              return themeSwitchStyle(
                                  onChanged: (val) {
                                    settingsController.toggleTheme();
                                  },
                                  value: settingsController.isDarkMode.value,
                                  size: controller.sized * 0.0007);
                            }),
                          ]),
                        ),
                      ),

                      SizedBox(height: sizedHeight * 0.06),
                    ],
                  ),
                ),
              ),
            )
          ]),
    ),
  );
}
