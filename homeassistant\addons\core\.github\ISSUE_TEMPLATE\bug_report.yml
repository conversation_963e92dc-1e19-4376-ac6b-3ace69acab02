---
# yamllint disable rule:line-length
name: Bug Report Form
description: Report an issue related to one of the official add-ons.
body:
  - type: markdown
    attributes:
      value: |
        This issue form is for reporting bugs with **supported** setups only!

        If you have a feature or enhancement request, please use our [Feature Requests][fr].

        [fr]: https://github.com/orgs/home-assistant/discussions

  - type: textarea
    validations:
      required: true
    attributes:
      label: Describe the issue you are experiencing
      description: Provide a clear and concise description of what the bug is.

  - type: markdown
    attributes:
      value: |
        ## Environment

  - type: dropdown
    validations:
      required: true
    attributes:
      label: What type of installation are you running?
      description: >
        If you don't know, can be found in [Settings -> System -> Repairs -> System Information](https://my.home-assistant.io/redirect/system_health/).
        It is listed as the `Installation Type` value.
      options:
        - Home Assistant OS
        - Home Assistant Supervised

  - type: dropdown
    validations:
      required: true
    attributes:
      label: Which operating system are you running on?
      options:
        - Home Assistant Operating System
        - Debian
        - Other (e.g., Raspbian/Raspberry Pi OS/Fedora)

  - type: dropdown
    validations:
      required: true
    attributes:
      label: Which add-on are you reporting an issue with?
      options:
        - CEC Scanner
        - File editor
        - deCONZ
        - DHCP Server
        - Dnsmasq
        - Duck DNS
        - Git pull
        - Google Assistant SDK
        - Let's Encrypt
        - MariaDB
        - Matter Server
        - Mosquitto broker
        - NGINX Home Assistant SSL proxy
        - OpenThread Border Router
        - Piper
        - RPC Shutdown
        - Samba share
        - Silicon Labs Flasher
        - Silicon Labs Multiprotocol
        - Terminal & SSH
        - TellStick
        - VLC
        - Whisper
        - Z-Wave JS

  - type: input
    validations:
      required: true
    attributes:
      label: What is the version of the add-on?
      description: Can be found in [Settings -> Add-ons](https://my.home-assistant.io/redirect/supervisor/) -> the add-on

  - type: markdown
    attributes:
      value: |
        # Details

  - type: textarea
    validations:
      required: true
    attributes:
      label: Steps to reproduce the issue
      description: |
        Please tell us exactly how to reproduce your issue.
        Provide clear and concise step by step instructions and add code snippets if needed.
      value: |
        1.
        2.
        3.
        ...

  - type: textarea
    validations:
      required: true
    attributes:
      label: System Health information
      description: >
        System Health information can be found in the top right menu in [Settings -> System -> Repairs](https://my.home-assistant.io/redirect/repairs/).
        Click the copy button at the bottom of the pop-up and paste it here.

        [![Open your Home Assistant instance and show health information about your system.](https://my.home-assistant.io/badges/system_health.svg)](https://my.home-assistant.io/redirect/system_health/)

  - type: textarea
    attributes:
      label: Anything in the Supervisor logs that might be useful for us?
      description: >
        Supervisor Logs can be found in [Settings -> System -> Logs](https://my.home-assistant.io/redirect/logs/)
        then choose `Supervisor` in the top right.

        [![Open your Home Assistant instance and show your Supervisor system logs.](https://my.home-assistant.io/badges/supervisor_logs.svg)](https://my.home-assistant.io/redirect/supervisor_logs/)
      render: txt

  - type: textarea
    attributes:
      label: Anything in the add-on logs that might be useful for us?
      description: >
        Addon Logs can be found in [Settings -> System -> Logs](https://my.home-assistant.io/redirect/logs/)
        then choose the add-on in the top right.

        [![Open your Home Assistant instance and show your Home Assistant logs.](https://my.home-assistant.io/badges/logs.svg)](https://my.home-assistant.io/redirect/logs/)
      render: txt

  - type: textarea
    attributes:
      label: Additional information
      description: >
        If you have any additional information for us, use the field below.
        Please note, you can attach screenshots or screen recordings here, by
        dragging and dropping files in the field below.
