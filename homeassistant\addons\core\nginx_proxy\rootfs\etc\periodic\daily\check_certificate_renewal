#!/command/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Check certificate renewal
# ==============================================================================

CERTFILE=$(bashio::config 'certfile')

if [ ! -f /tmp/certificate_timestamp ] || [[ $(stat "/ssl/${CERTFILE}" -c %y) != $(cat /tmp/certificate_timestamp) ]]; then
  bashio::log.info "Reloading nginx to reload new certificate file"
  stat "/ssl/${CERTFILE}" -c %y > /tmp/certificate_timestamp
  nginx -c /etc/nginx.conf -s reload
fi
