# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 13ms
  [gap of 13ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 40ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 24ms
  [gap of 23ms]
generate_cxx_metadata completed in 54ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 13ms
  [gap of 29ms]
generate_cxx_metadata completed in 45ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 17ms
  [gap of 20ms]
generate_cxx_metadata completed in 42ms

