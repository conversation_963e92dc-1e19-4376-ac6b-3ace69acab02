# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 66ms
    create-variant-model 26ms
    create-ARMEABI_V7A-model 93ms
    create-ARM64_V8A-model 18ms
    create-X86-model 13ms
    create-X86_64-model 16ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 14ms
    create-X86-model 11ms
    create-X86_64-model 11ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 387ms
  [gap of 46ms]
create_cxx_tasks completed in 452ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 61ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 104ms
    create-ARM64_V8A-model 17ms
    create-X86-model 19ms
    create-X86_64-model 16ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 16ms
    create-X86-model 16ms
    create-X86_64-model 15ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 16ms
    create-X86-model 17ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 436ms
  [gap of 54ms]
create_cxx_tasks completed in 508ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 43ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 72ms
    create-ARM64_V8A-model 15ms
    create-X86-model 14ms
    create-X86_64-model 12ms
    [gap of 14ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 17ms
    create-X86-model 17ms
    create-X86_64-model 16ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 341ms
  [gap of 40ms]
create_cxx_tasks completed in 396ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 55ms
    [gap of 11ms]
    create-variant-model 27ms
    create-ARMEABI_V7A-model 76ms
    create-ARM64_V8A-model 17ms
    create-X86-model 11ms
    create-X86_64-model 16ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 11ms
    create-X86-model 16ms
    create-X86_64-model 10ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 355ms
  [gap of 42ms]
create_cxx_tasks completed in 416ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 37ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 53ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 11ms
    [gap of 11ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 13ms
    create-X86-model 10ms
    [gap of 40ms]
    create-X86-model 10ms
  create-initial-cxx-model completed in 257ms
  [gap of 27ms]
create_cxx_tasks completed in 296ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 39ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 48ms
    [gap of 91ms]
  create-initial-cxx-model completed in 201ms
  [gap of 27ms]
create_cxx_tasks completed in 240ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 76ms
    create-variant-model 21ms
    create-ARMEABI_V7A-model 80ms
    create-ARM64_V8A-model 18ms
    create-X86-model 20ms
    create-X86_64-model 21ms
    create-module-model 13ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 12ms
    create-X86-model 22ms
    create-X86_64-model 20ms
    create-module-model 14ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 16ms
    create-X86-model 14ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 437ms
  [gap of 44ms]
create_cxx_tasks completed in 500ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 40ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 72ms
    create-ARM64_V8A-model 16ms
    create-X86-model 17ms
    [gap of 27ms]
    create-ARM64_V8A-model 11ms
    create-X86-model 10ms
    [gap of 20ms]
    create-ARMEABI_V7A-model 11ms
    [gap of 23ms]
  create-initial-cxx-model completed in 277ms
  [gap of 25ms]
create_cxx_tasks completed in 313ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 42ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 71ms
    create-ARM64_V8A-model 16ms
    create-X86-model 14ms
    create-X86_64-model 13ms
    [gap of 14ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 15ms
    create-X86-model 13ms
    create-X86_64-model 15ms
    create-module-model 10ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 317ms
  [gap of 59ms]
create_cxx_tasks completed in 390ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 37ms
    create-variant-model 22ms
    create-ARMEABI_V7A-model 45ms
    [gap of 40ms]
    create-ARM64_V8A-model 14ms
    [gap of 66ms]
  create-initial-cxx-model completed in 231ms
  [gap of 27ms]
create_cxx_tasks completed in 266ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 35ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 36ms
    [gap of 91ms]
  create-initial-cxx-model completed in 179ms
  [gap of 18ms]
create_cxx_tasks completed in 203ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 48ms
    create-variant-model 26ms
    create-ARMEABI_V7A-model 94ms
    create-ARM64_V8A-model 16ms
    create-X86-model 17ms
    create-X86_64-model 14ms
    [gap of 14ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 14ms
    create-X86-model 17ms
    create-X86_64-model 28ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 19ms
    create-X86-model 23ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 410ms
  [gap of 47ms]
create_cxx_tasks completed in 471ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 44ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 74ms
    create-ARM64_V8A-model 15ms
    create-X86-model 16ms
    create-X86_64-model 17ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 15ms
    create-X86-model 15ms
    create-X86_64-model 15ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 14ms
    create-X86-model 15ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 359ms
  [gap of 43ms]
create_cxx_tasks completed in 414ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 26ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 60ms
    [gap of 46ms]
    create-X86-model 20ms
    [gap of 48ms]
  create-initial-cxx-model completed in 222ms
  [gap of 21ms]
create_cxx_tasks completed in 251ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 54ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 82ms
    create-ARM64_V8A-model 13ms
    create-X86-model 12ms
    create-X86_64-model 16ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 13ms
    create-X86-model 15ms
    create-X86_64-model 12ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 10ms
  create-initial-cxx-model completed in 330ms
  [gap of 49ms]
create_cxx_tasks completed in 393ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 50ms
    create-variant-model 35ms
    create-ARMEABI_V7A-model 63ms
    create-ARM64_V8A-model 17ms
    create-X86-model 11ms
    create-X86_64-model 22ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 25ms
    create-X86-model 18ms
    create-X86_64-model 18ms
    create-module-model 17ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 16ms
    create-X86-model 13ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 386ms
  [gap of 42ms]
create_cxx_tasks completed in 445ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 27ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 40ms
    [gap of 70ms]
    create-ARMEABI_V7A-model 10ms
    [gap of 23ms]
  create-initial-cxx-model completed in 188ms
  [gap of 22ms]
create_cxx_tasks completed in 218ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 55ms
    [gap of 10ms]
    create-variant-model 25ms
    create-ARMEABI_V7A-model 118ms
    create-ARM64_V8A-model 21ms
    create-X86-model 33ms
    create-X86_64-model 38ms
    create-module-model 16ms
    create-variant-model 32ms
    create-ARMEABI_V7A-model 31ms
    create-ARM64_V8A-model 19ms
    create-X86-model 18ms
    create-X86_64-model 17ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 12ms
    create-X86-model 19ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 528ms
  [gap of 45ms]
create_cxx_tasks completed in 594ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 56ms
    [gap of 10ms]
    create-variant-model 27ms
    create-ARMEABI_V7A-model 79ms
    create-ARM64_V8A-model 17ms
    create-X86-model 21ms
    create-X86_64-model 19ms
    create-module-model 12ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 15ms
    create-X86-model 16ms
    create-X86_64-model 17ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 15ms
    create-X86-model 16ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 405ms
  [gap of 49ms]
create_cxx_tasks completed in 468ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 39ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 57ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    [gap of 22ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    create-X86_64-model 10ms
    [gap of 14ms]
    create-ARMEABI_V7A-model 28ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 277ms
  [gap of 32ms]
create_cxx_tasks completed in 319ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 59ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 76ms
    create-ARM64_V8A-model 17ms
    create-X86-model 18ms
    create-X86_64-model 12ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 15ms
    create-X86-model 14ms
    create-X86_64-model 15ms
    [gap of 18ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 11ms
    create-X86-model 13ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 365ms
  [gap of 46ms]
create_cxx_tasks completed in 426ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 37ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 53ms
    [gap of 16ms]
    create-X86_64-model 10ms
    [gap of 18ms]
    create-ARM64_V8A-model 10ms
    [gap of 59ms]
  create-initial-cxx-model completed in 222ms
  [gap of 31ms]
create_cxx_tasks completed in 263ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 96ms
    [gap of 11ms]
    create-variant-model 29ms
    create-ARMEABI_V7A-model 120ms
    create-ARM64_V8A-model 24ms
    create-X86-model 19ms
    create-X86_64-model 16ms
    create-module-model 12ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 30ms
    create-X86-model 30ms
    create-X86_64-model 23ms
    create-module-model 23ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 14ms
    create-X86-model 11ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 537ms
  [gap of 49ms]
create_cxx_tasks completed in 638ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 58ms
    create-variant-model 28ms
    create-ARMEABI_V7A-model 98ms
    create-ARM64_V8A-model 22ms
    create-X86-model 17ms
    create-X86_64-model 17ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 15ms
    create-X86-model 14ms
    create-X86_64-model 15ms
    create-module-model 10ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 21ms
    create-ARM64_V8A-model 12ms
    create-X86-model 13ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 408ms
  [gap of 43ms]
create_cxx_tasks completed in 469ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 36ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 35ms
    [gap of 52ms]
    create-X86_64-model 10ms
    [gap of 43ms]
  create-initial-cxx-model completed in 198ms
  [gap of 22ms]
create_cxx_tasks completed in 230ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 36ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 45ms
    [gap of 29ms]
    create-variant-model 14ms
    [gap of 60ms]
  create-initial-cxx-model completed in 203ms
  [gap of 26ms]
create_cxx_tasks completed in 238ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 75ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 74ms
    create-ARM64_V8A-model 45ms
    create-X86-model 16ms
    create-X86_64-model 15ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 14ms
    create-X86-model 17ms
    create-X86_64-model 16ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    create-X86-model 15ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 421ms
  [gap of 44ms]
create_cxx_tasks completed in 485ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 46ms
    create-variant-model 22ms
    create-ARMEABI_V7A-model 113ms
    create-ARM64_V8A-model 20ms
    create-X86-model 19ms
    create-X86_64-model 16ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 16ms
    create-X86-model 15ms
    create-X86_64-model 18ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 413ms
  [gap of 51ms]
create_cxx_tasks completed in 476ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 59ms
    [gap of 17ms]
    create-variant-model 21ms
    create-ARMEABI_V7A-model 75ms
    create-ARM64_V8A-model 22ms
    create-X86-model 13ms
    create-X86_64-model 12ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 47ms
    create-X86-model 13ms
    create-X86_64-model 16ms
    create-module-model 17ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 13ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 400ms
  [gap of 34ms]
create_cxx_tasks completed in 444ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 35ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 53ms
    create-ARM64_V8A-model 14ms
    create-X86-model 11ms
    create-module-model
      create-ndk-meta-abi-list 14ms
    create-module-model completed in 21ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 13ms
    create-X86_64-model 12ms
    [gap of 39ms]
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 266ms
  [gap of 33ms]
create_cxx_tasks completed in 308ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 70ms
    create-variant-model 21ms
    create-ARMEABI_V7A-model 87ms
    create-ARM64_V8A-model 18ms
    create-X86-model 17ms
    create-X86_64-model 11ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 11ms
    create-X86-model 14ms
    create-X86_64-model 10ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 13ms
    create-X86-model 14ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 373ms
  [gap of 46ms]
create_cxx_tasks completed in 432ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 61ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 90ms
    create-ARM64_V8A-model 21ms
    create-X86-model 18ms
    create-X86_64-model 17ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 14ms
    create-X86-model 15ms
    create-X86_64-model 15ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 391ms
  [gap of 50ms]
create_cxx_tasks completed in 456ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 21ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 33ms
    create-ARM64_V8A-model 10ms
    [gap of 31ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 10ms
    create-X86-model 21ms
    create-X86_64-model 10ms
    [gap of 42ms]
  create-initial-cxx-model completed in 212ms
  [gap of 23ms]
create_cxx_tasks completed in 242ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 37ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 51ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    [gap of 34ms]
    create-X86-model 11ms
    [gap of 56ms]
  create-initial-cxx-model completed in 230ms
  [gap of 32ms]
create_cxx_tasks completed in 271ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 43ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 50ms
    [gap of 17ms]
    create-X86_64-model 11ms
    [gap of 81ms]
  create-initial-cxx-model completed in 223ms
  [gap of 34ms]
create_cxx_tasks completed in 269ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 47ms
    create-variant-model 28ms
    create-ARMEABI_V7A-model 75ms
    create-ARM64_V8A-model 14ms
    create-X86-model 15ms
    create-X86_64-model 16ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 12ms
    create-X86-model 18ms
    create-X86_64-model 14ms
    [gap of 19ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 361ms
  [gap of 56ms]
create_cxx_tasks completed in 430ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 60ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 78ms
    create-ARM64_V8A-model 26ms
    create-X86-model 27ms
    create-X86_64-model 20ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 16ms
    create-X86-model 18ms
    create-X86_64-model 16ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 14ms
    create-X86-model 13ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 411ms
  [gap of 49ms]
create_cxx_tasks completed in 476ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 42ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 51ms
    [gap of 93ms]
  create-initial-cxx-model completed in 208ms
  [gap of 29ms]
create_cxx_tasks completed in 248ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 68ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 82ms
    create-ARM64_V8A-model 18ms
    create-X86-model 22ms
    create-X86_64-model 21ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 18ms
    create-X86-model 12ms
    create-X86_64-model 17ms
    create-module-model 14ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 11ms
    create-X86-model 13ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 405ms
  [gap of 63ms]
create_cxx_tasks completed in 488ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 81ms
    [gap of 12ms]
    create-variant-model 32ms
    create-ARMEABI_V7A-model 91ms
    create-ARM64_V8A-model 22ms
    create-X86-model 17ms
    create-X86_64-model 16ms
    create-module-model 14ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 23ms
    create-ARM64_V8A-model 23ms
    create-X86-model 17ms
    create-X86_64-model 20ms
    create-module-model 13ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 17ms
    create-X86-model 15ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 485ms
  [gap of 70ms]
create_cxx_tasks completed in 575ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 40ms
    [gap of 29ms]
    create-variant-model 26ms
    create-ARMEABI_V7A-model 85ms
    create-ARM64_V8A-model 21ms
    create-X86-model 20ms
    create-X86_64-model 19ms
    create-module-model 16ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 21ms
    create-ARM64_V8A-model 45ms
    create-X86-model 29ms
    create-X86_64-model 18ms
    create-module-model 10ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 462ms
  [gap of 44ms]
create_cxx_tasks completed in 520ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 60ms
    [gap of 10ms]
    create-variant-model 23ms
    create-ARMEABI_V7A-model 102ms
    create-ARM64_V8A-model 23ms
    create-X86-model 18ms
    create-X86_64-model 16ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 18ms
    create-X86-model 16ms
    create-X86_64-model 17ms
    create-module-model 12ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 14ms
    create-X86-model 10ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 427ms
  [gap of 43ms]
create_cxx_tasks completed in 491ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 45ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 43ms
    [gap of 100ms]
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 222ms
  [gap of 27ms]
create_cxx_tasks completed in 258ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 55ms
    create-variant-model 40ms
    create-ARMEABI_V7A-model 90ms
    create-ARM64_V8A-model 22ms
    create-X86-model 17ms
    create-X86_64-model 18ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 21ms
    create-X86-model 14ms
    create-X86_64-model 14ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 14ms
    create-X86-model 17ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 432ms
  [gap of 51ms]
create_cxx_tasks completed in 501ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 63ms
    create-variant-model 22ms
    create-ARMEABI_V7A-model 77ms
    create-ARM64_V8A-model 13ms
    create-X86-model 18ms
    create-X86_64-model 13ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 12ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 346ms
  [gap of 46ms]
create_cxx_tasks completed in 407ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 45ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 121ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    [gap of 44ms]
    create-X86_64-model 11ms
    [gap of 33ms]
    create-X86-model 10ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 328ms
  [gap of 46ms]
create_cxx_tasks completed in 386ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 55ms
    create-variant-model 21ms
    create-ARMEABI_V7A-model 79ms
    create-ARM64_V8A-model 11ms
    create-X86-model 14ms
    create-X86_64-model 11ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 26ms
    create-ARM64_V8A-model 11ms
    [gap of 33ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 10ms
    [gap of 16ms]
  create-initial-cxx-model completed in 327ms
  [gap of 33ms]
create_cxx_tasks completed in 374ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 40ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 82ms
    create-ARM64_V8A-model 13ms
    create-X86-model 11ms
    create-X86_64-model 12ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 14ms
    create-X86-model 12ms
    [gap of 25ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
  create-initial-cxx-model completed in 315ms
  [gap of 30ms]
create_cxx_tasks completed in 355ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 63ms
    create-variant-model 22ms
    create-ARMEABI_V7A-model 110ms
    create-ARM64_V8A-model 20ms
    create-X86-model 16ms
    create-X86_64-model 16ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 14ms
    create-X86-model 16ms
    create-X86_64-model 15ms
    create-module-model 12ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 18ms
    create-X86-model 14ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 427ms
  [gap of 67ms]
create_cxx_tasks completed in 509ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 52ms
    [gap of 13ms]
    create-variant-model 25ms
    create-ARMEABI_V7A-model 86ms
    create-ARM64_V8A-model 20ms
    create-X86-model 21ms
    create-X86_64-model 18ms
    create-module-model 12ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 16ms
    create-X86-model 17ms
    create-X86_64-model 16ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 13ms
    create-X86-model 12ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 402ms
  [gap of 40ms]
create_cxx_tasks completed in 456ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 32ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 62ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 12ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 18ms
    create-X86-model 14ms
    [gap of 19ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
  create-initial-cxx-model completed in 278ms
  [gap of 44ms]
create_cxx_tasks completed in 332ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 54ms
    [gap of 10ms]
    create-variant-model 25ms
    create-ARMEABI_V7A-model 78ms
    create-ARM64_V8A-model 18ms
    create-X86-model 21ms
    create-X86_64-model 18ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 14ms
    create-X86-model 13ms
    create-X86_64-model 16ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 12ms
    create-X86-model 14ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 383ms
  [gap of 55ms]
create_cxx_tasks completed in 452ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 28ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 53ms
    [gap of 93ms]
  create-initial-cxx-model completed in 194ms
  [gap of 24ms]
create_cxx_tasks completed in 226ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 51ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 84ms
    create-ARM64_V8A-model 17ms
    create-X86-model 16ms
    create-X86_64-model 15ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 21ms
    create-ARM64_V8A-model 14ms
    create-X86-model 17ms
    create-X86_64-model 17ms
    create-module-model 13ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 396ms
  [gap of 41ms]
create_cxx_tasks completed in 452ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 68ms
    create-variant-model 30ms
    create-ARMEABI_V7A-model 110ms
    create-ARM64_V8A-model 18ms
    create-X86-model 22ms
    create-X86_64-model 27ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 30ms
    create-ARM64_V8A-model 16ms
    create-X86-model 18ms
    create-X86_64-model 19ms
    create-module-model 12ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 21ms
    create-ARM64_V8A-model 17ms
    create-X86-model 20ms
    create-X86_64-model 23ms
  create-initial-cxx-model completed in 515ms
  [gap of 79ms]
create_cxx_tasks completed in 611ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 41ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 55ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 11ms
    [gap of 14ms]
    create-ARMEABI_V7A-model 12ms
    [gap of 17ms]
    create-X86_64-model 10ms
    [gap of 48ms]
  create-initial-cxx-model completed in 253ms
  [gap of 39ms]
create_cxx_tasks completed in 302ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 63ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 105ms
    create-ARM64_V8A-model 38ms
    create-X86-model 19ms
    create-X86_64-model 20ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 18ms
    create-X86-model 17ms
    create-X86_64-model 18ms
    create-module-model 14ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 13ms
    create-X86-model 17ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 461ms
  [gap of 48ms]
create_cxx_tasks completed in 524ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 43ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 85ms
    create-ARM64_V8A-model 13ms
    create-X86-model 15ms
    create-X86_64-model 11ms
    [gap of 13ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 322ms
  [gap of 44ms]
create_cxx_tasks completed in 376ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 38ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 50ms
    [gap of 15ms]
    create-X86_64-model 10ms
    [gap of 74ms]
  create-initial-cxx-model completed in 209ms
  [gap of 28ms]
create_cxx_tasks completed in 246ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 77ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 101ms
    create-ARM64_V8A-model 21ms
    create-X86-model 20ms
    create-X86_64-model 21ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 15ms
    create-X86-model 15ms
    create-X86_64-model 16ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 12ms
    create-X86-model 13ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 433ms
  [gap of 49ms]
create_cxx_tasks completed in 500ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 26ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 45ms
    create-ARM64_V8A-model 10ms
    [gap of 78ms]
    create-X86-model 10ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 200ms
  [gap of 22ms]
create_cxx_tasks completed in 229ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 118ms
    create-variant-model 21ms
    create-ARMEABI_V7A-model 93ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 15ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 16ms
    create-X86-model 16ms
    create-X86_64-model 11ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 13ms
    create-X86-model 14ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 433ms
  [gap of 39ms]
create_cxx_tasks completed in 492ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 71ms
    create-variant-model 21ms
    create-ARMEABI_V7A-model 81ms
    create-ARM64_V8A-model 22ms
    create-X86-model 38ms
    create-X86_64-model 29ms
    create-module-model 15ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 15ms
    create-X86-model 14ms
    create-X86_64-model 12ms
    create-module-model 12ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 26ms
    create-ARM64_V8A-model 28ms
    create-X86-model 18ms
    create-X86_64-model 19ms
  create-initial-cxx-model completed in 483ms
  [gap of 68ms]
create_cxx_tasks completed in 575ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 56ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 59ms
    create-ARM64_V8A-model 10ms
    create-X86_64-model 11ms
    [gap of 38ms]
    create-X86_64-model 10ms
    [gap of 23ms]
    create-ARM64_V8A-model 10ms
    [gap of 18ms]
  create-initial-cxx-model completed in 273ms
  [gap of 27ms]
create_cxx_tasks completed in 317ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 45ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 44ms
    [gap of 99ms]
  create-initial-cxx-model completed in 207ms
  [gap of 28ms]
create_cxx_tasks completed in 346ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 104ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 81ms
    create-ARM64_V8A-model 35ms
    create-X86-model 18ms
    create-X86_64-model 19ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 14ms
    [gap of 56ms]
    create-X86-model 10ms
  create-initial-cxx-model completed in 390ms
  [gap of 31ms]
create_cxx_tasks completed in 434ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 67ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 58ms
    [gap of 79ms]
    create-ARM64_V8A-model 10ms
    [gap of 18ms]
  create-initial-cxx-model completed in 274ms
  [gap of 31ms]
create_cxx_tasks completed in 321ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 81ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 101ms
    create-ARM64_V8A-model 38ms
    create-X86-model 27ms
    create-X86_64-model 17ms
    create-module-model 14ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 19ms
    create-X86-model 19ms
    create-X86_64-model 34ms
    create-module-model 38ms
    create-ARMEABI_V7A-model 31ms
    create-ARM64_V8A-model 44ms
    create-X86-model 11ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 574ms
  [gap of 50ms]
create_cxx_tasks completed in 640ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 46ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 49ms
    create-ARM64_V8A-model 13ms
    [gap of 44ms]
    create-X86-model 11ms
    create-X86_64-model 11ms
    [gap of 11ms]
    create-ARMEABI_V7A-model 10ms
    [gap of 25ms]
  create-initial-cxx-model completed in 242ms
  [gap of 37ms]
create_cxx_tasks completed in 290ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 68ms
    [gap of 10ms]
    create-variant-model 31ms
    create-ARMEABI_V7A-model 109ms
    create-ARM64_V8A-model 24ms
    create-X86-model 23ms
    create-X86_64-model 14ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 14ms
    create-X86-model 12ms
    create-X86_64-model 15ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 10ms
    create-X86-model 13ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 425ms
  [gap of 37ms]
create_cxx_tasks completed in 478ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 39ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 72ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 12ms
    [gap of 24ms]
    create-ARM64_V8A-model 18ms
    create-X86-model 13ms
    [gap of 17ms]
    create-module-model 12ms
    [gap of 38ms]
  create-initial-cxx-model completed in 303ms
  [gap of 33ms]
create_cxx_tasks completed in 346ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 60ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 106ms
    create-ARM64_V8A-model 23ms
    create-X86-model 17ms
    create-X86_64-model 18ms
    create-module-model 12ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 19ms
    create-X86-model 19ms
    create-X86_64-model 12ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 432ms
  [gap of 40ms]
create_cxx_tasks completed in 487ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 37ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 49ms
    [gap of 93ms]
  create-initial-cxx-model completed in 200ms
  [gap of 30ms]
create_cxx_tasks completed in 239ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 59ms
    create-variant-model 28ms
    create-ARMEABI_V7A-model 92ms
    create-ARM64_V8A-model 14ms
    create-X86-model 13ms
    create-X86_64-model 14ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 13ms
    create-X86-model 24ms
    create-X86_64-model 18ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 18ms
    create-X86-model 16ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 406ms
  [gap of 53ms]
create_cxx_tasks completed in 476ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 64ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 86ms
    create-ARM64_V8A-model 16ms
    create-X86-model 19ms
    create-X86_64-model 18ms
    create-module-model 10ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 19ms
    create-X86-model 17ms
    create-X86_64-model 19ms
    create-module-model 14ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 16ms
    create-X86-model 14ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 428ms
  [gap of 49ms]
create_cxx_tasks completed in 493ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 59ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 131ms
    create-ARM64_V8A-model 13ms
    create-X86-model 10ms
    create-X86_64-model 11ms
    [gap of 14ms]
    create-ARMEABI_V7A-model 28ms
    create-ARM64_V8A-model 16ms
    create-X86-model 11ms
    create-X86_64-model 15ms
    create-module-model 10ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 16ms
    create-X86-model 16ms
    create-X86_64-model 17ms
  create-initial-cxx-model completed in 437ms
  [gap of 64ms]
create_cxx_tasks completed in 517ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 39ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 74ms
    create-ARM64_V8A-model 13ms
    create-X86-model 16ms
    create-X86_64-model 16ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 11ms
    create-X86-model 15ms
    create-X86_64-model 16ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 12ms
    create-X86-model 14ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 340ms
  [gap of 45ms]
create_cxx_tasks completed in 396ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 62ms
    create-variant-model 21ms
    create-ARMEABI_V7A-model 80ms
    create-ARM64_V8A-model 14ms
    create-X86-model 19ms
    create-X86_64-model 15ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 15ms
    create-X86-model 19ms
    create-X86_64-model 12ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 18ms
  create-initial-cxx-model completed in 381ms
  [gap of 50ms]
create_cxx_tasks completed in 447ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 36ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 114ms
    [gap of 55ms]
    create-X86-model 10ms
    [gap of 47ms]
  create-initial-cxx-model completed in 283ms
  [gap of 23ms]
create_cxx_tasks completed in 314ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 19ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 33ms
    [gap of 69ms]
    create-ARMEABI_V7A-model 11ms
    [gap of 23ms]
  create-initial-cxx-model completed in 169ms
  [gap of 31ms]
create_cxx_tasks completed in 206ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 35ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 48ms
    create-ARM64_V8A-model 10ms
    [gap of 30ms]
    create-ARM64_V8A-model 10ms
    [gap of 57ms]
  create-initial-cxx-model completed in 210ms
  [gap of 23ms]
create_cxx_tasks completed in 243ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 85ms
    [gap of 11ms]
    create-variant-model 48ms
    create-ARMEABI_V7A-model 142ms
    create-ARM64_V8A-model 18ms
    create-X86-model 21ms
    create-X86_64-model 17ms
    create-module-model 18ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 21ms
    create-X86-model 21ms
    create-X86_64-model 20ms
    create-module-model 18ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 41ms
    create-ARM64_V8A-model 41ms
    create-X86-model 15ms
    create-X86_64-model 25ms
  create-initial-cxx-model completed in 619ms
  [gap of 47ms]
create_cxx_tasks completed in 686ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 52ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 66ms
    create-ARM64_V8A-model 13ms
    [gap of 26ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 15ms
    [gap of 21ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    [gap of 18ms]
  create-initial-cxx-model completed in 283ms
  [gap of 36ms]
create_cxx_tasks completed in 328ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 37ms
    create-variant-model 21ms
    create-ARMEABI_V7A-model 49ms
    create-X86-model 10ms
    [gap of 23ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    create-X86-model 15ms
    [gap of 33ms]
    create-ARM64_V8A-model 10ms
    [gap of 18ms]
  create-initial-cxx-model completed in 252ms
  [gap of 29ms]
create_cxx_tasks completed in 290ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 56ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 64ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 14ms
    [gap of 13ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 15ms
    create-X86-model 14ms
    create-X86_64-model 12ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 310ms
  [gap of 42ms]
create_cxx_tasks completed in 365ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 88ms
    create-variant-model 32ms
    create-ARMEABI_V7A-model 121ms
    create-ARM64_V8A-model 24ms
    create-X86-model 15ms
    create-X86_64-model 25ms
    create-module-model 14ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 55ms
    create-ARM64_V8A-model 19ms
    create-X86-model 33ms
    create-X86_64-model 16ms
    create-module-model 14ms
    create-ARMEABI_V7A-model 30ms
    create-ARM64_V8A-model 26ms
    create-X86-model 14ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 592ms
  [gap of 54ms]
create_cxx_tasks completed in 666ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 58ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 76ms
    create-ARM64_V8A-model 13ms
    create-X86-model 16ms
    create-X86_64-model 13ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 11ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 335ms
  [gap of 38ms]
create_cxx_tasks completed in 389ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 23ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 46ms
    create-X86-model 10ms
    [gap of 88ms]
  create-initial-cxx-model completed in 194ms
  [gap of 26ms]
create_cxx_tasks completed in 227ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 66ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 87ms
    create-ARM64_V8A-model 17ms
    create-X86-model 12ms
    create-X86_64-model 16ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 16ms
    create-X86-model 15ms
    [gap of 10ms]
    create-module-model 12ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 384ms
  [gap of 38ms]
create_cxx_tasks completed in 437ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 43ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 52ms
    [gap of 100ms]
  create-initial-cxx-model completed in 218ms
  [gap of 24ms]
create_cxx_tasks completed in 255ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 58ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 186ms
    create-ARM64_V8A-model 48ms
    create-X86-model 35ms
    create-X86_64-model 39ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 15ms
    create-X86-model 16ms
    create-X86_64-model 12ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 13ms
    create-X86-model 14ms
    [gap of 10ms]
  create-initial-cxx-model completed in 555ms
  [gap of 45ms]
create_cxx_tasks completed in 616ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 51ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 62ms
    [gap of 10ms]
    create-X86-model 11ms
    create-X86_64-model 12ms
    [gap of 13ms]
    create-ARMEABI_V7A-model 15ms
    [gap of 15ms]
    create-X86_64-model 12ms
    [gap of 24ms]
    create-ARM64_V8A-model 10ms
    create-X86-model 18ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 291ms
  [gap of 36ms]
create_cxx_tasks completed in 340ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 30ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 44ms
    [gap of 89ms]
  create-initial-cxx-model completed in 182ms
  [gap of 28ms]
create_cxx_tasks completed in 218ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 65ms
    [gap of 17ms]
    create-variant-model 26ms
    create-ARMEABI_V7A-model 65ms
    create-X86-model 16ms
    create-X86_64-model 17ms
    [gap of 24ms]
    create-ARM64_V8A-model 16ms
    create-X86-model 16ms
    [gap of 25ms]
    create-ARMEABI_V7A-model 16ms
    create-X86-model 14ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 344ms
  [gap of 44ms]
create_cxx_tasks completed in 405ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 49ms
    create-variant-model 21ms
    create-ARMEABI_V7A-model 81ms
    create-ARM64_V8A-model 25ms
    create-X86-model 33ms
    create-X86_64-model 29ms
    create-module-model 14ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 12ms
    create-X86-model 17ms
    create-X86_64-model 17ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 14ms
    create-X86-model 16ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 419ms
  [gap of 42ms]
create_cxx_tasks completed in 476ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 69ms
    create-variant-model 31ms
    create-ARMEABI_V7A-model 101ms
    create-ARM64_V8A-model 17ms
    create-X86-model 20ms
    create-X86_64-model 14ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 17ms
    create-X86-model 18ms
    create-X86_64-model 15ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 15ms
    create-X86-model 17ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 432ms
  [gap of 58ms]
create_cxx_tasks completed in 510ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 27ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 43ms
    create-ARM64_V8A-model 16ms
    create-X86-model 10ms
    create-X86_64-model 14ms
    create-module-model 10ms
    [gap of 22ms]
    create-X86-model 10ms
    [gap of 52ms]
  create-initial-cxx-model completed in 225ms
  [gap of 21ms]
create_cxx_tasks completed in 254ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 48ms
    create-variant-model 31ms
    create-ARMEABI_V7A-model 49ms
    [gap of 24ms]
    create-module-model 17ms
    [gap of 17ms]
    create-ARM64_V8A-model 16ms
    [gap of 24ms]
    create-variant-model 17ms
    create-ARM64_V8A-model 17ms
    [gap of 16ms]
  create-initial-cxx-model completed in 293ms
  [gap of 48ms]
create_cxx_tasks completed in 350ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 66ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 98ms
    create-ARM64_V8A-model 16ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 10ms
    [gap of 10ms]
    create-X86-model 10ms
    [gap of 23ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 354ms
  [gap of 36ms]
create_cxx_tasks completed in 402ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 59ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 83ms
    create-ARM64_V8A-model 13ms
    create-X86-model 17ms
    create-X86_64-model 14ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 16ms
    create-X86-model 16ms
    create-X86_64-model 18ms
    create-module-model 14ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 16ms
    create-X86-model 17ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 391ms
  [gap of 58ms]
create_cxx_tasks completed in 463ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 67ms
    create-variant-model 27ms
    create-ARMEABI_V7A-model 72ms
    create-ARM64_V8A-model 21ms
    create-X86-model 14ms
    create-module-model 15ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 16ms
    create-X86-model 17ms
    create-module-model 17ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 16ms
    create-X86-model 18ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 350ms
  [gap of 78ms]
create_cxx_tasks completed in 429ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 53ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 56ms
    create-ARM64_V8A-model 12ms
    create-X86_64-model 20ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 14ms
    create-X86-model 11ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 304ms
  [gap of 28ms]
create_cxx_tasks completed in 345ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 50ms
    [gap of 17ms]
    create-variant-model 27ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 63ms
    create-ARM64_V8A-model 16ms
    create-X86-model 17ms
    create-X86_64-model 17ms
    create-module-model
      create-ndk-meta-abi-list 15ms
    create-module-model completed in 17ms
    [gap of 16ms]
    create-X86-model 17ms
    create-X86_64-model 17ms
    create-variant-model 19ms
    [gap of 14ms]
    create-X86_64-model 23ms
  create-initial-cxx-model completed in 340ms
  [gap of 36ms]
create_cxx_tasks completed in 392ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 61ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 100ms
    create-ARM64_V8A-model 17ms
    create-X86-model 17ms
    create-X86_64-model 16ms
    create-module-model
      create-ndk-meta-abi-list 16ms
    create-module-model completed in 18ms
    [gap of 16ms]
    create-ARM64_V8A-model 16ms
    create-X86-model 17ms
    create-X86_64-model 16ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 13ms
    create-X86-model 17ms
    create-X86_64-model 17ms
  create-initial-cxx-model completed in 401ms
  [gap of 49ms]
create_cxx_tasks completed in 466ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 63ms
    create-variant-model 27ms
    create-ARMEABI_V7A-model 166ms
    create-ARM64_V8A-model 19ms
    create-X86-model 21ms
    create-X86_64-model 16ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 12ms
    create-X86-model 16ms
    create-X86_64-model 17ms
    create-module-model 24ms
    [gap of 10ms]
    create-ARM64_V8A-model 16ms
    create-X86-model 17ms
    create-X86_64-model 17ms
  create-initial-cxx-model completed in 481ms
  [gap of 62ms]
create_cxx_tasks completed in 560ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 49ms
    create-variant-model 30ms
    create-ARMEABI_V7A-model 69ms
    create-ARM64_V8A-model 12ms
    create-X86-model 13ms
    create-X86_64-model 12ms
    [gap of 13ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    [gap of 55ms]
  create-initial-cxx-model completed in 288ms
  [gap of 29ms]
create_cxx_tasks completed in 335ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 46ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 56ms
    [gap of 17ms]
    create-X86_64-model 27ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 24ms
    create-X86-model 11ms
    [gap of 35ms]
    create-X86-model 10ms
  create-initial-cxx-model completed in 296ms
  [gap of 40ms]
create_cxx_tasks completed in 348ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 17ms]
      create-project-model 17ms
      [gap of 41ms]
    create-module-model completed in 75ms
    create-ARMEABI_V7A-model 67ms
    create-ARM64_V8A-model 16ms
    [gap of 17ms]
    create-variant-model 16ms
    create-ARM64_V8A-model 17ms
    create-X86_64-model 17ms
    create-variant-model 16ms
    create-ARM64_V8A-model 17ms
    [gap of 17ms]
  create-initial-cxx-model completed in 284ms
  [gap of 33ms]
create_cxx_tasks completed in 333ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 72ms
    create-variant-model 27ms
    create-ARMEABI_V7A-model 90ms
    create-ARM64_V8A-model 25ms
    create-X86-model 27ms
    create-X86_64-model 22ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 18ms
    create-X86-model 21ms
    create-X86_64-model 17ms
    create-module-model 12ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 17ms
    create-X86-model 16ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 455ms
  [gap of 52ms]
create_cxx_tasks completed in 528ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 50ms
    [gap of 19ms]
    create-variant-model 15ms
    create-ARMEABI_V7A-model 88ms
    create-ARM64_V8A-model 11ms
    create-X86-model 17ms
    create-X86_64-model 34ms
    create-variant-model 39ms
    create-ARMEABI_V7A-model 26ms
    create-ARM64_V8A-model 19ms
    create-X86-model 32ms
    create-X86_64-model 17ms
    create-module-model 15ms
    create-variant-model 30ms
    create-ARMEABI_V7A-model 28ms
    create-ARM64_V8A-model 20ms
    create-X86-model 18ms
    create-X86_64-model 17ms
  create-initial-cxx-model completed in 502ms
  [gap of 55ms]
create_cxx_tasks completed in 573ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 34ms
    [gap of 16ms]
    create-variant-model 33ms
    create-ARMEABI_V7A-model 66ms
    create-ARM64_V8A-model 17ms
    create-X86-model 16ms
    create-module-model 16ms
    [gap of 17ms]
    create-ARM64_V8A-model 19ms
    create-X86-model 11ms
    create-X86_64-model 11ms
    [gap of 47ms]
  create-initial-cxx-model completed in 306ms
  [gap of 37ms]
create_cxx_tasks completed in 360ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 49ms
    create-variant-model 21ms
    create-ARMEABI_V7A-model 63ms
    [gap of 51ms]
    create-X86-model 10ms
    create-X86_64-model 10ms
    [gap of 39ms]
  create-initial-cxx-model completed in 252ms
  [gap of 28ms]
create_cxx_tasks completed in 290ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 95ms
    create-variant-model 26ms
    create-ARMEABI_V7A-model 86ms
    create-ARM64_V8A-model 20ms
    create-X86-model 18ms
    create-X86_64-model 16ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 30ms
    create-X86-model 19ms
    create-X86_64-model 19ms
    create-module-model 11ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 18ms
    create-X86-model 18ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 476ms
  [gap of 54ms]
create_cxx_tasks completed in 546ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 16ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 10ms
    create-X86_64-model 12ms
    [gap of 28ms]
    create-ARM64_V8A-model 12ms
    create-X86-model 11ms
    [gap of 10ms]
    create-module-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 195ms
create_cxx_tasks completed in 201ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 43ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 61ms
    create-ARM64_V8A-model 13ms
    create-X86-model 11ms
    create-X86_64-model 12ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 11ms
    [gap of 14ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 287ms
  [gap of 34ms]
create_cxx_tasks completed in 339ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 64ms
    create-variant-model 22ms
    create-ARMEABI_V7A-model 98ms
    create-ARM64_V8A-model 16ms
    create-X86-model 17ms
    create-X86_64-model 18ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 18ms
    create-X86-model 19ms
    create-X86_64-model 15ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 14ms
    [gap of 10ms]
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 415ms
  [gap of 69ms]
create_cxx_tasks completed in 501ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 13ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 13ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 12ms
    create-X86-model 14ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 214ms
create_cxx_tasks completed in 219ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 12ms
    create-X86_64-model 12ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    [gap of 25ms]
    create-ARMEABI_V7A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 186ms
create_cxx_tasks completed in 193ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 54ms
    create-variant-model 22ms
    create-ARMEABI_V7A-model 75ms
    create-ARM64_V8A-model 13ms
    create-X86-model 15ms
    create-X86_64-model 12ms
    [gap of 18ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 13ms
    create-X86-model 11ms
    create-X86_64-model 11ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 329ms
  [gap of 53ms]
create_cxx_tasks completed in 396ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 68ms
    create-variant-model 26ms
    create-ARMEABI_V7A-model 93ms
    create-ARM64_V8A-model 21ms
    create-X86-model 15ms
    create-X86_64-model 15ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 13ms
    create-X86-model 14ms
    create-X86_64-model 16ms
    [gap of 19ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 11ms
    create-X86-model 16ms
    create-X86_64-model 18ms
  create-initial-cxx-model completed in 411ms
  [gap of 54ms]
create_cxx_tasks completed in 481ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 60ms
    create-variant-model 26ms
    create-ARMEABI_V7A-model 91ms
    create-ARM64_V8A-model 18ms
    create-X86-model 12ms
    create-X86_64-model 12ms
    [gap of 19ms]
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 13ms
    create-X86-model 18ms
    create-X86_64-model 15ms
    create-module-model 12ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 17ms
    create-X86-model 12ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 400ms
  [gap of 49ms]
create_cxx_tasks completed in 465ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 11ms
    create-module-model 12ms
    [gap of 17ms]
    create-ARM64_V8A-model 10ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    create-X86-model 12ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 202ms
create_cxx_tasks completed in 209ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    create-X86-model 12ms
    create-X86_64-model 14ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 12ms
    create-X86-model 11ms
    create-X86_64-model 10ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 12ms
    create-X86-model 10ms
  create-initial-cxx-model completed in 202ms
create_cxx_tasks completed in 210ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 48ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 73ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 30ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 14ms
    create-X86-model 12ms
    create-X86_64-model 12ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 12ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 332ms
  [gap of 36ms]
create_cxx_tasks completed in 382ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 109ms]
    create-X86-model 10ms
  create-initial-cxx-model completed in 128ms
create_cxx_tasks completed in 136ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-ARMEABI_V7A-model 10ms
    create-X86-model 13ms
    create-X86_64-model 15ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 21ms
    create-X86-model 23ms
    create-X86_64-model 18ms
    create-module-model 20ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 33ms
    [gap of 62ms]
    create-ARM64_V8A-model 15ms
    create-X86-model 20ms
    create-X86_64-model 31ms
  create-initial-cxx-model completed in 362ms
create_cxx_tasks completed in 369ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 52ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 52ms
    [gap of 110ms]
  create-initial-cxx-model completed in 241ms
  [gap of 41ms]
create_cxx_tasks completed in 294ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 27ms]
      create-project-model 17ms
      [gap of 96ms]
    create-module-model completed in 140ms
    create-variant-model 67ms
    create-ARMEABI_V7A-model 190ms
    [gap of 20ms]
    create-ARM64_V8A-model 66ms
    create-X86-model 59ms
    [gap of 17ms]
    create-X86_64-model 81ms
    create-module-model 12ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 30ms
    create-ARM64_V8A-model 19ms
    create-X86-model 35ms
    create-X86_64-model 39ms
    create-module-model 11ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 38ms
    [gap of 12ms]
    create-ARM64_V8A-model 27ms
    create-X86-model 34ms
    [gap of 10ms]
    create-X86_64-model 23ms
  create-initial-cxx-model completed in 980ms
  [gap of 59ms]
create_cxx_tasks completed in 1100ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 61ms
    [gap of 10ms]
    create-variant-model 27ms
    create-ARMEABI_V7A-model 94ms
    create-ARM64_V8A-model 18ms
    create-X86-model 26ms
    create-X86_64-model 18ms
    create-module-model 13ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 21ms
    create-ARM64_V8A-model 20ms
    create-X86-model 17ms
    create-X86_64-model 14ms
    create-module-model 11ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 17ms
    create-X86-model 17ms
    create-X86_64-model 18ms
  create-initial-cxx-model completed in 448ms
  [gap of 63ms]
create_cxx_tasks completed in 531ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 71ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 90ms
    create-ARM64_V8A-model 17ms
    create-X86-model 12ms
    create-X86_64-model 12ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 18ms
    create-X86-model 18ms
    create-X86_64-model 18ms
    [gap of 11ms]
    create-variant-model 10ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 17ms
    create-X86-model 16ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 418ms
  [gap of 45ms]
create_cxx_tasks completed in 479ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 12ms
    create-X86-model 19ms
    create-X86_64-model 15ms
    create-module-model 14ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 14ms
    create-X86-model 12ms
    create-X86_64-model 11ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 219ms
create_cxx_tasks completed in 226ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 12ms
    create-X86-model 13ms
    create-X86_64-model 12ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 13ms
    [gap of 17ms]
    create-X86_64-model 10ms
    [gap of 12ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 183ms
create_cxx_tasks completed in 190ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 26ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 61ms
    create-ARM64_V8A-model 10ms
    create-X86-model 12ms
    create-X86_64-model 10ms
    [gap of 41ms]
    create-X86_64-model 10ms
    [gap of 37ms]
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 238ms
  [gap of 40ms]
create_cxx_tasks completed in 288ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 61ms
    create-variant-model 21ms
    create-ARMEABI_V7A-model 84ms
    create-ARM64_V8A-model 13ms
    create-X86-model 17ms
    create-X86_64-model 13ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 17ms
    create-X86-model 17ms
    create-X86_64-model 17ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 17ms
    create-X86-model 16ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 388ms
  [gap of 55ms]
create_cxx_tasks completed in 463ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    [gap of 20ms]
    create-ARM64_V8A-model 40ms
    create-X86_64-model 11ms
    [gap of 61ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    [gap of 18ms]
  create-initial-cxx-model completed in 193ms
create_cxx_tasks completed in 199ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 10ms
    create-X86_64-model 13ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 12ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 196ms
create_cxx_tasks completed in 205ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 65ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 68ms
    create-ARM64_V8A-model 13ms
    create-X86-model 12ms
    create-X86_64-model 14ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 15ms
    create-X86-model 13ms
    create-X86_64-model 14ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 339ms
  [gap of 45ms]
create_cxx_tasks completed in 400ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 45ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 68ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 12ms
    create-X86-model 11ms
    create-X86_64-model 13ms
    [gap of 14ms]
    create-ARMEABI_V7A-model 11ms
    [gap of 27ms]
  create-initial-cxx-model completed in 296ms
  [gap of 46ms]
create_cxx_tasks completed in 352ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 12ms]
    create-module-model 126ms
    [gap of 25ms]
    create-variant-model 57ms
    create-ARMEABI_V7A-model 160ms
    create-ARM64_V8A-model 23ms
    create-X86-model 21ms
    create-X86_64-model 17ms
    create-module-model 12ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 26ms
    create-ARM64_V8A-model 23ms
    create-X86-model 21ms
    create-X86_64-model 20ms
    create-module-model 16ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 35ms
    [gap of 22ms]
    create-ARM64_V8A-model 54ms
    create-X86-model 16ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 743ms
  [gap of 66ms]
create_cxx_tasks completed in 872ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 64ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 88ms
    create-ARM64_V8A-model 23ms
    create-X86-model 20ms
    create-X86_64-model 19ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 21ms
    create-X86-model 14ms
    create-X86_64-model 13ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 398ms
  [gap of 41ms]
create_cxx_tasks completed in 456ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 31ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 57ms
    create-ARM64_V8A-model 12ms
    [gap of 10ms]
    create-X86_64-model 10ms
    [gap of 23ms]
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 11ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 11ms
    create-X86-model 15ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 261ms
  [gap of 36ms]
create_cxx_tasks completed in 306ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 56ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 153ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 12ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 11ms
    create-X86-model 16ms
    create-X86_64-model 15ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 29ms
    create-X86-model 20ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 453ms
  [gap of 70ms]
create_cxx_tasks completed in 536ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 14ms
    create-X86-model 12ms
    create-X86_64-model 11ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 15ms
    create-X86-model 11ms
    create-X86_64-model 10ms
    [gap of 34ms]
    create-X86-model 11ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 206ms
create_cxx_tasks completed in 213ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 12ms
    create-X86-model 11ms
    create-X86_64-model 10ms
    create-module-model 19ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    [gap of 10ms]
    create-variant-model 11ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 207ms
create_cxx_tasks completed in 214ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 42ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 71ms
    create-ARM64_V8A-model 20ms
    create-X86-model 14ms
    create-X86_64-model 17ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 10ms
    create-X86-model 12ms
    create-X86_64-model 15ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 326ms
  [gap of 38ms]
create_cxx_tasks completed in 375ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 41ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 83ms
    create-X86-model 11ms
    [gap of 64ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 10ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 279ms
  [gap of 53ms]
create_cxx_tasks completed in 344ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 98ms
    create-variant-model 40ms
    create-ARMEABI_V7A-model 139ms
    [gap of 15ms]
    create-ARM64_V8A-model 35ms
    create-X86-model 29ms
    create-X86_64-model 18ms
    create-module-model 12ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 11ms
    create-X86-model 19ms
    create-X86_64-model 29ms
    create-module-model 15ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 17ms
    create-X86-model 19ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 607ms
  [gap of 60ms]
create_cxx_tasks completed in 692ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 14ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 16ms
    create-X86-model 14ms
    create-X86_64-model 18ms
    create-module-model 14ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 21ms
    create-X86-model 15ms
    create-X86_64-model 11ms
    create-module-model 13ms
    create-variant-model 11ms
    create-ARM64_V8A-model 12ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 258ms
create_cxx_tasks completed in 268ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 14ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 14ms
    create-X86-model 15ms
    create-X86_64-model 21ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    create-X86-model 15ms
    create-X86_64-model 15ms
    create-module-model 10ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 14ms
    create-X86-model 18ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 263ms
create_cxx_tasks completed in 272ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 61ms
    create-variant-model 28ms
    create-ARMEABI_V7A-model 104ms
    create-ARM64_V8A-model 19ms
    create-X86-model 18ms
    create-X86_64-model 16ms
    create-module-model 10ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 22ms
    create-ARM64_V8A-model 20ms
    create-X86-model 20ms
    create-X86_64-model 17ms
    create-module-model 13ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 19ms
    create-X86-model 19ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 469ms
  [gap of 60ms]
create_cxx_tasks completed in 549ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 21ms
    create-ARM64_V8A-model 15ms
    create-X86-model 14ms
    create-X86_64-model 15ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 14ms
    create-X86-model 15ms
    create-X86_64-model 14ms
    [gap of 18ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 11ms
    create-X86-model 15ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 248ms
create_cxx_tasks completed in 256ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 28ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 12ms
    [gap of 25ms]
    create-ARM64_V8A-model 11ms
    create-X86-model 10ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 220ms
create_cxx_tasks completed in 229ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 73ms
    [gap of 11ms]
    create-variant-model 35ms
    create-ARMEABI_V7A-model 109ms
    create-ARM64_V8A-model 20ms
    create-X86-model 22ms
    create-X86_64-model 44ms
    create-module-model 44ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 21ms
    create-X86-model 18ms
    create-X86_64-model 21ms
    create-module-model 12ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 14ms
    create-X86-model 16ms
    create-X86_64-model 27ms
  create-initial-cxx-model completed in 570ms
  [gap of 61ms]
create_cxx_tasks completed in 650ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 16ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 16ms
    create-module-model 14ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 12ms
    create-X86-model 15ms
    create-X86_64-model 11ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 241ms
  [gap of 10ms]
create_cxx_tasks completed in 252ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 38ms
    create-ARM64_V8A-model 21ms
    create-X86-model 97ms
    create-X86_64-model 81ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 16ms
    create-X86-model 30ms
    create-X86_64-model 63ms
    create-module-model 21ms
    create-ARMEABI_V7A-model 28ms
    create-ARM64_V8A-model 19ms
    create-X86-model 23ms
    [gap of 12ms]
    create-X86_64-model 109ms
  create-initial-cxx-model completed in 646ms
  [gap of 12ms]
create_cxx_tasks completed in 658ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 163ms
    [gap of 15ms]
    create-variant-model 31ms
    create-ARMEABI_V7A-model 107ms
    create-ARM64_V8A-model 23ms
    create-X86-model 20ms
    create-X86_64-model 14ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 13ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 10ms
    create-X86-model 14ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 509ms
  [gap of 40ms]
create_cxx_tasks completed in 566ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 87ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 79ms
    create-ARM64_V8A-model 12ms
    create-X86-model 14ms
    create-X86_64-model 12ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 11ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 10ms
    [gap of 10ms]
    create-X86-model 11ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 370ms
  [gap of 44ms]
create_cxx_tasks completed in 437ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 143ms]
      create-ndk-meta-abi-list 20ms
      [gap of 82ms]
    create-module-model completed in 245ms
    create-variant-model 54ms
    create-ARMEABI_V7A-model 224ms
    create-ARM64_V8A-model 64ms
    create-X86-model 34ms
    create-X86_64-model 61ms
    create-module-model 37ms
    create-variant-model 26ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 85ms
    create-X86-model 63ms
    create-X86_64-model 30ms
    create-module-model 26ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 39ms
    create-ARM64_V8A-model 45ms
    create-X86-model 79ms
    create-X86_64-model 20ms
  create-initial-cxx-model completed in 1214ms
  [gap of 166ms]
create_cxx_tasks completed in 1402ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 75ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 108ms
    create-ARM64_V8A-model 25ms
    create-X86-model 37ms
    create-X86_64-model 20ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 18ms
    create-X86-model 20ms
    create-X86_64-model 18ms
    create-module-model 13ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 15ms
    create-X86-model 14ms
    create-X86_64-model 22ms
  create-initial-cxx-model completed in 486ms
  [gap of 62ms]
create_cxx_tasks completed in 570ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 14ms
    create-ARMEABI_V7A-model 29ms
    create-ARM64_V8A-model 22ms
    create-X86-model 62ms
    create-X86_64-model 62ms
    create-module-model 12ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 14ms
    create-X86-model 12ms
    create-X86_64-model 14ms
    create-module-model 12ms
    create-variant-model 10ms
    [gap of 10ms]
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
  create-initial-cxx-model completed in 354ms
create_cxx_tasks completed in 362ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 14ms
    create-X86-model 11ms
    create-X86_64-model 12ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 13ms
    create-X86-model 10ms
    create-X86_64-model 11ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 12ms
    create-X86-model 16ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 217ms
create_cxx_tasks completed in 226ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 61ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 116ms
    create-ARM64_V8A-model 29ms
    create-X86-model 28ms
    create-X86_64-model 28ms
    create-module-model 16ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 28ms
    create-ARM64_V8A-model 30ms
    create-X86-model 24ms
    create-X86_64-model 32ms
    create-module-model 12ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 18ms
    create-X86-model 21ms
    create-X86_64-model 17ms
  create-initial-cxx-model completed in 550ms
  [gap of 59ms]
create_cxx_tasks completed in 631ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 129ms
    create-variant-model 39ms
    create-ARMEABI_V7A-model 174ms
    create-ARM64_V8A-model 21ms
    create-X86-model 22ms
    create-X86_64-model 22ms
    create-module-model 13ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 26ms
    create-ARM64_V8A-model 17ms
    create-X86-model 20ms
    create-X86_64-model 37ms
    create-module-model 30ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 26ms
    create-ARM64_V8A-model 22ms
    create-X86-model 18ms
    create-X86_64-model 20ms
  create-initial-cxx-model completed in 686ms
  [gap of 50ms]
create_cxx_tasks completed in 752ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 12ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 11ms
    [gap of 39ms]
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 182ms
create_cxx_tasks completed in 190ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 73ms
    [gap of 12ms]
    create-variant-model 33ms
    create-ARMEABI_V7A-model 101ms
    create-ARM64_V8A-model 20ms
    create-X86-model 18ms
    create-X86_64-model 21ms
    create-module-model 15ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 13ms
    create-X86-model 11ms
    create-X86_64-model 12ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 12ms
    create-X86-model 13ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 435ms
  [gap of 42ms]
create_cxx_tasks completed in 496ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 102ms
    [gap of 18ms]
    create-variant-model 59ms
    create-ARMEABI_V7A-model 117ms
    create-ARM64_V8A-model 19ms
    create-X86-model 23ms
    create-X86_64-model 17ms
    create-module-model 13ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 21ms
    create-X86-model 14ms
    create-X86_64-model 19ms
    create-module-model 14ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 13ms
    create-X86-model 17ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 546ms
  [gap of 58ms]
create_cxx_tasks completed in 626ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 67ms
    create-variant-model 22ms
    create-ARMEABI_V7A-model 82ms
    create-ARM64_V8A-model 20ms
    create-X86-model 49ms
    create-X86_64-model 22ms
    create-module-model 13ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 20ms
    create-X86-model 18ms
    create-X86_64-model 17ms
    create-module-model 10ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 14ms
    create-X86-model 12ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 469ms
  [gap of 72ms]
create_cxx_tasks completed in 558ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 100ms
    [gap of 11ms]
    create-variant-model 38ms
    create-ARMEABI_V7A-model 129ms
    create-ARM64_V8A-model 40ms
    create-X86-model 27ms
    create-X86_64-model 30ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 23ms
    create-ARM64_V8A-model 18ms
    create-X86-model 25ms
    create-X86_64-model 25ms
    create-module-model 24ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 20ms
    create-X86-model 20ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 618ms
  [gap of 64ms]
create_cxx_tasks completed in 711ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    create-X86_64-model 14ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 13ms
    create-X86-model 14ms
    create-X86_64-model 13ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 12ms
    create-X86-model 13ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 213ms
create_cxx_tasks completed in 221ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 12ms
    create-X86-model 16ms
    create-X86_64-model 20ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 28ms
    create-X86-model 16ms
    create-X86_64-model 16ms
    create-module-model 21ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 27ms
    create-ARM64_V8A-model 32ms
    create-X86-model 37ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 326ms
create_cxx_tasks completed in 335ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 68ms
    create-variant-model 22ms
    create-ARMEABI_V7A-model 107ms
    create-ARM64_V8A-model 16ms
    create-X86-model 19ms
    create-X86_64-model 17ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 16ms
    create-X86-model 19ms
    create-X86_64-model 15ms
    create-module-model
      create-ndk-meta-abi-list 12ms
    create-module-model completed in 23ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 14ms
    create-X86-model 15ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 441ms
  [gap of 40ms]
create_cxx_tasks completed in 496ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 65ms
    create-variant-model 28ms
    create-ARMEABI_V7A-model 106ms
    create-ARM64_V8A-model 23ms
    create-X86-model 25ms
    create-X86_64-model 28ms
    create-module-model 10ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 18ms
    create-X86-model 15ms
    create-X86_64-model 11ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 13ms
    [gap of 10ms]
    create-X86-model 13ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 444ms
  [gap of 47ms]
create_cxx_tasks completed in 506ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 42ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 46ms
    [gap of 91ms]
  create-initial-cxx-model completed in 202ms
  [gap of 22ms]
create_cxx_tasks completed in 247ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 53ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 85ms
    create-ARM64_V8A-model 16ms
    create-X86-model 20ms
    create-X86_64-model 20ms
    [gap of 18ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 14ms
    create-X86-model 17ms
    create-X86_64-model 16ms
    create-module-model 13ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 14ms
    create-X86-model 15ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 397ms
  [gap of 58ms]
create_cxx_tasks completed in 471ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 44ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 56ms
    [gap of 56ms]
    create-X86-model 10ms
    [gap of 52ms]
  create-initial-cxx-model completed in 238ms
  [gap of 32ms]
create_cxx_tasks completed in 280ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 47ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 57ms
    create-ARM64_V8A-model 10ms
    create-X86-model 13ms
    create-X86_64-model 11ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 17ms
    create-X86-model 10ms
    [gap of 22ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 10ms
    create-X86-model 13ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 301ms
  [gap of 38ms]
create_cxx_tasks completed in 351ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 51ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 63ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 10ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    create-X86_64-model 12ms
    [gap of 21ms]
    create-ARM64_V8A-model 11ms
    [gap of 15ms]
  create-initial-cxx-model completed in 282ms
  [gap of 31ms]
create_cxx_tasks completed in 327ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 70ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 97ms
    create-ARM64_V8A-model 19ms
    create-X86-model 17ms
    create-X86_64-model 20ms
    create-module-model 14ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 14ms
    create-X86-model 18ms
    create-X86_64-model 17ms
    create-module-model 11ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 35ms
    create-X86-model 22ms
    create-X86_64-model 24ms
  create-initial-cxx-model completed in 471ms
  [gap of 67ms]
create_cxx_tasks completed in 558ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 63ms
    create-variant-model 26ms
    create-ARMEABI_V7A-model 90ms
    create-ARM64_V8A-model 22ms
    create-X86-model 21ms
    create-X86_64-model 19ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 16ms
    create-X86_64-model 14ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 14ms
    create-X86-model 11ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 407ms
  [gap of 57ms]
create_cxx_tasks completed in 480ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 67ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 92ms
    create-ARM64_V8A-model 19ms
    create-X86-model 19ms
    create-X86_64-model 18ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 16ms
    create-X86-model 16ms
    create-X86_64-model 13ms
    [gap of 19ms]
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 13ms
    create-X86-model 15ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 415ms
  [gap of 50ms]
create_cxx_tasks completed in 483ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 14ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 77ms
    create-ARM64_V8A-model 20ms
    create-X86-model 16ms
    create-X86_64-model 25ms
    create-module-model 13ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 14ms
    create-X86-model 44ms
    create-X86_64-model 30ms
    create-module-model 34ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 25ms
    create-X86-model 36ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 462ms
create_cxx_tasks completed in 471ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 14ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 12ms
    create-X86-model 22ms
    create-X86_64-model 11ms
    [gap of 18ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 13ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 207ms
create_cxx_tasks completed in 214ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 16ms]
      create-project-model 15ms
      [gap of 78ms]
    create-module-model completed in 109ms
    create-variant-model 40ms
    create-ARMEABI_V7A-model 93ms
    create-ARM64_V8A-model 16ms
    create-X86-model 72ms
    create-X86_64-model 15ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 54ms
    create-X86-model 16ms
    [gap of 16ms]
    create-X86_64-model 15ms
    create-module-model 31ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 15ms
    create-X86-model 16ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 604ms
  [gap of 31ms]
create_cxx_tasks completed in 650ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 63ms
    create-variant-model 35ms
    create-ARMEABI_V7A-model 86ms
    create-ARM64_V8A-model 18ms
    create-X86-model 16ms
    create-X86_64-model 16ms
    [gap of 19ms]
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 15ms
    create-X86-model 16ms
    create-X86_64-model 13ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 13ms
    create-X86-model 12ms
    [gap of 10ms]
  create-initial-cxx-model completed in 397ms
  [gap of 40ms]
create_cxx_tasks completed in 452ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 45ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 67ms
    create-ARM64_V8A-model 14ms
    create-X86-model 15ms
    create-X86_64-model 10ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    [gap of 10ms]
    create-X86_64-model 10ms
    [gap of 40ms]
  create-initial-cxx-model completed in 277ms
  [gap of 28ms]
create_cxx_tasks completed in 315ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 75ms
    create-variant-model 32ms
    create-ARMEABI_V7A-model 109ms
    create-ARM64_V8A-model 22ms
    create-X86-model 24ms
    create-X86_64-model 22ms
    create-module-model 12ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 23ms
    create-X86-model 16ms
    create-X86_64-model 18ms
    create-module-model 14ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 18ms
    create-X86-model 25ms
    create-X86_64-model 20ms
  create-initial-cxx-model completed in 500ms
  [gap of 91ms]
create_cxx_tasks completed in 605ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 14ms
    create-variant-model 12ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 38ms
    create-ARM64_V8A-model 15ms
    create-X86-model 20ms
    create-X86_64-model 19ms
    create-module-model 16ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 21ms
    create-X86-model 18ms
    create-X86_64-model 30ms
    create-module-model 29ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 13ms
    create-X86-model 17ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 362ms
create_cxx_tasks completed in 372ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 13ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 54ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 27ms
    create-ARM64_V8A-model 14ms
    create-X86-model 13ms
    create-X86_64-model 15ms
    create-module-model 10ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 19ms
    create-X86-model 17ms
    create-X86_64-model 21ms
  create-initial-cxx-model completed in 315ms
create_cxx_tasks completed in 324ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 107ms
    [gap of 11ms]
    create-variant-model 36ms
    create-ARMEABI_V7A-model 596ms
    create-ARM64_V8A-model 189ms
    create-X86-model 106ms
    create-X86_64-model 22ms
    create-module-model
      [gap of 59ms]
      create-ndk-meta-abi-list 10ms
    create-module-model completed in 74ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 59ms
    create-ARM64_V8A-model 59ms
    create-X86-model 76ms
    create-X86_64-model 15ms
    create-module-model 26ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 40ms
    create-ARM64_V8A-model 14ms
    create-X86-model 12ms
    create-X86_64-model 35ms
  create-initial-cxx-model completed in 1529ms
  [gap of 56ms]
create_cxx_tasks completed in 1607ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 63ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 107ms
    create-ARM64_V8A-model 44ms
    create-X86-model 50ms
    create-X86_64-model 33ms
    create-module-model 12ms
    [gap of 11ms]
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 17ms
    create-X86-model 12ms
    create-X86_64-model 33ms
    create-module-model 12ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 15ms
    create-X86-model 16ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 545ms
  [gap of 46ms]
create_cxx_tasks completed in 611ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 72ms
    [gap of 10ms]
    create-variant-model 27ms
    create-ARMEABI_V7A-model 101ms
    create-ARM64_V8A-model 23ms
    create-X86-model 24ms
    create-X86_64-model 21ms
    create-module-model 15ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 26ms
    create-X86-model 26ms
    create-X86_64-model 19ms
    create-module-model 19ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 21ms
    create-X86-model 21ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 528ms
  [gap of 44ms]
create_cxx_tasks completed in 595ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 67ms
    create-variant-model 26ms
    create-ARMEABI_V7A-model 93ms
    create-ARM64_V8A-model 20ms
    create-X86-model 38ms
    create-X86_64-model 16ms
    create-module-model 15ms
    create-variant-model 26ms
    create-ARMEABI_V7A-model 17ms
    [gap of 16ms]
    create-ARM64_V8A-model 17ms
    create-X86-model 17ms
    create-X86_64-model 30ms
    create-module-model 17ms
    create-variant-model 33ms
    create-ARMEABI_V7A-model 35ms
    create-ARM64_V8A-model 14ms
    create-X86-model 17ms
    create-X86_64-model 17ms
  create-initial-cxx-model completed in 548ms
  [gap of 66ms]
create_cxx_tasks completed in 628ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 15ms]
    create-module-model 68ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 106ms
    create-ARM64_V8A-model 16ms
    create-X86-model 14ms
    create-X86_64-model 14ms
    create-module-model 10ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 14ms
    create-X86-model 18ms
    create-X86_64-model 15ms
    create-module-model 10ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 16ms
    create-X86-model 21ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 466ms
  [gap of 51ms]
create_cxx_tasks completed in 533ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 65ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 92ms
    create-ARM64_V8A-model 17ms
    create-X86-model 16ms
    create-X86_64-model 16ms
    [gap of 19ms]
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 34ms
    create-X86-model 14ms
    create-X86_64-model 16ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    create-X86-model 12ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 421ms
  [gap of 41ms]
create_cxx_tasks completed in 477ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 50ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 61ms
    [gap of 10ms]
    create-X86-model 10ms
    [gap of 64ms]
    create-ARMEABI_V7A-model 10ms
    [gap of 25ms]
  create-initial-cxx-model completed in 254ms
  [gap of 32ms]
create_cxx_tasks completed in 296ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 56ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 82ms
    create-ARM64_V8A-model 12ms
    create-X86-model 15ms
    create-X86_64-model 16ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 12ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 351ms
  [gap of 39ms]
create_cxx_tasks completed in 402ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 51ms
    create-variant-model 55ms
    create-ARMEABI_V7A-model 143ms
    create-ARM64_V8A-model 16ms
    create-X86-model 12ms
    create-X86_64-model 15ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 34ms
    create-X86-model 15ms
    create-X86_64-model 18ms
    create-module-model 11ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 15ms
    create-X86-model 14ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 492ms
  [gap of 58ms]
create_cxx_tasks completed in 561ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 153ms
    [gap of 14ms]
    create-variant-model 34ms
    create-ARMEABI_V7A-model 104ms
    create-ARM64_V8A-model 19ms
    create-X86-model 19ms
    create-X86_64-model 19ms
    create-module-model 11ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 18ms
    create-X86-model 19ms
    create-X86_64-model 16ms
    create-module-model 11ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 17ms
    create-X86-model 28ms
    create-X86_64-model 23ms
  create-initial-cxx-model completed in 581ms
  [gap of 76ms]
create_cxx_tasks completed in 677ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 61ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 111ms
    create-ARM64_V8A-model 18ms
    create-X86-model 20ms
    create-X86_64-model 19ms
    create-module-model 12ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 28ms
    create-X86-model 17ms
    create-X86_64-model 20ms
    create-module-model 12ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 466ms
  [gap of 49ms]
create_cxx_tasks completed in 530ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 100ms
    [gap of 11ms]
    create-variant-model 35ms
    create-ARMEABI_V7A-model 195ms
    create-ARM64_V8A-model 23ms
    create-X86-model 35ms
    create-X86_64-model 246ms
    create-module-model 21ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 41ms
    create-ARM64_V8A-model 26ms
    create-X86-model 36ms
    create-X86_64-model 29ms
    create-module-model 12ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 21ms
    create-ARM64_V8A-model 30ms
    create-X86-model 30ms
    create-X86_64-model 33ms
  create-initial-cxx-model completed in 977ms
  [gap of 60ms]
create_cxx_tasks completed in 1056ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 83ms
    [gap of 16ms]
    create-variant-model 34ms
    create-ARMEABI_V7A-model 101ms
    create-ARM64_V8A-model 15ms
    create-X86-model 34ms
    create-X86_64-model 16ms
    create-module-model
      create-ndk-meta-abi-list 17ms
    create-module-model completed in 17ms
    create-ARMEABI_V7A-model 29ms
    create-ARM64_V8A-model 16ms
    create-X86-model 21ms
    create-X86_64-model 17ms
    create-module-model 15ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 21ms
    create-ARM64_V8A-model 18ms
    create-X86-model 18ms
    create-X86_64-model 18ms
  create-initial-cxx-model completed in 513ms
  [gap of 58ms]
create_cxx_tasks completed in 636ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 83ms
    [gap of 17ms]
    create-variant-model 49ms
    create-ARMEABI_V7A-model 122ms
    create-ARM64_V8A-model 20ms
    create-X86-model 21ms
    create-X86_64-model 19ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 14ms
    create-X86-model 16ms
    create-X86_64-model 16ms
    create-module-model 10ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 14ms
    create-X86-model 12ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 498ms
  [gap of 40ms]
create_cxx_tasks completed in 555ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 67ms
    create-variant-model 28ms
    create-ARMEABI_V7A-model 96ms
    create-ARM64_V8A-model 23ms
    create-X86-model 21ms
    create-X86_64-model 21ms
    create-module-model 13ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 21ms
    create-X86-model 19ms
    create-X86_64-model 17ms
    create-module-model 12ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 17ms
    create-X86-model 16ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 474ms
  [gap of 51ms]
create_cxx_tasks completed in 539ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 13ms]
      create-project-model 18ms
      [gap of 49ms]
    create-module-model completed in 80ms
    create-variant-model 33ms
    create-ARMEABI_V7A-model 83ms
    create-ARM64_V8A-model 33ms
    create-X86-model 15ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 17ms
    create-X86-model 17ms
    create-X86_64-model 24ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 16ms
    create-X86-model 17ms
  create-initial-cxx-model completed in 396ms
  [gap of 50ms]
create_cxx_tasks completed in 458ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 50ms
    [gap of 15ms]
    create-variant-model 40ms
    create-ARMEABI_V7A-model 77ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 11ms
    [gap of 18ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 12ms
    [gap of 63ms]
  create-initial-cxx-model completed in 334ms
  [gap of 23ms]
create_cxx_tasks completed in 374ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 54ms
    [gap of 11ms]
    create-variant-model 26ms
    create-ARMEABI_V7A-model 83ms
    create-ARM64_V8A-model 20ms
    create-X86-model 19ms
    create-X86_64-model 17ms
    create-module-model 10ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 17ms
    create-X86-model 18ms
    create-X86_64-model 17ms
    create-module-model 12ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 16ms
    create-X86-model 14ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 422ms
  [gap of 46ms]
create_cxx_tasks completed in 487ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 51ms
    [gap of 11ms]
    create-variant-model 28ms
    create-ARMEABI_V7A-model 95ms
    create-ARM64_V8A-model 22ms
    create-X86-model 18ms
    create-X86_64-model 19ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 16ms
    create-X86-model 17ms
    create-X86_64-model 15ms
    create-module-model 11ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 15ms
    create-X86-model 14ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 424ms
  [gap of 57ms]
create_cxx_tasks completed in 493ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 70ms
    [gap of 10ms]
    create-variant-model 29ms
    create-ARMEABI_V7A-model 110ms
    create-ARM64_V8A-model 23ms
    create-X86-model 20ms
    create-X86_64-model 18ms
    [gap of 11ms]
    create-variant-model 10ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 19ms
    create-X86-model 19ms
    create-X86_64-model 16ms
    create-module-model 14ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 480ms
  [gap of 39ms]
create_cxx_tasks completed in 539ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 81ms
    [gap of 17ms]
    create-variant-model 31ms
    create-ARMEABI_V7A-model 101ms
    create-ARM64_V8A-model 12ms
    create-X86-model 24ms
    [gap of 22ms]
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 15ms
    create-X86-model 20ms
    create-X86_64-model 19ms
    [gap of 12ms]
    create-variant-model 16ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 18ms
    create-X86-model 12ms
  create-initial-cxx-model completed in 451ms
  [gap of 46ms]
create_cxx_tasks completed in 511ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 112ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 52ms
    create-ARM64_V8A-model 21ms
    create-X86-model 10ms
    create-X86_64-model 19ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 16ms
    [gap of 10ms]
    create-X86-model 17ms
    create-X86_64-model 10ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 10ms
    [gap of 10ms]
    create-X86-model 18ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 392ms
  [gap of 42ms]
create_cxx_tasks completed in 445ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 101ms
    [gap of 21ms]
    create-variant-model 29ms
    create-ARMEABI_V7A-model 100ms
    create-ARM64_V8A-model 16ms
    create-X86-model 18ms
    create-X86_64-model 16ms
    create-variant-model 22ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 16ms
    create-X86-model 28ms
    create-X86_64-model 21ms
    create-module-model 10ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 17ms
    create-X86-model 18ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 494ms
  [gap of 66ms]
create_cxx_tasks completed in 576ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 67ms
    [gap of 30ms]
    create-variant-model 21ms
    create-ARMEABI_V7A-model 82ms
    create-ARM64_V8A-model 15ms
    create-X86-model 21ms
    create-X86_64-model 17ms
    create-module-model 29ms
    [gap of 11ms]
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 23ms
    create-X86-model 22ms
    create-X86_64-model 19ms
    create-module-model 16ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 24ms
    create-X86-model 18ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 499ms
  [gap of 34ms]
create_cxx_tasks completed in 550ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 50ms
    [gap of 24ms]
    create-variant-model 26ms
    create-ARMEABI_V7A-model 77ms
    create-ARM64_V8A-model 14ms
    create-X86-model 13ms
    create-X86_64-model 13ms
    [gap of 23ms]
    create-ARM64_V8A-model 11ms
    [gap of 43ms]
    create-X86-model 10ms
  create-initial-cxx-model completed in 314ms
  [gap of 24ms]
create_cxx_tasks completed in 355ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 63ms
    create-variant-model 27ms
    create-ARMEABI_V7A-model 78ms
    create-ARM64_V8A-model 18ms
    create-X86-model 19ms
    create-X86_64-model 21ms
    create-module-model 12ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 17ms
    create-X86-model 17ms
    create-X86_64-model 17ms
    create-module-model 11ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 15ms
    create-X86-model 16ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 430ms
  [gap of 46ms]
create_cxx_tasks completed in 489ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 64ms
    create-variant-model 49ms
    create-ARMEABI_V7A-model 97ms
    create-ARM64_V8A-model 12ms
    create-X86_64-model 17ms
    create-module-model 15ms
    create-variant-model 17ms
    create-ARM64_V8A-model 30ms
    create-X86_64-model 17ms
    create-module-model 16ms
    create-ARMEABI_V7A-model 17ms
    create-X86-model 18ms
    create-X86_64-model 21ms
  create-initial-cxx-model completed in 405ms
  [gap of 27ms]
create_cxx_tasks completed in 445ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 17ms]
      create-project-model 12ms
      [gap of 37ms]
    create-module-model completed in 66ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 62ms
    create-ARM64_V8A-model 12ms
    create-X86-model 21ms
    create-X86_64-model 13ms
    [gap of 20ms]
    create-ARM64_V8A-model 22ms
    create-X86-model 11ms
    create-X86_64-model 20ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 23ms
    create-ARM64_V8A-model 11ms
    create-X86-model 10ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 362ms
  [gap of 35ms]
create_cxx_tasks completed in 422ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 54ms
    [gap of 31ms]
    create-variant-model 63ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 156ms
    create-ARM64_V8A-model 27ms
    create-X86-model 16ms
    create-X86_64-model 16ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 15ms
    create-X86-model 22ms
    create-module-model 16ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 15ms
    create-X86-model 28ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 546ms
  [gap of 46ms]
create_cxx_tasks completed in 606ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 95ms
    create-variant-model 36ms
    create-ARMEABI_V7A-model 110ms
    create-ARM64_V8A-model 22ms
    create-X86-model 20ms
    create-X86_64-model 19ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 16ms
    create-X86-model 20ms
    create-X86_64-model 15ms
    create-module-model 11ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 19ms
    create-X86-model 18ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 506ms
  [gap of 45ms]
create_cxx_tasks completed in 582ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 74ms
    create-variant-model 22ms
    create-ARMEABI_V7A-model 82ms
    create-ARM64_V8A-model 17ms
    create-X86-model 18ms
    create-X86_64-model 15ms
    create-module-model 10ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 16ms
    create-X86-model 12ms
    create-X86_64-model 11ms
    create-module-model 10ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 395ms
  [gap of 38ms]
create_cxx_tasks completed in 450ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 75ms
    create-variant-model 28ms
    create-ARMEABI_V7A-model 83ms
    create-ARM64_V8A-model 16ms
    create-X86-model 17ms
    create-X86_64-model 23ms
    [gap of 10ms]
    create-variant-model 17ms
    create-ARM64_V8A-model 34ms
    create-X86-model 19ms
    create-X86_64-model 17ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 18ms
    create-X86-model 16ms
    create-X86_64-model 17ms
  create-initial-cxx-model completed in 430ms
  [gap of 49ms]
create_cxx_tasks completed in 498ms

