//#include <ESP8266WiFi.h>
#include <ESPmDNS.h>
#include <WiFi.h>
#include <PubSubClient.h>
#include <String.h>
#include <DNSServer.h>
#include <WebServer.h>
#include <ArduinoJson.h>
#include <Preferences.h>

String mDnsHost="zain";
const int mqttPort = 1883;
int co = 25;
char* lasttopic = "input/SWITCHv4";
String code = "";
String clientId;
char* response;
String mytopic;
String message;
char* topic1;
char* topic2;
char* topic3;
char* topic4;
String Topic1;
String Topic2;
String Topic3;
String Topic4;
String Response;
String key = "False";
int PIN_RESET_BUTTON= 2;        
int RESET = 0; 
int pin1=13;
int pin2=12;
int pin3=14;
int pin4=27;
//int pin5=33;
//int pin6=32;
//int pin7=15;
//int pin8=4;
int pinstate1;
int pinstate2;
int pinstate3;
int pinstate4;
int pinOld1;
int pinOld2;
int pinOld3;
int pinOld4;
int switchstate1;
int switchstate2;
int switchstate3;
int switchstate4;
int relay1=18;
int relay2=5;
int relay3=22;
int relay4=23;

//int relay5=18;
//int relay6=5;
//int relay7=25;
//int relay8=26;
int led = 32;
WiFiClient espClient;
PubSubClient client(espClient);
WebServer server(5000);
String macAddress;
Preferences preferences;
// دالة للحصول على عنوان MAC
String getMacAddress() {
  uint8_t baseMac[6];
  esp_read_mac(baseMac, ESP_MAC_WIFI_SOFTAP);
  char baseMacChr[18] = {0};
  sprintf(baseMacChr, "%02X:%02X:%02X:%02X:%02X:%02X", baseMac[0], baseMac[1], baseMac[2], baseMac[3], baseMac[4], baseMac[5]);
  return String(baseMacChr);
}
void AP(String addressWiFi){
  IPAddress local_IP(192, 144, 4, 1);
  IPAddress gateway(192, 144, 4, 1);
  IPAddress subnet(255, 255, 255, 0);
  WiFi.softAPConfig(local_IP, gateway, subnet);
  Serial.println("Access Point Started");
  Serial.print("IP Address: ");
  Serial.println(WiFi.softAPIP());
  WiFi.softAP(addressWiFi.c_str(),"");
  server.on("/wifi", HTTP_POST, handlePost);

  // بدء الخادم
  server.begin();
  Serial.println("Server started");
  Serial.println("تشغيل نقطه الوصول مده دقيقة واحده");
  
  }
void handlePost() {
  Serial.println("sssss");
  if (server.hasArg("plain") == false) {
    server.send(400, "text/plain", "Body not received");
    return;
  }

  String body = server.arg("plain");
  DynamicJsonDocument doc(1024);
  deserializeJson(doc, body);

  const char* ssid = doc["ssid"];
  const char* password = doc["password"];
  Serial.println(ssid);
  WiFi.begin(ssid, password);

  int timeout = 10; // 10 ثواني كحد أقصى للاتصال
  while (WiFi.status() != WL_CONNECTED && timeout > 0) {
    delay(1000);
    timeout--;
  }

  if (WiFi.status() == WL_CONNECTED) {
    server.send(200, "text/plain", "Configuration successful");
    preferences.putString("ssid", ssid);
    preferences.putString("password", password);
    ESP.restart();
  } else {
    server.send(500, "text/plain", "Configuration failed");
  }
}


String getValue(String data, char separator, int index)
{
  int found = 0;
  int strIndex[] = {0, -1};
  int maxIndex = data.length() - 1;

  for (int i = 0; i <= maxIndex && found <= index; i++) {
    if (data.charAt(i) == separator || i == maxIndex) {
      found++;
      strIndex[0] = strIndex[1] + 1;
      strIndex[1] = (i == maxIndex) ? i + 1 : i;
    }
  }

  return found > index ? data.substring(strIndex[0], strIndex[1]) : "";
}

void setup_topic() {
  client.publish(lasttopic, code.c_str());
  client.subscribe(code.c_str());
  delay(500);
}
void setup() {

  Serial.begin(115200);
  pinMode(PIN_RESET_BUTTON, INPUT);
  pinMode(led, OUTPUT);
  digitalWrite(led,LOW);
  pinMode(relay1,OUTPUT);
  pinMode(relay2,OUTPUT);
  pinMode(relay3,OUTPUT);
  pinMode(relay4,OUTPUT);
//  pinMode(relay5,OUTPUT);
//  pinMode(relay6,OUTPUT);
//  pinMode(relay7,OUTPUT);
//  pinMode(relay8,OUTPUT);

  pinMode(pin1,INPUT_PULLUP);
  pinMode(pin2,INPUT_PULLUP);
  pinMode(pin3,INPUT_PULLUP);
  pinMode(pin4,INPUT_PULLUP);
//  pinMode(pin5,INPUT_PULLUP);
//  pinMode(pin6,INPUT_PULLUP);
//  pinMode(pin7,INPUT_PULLUP);
//  pinMode(pin8,INPUT_PULLUP);
  pinOld1=digitalRead(pin1);
  pinOld2=digitalRead(pin2);
  pinOld3=digitalRead(pin3);
  pinOld4=digitalRead(pin4);
//  pinOld5=digitalRead(pin5);
//  pinOld6=digitalRead(pin6);
//  pinOld7=digitalRead(pin7);
//  pinOld8=digitalRead(pin8);
  switchstate1=digitalRead(relay1);
  switchstate2=digitalRead(relay2);
  switchstate3=digitalRead(relay3);
  switchstate4=digitalRead(relay4);
//  switchstate5=digitalRead(relay5);
//  switchstate6=digitalRead(relay6);
//  switchstate7=digitalRead(relay7);
//  switchstate8=digitalRead(relay8);
  delay(2000);
  unsigned char mac[6];
  WiFi.macAddress(mac);
  code+=String(mac[0],16)+String(":")+String(mac[1],16)+String(":")+String(mac[2],16)+String(":")+String(mac[3],16)+String(":")+String(mac[4],16)+String(":")+String(mac[5],16);
  String codeAP = code;
  code.replace(":","_a_");
  preferences.begin("wifi-creds", false);
  macAddress = getMacAddress();
  Serial.print("MAC Address: ");
  Serial.println(macAddress);
  String addressWiFi="مفاتيح/" +macAddress;
  
  delay(5000);                                                                
  String savedSSID = preferences.getString("ssid", "");
  String savedPassword = preferences.getString("password", "");
  if(savedSSID == ""){
    AP(addressWiFi);
    }
  else if(WiFi.status() != WL_CONNECTED){
    WiFi.begin(savedSSID.c_str(), savedPassword.c_str());
    delay(5000);
    }                                                                    
  if(WiFi.status() == WL_CONNECTED) {
    Serial.println("connected"); //A-عند الانتهاء من الاتصال يظهر هذة الاسم علي السريال

    if (!MDNS.begin("esp32whatever")) {
      Serial.println("Error setting up MDNS responder!");
    } else {
      Serial.println("Finished intitializing the MDNS client...");
    }
    
    IPAddress serverIp = MDNS.queryHost(mDnsHost);
    while (serverIp.toString() == "0.0.0.0") {
      Serial.println("Trying again to resolve mDNS");
      delay(250);
      resetESP();
      switchs();
      serverIp = MDNS.queryHost(mDnsHost);
      // قراءة اسم الشبكة وكلمة المرور من الذاكرة
      String ssid = preferences.getString("ssid", "defaultSSID");
      String password = preferences.getString("password", "defaultPassword");
      Serial.println(ssid);
      if(WiFi.status() != WL_CONNECTED){
        ESP.restart();
      }
    }
    Serial.print("IP address of server: ");
    Serial.println(serverIp.toString());
    Serial.println("Done finding the mDNS details...");
    clientId="zain/"+String(code);
    client.setServer(serverIp, mqttPort);
    client.setCallback(callback);
    while (!client.connected()) {  
      if (client.connect(clientId.c_str())) {
        Serial.println("connected");
      } else {
        Serial.print("failed mqtt");
        //Serial.print(client.state());
       // delay(2000);
      }
      switchs();
      resetESP();
    }
    //  client.publish("esp8266", "Hello Raspberry Pi");
    //  client.subscribe("esp8266");
  }
 }
void resetESP(){
  RESET = digitalRead(PIN_RESET_BUTTON);
  if( RESET == 1) {                                 
    Serial.println("Erase settings and restart ...");
    const char* ssid = " ";                   // wifi ssid
    const char* password =  " ";
    WiFi.begin(ssid, password);
    preferences.putString("ssid", "");
    preferences.putString("password", "");
    delay(1000);
    //wifiManager.resetSettings();  
    ESP.restart();
  }
}
void switchs(){
  //  Serial.println(digitalRead(pin1));
  //  Serial.println(digitalRead(pin2));
  //  Serial.println(digitalRead(pin3));
  //  Serial.println(digitalRead(pin4));
    String ss="False";
    pinstate1=digitalRead(pin1);
    if (pinstate1!=pinOld1){
      ss="True";
      if(switchstate1==1){
        digitalWrite(relay1,LOW);
      }
      else{
        digitalWrite(relay1,HIGH);
      }
      delay(200);
      switchstate1=digitalRead(relay1);
      pinOld1=digitalRead(pin1);
    }
    delay(50);
    pinstate2=digitalRead(pin2);
    if (pinstate2!=pinOld2){
      ss="True";
      if(switchstate2==1){
        digitalWrite(relay2,LOW);
      }
      else{
        digitalWrite(relay2,HIGH);
      }
      delay(200);
      switchstate2=digitalRead(relay2);
      pinOld2=digitalRead(pin2);
    }
    delay(50);
    pinstate3=digitalRead(pin3);
    if (pinstate3!=pinOld3){
      ss="True";
      if(switchstate3==1){
        digitalWrite(relay3,LOW);
      }
      else{
        digitalWrite(relay3,HIGH);
      }
      delay(200);
      switchstate3=digitalRead(relay3);
      pinOld3=digitalRead(pin3);
    }
    delay(50);
    pinstate4=digitalRead(pin4);
    if (pinstate4!=pinOld4){
      ss="True";
      if(switchstate4==1){
        digitalWrite(relay4,LOW);
      }
      else{
        digitalWrite(relay4,HIGH);
      }
      delay(200);
      switchstate4=digitalRead(relay4);
      pinOld4=digitalRead(pin4);
    }
    delay(50);
    //  pinstate5=digitalRead(pin5);
  //  if (pinstate5!=pinOld5){
  //    if(switchstate5==1){
  //      digitalWrite(relay5,LOW);
  //    }
  //    else{
  //      digitalWrite(relay5,HIGH);
  //    }
  //    switchstate5=digitalRead(relay5);
  //    pinOld5=digitalRead(pin5);
  //  }
  
  //  pinstate6=digitalRead(pin6);
  //  if (pinstate6!=pinOld6){
  //    if(switchstate6==1){
  //      digitalWrite(relay6,LOW);
  //    }
  //    else{
  //      digitalWrite(relay6,HIGH);
  //    }
  //    switchstate6=digitalRead(relay6);
  //    pinOld6=digitalRead(pin6);
  //  }

    //  pinstate7=digitalRead(pin7);
  //  if (pinstate7!=pinOld7){
  //    if(switchstate7==1){
  //      digitalWrite(relay7,LOW);
  //    }
  //    else{
  //      digitalWrite(relay7,HIGH);
  //    }
  //    switchstate7=digitalRead(relay7);
  //    pinOld7=digitalRead(pin7);
  //  }
  
  //  pinstate8=digitalRead(pin8);
  //  if (pinstate8!=pinOld8){
  //    if(switchstate8==1){
  //      digitalWrite(relay8,LOW);
  //    }
  //    else{
  //      digitalWrite(relay8,HIGH);
  //    }
  //    switchstate8=digitalRead(relay8);
  //    pinOld8=digitalRead(pin8);
  //  }
    if(ss=="True" and key=="True"){
      delay(500);
      String s1;
      String s2;
      String s3;
      String s4;
      if(switchstate1==1){
        s1="_RUN";
      }
      else{
        s1="_OFF";
      }
      if(switchstate2==1){
        s2="_RUN";
      }
      else{
        s2="_OFF";
      }
      if(switchstate3==1){
        s3="_RUN";
      }
      else{
        s3="_OFF";
      }
      if(switchstate4==1){
        s4="_RUN";
      }
      else{
        s4="_OFF";
      }
      Serial.println(code+" response "+"v1"+s1+" v2"+s2+" v3"+s3+" v4"+s4);
      client.publish(lasttopic, (code+" response "+"v1"+s1+" v2"+s2+" v3"+s3+" v4"+s4).c_str());
      Serial.println("3333333333");
    }
}

void reconnect() {
  if (!client.connected()) {
    //if (WiFi.status() != WL_CONNECTED) return;
    if (client.connect(clientId.c_str())) {
      Serial.println("connected");
      mytopic="";
      key = "False";
      string2char(mytopic);

      }
    else {
      Serial.print("failed, rc=");
     // Serial.print(client.state());
     // Serial.println(" try again in 5 seconds");
      // Wait 5 seconds before retrying
 //     delay(5000);
    }
  }
}

void callback(char* topic, byte* payload, unsigned int length) {

  Serial.print("Message arrived in topic: ");
  Serial.println(topic);

  Serial.print("Message:");


  if ((String)topic == (String)code) {
    mytopic = "";
    for (int i = 0; i < length; i++) {
      mytopic = mytopic + (char)payload[i];
      //  Serial.println(newtopic);
      Serial.println((char)payload[i]);
    }
    if (mytopic=="remove"){
        Serial.println("Erase settings and restart ...");
        const char* ssid = "";                   // wifi ssid
        const char* password =  "";
        WiFi.begin(ssid, password);
        preferences.putString("ssid", "");
        preferences.putString("password", "");
        delay(1000);
        //wifiManager.resetSettings();  
        ESP.restart();
        delay(5000);
    }
    else if (mytopic=="re"){
        ESP.restart();
        delay(5000);
    }
//    else if (mytopic.indexOf("state") != -1){
  //      mytopic.replace("state RUN","RUN");
    //    mytemp= mytopic;
      //  Serial.println(mytemp);
    //}
    Serial.println(message);
  }
  else if ((String)topic == (String)topic1 or (String)topic == (String)topic2 or (String)topic == (String)topic3 or (String)topic == (String)topic4) {
    message = "";
    for (int i = 0; i < length; i++) {
      message = message + (char)payload[i];
      //  Serial.println(newtopic);
      Serial.println((char)payload[i]);
    }
    
    Response =  getValue(message, ' ', -1);
    message = getValue(message, ' ', 0) + " " + getValue(message, ' ', 1)+ " " + getValue(message, ' ', 2)+ " " + getValue(message, ' ', 3);
    
    response = const_cast<char*>(Response.c_str());
    Serial.println(message);
    if (message.indexOf("v1") != -1 or message.indexOf("x") != -1) {
      if (message.indexOf("v1_RUN") == -1 or message.indexOf("x") != -1){
        digitalWrite(relay1,LOW);
      }
       else{
        digitalWrite(relay1,HIGH);
      }
      delay(200);
      switchstate1=digitalRead(relay1);
    }

    if (message.indexOf("v2") != -1 or message.indexOf("x") != -1) {
      if (message.indexOf("v2_RUN") == -1 or message.indexOf("x") != -1){
        digitalWrite(relay2,LOW);
      }
       else{
        digitalWrite(relay2,HIGH);
      }
      delay(200);
      switchstate2=digitalRead(relay2);
    }

    if (message.indexOf("v3") != -1 or message.indexOf("x") != -1) {
      if (message.indexOf("v3_RUN") == -1 or message.indexOf("x") != -1){
        digitalWrite(relay3,LOW);
      }
       else{
        digitalWrite(relay3,HIGH);
      }
      delay(200);
      switchstate3=digitalRead(relay3);
    }

    if (message.indexOf("v4") != -1 or message.indexOf("x") != -1) {
      if (message.indexOf("v4_RUN") == -1 or message.indexOf("x") != -1){
        digitalWrite(relay4,LOW);
      }
       else{
        digitalWrite(relay4,HIGH);
      }
      delay(200);
      switchstate4=digitalRead(relay4);
    }
    delay(500);
    String s1;
    String s2;
    String s3;
    String s4;
    if(switchstate1==1){
      s1="_RUN";
    }
    else{
      s1="_OFF";
    }
    if(switchstate2==1){
      s2="_RUN";
    }
    else{
      s2="_OFF";
    }
    if(switchstate3==1){
      s3="_RUN";
    }
    else{
      s3="_OFF";
    }
    if(switchstate4==1){
      s4="_RUN";
    }
    else{
      s4="_OFF";
    }
    Serial.println(code+" response "+"v1"+s1+" v2"+s2+" v3"+s3+" v4"+s4);
    client.publish(lasttopic, (code+" response "+"v1"+s1+" v2"+s2+" v3"+s3+" v4"+s4).c_str());
    
    message = "";

  }
  else {
    Serial.println("xxx");
  }

  Serial.println();
  Serial.println("-----------------------");



}
char* string2char(String command) {
  if (command.length() == 0) {
    Serial.println("ccc");
    setup_topic();
  }
  if (command.length() != 0) {
    delay(200);
    Topic1 = "ROOMS/" + command + "/SWITCH%/" + String(code);
    Topic2 = "ROOMS/*/SWITCH%/" + String(code);
    Topic3 = "ROOMS/*/SWITCH%/*";
    Topic4 = "ROOMS/" + command + "/SWITCH%/*";
    //Serial.println((String)p);
    //client.publish("199", "Hello Raspberry Pi");
    topic1 = const_cast<char*>(Topic1.c_str());
    topic2 = const_cast<char*>(Topic2.c_str());
    topic3 = const_cast<char*>(Topic3.c_str());
    topic4 = const_cast<char*>(Topic4.c_str());
    Serial.println(topic1);
    Serial.println(topic2);
    Serial.println(topic3);
    Serial.println(topic4);
    client.subscribe(topic1);
    client.subscribe(topic2);
    client.subscribe(topic3);
    client.subscribe(topic4);
    key = "True";
    digitalWrite(led,HIGH);
  }
}

void loop() {
  String ssid = preferences.getString("ssid", "");
  String password = preferences.getString("password", "");
  Serial.println(ssid);
  if (ssid != "" and WiFi.status() != WL_CONNECTED){
    Serial.println("11111111");
    digitalWrite(led,LOW);
    WiFi.begin(ssid.c_str(), password.c_str());
    delay(500);
    if (WiFi.status() == WL_CONNECTED){
      ESP.restart();
    }
  }
  else if (WiFi.status() == WL_CONNECTED and !client.connected()) {
    digitalWrite(led,LOW);
    reconnect();
  }
  else if (client.connected()){
    client.loop();
    if (key != "True") {
      string2char(mytopic);
    }
    if (co == 30){
      client.publish("connect", code.c_str());
      co=0;  
    }
    else{
      co=co+1;
    }
  }
  else{
    digitalWrite(led,LOW);
  }
  //Serial.println(WiFi.status());
  server.handleClient();
  switchs();
  resetESP();
  delay(200);
//  Serial.println(digitalRead(pin1));
//  Serial.println(digitalRead(pin2));
//  Serial.println(digitalRead(pin3));
//  Serial.println(digitalRead(pin4));
}
