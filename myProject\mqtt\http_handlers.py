from flask import jsonify, request
from datetime import datetime
import sys

sys.path.append('/home/<USER>/myProject/modules')
import nw as nw

class HTTPHandlers:
    def __init__(self):
        pass
    
    def handle_wifi_connection(self):
        """معالجة طلب الاتصال بشبكة WiFi"""
        try:
            data = request.json
            
            if not data:
                return jsonify({
                    'status': 'error',
                    'message': 'لم يتم إرسال بيانات'
                }), 400
            
            ssid = data.get('ssid')
            password = data.get('password')
            
            if not ssid:
                return jsonify({
                    'status': 'error',
                    'message': 'اسم الشبكة مطلوب'
                }), 400
            
            if not password:
                return jsonify({
                    'status': 'error',
                    'message': 'كلمة المرور مطلوبة'
                }), 400
            
            # محاولة الاتصال بالشبكة
            result = nw.connect_wifi(ssid, password, '1')
            
            if result:
                return jsonify({
                    'status': 'success',
                    'message': 'تم الاتصال بالشبكة بنجاح'
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'فشل في الاتصال بالشبكة'
                }), 500
                
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'خطأ في معالجة الطلب: {str(e)}'
            }), 500
    
    def handle_system_status(self):
        """معالجة طلب حالة النظام"""
        try:
            from database_helper import DatabaseHelper
            import static as st
            
            db_helper = DatabaseHelper()
            
            # الحصول على معلومات النظام
            devices = db_helper.get_all_devices()
            phones = db_helper.get_all_phones()
            
            # إحصائيات الأجهزة
            device_stats = {
                'total': len(devices) if devices else 0,
                'connected': 0,
                'disconnected': 0,
                'by_type': {}
            }
            
            if devices:
                for device in devices:
                    device_type = device[1]  # Type column
                    if device_type not in device_stats['by_type']:
                        device_stats['by_type'][device_type] = 0
                    device_stats['by_type'][device_type] += 1
                    
                    # فحص حالة الاتصال
                    _, status = db_helper.check_device_connection_status(device[0])
                    if status == 'متصل':
                        device_stats['connected'] += 1
                    else:
                        device_stats['disconnected'] += 1
            
            # إحصائيات الهواتف
            phone_stats = {
                'total': len(phones) if phones else 0,
                'full_access': 0,
                'limited_access': 0,
                'no_access': 0
            }
            
            if phones:
                for phone in phones:
                    access = phone[3]  # access column
                    if access == 'full':
                        phone_stats['full_access'] += 1
                    elif access == 'never':
                        phone_stats['no_access'] += 1
                    else:
                        phone_stats['limited_access'] += 1
            
            return jsonify({
                'status': 'success',
                'data': {
                    'system_id': st.device_nb,
                    'devices': device_stats,
                    'phones': phone_stats,
                    'timestamp': str(datetime.now())
                }
            })
            
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'خطأ في الحصول على حالة النظام: {str(e)}'
            }), 500
    
    def handle_device_control(self):
        """معالجة طلب التحكم في الأجهزة"""
        try:
            data = request.json
            
            if not data:
                return jsonify({
                    'status': 'error',
                    'message': 'لم يتم إرسال بيانات'
                }), 400
            
            device_id = data.get('device_id')
            command = data.get('command')
            
            if not device_id:
                return jsonify({
                    'status': 'error',
                    'message': 'معرف الجهاز مطلوب'
                }), 400
            
            if not command:
                return jsonify({
                    'status': 'error',
                    'message': 'الأمر مطلوب'
                }), 400
            
            # التحقق من وجود الجهاز
            from database_helper import DatabaseHelper
            db_helper = DatabaseHelper()
            
            device_info = db_helper.get_device_info(device_id)
            if not device_info:
                return jsonify({
                    'status': 'error',
                    'message': 'الجهاز غير موجود'
                }), 404
            
            # إرسال الأمر عبر MQTT
            import paho.mqtt.publish as publish
            import static as st
            
            topic = f'ROOMS/*/{device_info[1]}%/{device_id}'
            message = f'{command} XX:XX:XX:XX:XX:XX'
            
            publish.single(topic, message, hostname=st.ip)
            
            return jsonify({
                'status': 'success',
                'message': 'تم إرسال الأمر بنجاح',
                'device_id': device_id,
                'command': command
            })
            
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'خطأ في التحكم بالجهاز: {str(e)}'
            }), 500
    
    def handle_get_devices(self):
        """معالجة طلب الحصول على قائمة الأجهزة"""
        try:
            from database_helper import DatabaseHelper
            
            db_helper = DatabaseHelper()
            devices = db_helper.get_all_devices()
            
            if not devices:
                return jsonify({
                    'status': 'success',
                    'data': [],
                    'message': 'لا توجد أجهزة'
                })
            
            devices_list = []
            for device in devices:
                device_id, device_type, room, connect_time, state = device
                
                # فحص حالة الاتصال
                _, connection_status = db_helper.check_device_connection_status(device_id)
                
                devices_list.append({
                    'id': device_id,
                    'type': device_type,
                    'room': room,
                    'state': state,
                    'connection_status': connection_status,
                    'last_seen': connect_time
                })
            
            return jsonify({
                'status': 'success',
                'data': devices_list,
                'count': len(devices_list)
            })
            
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'خطأ في الحصول على الأجهزة: {str(e)}'
            }), 500
    
    def handle_get_device_details(self, device_id):
        """معالجة طلب تفاصيل جهاز محدد"""
        try:
            from database_helper import DatabaseHelper
            
            db_helper = DatabaseHelper()
            device_info = db_helper.get_device_info(device_id)
            
            if not device_info:
                return jsonify({
                    'status': 'error',
                    'message': 'الجهاز غير موجود'
                }), 404
            
            device_id, device_type, room, connect_time, state = device_info
            
            # فحص حالة الاتصال
            _, connection_status = db_helper.check_device_connection_status(device_id)
            
            device_details = {
                'id': device_id,
                'type': device_type,
                'room': room,
                'state': state,
                'connection_status': connection_status,
                'last_seen': connect_time
            }
            
            # إضافة تفاصيل خاصة حسب نوع الجهاز
            if device_type == 'TV':
                channels = db_helper.get_tv_channels(device_id)
                device_details['channels'] = channels if channels else []
                
            elif 'SWITCH' in device_type:
                switch_config = db_helper.get_switch_config(device_id)
                device_details['switches'] = switch_config if switch_config else {}
            
            return jsonify({
                'status': 'success',
                'data': device_details
            })
            
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'خطأ في الحصول على تفاصيل الجهاز: {str(e)}'
            }), 500
    
    def handle_health_check(self):
        """معالجة طلب فحص صحة النظام"""
        try:
            import static as st
            from datetime import datetime
            
            return jsonify({
                'status': 'success',
                'data': {
                    'system_id': st.device_nb,
                    'timestamp': str(datetime.now()),
                    'services': {
                        'mqtt': 'running',
                        'database': 'connected',
                        'homebridge': 'active'
                    }
                }
            })
            
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'خطأ في فحص صحة النظام: {str(e)}'
            }), 500
