import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:sleek_circular_slider/sleek_circular_slider.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'routine_variables.dart';

Widget addRoutineTv({
  required Map device,
  required Map roomData,
  setState1,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  print(device);
  var tvPrivName = device['priv'];

  if (controller.addRoutine.containsKey(roomData['id']) &&
      controller.addRoutine[roomData['id']] != true &&
      controller.addRoutine[roomData['id']] != false &&
      controller.addRoutine[roomData['id']].containsKey(device['id'])) {
    if (controller.addRoutine[roomData['id']][device['id']]['state'] != null) {
      deviceState =
          controller.addRoutine[roomData['id']][device['id']]['state'];
    } else {
      deviceState = device['state'];
    }
    if (controller.addRoutine[roomData['id']][device['id']]['ch'] != null) {
      ch = controller.addRoutine[roomData['id']][device['id']]['ch'];
    } else {
      ch = '+1';
    }
    if (controller.addRoutine[roomData['id']][device['id']]['v'] != null) {
      volume = controller.addRoutine[roomData['id']][device['id']]['v'];
    } else {
      volume = '+1';
    }
  } else {
    deviceState = device['state'];
    sil = device['sil'];
    ch = '+1';
    volume = '+1';
  }

  return StatefulBuilder(builder: ((context, setState) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(
              height: controller.sizedHight * 0.02,
            ),
            Row(mainAxisSize: MainAxisSize.min, children: [
              containerIconsOption(
                content: Row(
                  children: [
                    Directionality(
                      textDirection: TextDirection.rtl,
                      child: switchStyle(
                          size: controller.sized * 0.001,
                          value: deviceState,
                          onChanged: (s) {
                            setState(
                              () {
                                deviceState = s;
                              },
                            );

                            if (controller.addRoutine
                                .containsKey(roomData['id'])) {
                              if (controller.addRoutine[roomData['id']] !=
                                      true &&
                                  controller.addRoutine[roomData['id']] !=
                                      false &&
                                  controller.addRoutine[roomData['id']]
                                      .containsKey(device['id'])) {
                                if (controller.addRoutine[roomData['id']]
                                        [device['id']]['state'] !=
                                    null) {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['state'] = s;
                                  if (s == false) {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['ch'] = null;
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['v'] = null;
                                  }
                                }
                              }
                            }
                          }),
                    ),
                    IconButton(
                        onPressed: () {
                          if (controller.addRoutine.containsKey('home')) {
                            controller.addRoutine = {};
                          }
                          if (controller.addRoutine
                              .containsKey(roomData['id'])) {
                            if (controller.addRoutine[roomData['id']] == true ||
                                controller.addRoutine[roomData['id']] ==
                                    false) {
                              controller.addRoutine[roomData['id']] = {};
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {
                                'state': deviceState,
                                'ch': null,
                                'v': null
                              };
                            } else {
                              if (controller.addRoutine[roomData['id']]
                                  .containsKey(device['id'])) {
                                if (controller.addRoutine[roomData['id']]
                                        [device['id']]['state'] ==
                                    null) {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['state'] = deviceState;
                                } else {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['state'] = null;

                                  if (controller
                                      .addRoutine[roomData['id']][device['id']]
                                      .values
                                      .every((element) => element == null)) {
                                    controller.addRoutine[roomData['id']]
                                        .remove(device['id']);
                                    if (controller
                                        .addRoutine[roomData['id']].isEmpty) {
                                      controller.addRoutine
                                          .remove(roomData['id']);
                                    }
                                  }
                                }
                              } else {
                                controller.addRoutine[roomData['id']]
                                    [device['id']] = {
                                  'state': deviceState,
                                  'ch': null,
                                  'v': null
                                };
                              }
                            }
                          } else {
                            controller.addRoutine[roomData['id']] = {};
                            controller.addRoutine[roomData['id']]
                                [device['id']] = {
                              'state': deviceState,
                              'ch': null,
                              'v': null
                            };
                          }
                          setState1(
                            () {
                              controller.addRoutine;
                            },
                          );
                          print(controller.addRoutine);
                        },
                        icon: iconStyle(
                          icon: controller.addRoutine
                                      .containsKey(roomData['id']) &&
                                  controller.addRoutine[roomData['id']] !=
                                      true &&
                                  controller.addRoutine[roomData['id']] !=
                                      false &&
                                  controller.addRoutine[roomData['id']]
                                      .containsKey(device['id']) &&
                                  controller.addRoutine[roomData['id']]
                                          [device['id']]['state'] !=
                                      null
                              ? Icons.check_circle_rounded
                              : Icons.add_circle_outline_rounded,
                          color: controller.addRoutine
                                      .containsKey(roomData['id']) &&
                                  controller.addRoutine[roomData['id']] !=
                                      true &&
                                  controller.addRoutine[roomData['id']] !=
                                      false &&
                                  controller.addRoutine[roomData['id']]
                                      .containsKey(device['id']) &&
                                  controller.addRoutine[roomData['id']]
                                          [device['id']]['state'] !=
                                      null
                              ? AppColors.primaryColor
                              : AppColors.warningColor,
                        ))
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  margin: EdgeInsets.zero,
                  alignment: Alignment.bottomRight,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                          child: txtStyle(
                        txt: tvPrivName!,
                      )),
                      SizedBox(
                        width: sized * 0.01,
                      ),
                      Container(
                        padding: EdgeInsets.only(left: sizedWidth * 0.01),
                        decoration: BoxDecoration(
                            border: Border(
                                left: BorderSide(
                                    color:
                                        AppColors.textColor.withOpacity(0.25),
                                    width: 1.5))),
                        child: txtStyle(
                          txt: 'تلفزيون',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(
                width: sizedWidth * 0.01,
              ),
              iconStyle(
                icon: Icons.tv,
                color: AppColors.warningColor,
                size: sized * 0.035,
              ),
            ]),
            SizedBox(
              height: sizedHeight * 0.05,
            ),
            txtStyle(
              txt: 'القنوات',
            ),
            Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(20)),
                  border: Border.all(
                      color: AppColors.textColor.withOpacity(0.25),
                      width: 1.5)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: controller.sizedHight * 0.02,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                              onPressed: () async {
                                if (client.connectionStatus!.state.name ==
                                    'connected') {
                                  Map chS = device['ch'];
                                  var selectitem = 0;
                                  if (chS.length == 0) {}
                                  TextEditingController name =
                                      TextEditingController(
                                    text: '',
                                  );
                                  TextEditingController number =
                                      TextEditingController(
                                    text: '',
                                  );
                                  bool edit = false;
                                  bool numEdit = false;
                                  String selectname = '';
                                  if (chS.length != 0) {
                                    selectname = chS.keys.toList()[0];
                                  }

                                  AwesomeDialog(
                                    context: context,
                                    dialogType: DialogType.noHeader,
                                    headerAnimationLoop: true,
                                    animType: AnimType.topSlide,
                                    dialogBackgroundColor:
                                        AppColors.backgroundColor2,
                                    body: chS.length != 0
                                        ? Column(
                                            children: [
                                              SizedBox(
                                                height: controller.sizedHight *
                                                    0.35,
                                                child: CupertinoPicker(
                                                  squeeze: 0.9,
                                                  // scrollController:
                                                  //     FixedExtentScrollController(
                                                  //         initialItem:
                                                  //             3),
                                                  itemExtent:
                                                      40, //height of each item

                                                  looping: false,

                                                  magnification: 1.2,
                                                  backgroundColor:
                                                      Colors.transparent,
                                                  children: <Widget>[
                                                    for (var c in chS.keys)
                                                      Padding(
                                                        padding: EdgeInsets.symmetric(
                                                            horizontal: controller
                                                                    .sizedWidth *
                                                                0.2),
                                                        child: Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .center,
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .center,
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          children: [
                                                            Expanded(
                                                              child: Text(
                                                                  chS[c],
                                                                  style:
                                                                      TextStyle(
                                                                    color: AppColors
                                                                        .textColor
                                                                        .withOpacity(
                                                                            0.8),
                                                                    fontSize:
                                                                        controller.sized *
                                                                            0.016,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                  )),
                                                            ),
                                                            Text(
                                                              c,
                                                              style: TextStyle(
                                                                color: AppColors
                                                                    .textColor
                                                                    .withOpacity(
                                                                        0.8),
                                                                fontSize: controller
                                                                        .sized *
                                                                    0.016,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                  ],
                                                  onSelectedItemChanged:
                                                      (int index) {
                                                    selectname = chS.keys
                                                        .toList()[index];
                                                  },
                                                  // onSelectedItemChanged: (int index) {
                                                  //   selectitem = index;
                                                  // },
                                                ),
                                              ),
                                              SizedBox(
                                                height: controller.sizedHight *
                                                    0.025,
                                              ),
                                              Material(
                                                color: Colors.transparent,
                                                child: CircleAvatar(
                                                    radius: controller.sized *
                                                        0.025,
                                                    backgroundColor:
                                                        AppColors.primaryColor,
                                                    child: IconButton(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              left: 10),
                                                      onPressed: () {
                                                        setState(
                                                          () {
                                                            ch =
                                                                chS[selectname];
                                                          },
                                                        );

                                                        if (controller
                                                            .addRoutine
                                                            .containsKey(
                                                                roomData[
                                                                    'id'])) {
                                                          if (controller.addRoutine[
                                                                      roomData[
                                                                          'id']] !=
                                                                  true &&
                                                              controller.addRoutine[
                                                                      roomData[
                                                                          'id']] !=
                                                                  false &&
                                                              controller
                                                                  .addRoutine[
                                                                      roomData[
                                                                          'id']]
                                                                  .containsKey(
                                                                      device[
                                                                          'id'])) {
                                                            if (controller
                                                                        .addRoutine[
                                                                    roomData[
                                                                        'id']][device[
                                                                    'id']]['ch'] !=
                                                                null) {
                                                              controller
                                                                      .addRoutine[
                                                                  roomData[
                                                                      'id']][device[
                                                                  'id']]['ch'] = ch;
                                                            }
                                                          }
                                                        }

                                                        Navigator.of(context)
                                                            .pop();
                                                      },
                                                      icon: Icon(
                                                        Icons.arrow_back_ios,
                                                        size: controller.sized *
                                                            0.038,
                                                        color: AppColors
                                                            .backgroundColor2,
                                                      ),
                                                    )),
                                              ),
                                              SizedBox(
                                                height: controller.sizedHight *
                                                    0.025,
                                              )
                                            ],
                                          )
                                        : Text(
                                            'لا يوجد اسماء قنوات مفضلة',
                                            style: TextStyle(
                                              color: AppColors.textColor
                                                  .withOpacity(0.8),
                                              fontSize:
                                                  controller.sized * 0.016,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),

                                    // btnCancelOnPress:
                                    //     () {},
                                  ).show();
                                }
                              },
                              icon: Icon(Icons.important_devices_rounded),
                              iconSize: sized * 0.035,
                              color: AppColors.textColor.withOpacity(0.7)),
                          IconButton(
                              onPressed: () {
                                TextEditingController ch123 =
                                    TextEditingController();
                                if (client.connectionStatus!.state.name ==
                                    'connected') {
                                  AwesomeDialog(
                                      context: context,
                                      dialogType: DialogType.noHeader,
                                      headerAnimationLoop: true,
                                      animType: AnimType.topSlide,
                                      dialogBackgroundColor:
                                          AppColors.backgroundColor2,
                                      body: Material(
                                        color: Colors.transparent,
                                        child: Container(
                                          child: Column(
                                            children: [
                                              TextField(
                                                autofocus: true,
                                                controller: ch123,
                                                cursorWidth: 0,
                                                style: TextStyle(
                                                    color: AppColors.textColor
                                                        .withOpacity(0.7),
                                                    fontSize: 25,
                                                    fontWeight:
                                                        FontWeight.bold),
                                                textInputAction:
                                                    TextInputAction.done,
                                                textAlign: TextAlign.center,
                                                decoration: InputDecoration(
                                                    hintStyle: TextStyle(
                                                        color: AppColors
                                                            .textColor
                                                            .withOpacity(0.7)),
                                                    border:
                                                        const UnderlineInputBorder(
                                                            borderSide:
                                                                BorderSide
                                                                    .none),
                                                    hintText: "ادخل الرقم"),
                                                keyboardType:
                                                    TextInputType.number,
                                                maxLength: 10,
                                              ),
                                              CircleAvatar(
                                                  radius:
                                                      controller.sized * 0.025,
                                                  backgroundColor:
                                                      AppColors.primaryColor,
                                                  child: IconButton(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            left: 10),
                                                    onPressed: () {
                                                      if (ch123
                                                          .text.isNumericOnly) {
                                                        setState(() {
                                                          ch = ch123.text;
                                                        });
                                                        if (controller
                                                            .addRoutine
                                                            .containsKey(
                                                                roomData[
                                                                    'id'])) {
                                                          if (controller.addRoutine[
                                                                      roomData[
                                                                          'id']] !=
                                                                  true &&
                                                              controller.addRoutine[
                                                                      roomData[
                                                                          'id']] !=
                                                                  false &&
                                                              controller
                                                                  .addRoutine[
                                                                      roomData[
                                                                          'id']]
                                                                  .containsKey(
                                                                      device[
                                                                          'id'])) {
                                                            if (controller
                                                                        .addRoutine[
                                                                    roomData[
                                                                        'id']][device[
                                                                    'id']]['ch'] !=
                                                                null) {
                                                              controller
                                                                      .addRoutine[
                                                                  roomData[
                                                                      'id']][device[
                                                                  'id']]['ch'] = ch;
                                                            }
                                                          }
                                                        }
                                                        Navigator.of(context)
                                                            .pop();
                                                      }
                                                    },
                                                    icon: Icon(
                                                      Icons.arrow_back_ios,
                                                      size: controller.sized *
                                                          0.038,
                                                      color: AppColors
                                                          .backgroundColor2
                                                          .withOpacity(0.85),
                                                    ),
                                                  )),
                                              SizedBox(
                                                height: controller.sizedHight *
                                                    0.02,
                                              )
                                            ],
                                          ),
                                        ),
                                      )).show();
                                }
                              },
                              icon: Icon(Icons.pin_rounded),
                              iconSize: sized * 0.035,
                              color: AppColors.textColor.withOpacity(0.7)),
                        ],
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                              onPressed: () {
                                setState((() {
                                  if (ch.contains('-') || ch.contains('+')) {
                                    ch = int.parse(ch) + 1;
                                    if (ch > 0) {
                                      ch = '+' + ch.toString();
                                    } else if (ch == 0) {
                                      ch = '+1';
                                    } else {
                                      ch = ch.toString();
                                    }
                                  } else {
                                    ch = '+1';
                                  }
                                }));
                                if (controller.addRoutine
                                    .containsKey(roomData['id'])) {
                                  if (controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']]['ch'] !=
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']]['ch'] = ch;
                                    }
                                  }
                                }
                              },
                              iconSize: controller.sized * 0.033,
                              color: AppColors.primaryColor,
                              icon: Icon(Icons.add_box_rounded)),
                          IconButton(
                              onPressed: () {
                                setState((() {
                                  if (ch.contains('-') || ch.contains('+')) {
                                    ch = int.parse(ch) - 1;
                                    if (ch > 0) {
                                      ch = '+' + ch.toString();
                                    } else if (ch == 0) {
                                      ch = '-1';
                                    } else {
                                      ch = ch.toString();
                                    }
                                  } else {
                                    ch = '-1';
                                  }
                                }));
                                if (controller.addRoutine
                                    .containsKey(roomData['id'])) {
                                  if (controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']]['ch'] !=
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']]['ch'] = ch;
                                    }
                                  }
                                }
                              },
                              iconSize: controller.sized * 0.033,
                              color: Colors.blue,
                              icon:
                                  Icon(Icons.indeterminate_check_box_rounded)),
                        ],
                      ),
                      SizedBox(
                        width: controller.sizedWidth * 0.05,
                      ),
                      Container(
                        decoration: BoxDecoration(
                          color: AppColors.textColor2.withOpacity(0.05),
                          borderRadius: BorderRadius.all(Radius.circular(25.5)),
                        ),
                        height: controller.sizedHight * 0.15,
                        width: controller.sizedWidth * 0.3,
                        child: FittedBox(
                          fit: BoxFit.contain,
                          child: Center(
                            child: Text(ch.toString(),
                                style: TextStyle(
                                  // fontSize: double.infinity,
                                  color: AppColors.textColor2.withOpacity(0.7),
                                )),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                      IconButton(
                          onPressed: () {
                            if (controller.addRoutine.containsKey('home')) {
                              controller.addRoutine = {};
                            }
                            if (controller.addRoutine
                                .containsKey(roomData['id'])) {
                              if (controller.addRoutine[roomData['id']] ==
                                      true ||
                                  controller.addRoutine[roomData['id']] ==
                                      false) {
                                controller.addRoutine[roomData['id']] = {};
                                controller.addRoutine[roomData['id']]
                                    [device['id']] = {
                                  'state': true,
                                  'ch': ch,
                                  'v': null,
                                };
                              } else {
                                if (controller.addRoutine[roomData['id']]
                                    .containsKey(device['id'])) {
                                  if (controller.addRoutine[roomData['id']]
                                          [device['id']]['ch'] ==
                                      null) {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['ch'] = ch;
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['state'] = true;
                                  } else {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['ch'] = null;

                                    if (controller
                                        .addRoutine[roomData['id']]
                                            [device['id']]
                                        .values
                                        .every((element) => element == null)) {
                                      controller.addRoutine[roomData['id']]
                                          .remove(device['id']);
                                      if (controller
                                          .addRoutine[roomData['id']].isEmpty) {
                                        controller.addRoutine
                                            .remove(roomData['id']);
                                      }
                                    }
                                  }
                                } else {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']] = {
                                    'state': true,
                                    'ch': ch,
                                    'v': null,
                                  };
                                }
                              }
                            } else {
                              controller.addRoutine[roomData['id']] = {};
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {
                                'state': true,
                                'ch': ch,
                                'v': null,
                              };
                            }
                            setState1(
                              () {
                                controller.addRoutine;
                              },
                            );
                            print(controller.addRoutine);
                          },
                          icon: iconStyle(
                            icon: controller.addRoutine
                                        .containsKey(roomData['id']) &&
                                    controller.addRoutine[roomData['id']] !=
                                        true &&
                                    controller.addRoutine[roomData['id']] !=
                                        false &&
                                    controller.addRoutine[roomData['id']]
                                        .containsKey(device['id']) &&
                                    controller.addRoutine[roomData['id']]
                                            [device['id']]['ch'] !=
                                        null
                                ? Icons.check_circle_rounded
                                : Icons.add_circle_outline_rounded,
                            color: controller.addRoutine
                                        .containsKey(roomData['id']) &&
                                    controller.addRoutine[roomData['id']] !=
                                        true &&
                                    controller.addRoutine[roomData['id']] !=
                                        false &&
                                    controller.addRoutine[roomData['id']]
                                        .containsKey(device['id']) &&
                                    controller.addRoutine[roomData['id']]
                                            [device['id']]['ch'] !=
                                        null
                                ? AppColors.primaryColor
                                : AppColors.warningColor,
                          )),
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                    ],
                  ),
                  SizedBox(
                    height: controller.sizedHight * 0.02,
                  ),
                ],
              ),
            ),
            SizedBox(
              height: sizedHeight * 0.05,
            ),
            txtStyle(
              txt: 'الصوت',
            ),
            Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(20)),
                  border: Border.all(
                      color: AppColors.textColor.withOpacity(0.25),
                      width: 1.5)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: controller.sizedHight * 0.02,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                      volume == true
                          ? IconButton(
                              onPressed: () {
                                setState(
                                  () {
                                    volume = false;
                                  },
                                );
                                if (controller.addRoutine
                                    .containsKey(roomData['id'])) {
                                  if (controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']]['v'] !=
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']]['v'] = volume;
                                    }
                                  }
                                }
                              },
                              icon: Icon(Icons.volume_off_rounded),
                              iconSize: sized * 0.035,
                              color: AppColors.textColor.withOpacity(0.7))
                          : IconButton(
                              onPressed: () {
                                setState(
                                  () {
                                    volume = true;
                                  },
                                );
                                if (controller.addRoutine
                                    .containsKey(roomData['id'])) {
                                  if (controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']]['v'] !=
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']]['v'] = volume;
                                    }
                                  }
                                }
                              },
                              icon: Icon(Icons.volume_up_rounded),
                              iconSize: sized * 0.035,
                              color: AppColors.textColor.withOpacity(0.7)),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                              onPressed: () {
                                setState((() {
                                  if (volume != true && volume != false) {
                                    volume = int.parse(volume) + 1;
                                    if (volume > 0) {
                                      volume = '+' + volume.toString();
                                    } else if (volume == 0) {
                                      volume = '+1';
                                    } else {
                                      volume = volume.toString();
                                    }
                                  } else {
                                    volume = '+1';
                                  }
                                }));
                                if (controller.addRoutine
                                    .containsKey(roomData['id'])) {
                                  if (controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']]['v'] !=
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']]['v'] = volume;
                                    }
                                  }
                                }
                              },
                              iconSize: controller.sized * 0.033,
                              color: AppColors.primaryColor,
                              icon: Icon(Icons.add_box_rounded)),
                          IconButton(
                              onPressed: () {
                                setState((() {
                                  if (volume != true && volume != false) {
                                    volume = int.parse(volume) - 1;
                                    if (volume > 0) {
                                      volume = '+' + volume.toString();
                                    } else if (volume == 0) {
                                      volume = '-1';
                                    } else {
                                      volume = volume.toString();
                                    }
                                  } else {
                                    volume = '-1';
                                  }
                                }));
                                if (controller.addRoutine
                                    .containsKey(roomData['id'])) {
                                  if (controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']]['v'] !=
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']]['v'] = volume;
                                    }
                                  }
                                }
                              },
                              iconSize: controller.sized * 0.033,
                              color: Colors.blue,
                              icon:
                                  Icon(Icons.indeterminate_check_box_rounded)),
                        ],
                      ),
                      SizedBox(
                        width: controller.sizedWidth * 0.05,
                      ),
                      Container(
                        decoration: BoxDecoration(
                          color: AppColors.textColor2.withOpacity(0.05),
                          borderRadius: BorderRadius.all(Radius.circular(25.5)),
                        ),
                        height: controller.sizedHight * 0.15,
                        width: controller.sizedWidth * 0.3,
                        child: FittedBox(
                          fit: BoxFit.contain,
                          child: Center(
                            child: volume == true || volume == false
                                ? Icon(
                                    volume == true
                                        ? Icons.volume_off_rounded
                                        : Icons.volume_up_rounded,
                                    color: AppColors.textColor.withOpacity(0.7))
                                : Text(volume.toString(),
                                    style: TextStyle(
                                      // fontSize: double.infinity,
                                      color:
                                          AppColors.textColor2.withOpacity(0.7),
                                    )),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                      IconButton(
                          onPressed: () {
                            if (controller.addRoutine.containsKey('home')) {
                              controller.addRoutine = {};
                            }
                            if (controller.addRoutine
                                .containsKey(roomData['id'])) {
                              if (controller.addRoutine[roomData['id']] ==
                                      true ||
                                  controller.addRoutine[roomData['id']] ==
                                      false) {
                                controller.addRoutine[roomData['id']] = {};
                                controller.addRoutine[roomData['id']]
                                    [device['id']] = {
                                  'state': true,
                                  'ch': null,
                                  'v': volume,
                                };
                              } else {
                                if (controller.addRoutine[roomData['id']]
                                    .containsKey(device['id'])) {
                                  if (controller.addRoutine[roomData['id']]
                                          [device['id']]['v'] ==
                                      null) {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['v'] = volume;
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['state'] = true;
                                  } else {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['v'] = null;

                                    if (controller
                                        .addRoutine[roomData['id']]
                                            [device['id']]
                                        .values
                                        .every((element) => element == null)) {
                                      controller.addRoutine[roomData['id']]
                                          .remove(device['id']);
                                      if (controller
                                          .addRoutine[roomData['id']].isEmpty) {
                                        controller.addRoutine
                                            .remove(roomData['id']);
                                      }
                                    }
                                  }
                                } else {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']] = {
                                    'state': true,
                                    'ch': null,
                                    'v': volume,
                                  };
                                }
                              }
                            } else {
                              controller.addRoutine[roomData['id']] = {};
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {
                                'state': true,
                                'ch': null,
                                'v': volume,
                              };
                            }
                            setState1(
                              () {
                                controller.addRoutine;
                              },
                            );
                            print(controller.addRoutine);
                          },
                          icon: iconStyle(
                            icon: controller.addRoutine
                                        .containsKey(roomData['id']) &&
                                    controller.addRoutine[roomData['id']] !=
                                        true &&
                                    controller.addRoutine[roomData['id']] !=
                                        false &&
                                    controller.addRoutine[roomData['id']]
                                        .containsKey(device['id']) &&
                                    controller.addRoutine[roomData['id']]
                                            [device['id']]['v'] !=
                                        null
                                ? Icons.check_circle_rounded
                                : Icons.add_circle_outline_rounded,
                            color: controller.addRoutine
                                        .containsKey(roomData['id']) &&
                                    controller.addRoutine[roomData['id']] !=
                                        true &&
                                    controller.addRoutine[roomData['id']] !=
                                        false &&
                                    controller.addRoutine[roomData['id']]
                                        .containsKey(device['id']) &&
                                    controller.addRoutine[roomData['id']]
                                            [device['id']]['v'] !=
                                        null
                                ? AppColors.primaryColor
                                : AppColors.warningColor,
                          )),
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                    ],
                  ),
                  SizedBox(
                    height: controller.sizedHight * 0.02,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }));
}
