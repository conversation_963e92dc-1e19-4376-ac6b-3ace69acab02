import datetime
from datetime import timedelta, date
import time
import sys
import re
import netifaces as ni

sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/nlt')
sys.path.append('/home/<USER>/myProject/modules')
import alarm as alarmm
import static as st


def _handle_alarm(self, tokens, text, command = None, topic = None):
        """معالجة طلبات التنبيهات والتذكيرات"""
        
        def calculate_next_date(now, day):
            """
            حساب التاريخ التالي للرقم المعطى من الشهر الحالي أو القادم
            """
            if day < now.day:
                # اليوم المطلوب مر بالفعل في هذا الشهر، ننتقل للشهر القادم
                if now.month == 12:
                    # إذا كنا في ديسمبر ننتقل إلى يناير من السنة القادمة
                    return now.replace(day=day, month=1, year=now.year + 1)
                else:
                    return now.replace(day=day, month=now.month + 1)
            else:
                # اليوم المطلوب لم يأتِ بعد في هذا الشهر
                return now.replace(day=day)

        def find_next_day_of_week(current_day, days_list):
            """
            إيجاد أقرب يوم من قائمة الأيام المعطاة
            current_day: يوم الأسبوع الحالي (0=الاثنين، 1=الثلاثاء، ... 6=الأحد)
            days_list: قائمة بأيام الأسبوع المطلوبة
            """
            for i in range(7):
                check_day = (current_day + i) % 7
                if check_day in days_list:
                    return check_day
            return current_day  # لا يجب أن نصل هنا أبدًا

        now = datetime.datetime.now()
        day =0
        month = 0
        year = 0
        hours = None
        minutes = None
        state = None
        room = {}
        result = {
            'type': '',        # نوع العملية (منبه أو تذكير)
            'title': '',         # عنوان التذكير (إن وجد)
            'datetime': None,    # وقت وتاريخ المنبه/التذكير
            'repeat': '',     # هل يتكرر أم لا
            'days': [],          # أيام التكرار (للمنبه المتكرر)
            
        }
        text = text.replace('ساع ربع','ساع 15 دقيق')
        text = text.replace('ساع ثلث','ساع 20 دقيق')
        text = text.replace('ساع نصف','ساع 30 دقيق')
        text = text.replace('ساع نص','ساع 30 دقيق')
        text = text.replace('ساع و ربع','ساع 15 دقيق')
        text = text.replace('ساع و ثلث','ساع 20 دقيق')
        text = text.replace('ساع و نصف','ساع 30 دقيق')
        text = text.replace('ساع و نص','ساع 30 دقيق')
        text = text.replace('ربع ساع','15 دقيق')
        text = text.replace('ثلث ساع','20 دقيق')
        text = text.replace('نصف ساع','30 دقيق')
        text = text.replace('نص ساع','30 دقيق')
        
        text = text.replace('ساع','clock')
        text = text.replace('دقيق','minute')
        text = text.replace('دقايق','minute')
        text = text.replace('دقاءق','minute')
        text = text.replace('WITHOUT','NOT')
        text = text.replace('EXPECT','NOT')
        text = text.replace('بعد clock','1 clock')
        text = text.replace('بعد minute','1 minute')
        
        text = text.replace('بعد DAY','1 DAY')
        text = text.replace('بعد WEEK','1 WEEK')
        text = text.replace('بعد MONTH','1 MONTH')
        text = text.replace('بعد YEAR','1 YEAR')
        print(text)
        if '1 clock' in text or '1 minute' in text or '1 DAY' in text or '1 WEEK' in text or '1 MONTH' in text or '1 YEAR' in text or '15 minute' in text or '20 minute' in text or '30 minute' in text:
            tokens.append('NUMBER')
        # تحديد نوع العملية
        if 'DEVICE' in tokens or 'JOB' in text:
            result['type'] = "job"
        elif 'ALARM' in text:
            result['type'] = "alarm"
        elif 'REMIND' in text:
            result['type'] = "remind"
        
            # استخراج عنوان التذكير (كل النص قبل كلمة DAY أو في)
#             match = re.search(r"REMIND\s+(.+?)\s+(?:DAY|في)", text)
#             if match:
#                 result['title'] = match.group(1).strip()
           
        if 'STOP' in text or 'OFF' in text:
            state = 'STOP'
        elif 'DELETE' in text:
            state = 'OFF'
        elif 'RUN' in text or '+' in text:
            state = 'ON'
        else:
            state = '_'
        
        # التعامل مع التكرار
        if ('ALL DAY' in text or 'ALL WEEK' in text or 'ALL Sat' in text  or 'ALL Sun' in text or 'ALL Mon' in text or 'ALL Tue' in text or 'ALL Wed' in text or 'ALL Thu' in text or 'ALL Fri' in text) and ('NOT RE' not in text):
            result['repeat'] = 'ON'
        elif 'NOT RE' in text:
            result['repeat'] = 'OFF'
        else:
            result['repeat'] = 'OFF'
            
        # معالجة ALL DAY و ALL WEEK
        if 'DAY' in tokens:
            for i in st.wdayN:
                if i in text and i not in result['days']:
                    result['days'].append(i)
        
        elif "ALL DAY" in text or "ALL WEEK" in text:
            result['days'] = list(st.wdayN)  # كل أيام الأسبوع
        
            
        # معالجة عبارة "ما عدا"
        if "NOT" in text:
            # استخراج الأيام المستثناة
            expectDays = text.split()[text.split().index('NOT')+1:]
            for i in expectDays:
                if i in st.wdayN:
                    if i in result['days']:
                        result['days'].remove(i)
        
        if 'NUMBER' in tokens:
            findNumber = text.split()
            for i in range(len(findNumber)):
                print(findNumber[i])
                if findNumber[i].isnumeric():
                    if i+1 != len(findNumber) and (findNumber[i+1] == 'clock'):
                        # معالجة إضافة ساعات
                        hours = int(findNumber[i])
                        print(findNumber[i])
                        if hours:
                            target_time = now + timedelta(hours=hours)
                            now = target_time
                            print(target_time)
                            if result['datetime'] is None:
                                result['datetime'] = target_time
                            else:
                                # نضيف الساعات إلى التاريخ الموجود مسبقًا
                                result['datetime'] = result['datetime'].replace(hour=target_time.hour, minute=target_time.minute)
                    elif i+1 != len(findNumber) and (findNumber[i+1] == 'minute'):
                        
                        # معالجة إضافة دقائق
                        minutes = int(findNumber[i])
                        if minutes:
                            target_time = now + timedelta(minutes=minutes)
                            now = target_time
                            print(now)
                            if result['datetime'] is None:
                                result['datetime'] = target_time
                            else:
                                result['datetime'] = result['datetime'].replace(hour=target_time.hour, minute=target_time.minute)
                    elif (i+1 != len(findNumber) and (findNumber[i+1] == 'DAY')) or (i+1 != len(findNumber) and (findNumber[i+1] == 'WEEK')) and result['type'] != "job":
                        # معالجة إضافة أيام و اسابيع
                        weeks = int(findNumber[i])*7 if findNumber[i+1] == 'WEEK' else None 
                        if weeks:
                            days = weeks
                        else:
                            days = int(findNumber[i])
                        if days:
                            target_date = now + timedelta(days=days)
                            now = target_date
                            if result['datetime'] is None:
                                result['datetime'] = target_date
                            else:
                                result['datetime'] = result['datetime'].replace(day=target_date.day, month=target_date.month, year=target_date.year)
                    elif i+1 != len(findNumber) and (findNumber[i+1] == 'MONTH') and result['type'] != "job":
                        # معالجة إضافة اشهر
                        months = int(findNumber[i])
                        if months:
                            target_date = now + timedelta(month=months)
                            now = target_date
                            if result['datetime'] is None:
                                result['datetime'] = target_date
                            else:
                                result['datetime'] = result['datetime'].replace(day=target_date.day, month=target_date.month, year=target_date.year)
                    elif i+1 != len(findNumber) and (findNumber[i+1] == 'YEAR') and result['type'] != "job":
                        # معالجة إضافة اشهر
                        years = int(findNumber[i])
                        if years:
                            target_date = now + timedelta(month=years)
                            now = target_date
                            if result['datetime'] is None:
                                result['datetime'] = target_date
                            else:
                                result['datetime'] = result['datetime'].replace(day=target_date.day, month=target_date.month, year=target_date.year)
                        
                    elif result['type'] != "job":
                        # معالجه الايام او السنين (مثل "DAY 27")
                            if int(findNumber[i])<32:            
                                day = int(findNumber[i])
                                target_day = calculate_next_date(now, day)
                                now = target_day
                                if result['datetime'] is None:
                                    result['datetime'] = target_day
                                else:
                                    result['datetime'] = result['datetime'].replace(day=target_day.day, month=target_day.month, year=target_day.year)
                            else:
                                year = int(findNumber[i])
                                if month == 0:
                                    month = now.month 
                                if day == 0:
                                    day = now.day 
                                target_date = datetime.datetime(year, month, day)
                                now = target_date
                                if result['datetime'] is None:
                                    result['datetime'] = target_date
                                else:
                                    result['datetime'] = result['datetime'].replace(year=year)

        # معالجة تاريخ محدد مع شهر (مثل "في 5 من Apr")
        if 'MONTH' in tokens and result['type'] != "job":
            for i in text.split():
                if i in st.nmonths:
                    month = i
                    month = st.nmonths[month]
                    if year == 0:
                        year = now.year
                    # التحقق مما إذا كان التاريخ قد مر بالفعل في هذه السنة
                    elif (month < now.month or (month == now.month and day < now.day)) and year == now.year:
                        year += 1
                    
                    if day == 0:
                        day = now.day 
                    
                        
                    target_date = datetime.datetime(year, month, day)
                    now = target_date
                    if result['datetime'] is None:
                        result['datetime'] = target_date
                    else:
                        result['datetime'] = result['datetime'].replace(day=day, month=month, year=year)
                    
                        
        # معالجة وقت محدد (مثل 6:30)
        if ':' in text:
            for i in text.split():
                if ':' in i:
                    hours = int(i.split(':')[0])
                    minute = int(i.split(':')[1])        
                    isAM_PM = None
                    if 'AM' in text:
                        isAM_PM = 'AM'
                    elif 'PM' in text:
                        isAM_PM = 'PM'
                    else :
                        print(i,now.strftime('%r').split(':')[0]+':'+now.strftime('%r').split(':')[1])
                        if (now.strftime('%p')=='PM' and hours > 12 and i > now.strftime('%H:%M')):
                            print('#'+now.strftime('%H:%M'))
                            isAM_PM = now.strftime('%p')
                        elif (now.strftime('%H') != '00' and hours > int(now.strftime('%r').split(':')[0])) or (hours == int(now.strftime('%r').split(':')[0]) and minute > int(now.strftime('%r').split(':')[1])):
                            print('##'+now.strftime('%H:%M'))
                            isAM_PM = now.strftime('%p')
                        elif (now.strftime('%H') == '00' and hours == 12 and minute > int(now.strftime('%M'))):
                            print('###'+now.strftime('%H:%M'))
                            isAM_PM = now.strftime('%p')
                        else:
                            if now.strftime('%p')=='AM':
                                isAM_PM = 'PM'
                            else:
                                isAM_PM = 'AM'
                    if isAM_PM == 'PM' and int(hours) < 12:
                        hours += 12
                    elif isAM_PM == 'AM' and int(hours) == 12:
                        hours = 0
                    
                    target_time = now.replace(hour=hours, minute=minute, second=0, microsecond=0)
                    now = target_time
               #     if target_time <= now and not result['days']:
                       # target_time += timedelta(days=1)
                    
                    if result['datetime'] is None:
                        result['datetime'] = target_time
                    else:
                        result['datetime'] = result['datetime'].replace(hour=hours, minute=minute)
                 
        # حساب التاريخ النهائي بناءً على الأيام المحددة (إذا وجدت)
        if result['days'] and result['datetime'] and result['type'] != "job":
            # احصل على يوم الأسبوع الحالي (0=الاثنين، 6=الأحد) وفقًا لنظام Python
            current_weekday = now.weekday()
            
            # إذا كان هناك أيام محددة، يجب أن نختار أقرب يوم مستقبلي
            next_day = find_next_day_of_week(current_weekday, result['days'])
            days_ahead = (next_day - current_weekday) % 7
            
            if days_ahead == 0 and result['datetime'].time() <= now.time():
                # إذا كان اليوم هو نفس اليوم والوقت قد مر، ننتقل إلى الأسبوع القادم
                days_ahead = 7
            
            next_date = now.date() + timedelta(days=days_ahead)
            result['datetime'] = result['datetime'].replace(
                year=next_date.year,
                month=next_date.month,
                day=next_date.day
            )        
        
        if result['type'] != "job":
            if 'ROOMS' in tokens:
                
                for i in text.split():
                    if 'room_' in i:
                        room[i.split('(')[0]] = i[i.index('(')+1:i.index(')')].replace('_',' ')
                    
                for i in room:
                    self.system_self.cursor.execute("SELECT Rooms FROM Devices WHERE Type LIKE '%s' AND rooms = '%s'"% ('ZAIN%',i))
                    reslt = self.system_self.cursor.fetchone()
                    if not reslt:
                        self.system_self.tts.say(f"{room[i]} لا تحتوي جهاز مساعد صوتي الرجاء التاكد من الاعدادات")
                        room.pop(i) 
            if room == {} and ('HOME' in text or 'ALL ROOM' in text):
                self.system_self.cursor.execute("SELECT Rooms FROM Devices WHERE Type LIKE '%s'"% ('ZAIN%'))
                results = self.system_self.cursor.fetchall()
                if results:
                    for i in results:
                        room[i['Rooms']]='None' 
            if room == {}:
                room[self.myroom] = 'myroom' 
        
        print(year,month,day,hours,minutes)
        topic='None' if result['type'] != "job" else topic
        command='None' if not command else command
        alarm = result['type']
        year = str(result['datetime'].year) if result['days'] == [] and result['datetime']!=None  else str(now.year)+'#'
        month = str(result['datetime'].month) if result['days'] == [] and result['datetime']!=None else str(now.month)+'#'
        mday = str(result['datetime'].day) if result['days'] == [] and result['datetime']!=None else str(now.day)+'#'
        awday = ' '.join(result['days']) if result['days'] != [] else 'None'
        nclock = str(result['datetime'].strftime('%l:%M %p')).strip() if hours or minutes else str(now.strftime('%l:%M %p'))+'#'.strip()
        re = result['repeat']
        print(year,month,mday,nclock)
        if alarm == 'remind':
            alarmm.remind(self,alarm,year,month,mday,awday,nclock,re,state,room)
        elif alarm == 'alarm':
            alarmm.alarM(self,awday,nclock,re,state,room)
        else :
            return [awday,nclock,re]
#         publish.single('edit','1', hostname=st.ip)