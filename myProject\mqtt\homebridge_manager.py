import mysql.connector
import subprocess
import time
import os
import sys

sys.path.append('/home/<USER>/myProject/resources')
import static as st

class HomebridgeManager:
    def __init__(self, system):
        self.system = system
    
    def setup_homebridge(self, client):
        """إعداد Homebridge وإنشاء ملف التكوين"""
        
        # إنشاء بداية ملف التكوين
        self._create_config_header()
        
        # الاتصال بقاعدة البيانات
        db = self._get_db_connection()
        cursor = db.cursor()
        
        try:
            cursor.execute("SELECT id,type FROM Devices")
            result = cursor.fetchall()
            # لا نعيد تعيين listdevices هنا
            print(result)
            
            for i, device in enumerate(result):
                device_id, device_type = device
                
                if device_type == 'AC':
                    self._add_ac_device(device_id, client)
                elif device_type == 'TV':
                    self._add_tv_device(device_id, cursor, client)
                elif device_type == 'LIGHT':
                    self._add_light_device(device_id, client)
                elif 'SWITCH' in device_type:
                    self._add_switch_device(device_id, cursor, client)
                
                if i < len(result) - 1:
                    self._append_to_config(',')
            
            self._finalize_config()
            self._apply_config()
            
        except Exception as e:
            print(f"خطأ في إعداد Homebridge: {e}")
        finally:
            cursor.close()
            db.close()
    
    def _get_db_connection(self):
        while True:
            try:
                return mysql.connector.connect(
                    user='root', 
                    host=st.ip, 
                    passwd='zain', 
                    database='zain'
                )
            except:
                time.sleep(5)
                continue
    
    def _create_config_header(self):
        config_content = '''{
    "bridge": {
        "name": "ZainAssistant %s",
        "username": "%s",
        "port": 51722,
        "pin": "123-45-678"
    },
    "platforms": [
        {
            "name": "Config",
            "port": 8581,
            "platform": "config"
        }
    ],
    "accessories": [
    
''' % (st.device_nb.upper(), st.homeBridgeSerial)
        
        with open(st.pathFiles + 'configjson.txt', 'w') as f:
            f.write(config_content)
    
    def _append_to_config(self, content):
        with open(st.pathFiles + 'configjson.txt', 'a') as f:
            f.write(content)
    
    def _add_ac_device(self, device_id, client):
        # تحديث listdevices بدلاً من إعادة تعيينه
        if device_id not in self.system.listdevices:
            self.system.listdevices[device_id] = {}
        self.system.listdevices[device_id]['device'] = 'AC'
        
        ac_config = '''
        {
            "accessory": "mqttthing",
            "type": "heaterCooler",
            "name": "مكيف %s",
            "logMqtt": true,
            "topics": {
                "setActive": "homebridge/%s/setActive",
                "getActive": "homebridge/%s/getActive",
                "getCurrentHeaterCoolerState": "homebridge/%s/getCurrentHeaterCoolerState",
                "setTargetHeaterCoolerState": "homebridge/%s/setTargetHeaterCoolerState",
                "getTargetHeaterCoolerState": "homebridge/%s/getTargetHeaterCoolerState",
                "getCurrentTemperature": "homebridge/%s/getCurrentTemperature",
                "setCoolingThresholdTemperature": "homebridge/%s/setCoolingThresholdTemperature",
                "setHeatingThresholdTemperature": "homebridge/%s/setHeatingThresholdTemperature",
                "getCoolingThresholdTemperature": "homebridge/%s/getCoolingThresholdTemperature",
                "getHeatingThresholdTemperature": "homebridge/%s/getHeatingThresholdTemperature",
                "setSwingMode": "homebridge/%s/setSwingMode",
                "getSwingMode": "homebridge/%s/getSwingMode",
                "getRotationSpeed": "homebridge/%s/getRotationSpeed",
                "setRotationSpeed": {
                    "topic": "homebridge/%s/setRotationSpeed",
                    "apply": "return [ '0', '1', '2', '3', '4' ][ Math.floor( message / 25 ) ];"
                }
            },
            "minTemperature": 16,
            "maxTemperature": 32
        }''' % tuple([device_id] * 15)
        
        self._append_to_config(ac_config)
        
        topics = [
            'setActive', 'setTargetHeaterCoolerState', 'setCoolingThresholdTemperature',
            'setHeatingThresholdTemperature', 'setSwingMode', 'setRotationSpeed'
        ]
        
        for topic in topics:
            client.subscribe(f'homebridge/{device_id}/{topic}')
    
    # ... (بقية الدوال بنفس التعديلات لاستخدام self.system.listdevices)
    def _add_tv_device(self, device_id, cursor, client):
        """إضافة جهاز تلفزيون لـ Homebridge"""
        self.system.listdevices[device_id] = {'device': 'TV'}
        
        # الحصول على قنوات التلفزيون
        cursor.execute("SELECT * FROM %s_TV" % device_id)
        resultCH = cursor.fetchall()
        
        channels = {}
        ch_config = ''
        
        if resultCH:
            ch_config += '''
            "inputs": ['''
            
            for c in resultCH:
                channels[c[0]] = c[1]
            
            for i, channel in enumerate(channels):
                ch_config += '''
                {
                    "name": "%s",
                    "value": "%s"
                }''' % (channel, channels[channel])
                
                if i < len(channels) - 1:
                    ch_config += ','
            
            ch_config += '''
            ],'''
        
        tv_config = '''
         {
            "accessory": "mqttthing",
            "type": "television",
            "name": "%s تلفزيون",
            "topics": {
                "setActive": "homebridge/%s/setActive",
                "getActive": "homebridge/%s/getActive",
                "setActiveInput": "homebridge/%s/setActive",
                "getActiveInput": "homebridge/%s/getActive",
                "setRemoteKey": "homebridge/%s/setActive"
            },%s
            "onValue": "POWER-ON",
            "offValue": "POWER-OFF"
        }''' % (device_id, device_id, device_id, device_id, device_id, device_id, ch_config)
        
        self._append_to_config(tv_config)
        client.subscribe(f'homebridge/{device_id}/setActive')
    
    def _add_light_device(self, device_id, client):
        """إضافة جهاز إضاءة لـ Homebridge"""
        self.system.listdevices[device_id] = {'device': 'LIGHT'}
        
        light_config = '''
        {
            "accessory": "mqttthing",
            "type": "lightbulb-OnOff",
            "name": "ضوء %s",
            "topics": {
                "getOn": "homebridge/%s/getOn",
                "setOn": "homebridge/%s/setOn"
            },
            "logMqtt": true,
            "integerValue": true
        }''' % (device_id, device_id, device_id)
        
        self._append_to_config(light_config)
        client.subscribe(f'homebridge/{device_id}/setOn')
    
    def _add_switch_device(self, device_id, cursor, client):
        """إضافة جهاز مفاتيح لـ Homebridge"""
        self.system.listdevices[device_id] = {'device': 'SWITCH'}
        
        # الحصول على معلومات المفاتيح
        cursor.execute("SELECT * FROM %s_SWITCH WHERE id=1" % device_id)
        switch_data = cursor.fetchone()
        
        if not switch_data:
            return
        
        # تحويل النتيجة إلى قاموس
        cursor.execute("DESCRIBE %s_SWITCH" % device_id)
        columns = [col[0] for col in cursor.fetchall()]
        switch_dict = dict(zip(columns, switch_data))
        
        sw_config = ''
        
        for switch_name in switch_dict:
            if switch_name != 'id':
                client.subscribe(f'homebridge/{device_id}::{switch_name}/setActive')
                switch_type = switch_dict[switch_name]
                
                if 'SWITCH' in switch_type:
                    sw_config += '''
                {
                    "type": "switch",
                    "name": "مفتاح %s",
                    "topics": {
                        "getOn": "homebridge/%s/getActive",
                        "setOn": "homebridge/%s/setActive"
                    }
                },\n''' % (switch_name, f'{device_id}::{switch_name}', f'{device_id}::{switch_name}')
                    
                elif 'LIGHT' in switch_type:
                    sw_config += '''
                {
                    "type": "lightbulb-OnOff",
                    "name": "اضائه %s",
                    "topics": {
                        "getOn": "homebridge/%s/getActive",
                        "setOn": "homebridge/%s/setActive"
                    }
                },\n''' % (switch_name, f'{device_id}::{switch_name}', f'{device_id}::{switch_name}')
                    
                elif 'VAN' in switch_type:
                    sw_config += '''
                {
                    "type": "fan",
                    "name": "مروحه %s",
                    "topics": {
                        "getOn": "homebridge/%s/getActive",
                        "setOn": "homebridge/%s/setActive"
                    }
                },\n''' % (switch_name, f'{device_id}::{switch_name}', f'{device_id}::{switch_name}')
        
        # إزالة الفاصلة الأخيرة
        sw_config = sw_config.rstrip(',\n')
        
        switch_config = '''
        {
            "accessory": "mqttthing",
            "type": "custom",
            "name": "مفاتيح %s",
            "logMqtt": true,
            "services": [%s
            ]
        }''' % (device_id, sw_config)
        
        self._append_to_config(switch_config)
    
    def _finalize_config(self):
        """إنهاء ملف التكوين"""
        self._append_to_config('''
    ]
}''')
        
    def _apply_config(self):
        try:
            subprocess.Popen(
                f"sudo cp {st.pathFiles}configjson.txt /var/lib/homebridge/config.json", 
                shell=True, 
                preexec_fn=os.setsid
            )
            time.sleep(1)
            subprocess.run("sudo hb-service restart", shell=True)
        except Exception as e:
            print(f"خطأ في تطبيق تكوين Homebridge: {e}")