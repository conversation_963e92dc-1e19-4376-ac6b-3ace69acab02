import 'dart:math';

/// تصنيفات التوصيات
enum RecommendationCategory {
  climate, // التكييف والتدفئة
  airQuality, // جودة الهواء
  safety, // الأمان
  energySaving, // توفير الطاقة
  comfort, // الراحة
  garden, // الحديقة
  lighting, // الإضاءة
}

/// نموذج التوصية الذكية
class SmartRecommendation {
  final String id;
  final String title;
  final String description;
  final String action;
  final String deviceType;
  final int priority; // 1-10 (10 = أعلى أولوية)
  final RecommendationCategory category;
  final int estimatedEnergySaving; // نسبة توفير الطاقة المتوقعة
  final int comfortImpact; // تأثير على الراحة (1-10)
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  SmartRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.action,
    required this.deviceType,
    required this.priority,
    required this.category,
    this.estimatedEnergySaving = 0,
    this.comfortImpact = 5,
    DateTime? timestamp,
    this.metadata,
  }) : timestamp = timestamp ?? DateTime.now();

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'action': action,
      'deviceType': deviceType,
      'priority': priority,
      'category': category.toString(),
      'estimatedEnergySaving': estimatedEnergySaving,
      'comfortImpact': comfortImpact,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// إنشاء من JSON
  factory SmartRecommendation.fromJson(Map<String, dynamic> json) {
    return SmartRecommendation(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      action: json['action'],
      deviceType: json['deviceType'],
      priority: json['priority'],
      category: RecommendationCategory.values.firstWhere(
        (e) => e.toString() == json['category'],
        orElse: () => RecommendationCategory.comfort,
      ),
      estimatedEnergySaving: json['estimatedEnergySaving'] ?? 0,
      comfortImpact: json['comfortImpact'] ?? 5,
      timestamp: DateTime.parse(json['timestamp']),
      metadata: json['metadata'],
    );
  }

  /// الحصول على أيقونة التصنيف
  String getCategoryIcon() {
    switch (category) {
      case RecommendationCategory.climate:
        return '❄️';
      case RecommendationCategory.airQuality:
        return '🌬️';
      case RecommendationCategory.safety:
        return '🛡️';
      case RecommendationCategory.energySaving:
        return '⚡';
      case RecommendationCategory.comfort:
        return '😌';
      case RecommendationCategory.garden:
        return '🌱';
      case RecommendationCategory.lighting:
        return '💡';
    }
  }

  /// الحصول على لون التصنيف
  String getCategoryColor() {
    switch (category) {
      case RecommendationCategory.climate:
        return '#2196F3'; // أزرق
      case RecommendationCategory.airQuality:
        return '#4CAF50'; // أخضر
      case RecommendationCategory.safety:
        return '#F44336'; // أحمر
      case RecommendationCategory.energySaving:
        return '#FF9800'; // برتقالي
      case RecommendationCategory.comfort:
        return '#9C27B0'; // بنفسجي
      case RecommendationCategory.garden:
        return '#8BC34A'; // أخضر فاتح
      case RecommendationCategory.lighting:
        return '#FFEB3B'; // أصفر
    }
  }
}

/// محرك التوصيات الذكية المتقدم
class SmartRecommendations {
  /// تحليل بيانات الطقس وإنشاء توصيات ذكية
  static List<SmartRecommendation> analyzeWeatherAndGenerateRecommendations(
    Map<String, dynamic> weatherData,
    Map<String, dynamic>? previousWeatherData,
  ) {
    final recommendations = <SmartRecommendation>[];

    final temperature = weatherData['temperature']?.toDouble() ?? 0.0;
    final humidity = weatherData['humidity']?.toDouble() ?? 0.0;
    final condition = weatherData['condition'] as String? ?? '';
    final windSpeed = weatherData['wind_speed']?.toDouble() ?? 0.0;
    final pressure = weatherData['pressure']?.toDouble() ?? 0.0;
    final uvIndex = weatherData['uv_index']?.toDouble() ?? 0.0;
    final cloudCoverage = weatherData['cloud_coverage']?.toDouble() ?? 0.0;

    final currentHour = DateTime.now().hour;
    final isDay = currentHour >= 6 && currentHour <= 18;
    final isEvening = currentHour >= 18 && currentHour <= 22;
    final isNight = currentHour >= 22 || currentHour <= 6;
    final isMorning = currentHour >= 6 && currentHour <= 10;

    // توصيات التكييف والتدفئة
    recommendations.addAll(_generateClimateRecommendations(
        temperature, humidity, condition, currentHour, isDay));

    // توصيات النوافذ والتهوية
    recommendations.addAll(_generateVentilationRecommendations(
        temperature, humidity, windSpeed, condition, isEvening));

    // توصيات الإضاءة
    recommendations.addAll(_generateLightingRecommendations(
        condition, cloudCoverage, uvIndex, currentHour, isDay, isNight));

    // توصيات الري والحديقة
    recommendations.addAll(_generateGardenRecommendations(
        temperature, humidity, condition, windSpeed, isMorning, isEvening));

    // توصيات الأمان والحماية
    recommendations.addAll(_generateSafetyRecommendations(
        condition, windSpeed, temperature, uvIndex));

    // توصيات توفير الطاقة
    recommendations.addAll(_generateEnergyRecommendations(
        temperature, condition, currentHour, isDay));

    // توصيات الراحة الشخصية
    recommendations.addAll(_generateComfortRecommendations(
        temperature, humidity, condition, pressure));

    // ترتيب التوصيات حسب الأولوية
    recommendations.sort((a, b) => b.priority.compareTo(a.priority));

    return recommendations.take(5).toList(); // أفضل 5 توصيات
  }

  /// توصيات التكييف والتدفئة
  static List<SmartRecommendation> _generateClimateRecommendations(
      double temperature,
      double humidity,
      String condition,
      int currentHour,
      bool isDay) {
    final recommendations = <SmartRecommendation>[];

    // توصيات المكيف
    if (temperature > 30.0 && isDay) {
      final priority = temperature > 35.0 ? 9 : 7;
      recommendations.add(SmartRecommendation(
        id: 'ac_cooling',
        title: '❄️ تشغيل المكيف',
        description:
            'الطقس حار (${temperature.toStringAsFixed(1)}°C) - يُنصح بتشغيل المكيف',
        action: 'turn_on_ac',
        deviceType: 'air_conditioner',
        priority: priority,
        category: RecommendationCategory.climate,
        estimatedEnergySaving:
            temperature > 35.0 ? 0 : 15, // لا توفير في الحر الشديد
        comfortImpact: 8,
      ));
    }

    // توصيات التدفئة
    if (temperature < 18.0 && (currentHour <= 8 || currentHour >= 20)) {
      recommendations.add(SmartRecommendation(
        id: 'heating_on',
        title: '🔥 تشغيل التدفئة',
        description:
            'الطقس بارد (${temperature.toStringAsFixed(1)}°C) - يُنصح بتشغيل التدفئة',
        action: 'turn_on_heater',
        deviceType: 'heater',
        priority: temperature < 10.0 ? 9 : 6,
        category: RecommendationCategory.climate,
        estimatedEnergySaving: 0,
        comfortImpact: 7,
      ));
    }

    // توصيات إيقاف المكيف في الطقس المعتدل
    if (temperature >= 22.0 &&
        temperature <= 26.0 &&
        condition.contains('clear')) {
      recommendations.add(SmartRecommendation(
        id: 'ac_off_moderate',
        title: '🌿 إيقاف المكيف',
        description:
            'الطقس معتدل (${temperature.toStringAsFixed(1)}°C) - يمكن إيقاف المكيف وتوفير الطاقة',
        action: 'turn_off_ac',
        deviceType: 'air_conditioner',
        priority: 5,
        category: RecommendationCategory.energySaving,
        estimatedEnergySaving: 40,
        comfortImpact: 6,
      ));
    }

    return recommendations;
  }

  /// توصيات النوافذ والتهوية
  static List<SmartRecommendation> _generateVentilationRecommendations(
      double temperature,
      double humidity,
      double windSpeed,
      String condition,
      bool isEvening) {
    final recommendations = <SmartRecommendation>[];

    // فتح النوافذ في الطقس المعتدل
    if (temperature >= 20.0 &&
        temperature <= 26.0 &&
        humidity < 70.0 &&
        windSpeed < 20.0 &&
        !condition.contains('rain')) {
      recommendations.add(SmartRecommendation(
        id: 'open_windows',
        title: '🪟 فتح النوافذ',
        description:
            'الطقس مثالي للتهوية الطبيعية - فتح النوافذ سيحسن جودة الهواء',
        action: 'open_windows',
        deviceType: 'windows',
        priority: 6,
        category: RecommendationCategory.airQuality,
        estimatedEnergySaving: 30,
        comfortImpact: 7,
      ));
    }

    // إغلاق النوافذ في الطقس السيء
    if (condition.contains('rain') || windSpeed > 40.0 || temperature > 35.0) {
      recommendations.add(SmartRecommendation(
        id: 'close_windows',
        title: '🔒 إغلاق النوافذ',
        description: 'الطقس غير مناسب - يُنصح بإغلاق النوافذ للحماية',
        action: 'close_windows',
        deviceType: 'windows',
        priority: 8,
        category: RecommendationCategory.safety,
        estimatedEnergySaving: 0,
        comfortImpact: 6,
      ));
    }

    // تشغيل المراوح في الطقس الحار
    if (temperature > 28.0 && humidity < 80.0 && isEvening) {
      recommendations.add(SmartRecommendation(
        id: 'fans_on',
        title: '💨 تشغيل المراوح',
        description: 'المراوح ستساعد في تحريك الهواء وتحسين الراحة',
        action: 'turn_on_fans',
        deviceType: 'fan',
        priority: 5,
        category: RecommendationCategory.comfort,
        estimatedEnergySaving: 20, // أقل استهلاكاً من المكيف
        comfortImpact: 6,
      ));
    }

    return recommendations;
  }

  /// توصيات الإضاءة
  static List<SmartRecommendation> _generateLightingRecommendations(
      String condition,
      double cloudCoverage,
      double uvIndex,
      int currentHour,
      bool isDay,
      bool isNight) {
    final recommendations = <SmartRecommendation>[];

    // إضاءة إضافية في الطقس الغائم
    if (isDay && (cloudCoverage > 80.0 || condition.contains('cloudy'))) {
      recommendations.add(SmartRecommendation(
        id: 'lights_on_cloudy',
        title: '💡 تشغيل الإضاءة',
        description: 'الطقس غائم - تشغيل الإضاءة سيحسن الرؤية والمزاج',
        action: 'turn_on_lights',
        deviceType: 'lights',
        priority: 4,
        category: RecommendationCategory.comfort,
        estimatedEnergySaving: -10, // استهلاك إضافي
        comfortImpact: 7,
      ));
    }

    // إطفاء الأضواء في الطقس المشمس
    if (isDay && condition.contains('sunny') && uvIndex > 3.0) {
      recommendations.add(SmartRecommendation(
        id: 'lights_off_sunny',
        title: '🌞 إطفاء الإضاءة',
        description: 'الشمس مشرقة - يمكن الاعتماد على الإضاءة الطبيعية',
        action: 'turn_off_lights',
        deviceType: 'lights',
        priority: 3,
        category: RecommendationCategory.energySaving,
        estimatedEnergySaving: 25,
        comfortImpact: 5,
      ));
    }

    return recommendations;
  }

  /// توصيات الري والحديقة
  static List<SmartRecommendation> _generateGardenRecommendations(
      double temperature,
      double humidity,
      String condition,
      double windSpeed,
      bool isMorning,
      bool isEvening) {
    final recommendations = <SmartRecommendation>[];

    // ري النباتات في الصباح الباكر
    if (isMorning &&
        temperature > 20.0 &&
        humidity < 60.0 &&
        !condition.contains('rain') &&
        windSpeed < 15.0) {
      recommendations.add(SmartRecommendation(
        id: 'water_plants_morning',
        title: '🌱 ري النباتات',
        description: 'الوقت مثالي لري النباتات - الطقس جاف والشمس ليست قوية',
        action: 'water_plants',
        deviceType: 'irrigation',
        priority: 6,
        category: RecommendationCategory.garden,
        estimatedEnergySaving: 0,
        comfortImpact: 4,
      ));
    }

    // تجنب الري في الطقس الممطر
    if (condition.contains('rain')) {
      recommendations.add(SmartRecommendation(
        id: 'skip_watering',
        title: '🌧️ تأجيل الري',
        description: 'الطقس ممطر - النباتات لا تحتاج ري إضافي',
        action: 'skip_irrigation',
        deviceType: 'irrigation',
        priority: 3,
        category: RecommendationCategory.energySaving,
        estimatedEnergySaving: 15,
        comfortImpact: 2,
      ));
    }

    return recommendations;
  }

  /// توصيات الأمان والحماية
  static List<SmartRecommendation> _generateSafetyRecommendations(
      String condition, double windSpeed, double temperature, double uvIndex) {
    final recommendations = <SmartRecommendation>[];

    // تحذير من الأشعة فوق البنفسجية
    if (uvIndex > 7.0) {
      recommendations.add(SmartRecommendation(
        id: 'uv_protection',
        title: '☀️ حماية من الأشعة',
        description:
            'مؤشر الأشعة فوق البنفسجية عالي - تجنب التعرض المباشر للشمس',
        action: 'close_blinds',
        deviceType: 'blinds',
        priority: 7,
        category: RecommendationCategory.safety,
        estimatedEnergySaving: 10,
        comfortImpact: 6,
      ));
    }

    // تأمين الأشياء في الرياح القوية
    if (windSpeed > 50.0) {
      recommendations.add(SmartRecommendation(
        id: 'secure_items',
        title: '💨 تأمين الممتلكات',
        description: 'رياح قوية - تأكد من تأمين الأشياء الخارجية',
        action: 'secure_outdoor_items',
        deviceType: 'security',
        priority: 9,
        category: RecommendationCategory.safety,
        estimatedEnergySaving: 0,
        comfortImpact: 3,
      ));
    }

    return recommendations;
  }

  /// توصيات توفير الطاقة
  static List<SmartRecommendation> _generateEnergyRecommendations(
      double temperature, String condition, int currentHour, bool isDay) {
    final recommendations = <SmartRecommendation>[];

    // استخدام الطاقة الشمسية
    if (condition.contains('sunny') && isDay) {
      recommendations.add(SmartRecommendation(
        id: 'solar_energy',
        title: '⚡ استغلال الطاقة الشمسية',
        description: 'الشمس مشرقة - وقت مثالي لشحن البطاريات الشمسية',
        action: 'optimize_solar',
        deviceType: 'solar_panel',
        priority: 4,
        category: RecommendationCategory.energySaving,
        estimatedEnergySaving: 50,
        comfortImpact: 3,
      ));
    }

    return recommendations;
  }

  /// توصيات الراحة الشخصية
  static List<SmartRecommendation> _generateComfortRecommendations(
      double temperature, double humidity, String condition, double pressure) {
    final recommendations = <SmartRecommendation>[];

    // تحسين الرطوبة
    if (humidity < 30.0) {
      recommendations.add(SmartRecommendation(
        id: 'increase_humidity',
        title: '💧 زيادة الرطوبة',
        description: 'الهواء جاف جداً - تشغيل المرطب سيحسن الراحة',
        action: 'turn_on_humidifier',
        deviceType: 'humidifier',
        priority: 5,
        category: RecommendationCategory.comfort,
        estimatedEnergySaving: -5,
        comfortImpact: 8,
      ));
    } else if (humidity > 80.0) {
      recommendations.add(SmartRecommendation(
        id: 'decrease_humidity',
        title: '🌬️ تقليل الرطوبة',
        description: 'الرطوبة عالية - تشغيل مزيل الرطوبة سيحسن الراحة',
        action: 'turn_on_dehumidifier',
        deviceType: 'dehumidifier',
        priority: 6,
        category: RecommendationCategory.comfort,
        estimatedEnergySaving: -8,
        comfortImpact: 7,
      ));
    }

    return recommendations;
  }
}
