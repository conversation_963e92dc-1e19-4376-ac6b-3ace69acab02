From 2d67b5d8d711e61551a28cac522484fd5cbc4982 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 7 Jan 2024 00:35:53 +0100
Subject: [PATCH] [channel-monitor] disable by default

Leave the channel monitor disabled by default. The impact on production
systems is not entirly clear. The channel scan could lead to missed
packets. Disabling by default allows to enable the feature at compile
time so developers and users can enable it at runtime for testing.

Signed-off-by: <PERSON> <<EMAIL>>
---
 src/core/thread/thread_netif.cpp | 3 ---
 1 file changed, 3 deletions(-)

diff --git a/src/core/thread/thread_netif.cpp b/src/core/thread/thread_netif.cpp
index 8e6848832..c79660de8 100644
--- a/src/core/thread/thread_netif.cpp
+++ b/src/core/thread/thread_netif.cpp
@@ -59,9 +59,6 @@ void ThreadNetif::Up(void)
 
     // Enable the MAC just in case it was disabled while the Interface was down.
     Get<Mac::Mac>().SetEnabled(true);
-#if OPENTHREAD_CONFIG_CHANNEL_MONITOR_ENABLE
-    IgnoreError(Get<Utils::ChannelMonitor>().Start());
-#endif
     Get<MeshForwarder>().Start();
 
     mIsUp = true;
-- 
2.45.2

