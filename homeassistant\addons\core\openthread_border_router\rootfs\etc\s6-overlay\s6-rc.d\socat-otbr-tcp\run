#!/usr/bin/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Start socat TCP client for OTBR agent daemon
# ==============================================================================

declare network_device

network_device=$(bashio::config 'network_device')

bashio::log.info "Starting socat TCP client for OTBR daemon..."
exec s6-notifyoncheck -d -s 300 -w 300 \
    "/usr/bin/socat" -d pty,raw,echo=0,link=/tmp/ttyOTBR,ignoreeof \
    "tcp:${network_device}"
