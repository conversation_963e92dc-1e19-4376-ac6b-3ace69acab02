import 'dart:async';

import 'package:get/get.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mysql1/mysql1.dart';
import 'package:network_info_plus/network_info_plus.dart';

import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:zaen/modules/local/ip.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:uuid/uuid.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:http/http.dart' as http;

getSystems() async {
  print(12445);
  // var databasesPath = await getDatabasesPath();
  // String path = join(databasesPath, 'zain.db');
  // await deleteDatabase(path);
  List systems = [];
  var db = await openDatabase('zain.db', version: 3,
      // انشاء قاعدة بيانات و عمل جدول للمنزل
      onCreate: (db, version) async {
    await db
        .execute(
            'CREATE TABLE systems (name TEXT, type TEXT, lat TEXT, lon TEXT)')
        .then((value) {
      print('created new db');
    }).catchError((v) {
      print(v);
    });
  }, onOpen: (db) async {
    // اذا كان يوجد قاعدة بيانات قم بانشاء الجدول

    systems = await db.rawQuery('SELECT * FROM systems');
  });
  db.close();
  if (systems.isNotEmpty) {
    for (Map system in systems) {
      print(system['name']);
      var dbSys = await openDatabase('${system['name']}.db', version: 3);
      var name = await dbSys.rawQuery('SELECT name FROM home');
      Map<String, dynamic> updatedSystem =
          Map.from(system); // إنشاء نسخة جديدة قابلة للتعديل
      updatedSystem['title'] = name[0]['name'];
      controller.systems.add(updatedSystem);
      await dbSys.close();
    }
  }
  controller.update();
  print(424214);
}

addSystem(name, type, sysImage, lat, lon, sysName, sysType) async {
  print(12445);
  // var databasesPath = await getDatabasesPath();
  // String path = join(databasesPath, '$name.db');
  // await deleteDatabase(path);
  var db = await openDatabase('zain.db', version: 3,
      // انشاء قاعدة بيانات و عمل جدول للمنزل
      onOpen: (db) async {
    // اذا كان يوجد قاعدة بيانات قم بانشاء الجدول

    await db.rawInsert(
        'INSERT INTO systems(name, type, lat,lon) VALUES(?, ?, ?, ?)',
        [name, type, lat, lon]);
  });
  db.close();
  print(424214);
  print(name);
  var dbSys = await openDatabase('$name.db', version: 3,
      // انشاء قاعدة بيانات و عمل جدول للمنزل
      onCreate: (dbSys, version) async {
    await dbSys
        .execute('CREATE TABLE home (name TEXT, type TEXT,image TEXT)')
        .then((value) {
      print('created new home');
    }).catchError((v) {
      print(v);
    });
    await dbSys
        .execute(
            'CREATE TABLE routine (id INTEGER PRIMARY KEY,name TEXT, routine TEXT, icon TEXT)')
        .then((value) {
      print('created new routine');
    }).catchError((v) {
      // print(v);
    });
    dbSys.execute('CREATE TABLE devices (id TEXT, name TEXT)').then((value) {
      print('created new devices');
    }).catchError((v) {
      // print(v);
    });

    dbSys
        .execute('CREATE TABLE rooms (id TEXT, name TEXT,image TEXT)')
        .then((value) {
      print('created new rooms');
    }).catchError((v) {
      // print(v);
    });

    // عمل قاعده بيانات الملحقات المفضله
    dbSys.execute('CREATE TABLE favorite (id TEXT)').then((value) {
      print('created new favorite');
    }).catchError((v) {
      // print(v);
    });
  }, onOpen: (dbSys) async {
    // اذا كان يوجد قاعدة بيانات قم بانشاء الجدول

    print('open db');

    // حذف جدول الاختصارات السريعة
    // var count = await db.rawDelete('DROP TABLE routine');

    // عمل جدول للاختصارات السريعة

    // ادخال بيانات الى جدول المنزل
    await dbSys.transaction((txn) async {
      await txn.rawInsert('INSERT INTO home(name, type, image) VALUES(?, ?, ?)',
          [sysName, sysType, sysImage]);
    }).then((value) {
      // print(value);
    }).catchError((onError) {
      // print(onError);
    });
    // var count =
    //     await db.rawDelete('DELETE FROM rooms WHERE name = ?', ['مطبخ']);

    //  عمل جدول للاجهزة و الغرف
  });
  print(1234);
  dbSys.close();
}

appDatabase() async {
  // print(12445);
  var databasesPath = await getDatabasesPath();
  String path = join(databasesPath, 'zain.db');
  await deleteDatabase(path);

  var db = await openDatabase('${controller.system}.db', version: 3,
      // انشاء قاعدة بيانات و عمل جدول للمنزل
      onCreate: (db, version) async {
    await db
        .execute('CREATE TABLE home (name TEXT, type TEXT, image TEXT)')
        .then((value) {
      print('created new db');
    }).catchError((v) {
      print(v);
    });
  }, onOpen: (db) async {
    // اذا كان يوجد قاعدة بيانات قم بانشاء الجدول
    db
        .execute('CREATE TABLE home (name TEXT, type TEXT,image TEXT)')
        .then((value) {
      print('created new home');
    }).catchError((v) {
      print(v);
    });

    print('open db');

    // حذف جدول الاختصارات السريعة
    // var count = await db.rawDelete('DROP TABLE routine');

    // عمل جدول للاختصارات السريعة
    db
        .execute(
            'CREATE TABLE routine (id INTEGER PRIMARY KEY,name TEXT, routine TEXT, icon TEXT)')
        .then((value) {
      // print('created new routine');
    }).catchError((v) {
      // print(v);
    });

    // ادخال بيانات الى جدول المنزل
    await db.transaction((txn) async {
      int id2 = await txn.rawInsert(
          'INSERT INTO home(name, type, image) VALUES(?, ?, ?)',
          ['المنزل', 'home', 'home2.jpg']);
    }).then((value) {
      // print(value);
    }).catchError((onError) {
      // print(onError);
    });
    // var count =
    //     await db.rawDelete('DELETE FROM rooms WHERE name = ?', ['مطبخ']);
    var list = await db.rawQuery('SELECT * FROM home').then((value) {
      // print(value);
    }).catchError((onError) {
      // print(onError);
    });

    //  عمل جدول للاجهزة و الغرف
    db.execute('CREATE TABLE devices (id TEXT, name TEXT)').then((value) {
      // print('created new devices');
    }).catchError((v) {
      // print(v);
    });

    db
        .execute('CREATE TABLE rooms (id TEXT, name TEXT,image TEXT)')
        .then((value) {
      // print('created new db');
    }).catchError((v) {
      // print(v);
    });

    // عمل قاعده بيانات الملحقات المفضله
    db.execute('CREATE TABLE favorite (id TEXT)').then((value) {
      // print('created new db');
    }).catchError((v) {
      // print(v);
    });

    //  ادخال بيانات للغرفة

    await db.transaction((txn) async {
      int id2 = await txn.rawInsert(
          'INSERT INTO rooms(id, name, image) VALUES(?, ?, ?)',
          ['غرفه_01', 'غرفتي', 'office1.JPG']);
    }).then((value) {
      // print(value);
    }).catchError((onError) {
      // print(onError);
    });
    await db.transaction((txn) async {
      int id2 = await txn.rawInsert(
          'INSERT INTO rooms(id, name, image) VALUES(?, ?, ?)',
          ['غرفه_02', 'مطبخ', 'kitchen2.jpg']);
    }).then((value) {
      // print(value);
    }).catchError((onError) {
      // print(onError);
    });
  });
  db.close();
  // print(424214);
}
