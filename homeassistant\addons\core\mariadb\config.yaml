---
version: 2.7.2
slug: mariadb
name: MariaDB
description: A SQL database server
url: https://github.com/home-assistant/addons/tree/master/mariadb
arch:
  - armhf
  - armv7
  - aarch64
  - amd64
  - i386
backup_post: unlock-tables-for-backup
backup_pre: lock-tables-for-backup
image: homeassistant/{arch}-addon-mariadb
init: false
options:
  databases:
    - homeassistant
  logins:
    - password: null
      username: homeassistant
  rights:
    - database: homeassistant
      username: homeassistant
ports:
  3306/tcp: null
schema:
  databases:
    - str
  logins:
    - username: str
      password: password
  rights:
    - database: str
      privileges:
        - "list(ALTER|CREATE|CREATE ROUTINE|CREATE TEMPORARY TABLES|\
          CREATE VIEW|DELETE|DELETE HISTORY|DROP|EVENT|GRANT OPTION|INDEX|\
          INSERT|LOCK TABLES|SELECT|SHOW VIEW|TRIGGER|UPDATE)?"
      username: str
  mariadb_server_args:
    - str?
services:
  - mysql:provide
startup: system
timeout: 300
