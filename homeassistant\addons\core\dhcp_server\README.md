# Home Assistant Add-on: DHCP server

> [!CAUTION]
> **Deprecation notice**
> The maintenance of the ISC DHCP Server ended in 2022 already. Alpine Linux
> dropped the package in Alpine 3.21. Hence, the add-on is deprecated as well.
> Consider using the DHCP functionality of your router instead.

A simple DHCP server.

![Supports aarch64 Architecture][aarch64-shield] ![Supports amd64 Architecture][amd64-shield] ![Supports armhf Architecture][armhf-shield] ![Supports armv7 Architecture][armv7-shield] ![Supports i386 Architecture][i386-shield]

This add-on provides a simple DHCP server for your network.
It provides some basic needs, like, reserving IP addresses for your devices
to ensure they always get assigned the same IP address.

[aarch64-shield]: https://img.shields.io/badge/aarch64-yes-green.svg
[amd64-shield]: https://img.shields.io/badge/amd64-yes-green.svg
[armhf-shield]: https://img.shields.io/badge/armhf-yes-green.svg
[armv7-shield]: https://img.shields.io/badge/armv7-yes-green.svg
[i386-shield]: https://img.shields.io/badge/i386-yes-green.svg
