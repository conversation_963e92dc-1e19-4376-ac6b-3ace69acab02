import 'package:flutter/material.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/commands/ac.dart';
import 'package:zaen/shared/commands/tv.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:reorderable_grid_view/reorderable_grid_view.dart';
import '../../../../shared/components/config.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Widget SW(
  device,
  room,
  Function(String?, bool?) switchTapp,
) {
  Map swList = device;
  print(device);
  return Directionality(
    textDirection: TextDirection.rtl,
    child: Column(
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.all(controller.sized * 0.01),
                child: iconStyle(
                    icon: Icons.power_outlined, color: AppColors.warningColor),
              ),
              Container(
                margin: EdgeInsets.zero,
                // padding:
                //     EdgeInsets.only(right: controller.sizedWidth * 0.01),
                // color: Colors.blueGrey.shade600,
                // alignment: Alignment.bottomRight,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Container(
                          padding: EdgeInsets.only(
                            left: controller.sizedWidth * 0.008,
                          ),
                          decoration: BoxDecoration(
                              border: Border(
                                  left: BorderSide(
                                      color: AppColors.textColor3
                                          .withOpacity(0.45),
                                      width: 1))),
                          child: txtStyle(
                            align: TextAlign.right,
                            txt: 'مفاتيح',
                          ),
                        ),
                        Container(
                            width: controller.sizedWidth * 0.35,
                            padding: EdgeInsets.only(
                                right: controller.sizedWidth * 0.01),
                            child: txtStyle(
                              align: TextAlign.right,
                              txt: device['priv'] == 'x'
                                  ? 'لا يوجد اسم'
                                  : device['priv'].split('_')[0].length > 14
                                      ? device['priv']
                                              .split('_')[0]
                                              .substring(0, 13) +
                                          '...'
                                      : device['priv'].split('_')[0],
                            )),
                      ],
                    ),
                    SizedBox(
                      height: controller.sizedHight * 0.005,
                    ),
                    Row(
                      textDirection: TextDirection.rtl,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        txtStyle(txt: controller.rooms[room]['privName']),
                      ],
                    ),
                  ],
                ),
              ),
              Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: controller.sized * 0.008),
                child: switchStyle(
                    value: device['state'] ?? false,
                    onChanged: (val) {
                      sswitch() {
                        switchTap('state', device['state'], device['id']);

                        final builder = MqttClientPayloadBuilder();

                        String pubMassege = controller.rooms[room]['devices']
                                [device['id']]['id'] +
                            ' SWITCH';

                        if (val == false) {
                          for (var j in controller
                              .rooms[room]['devices'][controller.rooms[room]
                                  ['devices'][device['id']]['id']]
                              .keys
                              .toList()) {
                            if (j != 'id' &&
                                j != 'device' &&
                                j != 'state' &&
                                j != 'pub' &&
                                j != 'priv' &&
                                j != 'pubName' &&
                                j != 'privName') {
                              pubMassege += ' ' + j + '_OFF';
                              // setState(() {
                              controller.rooms[room]['devices'][device['id']][j]
                                  ['state'] = val;
                              // });
                            }
                          }

                          roomState = false;
                          for (var j
                              in controller.rooms[room]['devices'].values) {
                            if (j['state'] == true && j['type'] != 'ZAIN') {
                              roomState = true;
                              print(1111111112222222);
                            }
                            // setState(() {

                            // });
                          }
                        } else if (val == true) {
                          // في هذه الحاله يجب ان يذهب الى قاعده البيانات و استخراج اخر حاله مخزنه في المساعد
                          roomState = true;
                          for (var j in controller
                              .rooms[room]['devices'][controller.rooms[room]
                                  ['devices'][device['id']]['id']]
                              .keys
                              .toList()) {
                            if (j != 'id' &&
                                j != 'device' &&
                                j != 'state' &&
                                j != 'pub' &&
                                j != 'priv' &&
                                j != 'pubName' &&
                                j != 'privName') {
                              pubMassege += ' ' + j + '_RUN';
                              // setState(() {
                              controller.rooms[room]['devices'][device['id']][j]
                                  ['state'] = val;
                              // });
                            }
                          }
                        }
                        controller.rooms[room]['state'] = roomState;
                        if (val == true) {
                          controller.homeState = true;
                        } else {
                          controller.homeState = false;
                          for (var i in controller.rooms.values) {
                            if (i['state'] == true) {
                              controller.homeState = true;
                            }
                          }
                        }
                        builder.addString(pubMassege);

                        client.publishMessage(controller.homeId + "/app/zain",
                            MqttQos.atLeastOnce, builder.payload!);

                        controller.update();
                      }

                      if (!controller.canControlDevices()) {
                        showNoPermissionDialog(
                            customMessage:
                                'ليس لديك صلاحية للتحكم في المفاتيح');
                        return;
                      }
                      roomId = room;
                      sswitch();
                    }),
              )
            ],
          ),
        ),
        Container(
          height: controller.sizedHight * 0.25,
          padding: EdgeInsets.symmetric(
            horizontal: controller.sizedWidth * 0.1,
          ),
          child: GridView(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            // physics: NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 0.1,
              mainAxisExtent: 100,
            ),
            children: [
              for (String i
                  in swList.keys.toList().getRange(0, swList.length - 4))
                Container(
                    // height: 160,
                    margin: const EdgeInsets.symmetric(horizontal: 5),
                    // color: AppColors.textColor,
                    child: CircularPercentIndicator(
                        radius: controller.sized * 0.03,
                        lineWidth: controller.sized * 0.006,
                        percent: (swList[i]['state'] ?? false) ? 0.965 : 0.0,
                        // startAngle: 1,
                        backgroundWidth: controller.sized * 0.007,
                        backgroundColor: (swList[i]['state'] ?? false)
                            ? Colors.transparent
                            : AppColors.backgroundSecondary.withOpacity(0.3),
                        footer: Container(
                          // height: 30,
                          child: Text(
                            device['priv'].split('_')[
                                int.parse(i.replaceFirst(RegExp(r'v'), ''))],
                            // 'ssss',
                            textDirection: TextDirection.rtl,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: (swList[i]['state'] ?? false)
                                  ? AppColors.textColor2
                                  : AppColors.textColor2.withOpacity(0.45),
                              fontSize: (swList[i]['state'] ?? false)
                                  ? controller.sized * 0.011
                                  : controller.sized * 0.011,
                            ),
                          ),
                        ),
                        center: IconButton(
                          onPressed: () {
                            switchTapp(i, swList[i]['state'] ?? false);
                          },
                          splashColor: Colors.transparent,
                          icon: Icon(
                            swList[i]['type'] == 'LIGHT'
                                ? Icons.lightbulb_outline
                                : swList[i]['type'] == 'VAN'
                                    ? Icons.storm_outlined
                                    : Icons.power_outlined,
                            color: (swList[i]['state'] ?? false)
                                ? AppColors.textColor.withOpacity(0.7)
                                : AppColors.textColor.withOpacity(0.25),
                            size: controller.sized * 0.03,
                          ),
                        ),
                        linearGradient: const LinearGradient(
                            begin: Alignment.topRight,
                            end: Alignment.bottomLeft,
                            colors: <Color>[
                              Color(0xFF6DD400),
                              Color(0xFF1AB600),
                            ]),
                        rotateLinearGradient: true,
                        circularStrokeCap: CircularStrokeCap.round)),
            ],
          ),
        ),
      ],
    ),
  );
}
