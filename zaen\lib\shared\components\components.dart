// Main components file - now organized and modular
// This file serves as the main entry point for all components

// Export all the organized component modules
export 'page_transitions.dart';
export 'navigation_helpers.dart';
export 'animated_widgets.dart';
export 'page_slide_widgets.dart';
export 'form_widgets.dart';
export 'button_widgets.dart';
export 'container_widgets.dart';
export 'text_icon_widgets.dart';
export 'device_widgets.dart';
export 'shortcut_widgets.dart';
export 'permission_widgets.dart';

// Re-export commonly used imports for convenience
export 'package:flutter/material.dart';
export 'package:zaen/shared/themes/app_colors.dart';
export 'package:zaen/shared/components/config.dart';
export 'package:zaen/shared/components/constants.dart';
export '../../controller/controller.dart';

int? _activePage;
