import 'package:zaen/shared/components/components.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/models/shortcuts.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/view/room/double_tap/sw_double_tap.dart';
import 'package:zaen/shared/themes/app_colors.dart';

roomSw({
  required var context,
  required var i,
}) =>
    shortcutSwitch(
      sizedWidth: controller.sizedWidth,
      sizedHeight: controller.sizedHight,
      sized: controller.sized,
      connect: controller.devices[i['id']],
      SwPrivName: i['priv'],
      // image: AssetImage("assets/images/sw.jpg"),
      deviceState: i['state'],
      swList: i,
      doubleTap: () {
        swDoubleTap(context: context, i: i);
      },

      switchState: (val) {
        if (!controller.canControlDevices()) {
          showNoPermissionDialog(
              customMessage: 'ليس لديك صلاحية للتحكم في المفاتيح');
          return null;
        }
        if (client.connectionStatus!.state.name == 'connected') {
          switchTap('state', i['state'], i['id']);

          final builder = MqttClientPayloadBuilder();

          String pubMassege = i['id'] + ' SWITCH';

          if (val == false) {
            for (var j
                in controller.rooms[roomId]['devices'][i['id']].keys.toList()) {
              if (j != 'id' &&
                  j != 'device' &&
                  j != 'state' &&
                  j != 'pub' &&
                  j != 'priv' &&
                  j != 'pubName' &&
                  j != 'privName') {
                pubMassege += ' ' + j + '_OFF';
                // setState(() {
                controller.rooms[roomId]['devices'][i['id']][j]['state'] = val;
                // });
              }
            }

            roomState = false;
            for (var j in controller.rooms[roomId]['devices'].values) {
              if (j['state'] == true && j['type'] != 'ZAIN') {
                roomState = true;
                print(1111111112222222);
              }
              // setState(() {

              // });
            }
          } else if (val == true) {
            // في هذه الحاله يجب ان يذهب الى قاعده البيانات و استخراج اخر حاله مخزنه في المساعد
            roomState = true;
            for (var j
                in controller.rooms[roomId]['devices'][i['id']].keys.toList()) {
              if (j != 'id' &&
                  j != 'device' &&
                  j != 'state' &&
                  j != 'pub' &&
                  j != 'priv' &&
                  j != 'pubName' &&
                  j != 'privName') {
                pubMassege += ' ' + j + '_RUN';
                // setState(() {
                controller.rooms[roomId]['devices'][i['id']][j]['state'] = val;
                // });
              }
            }
          }
          controller.rooms[roomId]['state'] = roomState;
          if (val == true) {
            controller.homeState = true;
          } else {
            controller.homeState = false;
            for (var i in controller.rooms.values) {
              if (i['state'] == true) {
                controller.homeState = true;
              }
            }
          }
          builder.addString(pubMassege);

          client.publishMessage(controller.homeId + "/app/zain",
              MqttQos.atLeastOnce, builder.payload!);

          controller.update();
        }
      },
      switchTap: (sw, val) {
        if (!controller.canManageData()) {
          showNoPermissionDialog(
              customMessage: 'ليس لديك صلاحية لتعديل اسماء المفاتيح');
          return null;
        }
        if (client.connectionStatus!.state.name == 'connected') {
          final builder = MqttClientPayloadBuilder();

          String pubMassege = i['id'] + ' SWITCH';

          // setState(( {
          controller.rooms[roomId]['devices'][i['id']][sw]['state'] = !val!;
          pubMassege += ' ' + sw! + (!val == true ? '_RUN' : '_OFF');
          //

          var F = false;
          for (var j
              in controller.rooms[roomId]['devices'][i['id']].keys.toList()) {
            if (j != 'id' &&
                j != 'device' &&
                j != 'state' &&
                j != 'pub' &&
                j != 'priv' &&
                j != 'pubName' &&
                j != 'privName') {
              if (controller.rooms[roomId]['devices'][i['id']][j]['state'] ==
                  true) {
                F = true;
              }
            }
          }
          if (F && i['state'] == false) {
            switchTap('state', i['state'], i['id']);
            // setState(() {
            roomState = true;
            // });
          } else if (!F && i['state'] == true) {
            switchTap('state', i['state'], i['id']);
            roomState = false;
            for (var j in controller.rooms[roomId]['devices'].values) {
              if (j['state'] == true) {
                roomState = true;
              }
              // setState(() {

              // });
            }
          }

          controller.rooms[roomId]['state'] = roomState;
          if (roomState == true) {
            controller.homeState = true;
          } else {
            controller.homeState = false;
            for (var i in controller.rooms.values) {
              if (i['state'] == true) {
                controller.homeState = true;
              }
            }
          }
          builder.addString(pubMassege);

          client.publishMessage(controller.homeId + "/app/zain",
              MqttQos.atLeastOnce, builder.payload!);

          controller.update();
        }
      },
    );
