import 'package:zaen/shared/components/components.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/models/shortcuts.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/view/room/double_tap/zain_double_tap.dart';
import 'package:zaen/shared/themes/app_colors.dart';

roomZain({
  required var context,
  required var i,
}) =>
    shortcutZain(
        sizedWidth: controller.sizedWidth,
        sizedHeight: controller.sizedHight,
        sized: controller.sized,
        connect: true,
        ZName: i['pubName'],
        Zmain: i['device'] == 'ZAIN-Main' ? true : false,
        volume: i['volume'],
        hearing: i['hearing'],
        sil: i['sil'],
        lestin: i['lestin'],
        play: i['play'],
        tapOn_VolumeMute: () {
          if (client.connectionStatus!.state.name == 'connected') {
            switchTap('sil', i['sil'], i['id']);
            controller.update();
          }
        },
        tapOn_hearingMute: () {
          if (client.connectionStatus!.state.name == 'connected') {
            switchTap('lestin', i['lestin'], i['id']);
            controller.update();
          }
        },
        vState: (val) {
          // setState(( {
          if (client.connectionStatus!.state.name == 'connected') {
            controller.rooms[roomId]['devices'][i['id']]['volume'] = val;
            // );
            if (val?.toInt() == 0 && i['sil'] == true) {
              switchTap('sil', i['sil'], i['id']);
            } else if (val?.toInt() != 0 && i['sil'] == false) {
              switchTap('sil', i['sil'], i['id']);
            }
            controller.update();
          }
        },
        hState: (val) {
          // setState(( {
          if (client.connectionStatus!.state.name == 'connected') {
            controller.rooms[roomId]['devices'][i['id']]['hearing'] = val;
            // );

            if (val?.toInt() == 0 && i['lestin'] == true) {
              switchTap('lestin', i['lestin'], i['id']);
            } else if (val?.toInt() != 0 && i['lestin'] == false) {
              switchTap('lestin', i['lestin'], i['id']);
            }
            controller.update();
          }
        },
        TapPlay: () {
          if (client.connectionStatus!.state.name == 'connected') {
            switchTap('play', i['play'], i['id']);
            controller.update();
          }
        },
        Play: () {
          if (client.connectionStatus!.state.name == 'connected') {
            if (i['play'] == true) {
              switchTap('play', i['play'], i['id']);
              controller.update();
            }
          }
        },
        doubleTap: () {
          zainDoubleTap(context: context, i: i);
        },
        tapOn_Switch_Icon: () {});
