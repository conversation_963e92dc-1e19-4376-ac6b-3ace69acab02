#!/usr/bin/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Take down the container if cpcd exits
# ==============================================================================
bashio::log.info "CPC ended with exit code ${1} (signal ${2})..."
rm -r /dev/shm/cpcd


# shellcheck disable=SC2086
if [ ${1} -ne 0 ] && [ ${1} -ne 256 ]; then
    /run/s6/basedir/bin/halt
fi
