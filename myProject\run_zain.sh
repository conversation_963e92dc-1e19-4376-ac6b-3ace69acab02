#!/bin/bash

# سكريبت تشغيل نظام زين للبيت الذكي
# الاستخدام: ./run_zain.sh

clear

echo "🏠 نظام زين للبيت الذكي المتكامل"
echo "=================================="
echo "🔗 مع دعم Home Assistant"
echo "📱 تطبيق Flutter + 🤖 أتمتة ذكية متقدمة"
echo "🚀 محرك الأتمتة المتقدمة مع دعم الشروط المتعددة"
echo "=================================="
echo ""

# التحقق من المسار
if [ ! -f "play.py" ]; then
    echo "❌ خطأ: يجب تشغيل السكريبت من مجلد myProject"
    echo "استخدم: cd /home/<USER>/myProject && ./run_zain.sh"
    exit 1
fi

echo "🔍 فحص المتطلبات..."

# فحص Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 غير متوفر"
    exit 1
fi

# فحص الملفات المطلوبة
if [ ! -f "/home/<USER>/ha.py" ]; then
    echo "⚠️ ملف ha.py غير موجود - سيتم تشغيل النظام بدون تكامل Home Assistant"
fi

echo "✅ جميع المتطلبات متوفرة"
echo ""

echo "📋 تعليمات الاستخدام:"
echo "1️⃣ تأكد من تشغيل Home Assistant على http://zain.local:8123"
echo "2️⃣ تأكد من تشغيل MySQL وMQTT Broker"
echo "3️⃣ افتح تطبيق Flutter على الهاتف"
echo "4️⃣ استمتع بالنظام الذكي!"
echo ""
echo "🔧 للإيقاف: اضغط Ctrl+C"
echo ""

echo "⏳ بدء التشغيل خلال 3 ثوانٍ..."
sleep 3

echo "🚀 بدء نظام زين للبيت الذكي..."
echo "=================================="

# بدء محرك الأتمتة المتقدمة في الخلفية
echo "🤖 بدء محرك الأتمتة المتقدمة..."
cd mqtt
python3 automation_engine.py &
AUTOMATION_PID=$!
cd ..

# انتظار قليل لبدء محرك الأتمتة
sleep 2

echo "✅ محرك الأتمتة المتقدمة يعمل (PID: $AUTOMATION_PID)"

# دالة لإيقاف جميع العمليات عند الخروج
cleanup() {
    echo ""
    echo "⏹️ إيقاف النظام..."
    if [ ! -z "$AUTOMATION_PID" ]; then
        echo "🛑 إيقاف محرك الأتمتة..."
        kill $AUTOMATION_PID 2>/dev/null
    fi
    echo "✅ تم إيقاف النظام بنجاح"
    exit 0
}

# تسجيل دالة التنظيف للتشغيل عند الخروج
trap cleanup SIGINT SIGTERM

echo "🎯 النظام الرئيسي جاهز للتشغيل..."

# تشغيل النظام الرئيسي
python3 play.py
