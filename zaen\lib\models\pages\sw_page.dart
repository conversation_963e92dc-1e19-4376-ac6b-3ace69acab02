import 'package:get/get.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';

// استدعاء المتغيرات العامة من room_page.dart
import 'package:zaen/models/pages/room_page.dart';

Widget SWPage({
  String? id,
  var deviceState = null,
  String? roomN,
  required Function() del,
  required Function() Dfavorite,
  required Function(int?, String?) swType,
  required Function() editRoom,
  required Function(bool?, String?, int?) editPrivName,
  required Function(String?) editNames,
  required Function(String?, String?) editSwNames,
  String? SwPrivName,
  required Map swList,
  required Function(bool?) switchState,
  required Function(String?, bool?) switchTap,
  required bool connect,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  TextEditingController editPriv;

  if (pageVer == 1 || connect == false) {
    editPriv = TextEditingController(
      text: SwPrivName!.split('_')[0] != 'x' ? SwPrivName.split('_')[0] : 'X',
    );
  } else if (pageHor != null) {
    editPriv = TextEditingController(
      text: SwPrivName!.split('_')[pageHor! + 1] != 'x'
          ? SwPrivName.split('_')[pageHor! + 1]
          : 'X',
    );
  } else {
    editPriv = TextEditingController(
      text: SwPrivName!.split('_')[1] != 'x' ? SwPrivName.split('_')[1] : 'X',
    );
    pageHor = 0;
    pageVer = 0;
  }
  bool privN = false;
  return pageSlide(
    content: GestureDetector(
      onTap: () {
        if (privN) {
          if (editPriv.text == '' ||
              editPriv.text == null ||
              editPriv.text == 'X' ||
              editPriv.text == 'x') {
            editPriv.text =
                SwPrivName.split('_')[pageVer == 1 ? 0 : pageHor! + 1] != 'x'
                    ? SwPrivName.split('_')[pageVer == 1 ? 0 : pageHor! + 1]
                    : 'X';
            privN = false;
          } else {
            for (var i = 0; i < editPriv.text.length; i++) {
              if (arabic.contains(editPriv.text[i]) ||
                  editPriv.text[i].isNumericOnly) {
                privN = true;
              } else {
                editPriv.text =
                    SwPrivName.split('_')[pageVer == 1 ? 0 : pageHor! + 1] !=
                            'x'
                        ? SwPrivName.split('_')[pageVer == 1 ? 0 : pageHor! + 1]
                        : 'X';
                privN = false;
                break;
              }
            }

            if (privN) {
              editPrivName(
                  privN, editPriv.text, pageVer == 1 ? 0 : pageHor! + 1);
              privN = false;
            }
          }
        }
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: PageView(
        scrollDirection: Axis.vertical,
        onPageChanged: (i) {
          pageVer = i;
          if (i == 1) {
            editPriv.text = SwPrivName.split('_')[0] != 'x'
                ? SwPrivName.split('_')[0]
                : 'X';
          } else {
            editPriv.text = SwPrivName.split('_')[pageHor! + 1] != 'x'
                ? SwPrivName.split('_')[pageHor! + 1]
                : 'X';
          }
          FocusManager.instance.primaryFocus?.unfocus();
        },
        physics: BouncingScrollPhysics(),
        children: connect == true &&
                client.connectionStatus!.state.name == 'connected'
            ? [
                Container(
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: sizedWidth * 0.035),
                        child: Row(mainAxisSize: MainAxisSize.min, children: [
                          Directionality(
                            textDirection: TextDirection.rtl,
                            child: switchStyle(
                                onChanged: switchState, value: deviceState),
                          ),
                          Expanded(
                            child: Container(
                              alignment: Alignment.bottomRight,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Container(
                                      width: controller.sizedWidth * 0.44,
                                      padding: EdgeInsets.only(
                                          right: controller.sized * 0.01),
                                      child: FittedBox(
                                        alignment: Alignment.centerRight,
                                        fit: BoxFit.scaleDown,
                                        child: txtStyle(
                                            align: TextAlign.right,
                                            txt: SwPrivName.split('_')[0] != 'x'
                                                ? SwPrivName.split('_')[0]
                                                : 'لا يوجد اسم'),
                                      )),
                                  Container(
                                      padding: EdgeInsets.only(
                                          left: controller.sizedWidth * 0.01),
                                      decoration: BoxDecoration(
                                          border: Border(
                                              left: BorderSide(
                                                  color: AppColors.textColor
                                                      .withOpacity(0.25),
                                                  width: 1.5))),
                                      child: txtStyle(
                                          align: TextAlign.right,
                                          txt: 'مفاتيح',
                                          color: AppColors.textColor3)),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            width: sizedWidth * 0.01,
                          ),
                          iconStyle(
                            icon: Icons.power_outlined,
                            color: AppColors.warningColor,
                            size: sized * 0.035,
                          ),
                        ]),
                      ),
                      Expanded(
                        child: PageView(
                          scrollDirection: Axis.horizontal,
                          physics: BouncingScrollPhysics(),
                          onPageChanged: (val) {
                            pageHor = val;
                            editPriv.text =
                                SwPrivName.split('_')[val + 1] == 'x'
                                    ? 'X'
                                    : SwPrivName.split('_')[val + 1];
                          },
                          children: [
                            for (var i in swList.keys
                                .toList()
                                .getRange(0, swList.length - 4))
                              SingleChildScrollView(
                                padding: EdgeInsets.zero,
                                physics: BouncingScrollPhysics(
                                    parent: NeverScrollableScrollPhysics()),
                                child: Column(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.only(
                                        right: sized * 0.02,
                                        bottom: 0,
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          Container(
                                            width: sizedWidth * 0.85,
                                            child: TextFormField(
                                              controller: editPriv,
                                              maxLength: 16,
                                              showCursor: true,
                                              cursorColor: AppColors.primary,
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                color: AppColors.textPrimary,
                                                fontSize: sized * 0.015,
                                                fontWeight: FontWeight.w500,
                                              ),
                                              onChanged: (i) {
                                                privN = true;
                                              },
                                              onEditingComplete: () {
                                                FocusManager
                                                    .instance.primaryFocus
                                                    ?.unfocus();
                                                if (editPriv.text == '' ||
                                                    editPriv.text == null ||
                                                    editPriv.text == 'X' ||
                                                    editPriv.text == 'x') {
                                                  editPriv
                                                      .text = SwPrivName.split(
                                                              '_')[pageVer ==
                                                                  1
                                                              ? 0
                                                              : pageHor! + 1] !=
                                                          'x'
                                                      ? SwPrivName.split('_')[
                                                          pageVer == 1
                                                              ? 0
                                                              : pageHor! + 1]
                                                      : 'X';
                                                  privN = false;
                                                } else if (editPriv.text !=
                                                    SwPrivName.split('_')[
                                                        pageVer == 1
                                                            ? 0
                                                            : pageHor! + 1]) {
                                                  for (var i = 0;
                                                      i < editPriv.text.length;
                                                      i++) {
                                                    if (arabic.contains(
                                                            editPriv.text[i]) ||
                                                        editPriv.text[i]
                                                            .isNumericOnly) {
                                                      privN = true;
                                                    } else {
                                                      editPriv.text = SwPrivName
                                                                      .split(
                                                                          '_')[
                                                                  pageVer == 1
                                                                      ? 0
                                                                      : pageHor! +
                                                                          1] !=
                                                              'x'
                                                          ? SwPrivName.split(
                                                              '_')[pageVer ==
                                                                  1
                                                              ? 0
                                                              : pageHor! + 1]
                                                          : 'X';
                                                      privN = false;
                                                      break;
                                                    }
                                                  }
                                                  if (privN) {
                                                    editPrivName(
                                                        privN,
                                                        editPriv.text,
                                                        pageVer == 1
                                                            ? 0
                                                            : pageHor! + 1);
                                                    privN = false;
                                                  }
                                                }
                                              },
                                              decoration: InputDecoration(
                                                hintText: 'اسم المفتاح',
                                                hintStyle: TextStyle(
                                                  color: AppColors.textHint,
                                                  fontSize: sized * 0.02,
                                                  fontWeight: FontWeight.normal,
                                                ),
                                                filled: true,
                                                fillColor: AppColors.surface,
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                  horizontal: sizedWidth * 0.04,
                                                  vertical: sizedHeight * 0.015,
                                                ),
                                                border: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.border,
                                                    width: 1.0,
                                                  ),
                                                ),
                                                enabledBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.border,
                                                    width: 1.0,
                                                  ),
                                                ),
                                                focusedBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.primary,
                                                    width: 2.0,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      width: sized * 0.24,
                                      height: sized * 0.24,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: swList[i]['state']
                                              ? AppColors.primaryColor
                                              : AppColors.textColor2
                                                  .withOpacity(0.2),
                                          width: sized * 0.01,
                                        ),
                                      ),
                                      child: IconButton(
                                        padding: EdgeInsets.zero,
                                        onPressed: () {
                                          switchTap(i, swList[i]['state']);
                                        },
                                        splashColor: Colors.transparent,
                                        icon: iconStyle(
                                          icon: swList[i]['type'] == 'LIGHT'
                                              ? Icons.lightbulb_rounded
                                              : swList[i]['type'] == 'VAN'
                                                  ? Icons.storm_outlined
                                                  : Icons.power_outlined,
                                          color: swList[i]['type'] == 'LIGHT' &&
                                                  swList[i]['state']
                                              ? AppColors.warning
                                              : swList[i]['type'] == 'VAN' &&
                                                      swList[i]['state']
                                                  ? AppColors.info
                                                  : swList[i]['type'] ==
                                                              'SWITCH' &&
                                                          swList[i]['state']
                                                      ? AppColors.success
                                                      : AppColors.textHint,
                                          size: sized * 0.12,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: sizedWidth * 0.03),
                                      child: Column(
                                        children: [
                                          SizedBox(
                                            height: sizedHeight * 0.05,
                                          ),
                                          Container(
                                              decoration: BoxDecoration(
                                                border: Border(
                                                  top: BorderSide(
                                                      color: AppColors
                                                          .backgroundColor2,
                                                      width: 1.5),
                                                ),
                                              ),
                                              child: MaterialButton(
                                                padding: EdgeInsets.zero,
                                                onPressed: () {
                                                  swType(
                                                      swList[i]['type'] ==
                                                              'SWITCH'
                                                          ? 0
                                                          : swList[i]['type'] ==
                                                                  'LIGHT'
                                                              ? 1
                                                              : 2,
                                                      i);
                                                },
                                                child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.max,
                                                    children: [
                                                      iconStyle(
                                                        icon: Icons
                                                            .arrow_drop_down,
                                                        size: sized * 0.03,
                                                        color:
                                                            AppColors.textHint,
                                                      ),
                                                      Expanded(
                                                          child: SizedBox(
                                                              width: double
                                                                  .infinity)),
                                                      Row(
                                                        children: [
                                                          txtStyle(
                                                              txt:
                                                                  '${swList[i]['type'] == 'LIGHT' ? 'ضوء' : swList[i]['type'] == 'VAN' ? 'مروحه' : 'مفتاح'}',
                                                              size:
                                                                  sized * 0.015,
                                                              color: AppColors
                                                                  .textSecondary),
                                                          txtStyle(
                                                              txt:
                                                                  'نوع المفتاح : ',
                                                              size:
                                                                  sized * 0.015,
                                                              color: AppColors
                                                                  .textHint),
                                                        ],
                                                      ),
                                                    ]),
                                              )),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.symmetric(
                                          horizontal: sizedWidth * 0.03),
                                      decoration: BoxDecoration(
                                        border: Border(
                                          top: BorderSide(
                                              color: AppColors.backgroundColor2,
                                              width: 1.5),
                                        ),
                                      ),
                                      child: Column(
                                        children: [
                                          SizedBox(
                                            height: sizedHeight * 0.02,
                                          ),
                                          MaterialButton(
                                            padding: EdgeInsets.zero,
                                            onPressed: () {
                                              editSwNames('show', i);
                                            },
                                            child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  iconStyle(
                                                    icon: Icons.arrow_drop_down,
                                                    size: sized * 0.03,
                                                    color: AppColors.textColor3,
                                                  ),
                                                  Expanded(
                                                      child: SizedBox(
                                                          width:
                                                              double.infinity)),
                                                  txtStyle(
                                                      txt:
                                                          'اسماء و صفات المفتاح العامة',
                                                      size: sized * 0.015,
                                                      color:
                                                          AppColors.textColor2),
                                                ]),
                                          ),
                                          Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                IconButton(
                                                  onPressed: () {
                                                    editSwNames('edit', i);
                                                  },
                                                  icon:
                                                      Icon(Icons.edit_outlined),
                                                  color: Colors.cyan,
                                                  iconSize: sized * 0.03,
                                                ),
                                                SizedBox(
                                                  width: sizedWidth * 0.075,
                                                ),
                                                IconButton(
                                                  onPressed: () {
                                                    editSwNames('del', i);
                                                  },
                                                  icon: Icon(Icons
                                                      .delete_sweep_rounded),
                                                  color: Color.fromARGB(
                                                      255, 243, 33, 18),
                                                  iconSize: sized * 0.032,
                                                ),
                                                SizedBox(
                                                  width: sizedWidth * 0.075,
                                                ),
                                                IconButton(
                                                  onPressed: () {
                                                    editSwNames('add', i);
                                                  },
                                                  icon: Icon(Icons.add_rounded),
                                                  color: AppColors.primaryColor,
                                                  iconSize: sized * 0.032,
                                                ),
                                              ]),
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      height: sizedHeight * 0.05,
                                    )
                                  ],
                                ),
                              )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                pageSetting(
                    type: 'مفاتيح',
                    del: del,
                    id: id,
                    Dfavorite: Dfavorite,
                    editRoom: editRoom,
                    editNames: editNames,
                    privName: SwPrivName.split('_')[0],
                    editPrivNameSw: editPrivName,
                    roomN: roomN,
                    isSw: true)
              ]
            : [
                pageSetting(
                    type: 'مفاتيح',
                    del: del,
                    id: id,
                    Dfavorite: Dfavorite,
                    editRoom: editRoom,
                    editNames: editNames,
                    privName: SwPrivName.split('_')[0],
                    editPrivNameSw: editPrivName,
                    roomN: roomN,
                    isSw: true,
                    connect: false)
              ],
      ),
    ),
  );
}
