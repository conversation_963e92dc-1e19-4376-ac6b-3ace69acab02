import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mysql1/mysql1.dart';
import 'package:sqflite/sqflite.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/view/home/<USER>/settingFolder/routineWord.dart';
import 'package:zaen/view/home/<USER>/settingFolder/tasks.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Widget actionButtonsWidget({
  required HomeController controller,
  required bool add,
  required bool isShortcut,
  required bool isWords,
  required bool isTask,
  required bool isScheduler,
  required bool isSetting,
  required String myId,
  required String routineIcon,
  required TextEditingController name1,
  required TextEditingController name2,
  required GlobalKey<FormState> kname1,
  required GlobalKey<FormState> kname2,
  required List<String> days,
  required Map<String, String> weekDays,
  required int h,
  required int m,
  required bool isAM,
  required bool re,
  required int p,
  required PageController pageController,
  required Function setState,
  required bool edit,
}) {
  return Center(
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        submitButtom(
          text: add ? "خروج" : "تعديل",
          onPressed: () async {
            await handleSubmitAction(
              controller: controller,
              add: add,
              isShortcut: isShortcut,
              isWords: isWords,
              isTask: isTask,
              isScheduler: isScheduler,
              isSetting: isSetting,
              myId: myId,
              routineIcon: routineIcon,
              name1: name1,
              name2: name2,
              kname1: kname1,
              kname2: kname2,
              days: days,
              weekDays: weekDays,
              h: h,
              m: m,
              isAM: isAM,
              re: re,
              p: p,
              pageController: pageController,
              setState: setState,
            );
          },
        ),
        add == false
            ? Padding(
                padding: EdgeInsets.symmetric(
                    horizontal: controller.sizedWidth * 0.02),
                child: delButtom(
                  text: 'حذف',
                  onPressed: () async {
                    await handleDeleteAction(
                      controller: controller,
                      isShortcut: isShortcut,
                      isWords: isWords,
                      isTask: isTask,
                      isScheduler: isScheduler,
                      isSetting: isSetting,
                      myId: myId,
                      kname1: kname1,
                      kname2: kname2,
                      p: p,
                      pageController: pageController,
                      setState: setState,
                    );
                  },
                ),
              )
            : Container()
      ],
    ),
  );
}

Future<void> handleSubmitAction({
  required HomeController controller,
  required bool add,
  required bool isShortcut,
  required bool isWords,
  required bool isTask,
  required bool isScheduler,
  required bool isSetting,
  required String myId,
  required String routineIcon,
  required TextEditingController name1,
  required TextEditingController name2,
  required GlobalKey<FormState> kname1,
  required GlobalKey<FormState> kname2,
  required List<String> days,
  required Map<String, String> weekDays,
  required int h,
  required int m,
  required bool isAM,
  required bool re,
  required int p,
  required PageController pageController,
  required Function setState,
}) async {
  print(isTask);
  print(isScheduler);
  if (isShortcut) {
    if (kname1.currentState != null) {
      var formdata = kname1.currentState;
      if (formdata!.validate() != true) {
        return;
      }
    }

    FocusManager.instance.primaryFocus?.unfocus();
    print(name1.text);
    print(routineIcon);

    print('55fsdgsdg555555555555555555555');

    var appDB = await openDatabase('${controller.system}.db', version: 3);
    if (add) {
      appDB.rawQuery('insert into routine(name,routine,icon) values(?,?,?)', [
        name1.text.isEmpty ? 'None' : name1.text,
        json.encode(controller.addRoutine),
        routineIcon
      ]);
    } else {
      await appDB.transaction((txn) async {
        print(12345);
        await txn.rawUpdate(
            'UPDATE routine SET name = ?, routine = ?, icon = ? WHERE id = ?', [
          name1.text.isEmpty ? 'None' : name1.text,
          json.encode(controller.addRoutine),
          routineIcon,
          myId
        ]);
        print(123456);
      });
    }
    getDevices();
    if (isSetting == false) {}
  }
  if (isWords) {
    if (kname2.currentState != null) {
      var formdata = kname2.currentState;
      if (formdata!.validate() != true) {
        if (isShortcut) {
          isShortcut = false;
        }
        return;
      }
    }
    final conn = await MySqlConnection.connect(ConnectionSettings(
        host: controller.hostZain.value,
        // port: 80,
        user: 'root',
        db: 'zain',
        password: 'zain',
        characterSet: CharacterSet.UTF8));

    if (add) {
      await conn.query("INSERT INTO RDevice(route,word) values(?,?)", [
        json.encode(controller.addRoutine),
        name2.text,
      ]);
    } else {
      await conn.query("UPDATE RDevice SET route = ?, word = ? WHERE id = ?",
          [json.encode(controller.addRoutine), name2.text, myId]);
    }
    conn.close();
    await getDevices();
  }
  if (isTask && isScheduler) {
    print('4444444442322222222221111111111');
    final conn = await MySqlConnection.connect(ConnectionSettings(
        host: controller.hostZain.value,
        // port: 80,
        user: 'root',
        db: 'zain',
        password: 'zain',
        characterSet: CharacterSet.UTF8));
    String wdays = '';
    if (days.isEmpty) {
      wdays = 'None';
    } else {
      for (var i in days) {
        wdays += weekDays[i]! + ' ';
      }
      wdays = wdays.substring(0, wdays.length - 1);
    }
    print(wdays);
    String clock =
        h.toString() + ':' + (m > 9 ? m.toString() : '0' + m.toString());
    String time = isAM ? 'AM' : 'PM';
    String ree = re ? 'ON' : 'OFF';
    if (add) {
      await conn.query(
          "INSERT INTO ADevice(route,wday,nclock,re,state) values(?,?,?,?,?)", [
        json.encode(controller.addRoutine),
        wdays,
        '$clock $time',
        ree,
        'ON'
      ]);
    } else {
      await conn.query(
          "UPDATE ADevice SET route = ?, topic = ?, command = ?, wday = ?, nclock = ?, re = ?, state = ? WHERE id = ?",
          [
            json.encode(controller.addRoutine),
            null,
            null,
            wdays,
            '$clock $time',
            ree,
            'ON',
            myId
          ]);
    }
    conn.close();
    await getDevices();
  }
  if (isTask && isScheduler && isSetting) {
    setState(() {
      p = 4;
      controller.addRoutine.clear();
      isSetting = true;
      isShortcut = false;
      isTask = true;
      isWords = false;
      add = true;
      myId = '';
    });

    pageController.jumpToPage(p);
    tasks(Get.context!, setState);
  } else if (isWords && isSetting) {
    setState(() {
      p = 4;
      controller.addRoutine.clear();
      isSetting = true;
      isShortcut = false;
      isTask = false;
      isWords = true;
      add = true;
      myId = '';
    });

    pageController.jumpToPage(p);
    routineWords(Get.context!, setState);
  } else {
    Navigator.of(Get.context!).pop();
  }
}

Future<void> handleDeleteAction({
  required HomeController controller,
  required bool isShortcut,
  required bool isWords,
  required bool isTask,
  required bool isScheduler,
  required bool isSetting,
  required String myId,
  required GlobalKey<FormState> kname1,
  required GlobalKey<FormState> kname2,
  required int p,
  required PageController pageController,
  required Function setState,
}) async {
  var formdata = kname1.currentState;
  // var formdata2 = kname2.currentState;
  if (isShortcut && formdata!.validate()) {
    FocusManager.instance.primaryFocus?.unfocus();
    var appDB = await openDatabase('${controller.system}.db', version: 3);
    await appDB.rawQuery('DELETE FROM routine WHERE id = ${myId}');
    getDevices();
    Navigator.of(Get.context!).pop();
  } else if (isWords) {
    final conn = await MySqlConnection.connect(ConnectionSettings(
        host: controller.hostZain.value,
        // port: 80,
        user: 'root',
        db: 'zain',
        password: 'zain',
        characterSet: CharacterSet.UTF8));
    await conn.query("DELETE FROM RDevice WHERE id = ?", [myId]);
    setState(() {
      p = 4;
      controller.addRoutine.clear();
      isSetting = true;
      isShortcut = false;
      isTask = false;
      isWords = true;
      // add = true;
      // myId = '';
    });
    await getDevices();
    pageController.jumpToPage(p);
    routineWords(Get.context!, setState);
  } else if (isScheduler && isTask) {
    final conn = await MySqlConnection.connect(ConnectionSettings(
        host: controller.hostZain.value,
        // port: 80,
        user: 'root',
        db: 'zain',
        password: 'zain',
        characterSet: CharacterSet.UTF8));
    await conn.query("DELETE FROM ADevice WHERE id = ?", [myId]);
    setState(() {
      p = 4;
      controller.addRoutine.clear();
      isSetting = true;
      isShortcut = false;
      isTask = true;
      isWords = false;
      // add = true;
      // myId = '';
    });
    await getDevices();
    pageController.jumpToPage(p);
    tasks(Get.context!, setState);
  }
}
