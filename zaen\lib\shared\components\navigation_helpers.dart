import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'page_transitions.dart';

// ===== دوال مساعدة للتنقل مع الحركات =====

// التنقل مع حركة انزلاق من اليمين
void navigateWithSlideRight(BuildContext context, Widget page) {
  Navigator.of(context).push(SlideRightToLeftRoute(page: page));
}

// التنقل مع حركة انزلاق من اليسار
void navigateWithSlideLeft(BuildContext context, Widget page) {
  Navigator.of(context).push(SlideLeftToRightRoute(page: page));
}

// التنقل مع حركة انزلاق من الأسفل
void navigateWithSlideUp(BuildContext context, Widget page) {
  Navigator.of(context).push(SlideBottomToTopRoute(page: page));
}

// التنقل مع حركة تكبير
void navigateWithScale(BuildContext context, Widget page) {
  Navigator.of(context).push(ScaleRoute(page: page));
}

// التنقل مع حركة دوران وتكبير
void navigateWithRotation(BuildContext context, Widget page) {
  Navigator.of(context).push(RotationScaleRoute(page: page));
}

// التنقل مع حركة انزلاق مع عمق
void navigateWithDepth(BuildContext context, Widget page) {
  Navigator.of(context).push(SlideWithDepthRoute(page: page));
}

// التنقل مع حركة دخول الغرفة المخصصة
void navigateToRoom(BuildContext context, Widget page) {
  Navigator.of(context).push(RoomEntranceRoute(page: page));
}

// حركة بسيطة وآمنة للغرف
void navigateToRoomSafe(BuildContext context, Widget page) {
  Navigator.of(context).push(
    PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 400),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    ),
  );
}

// التنقل مع حركة الباب المنزلق
void navigateWithSlidingDoor(BuildContext context, Widget page) {
  Navigator.of(context).push(SlidingDoorRoute(page: page));
}

// دالة مخصصة للتنقل إلى الغرف مع خيارات متعددة
void navigateToRoomWithStyle(
  BuildContext context,
  Widget page, {
  RoomTransitionStyle style = RoomTransitionStyle.entrance,
}) {
  switch (style) {
    case RoomTransitionStyle.entrance:
      Navigator.of(context).push(RoomEntranceRoute(page: page));
      break;
    case RoomTransitionStyle.slidingDoor:
      Navigator.of(context).push(SlidingDoorRoute(page: page));
      break;
    case RoomTransitionStyle.depth:
      Navigator.of(context).push(SlideWithDepthRoute(page: page));
      break;
  }
}

enum RoomTransitionStyle {
  entrance,
  slidingDoor,
  depth,
}

// دالة عامة للتنقل مع اختيار نوع الحركة
enum PageTransitionType {
  slideRight,
  slideLeft,
  slideUp,
  scale,
  rotation,
  depth,
  roomEntrance,
  slidingDoor,
}

void navigateWithAnimation(
  BuildContext context,
  Widget page, {
  PageTransitionType type = PageTransitionType.slideRight,
  Duration? duration,
}) {
  PageRouteBuilder route;

  switch (type) {
    case PageTransitionType.slideRight:
      route = SlideRightToLeftRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 400),
      );
      break;
    case PageTransitionType.slideLeft:
      route = SlideLeftToRightRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 400),
      );
      break;
    case PageTransitionType.slideUp:
      route = SlideBottomToTopRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 500),
      );
      break;
    case PageTransitionType.scale:
      route = ScaleRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 600),
      );
      break;
    case PageTransitionType.rotation:
      route = RotationScaleRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 800),
      );
      break;
    case PageTransitionType.depth:
      route = SlideWithDepthRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 500),
      );
      break;
    case PageTransitionType.roomEntrance:
      route = RoomEntranceRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 700),
      );
      break;
    case PageTransitionType.slidingDoor:
      route = SlidingDoorRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 600),
      );
      break;
  }

  Navigator.of(context).push(route);
}

// دوال مساعدة للاستخدام مع GetX
class AnimatedNavigation {
  // التنقل مع حركة انزلاق من اليمين
  static Future<T?> toWithSlideRight<T>(Widget page) {
    return Get.to<T>(
          () => page,
          transition: Transition.rightToLeft,
          duration: const Duration(milliseconds: 400),
        ) ??
        Future.value(null);
  }

  // التنقل مع حركة انزلاق من اليسار
  static Future<T?> toWithSlideLeft<T>(Widget page) {
    return Get.to<T>(
          () => page,
          transition: Transition.leftToRight,
          duration: const Duration(milliseconds: 400),
        ) ??
        Future.value(null);
  }

  // التنقل مع حركة انزلاق من الأسفل
  static Future<T?> toWithSlideUp<T>(Widget page) {
    return Get.to<T>(
          () => page,
          transition: Transition.downToUp,
          duration: const Duration(milliseconds: 500),
        ) ??
        Future.value(null);
  }

  // التنقل مع حركة تكبير
  static Future<T?> toWithScale<T>(Widget page) {
    return Get.to<T>(
          () => page,
          transition: Transition.zoom,
          duration: const Duration(milliseconds: 600),
        ) ??
        Future.value(null);
  }

  // التنقل مع حركة شفافية
  static Future<T?> toWithFade<T>(Widget page) {
    return Get.to<T>(
          () => page,
          transition: Transition.fade,
          duration: const Duration(milliseconds: 400),
        ) ??
        Future.value(null);
  }

  // التنقل مع حركة مخصصة
  static Future<T?> toWithCustomAnimation<T>(
    Widget page, {
    Transition? transition,
    Duration? duration,
    Curve? curve,
  }) {
    return Get.to<T>(
          () => page,
          transition: transition ?? Transition.rightToLeft,
          duration: duration ?? const Duration(milliseconds: 400),
          curve: curve ?? Curves.easeInOut,
        ) ??
        Future.value(null);
  }

  // التنقل باستخدام اسم الصفحة مع حركة
  static Future<T?> toNamedWithAnimation<T>(
    String routeName, {
    dynamic arguments,
  }) {
    return Get.toNamed<T>(
          routeName,
          arguments: arguments,
        ) ??
        Future.value(null);
  }
}

// ===== PageTransitionBuilder مخصص للتطبيق =====

class CustomPageTransitionBuilder extends PageTransitionsBuilder {
  @override
  Widget buildTransitions<T extends Object?>(
    PageRoute<T> route,
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    // حركة انزلاق من اليمين مع تأثير العمق
    const begin = Offset(1.0, 0.0);
    const end = Offset.zero;
    const curve = Curves.easeInOutCubic;

    var slideTween = Tween(begin: begin, end: end).chain(
      CurveTween(curve: curve),
    );

    var scaleTween = Tween(begin: 0.85, end: 1.0).chain(
      CurveTween(curve: curve),
    );

    var fadeTween = Tween(begin: 0.0, end: 1.0).chain(
      CurveTween(curve: Curves.easeIn),
    );

    return SlideTransition(
      position: animation.drive(slideTween),
      child: ScaleTransition(
        scale: animation.drive(scaleTween),
        child: FadeTransition(
          opacity: animation.drive(fadeTween),
          child: child,
        ),
      ),
    );
  }
}
