#!/usr/bin/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Start mDNSResponder daemon
# ==============================================================================
bashio::log.info "Starting mDNS Responder..."

# mdnsd runs as daemon except when using debug mode. We prefer non-daemon, so
# start in debug by default. It seems nto to generate a vast amount of
# messages.
exec /usr/sbin/mdnsd -debug
