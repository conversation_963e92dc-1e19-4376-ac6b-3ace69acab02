#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام زين للبيت الذكي - ملف التشغيل الرئيسي
"""

import sys
import os
import signal
import time
import subprocess
import threading
from datetime import datetime

def print_banner():
    """طباعة شعار النظام"""
    print("🏠 نظام زين للبيت الذكي المتكامل")
    print("=" * 50)
    print("🔗 مع دعم Home Assistant")
    print("📱 تطبيق Flutter + 🤖 أتمتة ذكية متقدمة")
    print("🚀 محرك الأتمتة المتقدمة مع دعم الشروط المتعددة")
    print("=" * 50)

def check_services():
    """فحص الخدمات الأساسية"""
    print("🔍 فحص المتطلبات...")
    
    services = ["mysql", "mosquitto"]
    all_good = True
    
    for service in services:
        try:
            result = subprocess.run(['systemctl', 'is-active', service], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {service} يعمل")
            else:
                print(f"⚠️ {service} غير نشط")
                all_good = False
        except:
            print(f"❌ لا يمكن فحص {service}")
            all_good = False
    
    if all_good:
        print("✅ جميع المتطلبات متوفرة")
    else:
        print("⚠️ بعض المتطلبات غير متوفرة - سيتم المتابعة")
    
    return all_good

def print_instructions():
    """طباعة تعليمات الاستخدام"""
    print("\n📋 تعليمات الاستخدام:")
    print("1️⃣ تأكد من تشغيل Home Assistant على http://zain.local:8123")
    print("2️⃣ تأكد من تشغيل MySQL وMQTT Broker")
    print("3️⃣ افتح تطبيق Flutter على الهاتف")
    print("4️⃣ استمتع بالنظام الذكي!")
    print("\n🔧 للإيقاف: اضغط Ctrl+C")

def signal_handler(signum, frame):
    """معالج إشارات النظام للإغلاق الآمن"""
    print(f"\n🛑 تم استلام إشارة الإغلاق {signum}")
    print("🔄 إغلاق النظام بأمان...")
    
    # إنهاء جميع العمليات
    try:
        subprocess.run(['pkill', '-f', 'run.py'], timeout=5)
        subprocess.run(['pkill', '-f', 'play.py'], timeout=5)
    except:
        pass
    
    print("✅ تم إغلاق النظام")
    os._exit(0)

def start_mqtt_system():
    """بدء نظام MQTT"""
    try:
        print("🚀 بدء نظام MQTT...")
        
        # تغيير المجلد إلى mqtt
        mqtt_dir = '/home/<USER>/myProject/mqtt'
        
        # بدء نظام MQTT
        mqtt_process = subprocess.Popen([
            sys.executable, 'run.py'
        ], cwd=mqtt_dir)
        
        return mqtt_process
        
    except Exception as e:
        print(f"❌ خطأ في بدء نظام MQTT: {e}")
        return None

def start_main_system():
    """بدء النظام الرئيسي"""
    try:
        print("🎯 بدء النظام الرئيسي...")
        
        # تغيير المجلد إلى المجلد الرئيسي
        main_dir = '/home/<USER>/myProject'
        
        # بدء النظام الرئيسي
        main_process = subprocess.Popen([
            sys.executable, 'play.py'
        ], cwd=main_dir)
        
        return main_process
        
    except Exception as e:
        print(f"❌ خطأ في بدء النظام الرئيسي: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    # طباعة الشعار
    print_banner()
    
    # فحص الخدمات
    check_services()
    
    # طباعة التعليمات
    print_instructions()
    
    # إعداد معالجات الإشارات
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # انتظار قصير قبل البدء
        print("\n⏳ بدء التشغيل خلال 3 ثوانٍ...")
        time.sleep(3)
        
        print("🚀 بدء نظام زين للبيت الذكي...")
        print("=" * 50)
        
        # بدء نظام MQTT
        mqtt_process = start_mqtt_system()
        if not mqtt_process:
            print("❌ فشل في بدء نظام MQTT")
            return
        
        # انتظار قصير لبدء MQTT
        time.sleep(3)
        
        # بدء النظام الرئيسي
        main_process = start_main_system()
        if not main_process:
            print("❌ فشل في بدء النظام الرئيسي")
            return
        
        print("🚀 جميع خدمات النظام تعمل الآن")
        print("تشغيل النظام الجديد المحسن...")
        
        # انتظار إنهاء العمليات
        try:
            # انتظار العمليتين
            while mqtt_process.poll() is None and main_process.poll() is None:
                time.sleep(1)
        except KeyboardInterrupt:
            signal_handler(signal.SIGINT, None)
            
    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
