---
version: 8.0.0
slug: matter_server
name: Matter Server
description: Matter WebSocket Server for Home Assistant Matter support.
url: >-
  https://github.com/home-assistant/addons/tree/master/matter_server
arch:
  - aarch64
  - amd64
discovery:
  - matter
hassio_api: true
homeassistant: 2024.6.0
# IPC is only used within the Add-on
host_ipc: false
host_network: true
host_dbus: true
image: homeassistant/{arch}-addon-matter-server
ingress: true
ingress_port: 5580
init: false
map:
  - type: addon_config
    read_only: false
options:
  log_level: info
  log_level_sdk: error
  beta: false
  enable_test_net_dcl: false
schema:
  log_level: list(verbose|debug|info|warning|error|critical)
  log_level_sdk: list(automation|detail|progress|error|none)?
  beta: bool?
  enable_test_net_dcl: bool?
  bluetooth_adapter_id: int?
  matter_server_args:
    - str?
  matter_server_version: str?
  matter_sdk_wheels_version: str?
ports:
  5580/tcp: null
stage: stable
startup: services
