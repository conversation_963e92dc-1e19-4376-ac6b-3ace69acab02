---
configuration:
  device:
    name: Device
    description: The serial port where the OpenThread RCP radio is attached.
  baudrate:
    name: Baudrate
    description: >-
      The serial port baudrate used to communicate with the Silicon Labs radio.
  flow_control:
    name: Hardware flow control
    description: Enable hardware flow control for serial port.
  network_device:
    name: Network Device
    description: >-
      <host:port> when connecting to a device via sockets (takes precedence
      over above configuration). Not recommended! See documentation for more
      information.
  autoflash_firmware:
    name: Automatically flash firmware
    description: >-
      Automatically flash OpenThread RCP firmware on Home Assistant Yellow and
      SkyConnect/Connect ZBT-1.
  otbr_log_level:
    name: OpenThread Border Router agent log level
    description: >-
      Set logging level of the OpenThread Border Router agent (otbr-agent).
  firewall:
    name: OTBR firewall
    description: >-
      Use OpenThread Border Router firewall to block unnecessary traffic.
  nat64:
    name: NAT64
    description: >-
      Enable IPv6 to IPv4 network address translation. This allows Thread
      devices to communicate with devices on the Internet.
network:
  8080/tcp: OpenThread Web port
  8081/tcp: OpenThread REST API port
