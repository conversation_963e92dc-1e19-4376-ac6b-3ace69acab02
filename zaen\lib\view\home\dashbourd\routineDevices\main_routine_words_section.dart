import 'package:expansion_tile_card/expansion_tile_card.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/view/room/double_tap/edit_names.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Widget wordsSectionWidget({
  required HomeController controller,
  required bool add,
  required bool isWords,
  required TextEditingController name2,
  required GlobalKey<FormState> kname2,
  required Function(bool) onWordsChanged,
  required bool edit,
}) {
  return add || isWords
      ? containerPageOption(
          content: ExpansionTileCard(
              onExpansionChanged: (words) {
                onWordsChanged(words);
              },
              initiallyExpanded: isWords ? true : false,
              duration: Duration(milliseconds: 400),
              baseColor: Colors.transparent,
              expandedColor: Colors.transparent,
              borderRadius: BorderRadius.all(Radius.circular(17)),
              shadowColor: Colors.transparent,
              contentPadding: EdgeInsets.zero,
              trailing: Icon(
                isWords
                    ? Icons.check_box_rounded
                    : Icons.check_box_outline_blank_rounded,
                color: isWords
                    ? AppColors.primaryColor
                    : AppColors.textColor2.withOpacity(0.5),
                size: controller.sized * 0.027,
              ),
              title: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  iconStyle(
                      icon: Icons.add_comment,
                      color: AppColors.warningColor,
                      size: controller.sized * 0.025),
                  SizedBox(
                    width: controller.sizedWidth * 0.03,
                  ),
                  Expanded(
                    child: txtStyle(
                      align: TextAlign.start,
                      txt: 'اضافة الى الكلمات الروتينية',
                    ),
                  ),
                ],
              ),
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: controller.sizedWidth * 0.02),
                  child: Divider(
                    color: AppColors.textColor2.withOpacity(0.3),
                    endIndent: 2,
                    indent: 2,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: controller.sizedWidth * 0.02),
                  child: Form(
                    key: kname2,
                    child: Container(
                      width: controller.sizedWidth * 0.85,
                      child: TextFormField(
                        controller: name2,
                        validator: (val) {
                          if (isWords && (val == null || val.isEmpty)) {
                            return 'يجب عليك ملء هذه الخانة';
                          }
                          for (var i = 0; i < name2.text.length; i++) {
                            if (arabic.contains(name2.text[i]) ||
                                name2.text[i].isNumericOnly) {
                              edit = true;
                            } else {
                              return 'قم بادخال حروف عربية او ارقام فقط';
                            }
                          }
                          for (var word in controller.routineWords!) {
                            if (editChar(word.fields['word']) ==
                                editChar(name2.text)) {
                              return 'هذه الجمله مستخدمه في الكلمات الروتينيه';
                            }
                          }

                          return null;
                        },
                        maxLength: 25,
                        showCursor: true,
                        cursorColor: AppColors.primary,
                        textDirection: TextDirection.rtl,
                        style: TextStyle(
                          color: AppColors.textPrimary,
                          fontSize: controller.sized * 0.012,
                          fontWeight: FontWeight.w500,
                        ),
                        onEditingComplete: () {
                          FocusManager.instance.primaryFocus?.unfocus();
                          if (kname2.currentState != null) {
                            var formdata = kname2.currentState;
                            formdata!.validate();
                          }
                        },
                        decoration: InputDecoration(
                          hintText: 'مثلاً: صباح الخير',
                          hintStyle: TextStyle(
                            color: AppColors.textHint,
                            fontSize: controller.sized * 0.011,
                            fontWeight: FontWeight.normal,
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: controller.sizedWidth * 0.04,
                            vertical: controller.sizedHight * 0.015,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppColors.border,
                              width: 1.0,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppColors.border,
                              width: 1.0,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppColors.primary,
                              width: 2.0,
                            ),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppColors.error,
                              width: 1.5,
                            ),
                          ),
                          focusedErrorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppColors.error,
                              width: 2.0,
                            ),
                          ),
                          suffixIcon: Icon(
                            Icons.edit_rounded,
                            size: controller.sized * 0.017,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ]),
        )
      : Container();
}
