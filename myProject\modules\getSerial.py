import sys
sys.path.append('/home/<USER>/myProject/resources')
# from s2 import *
import static as st

def getSerial():
    try:
        file=open('/proc/cpuinfo','r')
        for i in file:
            if i[0:6] == 'Serial':
                serial=i[10:]
        file.close()
    except:
        serial='Err'
    return serial

while 1:
    serial=getSerial()
    homeBridgeSerial = st.randomSerial()
    if serial!='Err':
        break

p=open(st.pathFiles+'serial.txt','w')
p.write(serial)
p.close()
p=open(st.pathFiles+'homeBridgeSerial.txt','w')
p.write(homeBridgeSerial)
p.close()
