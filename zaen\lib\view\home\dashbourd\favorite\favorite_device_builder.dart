import 'package:flutter/material.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/commands/ac.dart';
import 'package:zaen/shared/commands/tv.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/view/home/<USER>/favorite/ac.dart';
import 'package:zaen/view/home/<USER>/favorite/sw.dart';
import 'package:zaen/view/home/<USER>/favorite/tv.dart';
import 'package:zaen/view/home/<USER>/favorite/zain.dart';
import '../../../../shared/components/config.dart';
import 'package:zaen/shared/themes/app_colors.dart';

class FavoriteDeviceBuilder {
  // Build expanded device widget
  static Widget buildExpandedDevice(String deviceId, String room, int index,
      List stackDevices, BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (controller.rooms[room]['devices'][stackDevices[index]]['device']
              .contains('ZAIN'))
            ZAEN(
              tapOn_Switch_Icon: () {},
              device: controller.rooms[room]['devices'][stackDevices[index]],
              room: room,
              ZName: controller.rooms[room]['devices'][stackDevices[index]]
                  ['pubName'],
              Zmain: controller.rooms[room]['devices'][stackDevices[index]]
                      ['device'] ==
                  'ZAIN-Main',
              volume: controller.rooms[room]['devices'][stackDevices[index]]
                  ['volume'],
              hearing: controller.rooms[room]['devices'][stackDevices[index]]
                  ['hearing'],
              sil: controller.rooms[room]['devices'][stackDevices[index]]
                  ['sil'],
              lestin: controller.rooms[room]['devices'][stackDevices[index]]
                  ['lestin'],
              play: controller.rooms[room]['devices'][stackDevices[index]]
                  ['play'],
              tapOn_VolumeMute: () {
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room; // تعيين roomId
                  switchTap(
                      'sil',
                      controller.rooms[room]['devices'][stackDevices[index]]
                          ['sil'],
                      controller.rooms[room]['devices'][stackDevices[index]]
                          ['id']);
                  controller.update();
                }
              },
              tapOn_hearingMute: () {
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room; // تعيين roomId
                  switchTap(
                      'lestin',
                      controller.rooms[room]['devices'][stackDevices[index]]
                          ['lestin'],
                      controller.rooms[room]['devices'][stackDevices[index]]
                          ['id']);
                  controller.update();
                }
              },
              TapPlay: () {
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room; // تعيين roomId
                  switchTap(
                      'play',
                      controller.rooms[room]['devices'][stackDevices[index]]
                          ['play'],
                      controller.rooms[room]['devices'][stackDevices[index]]
                          ['id']);
                  controller.update();
                }
              },
              Play: () {
                if (client.connectionStatus!.state.name == 'connected') {
                  if (controller.rooms[room]['devices'][stackDevices[index]]
                          ['play'] ==
                      true) {
                    roomId = room; // تعيين roomId
                    switchTap(
                        'play',
                        controller.rooms[room]['devices'][stackDevices[index]]
                            ['play'],
                        controller.rooms[room]['devices'][stackDevices[index]]
                            ['id']);
                    controller.update();
                  }
                }
              },
              vState: (val) {
                if (client.connectionStatus!.state.name == 'connected') {
                  controller.rooms[room]['devices'][stackDevices[index]]
                      ['volume'] = val;
                  if (val?.toInt() == 0 &&
                      (controller.rooms[room]['devices'][stackDevices[index]]
                                  ['sil'] ??
                              false) ==
                          true) {
                    switchTap(
                        'sil',
                        controller.rooms[room]['devices'][stackDevices[index]]
                            ['sil'],
                        controller.rooms[room]['devices'][stackDevices[index]]
                            ['id']);
                  } else if (val?.toInt() != 0 &&
                      (controller.rooms[room]['devices'][stackDevices[index]]
                                  ['sil'] ??
                              false) ==
                          false) {
                    switchTap(
                        'sil',
                        controller.rooms[room]['devices'][stackDevices[index]]
                            ['sil'],
                        controller.rooms[room]['devices'][stackDevices[index]]
                            ['id']);
                  }
                  controller.update();
                }
              },
              hState: (val) {
                if (client.connectionStatus!.state.name == 'connected') {
                  controller.rooms[room]['devices'][stackDevices[index]]
                      ['hearing'] = val;
                  if (val?.toInt() == 0 &&
                      controller.rooms[room]['devices'][stackDevices[index]]
                              ['lestin'] ==
                          true) {
                    switchTap(
                        'lestin',
                        controller.rooms[room]['devices'][stackDevices[index]]
                            ['lestin'],
                        controller.rooms[room]['devices'][stackDevices[index]]
                            ['id']);
                  } else if (val?.toInt() != 0 &&
                      controller.rooms[room]['devices'][stackDevices[index]]
                              ['lestin'] ==
                          false) {
                    switchTap(
                        'lestin',
                        controller.rooms[room]['devices'][stackDevices[index]]
                            ['lestin'],
                        controller.rooms[room]['devices'][stackDevices[index]]
                            ['id']);
                  }
                  controller.update();
                }
              },
            ),
          if (controller.rooms[room]['devices'][stackDevices[index]]['device']
              .contains('TV'))
            TV(
              device: controller.rooms[room]['devices'][stackDevices[index]],
              room: room,
              tapOn_VolumeMute: () {
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room; // تعيين roomId
                  var device =
                      controller.rooms[room]['devices'][stackDevices[index]];
                  commandTvRemote(
                      (device['sil'] ?? false) == true ? 'SIL-OFF' : 'SIL-ON',
                      device,
                      room);
                }
              },
              tapOn_VolumeUp: () {
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room; // تعيين roomId
                  commandTvRemote(
                      'VOICE + 1',
                      controller.rooms[room]['devices'][stackDevices[index]],
                      room);
                }
              },
              tapOn_VolumeDown: () {
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room; // تعيين roomId
                  commandTvRemote(
                      'VOICE - 1',
                      controller.rooms[room]['devices'][stackDevices[index]],
                      room);
                }
              },
              tapOn_ChUp: () {
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room; // تعيين roomId
                  commandTvRemote(
                      'CH + 1',
                      controller.rooms[room]['devices'][stackDevices[index]],
                      room);
                }
              },
              tapOn_ChDown: () {
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room; // تعيين roomId
                  commandTvRemote(
                      'CH - 1',
                      controller.rooms[room]['devices'][stackDevices[index]],
                      room);
                }
              },
              tapOn_123: () {
                if (!controller.canControlDevices()) {
                  showNoPermissionDialog(
                      customMessage: 'ليس لديك صلاحية للتحكم في التلفاز');
                  return;
                }
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room;
                  TextEditingController ch = TextEditingController();
                  var device =
                      controller.rooms[room]['devices'][stackDevices[index]];

                  showBottomSheet(
                      enableDrag: true,
                      backgroundColor: Colors.transparent,
                      context: context,
                      builder: (context) {
                        return Container(
                          padding: const EdgeInsets.symmetric(horizontal: 25),
                          decoration: BoxDecoration(
                            color: AppColors.backgroundColor.withOpacity(0.975),
                            borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(25.5)),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              FractionallySizedBox(
                                widthFactor: 0.25,
                                child: Container(
                                  margin: const EdgeInsets.symmetric(
                                    vertical: 16.0,
                                  ),
                                  child: Container(
                                    height: 5.0,
                                    decoration: BoxDecoration(
                                      color:
                                          AppColors.textColor.withOpacity(0.5),
                                      borderRadius: const BorderRadius.all(
                                          Radius.circular(2.5)),
                                    ),
                                  ),
                                ),
                              ),
                              TextField(
                                autofocus: true,
                                controller: ch,
                                cursorColor: AppColors.primaryColor,
                                style: TextStyle(
                                    color: AppColors.textColor,
                                    fontSize: 25,
                                    fontWeight: FontWeight.w600),
                                textInputAction: TextInputAction.done,
                                textAlign: TextAlign.center,
                                decoration: InputDecoration(
                                  hintText: "ادخل الرقم",
                                  hintStyle: TextStyle(
                                    color: AppColors.textColor.withOpacity(0.5),
                                    fontSize: 20,
                                    fontWeight: FontWeight.normal,
                                  ),
                                  filled: true,
                                  fillColor: AppColors.backgroundColor2,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 15,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                      color:
                                          AppColors.textColor.withOpacity(0.3),
                                      width: 1.0,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                      color:
                                          AppColors.textColor.withOpacity(0.3),
                                      width: 1.0,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                      color: AppColors.primaryColor,
                                      width: 2.0,
                                    ),
                                  ),
                                ),
                                keyboardType: TextInputType.number,
                                maxLength: 10,
                              ),
                              CircleAvatar(
                                  radius: 20,
                                  backgroundColor: AppColors.primaryColor,
                                  child: IconButton(
                                    padding: const EdgeInsets.only(left: 10),
                                    onPressed: () {
                                      if (ch.text.isNotEmpty &&
                                          RegExp(r'^[0-9]+$')
                                              .hasMatch(ch.text)) {
                                        commandTvRemote(
                                            'CH = ${ch.text}', device, room);
                                        Navigator.pop(context);
                                      }
                                    },
                                    icon: Icon(
                                      Icons.arrow_back_ios,
                                      size: 30,
                                      color: AppColors.backgroundColor
                                          .withOpacity(0.85),
                                    ),
                                  )),
                              const SizedBox(
                                height: 25,
                              )
                            ],
                          ),
                        );
                      });
                  controller.update();
                }
              },
              tapOn_menu: () {
                if (!controller.canControlDevices()) {
                  showNoPermissionDialog(
                      customMessage: 'ليس لديك صلاحية للتحكم في التلفاز');
                  return;
                }
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room; // تعيين roomId
                  var device =
                      controller.rooms[room]['devices'][stackDevices[index]];

                  // تشغيل التلفاز إذا كان مطفأ
                  if (device['state'] == false) {
                    switchTap('state', device['state'], device['id']);
                  }
                  controller.rooms[room]['state'] = true;
                  controller.homeState = true;

                  // إرسال أمر القائمة مباشرة
                  final builder = MqttClientPayloadBuilder();
                  builder.addString('${device['id']} TV M');
                  client.publishMessage('${controller.homeId}/app/zain',
                      MqttQos.atLeastOnce, builder.payload!);
                  controller.update();
                }
              },
              tapOn_star: () {
                if (!controller.canControlDevices()) {
                  showNoPermissionDialog(
                      customMessage: 'ليس لديك صلاحية للتحكم في التلفاز');
                  return;
                }
              },
              sil: controller.rooms[room]['devices'][stackDevices[index]]
                      ['sil'] ??
                  false,
            ),
          if (controller.rooms[room]['devices'][stackDevices[index]]['device']
              .contains('SW'))
            SW(
              controller.rooms[room]['devices'][stackDevices[index]],
              room,
              (String? sw, bool? val) {
                // دالة للتعامل مع المفاتيح الفرعية
                if (!controller.canControlDevices()) {
                  showNoPermissionDialog(
                      customMessage: 'ليس لديك صلاحية للتحكم في المفاتيح');
                  return;
                }
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room;
                  var device =
                      controller.rooms[room]['devices'][stackDevices[index]];

                  if (sw == null) {
                    // التعامل مع المفتاح الرئيسي
                    switchTap('state', device['state'], device['id']);

                    final builder = MqttClientPayloadBuilder();
                    String pubMassege = device['id'] + ' SWITCH';

                    if (val == false) {
                      for (var j in controller
                          .rooms[room]['devices'][device['id']].keys
                          .toList()) {
                        if (j != 'id' &&
                            j != 'device' &&
                            j != 'state' &&
                            j != 'pub' &&
                            j != 'priv' &&
                            j != 'pubName' &&
                            j != 'privName') {
                          pubMassege += ' ${j}_OFF';
                          controller.rooms[room]['devices'][device['id']][j]
                              ['state'] = val;
                        }
                      }

                      roomState = false;
                      for (var j in controller.rooms[room]['devices'].values) {
                        if (j['state'] == true && j['device'] != 'ZAIN') {
                          roomState = true;
                        }
                      }
                    } else if (val == true) {
                      roomState = true;
                      for (var j in controller
                          .rooms[room]['devices'][device['id']].keys
                          .toList()) {
                        if (j != 'id' &&
                            j != 'device' &&
                            j != 'state' &&
                            j != 'pub' &&
                            j != 'priv' &&
                            j != 'pubName' &&
                            j != 'privName') {
                          pubMassege += ' ${j}_RUN';
                          controller.rooms[room]['devices'][device['id']][j]
                              ['state'] = val;
                        }
                      }
                    }

                    controller.rooms[room]['state'] = roomState;
                    if (val == true) {
                      controller.homeState = true;
                    } else {
                      controller.homeState = false;
                      for (var i in controller.rooms.values) {
                        if (i['state'] == true) {
                          controller.homeState = true;
                        }
                      }
                    }

                    builder.addString(pubMassege);
                    client.publishMessage('${controller.homeId}/app/zain',
                        MqttQos.atLeastOnce, builder.payload!);
                  } else {
                    // التعامل مع المفاتيح الفرعية
                    final builder = MqttClientPayloadBuilder();
                    String pubMassege = device['id'] + ' SWITCH';

                    // تحديث حالة المفتاح الفرعي
                    controller.rooms[room]['devices'][device['id']][sw]
                        ['state'] = !val!;
                    pubMassege += ' $sw${!val == true ? '_RUN' : '_OFF'}';

                    // فحص إذا كان هناك أي مفتاح فرعي مشغل
                    var F = false;
                    for (var j in controller
                        .rooms[room]['devices'][device['id']].keys
                        .toList()) {
                      if (j != 'id' &&
                          j != 'device' &&
                          j != 'state' &&
                          j != 'pub' &&
                          j != 'priv' &&
                          j != 'pubName' &&
                          j != 'privName') {
                        if (controller.rooms[room]['devices'][device['id']][j]
                                ['state'] ==
                            true) {
                          F = true;
                        }
                      }
                    }

                    // تحديث حالة المفتاح الرئيسي
                    if (F && device['state'] == false) {
                      switchTap('state', device['state'], device['id']);
                      roomState = true;
                    } else if (!F && device['state'] == true) {
                      switchTap('state', device['state'], device['id']);
                      roomState = false;
                      for (var j in controller.rooms[room]['devices'].values) {
                        if (j['state'] == true) {
                          roomState = true;
                        }
                      }
                    }

                    // تحديث حالة الغرفة والمنزل
                    controller.rooms[room]['state'] = roomState;
                    if (roomState == true) {
                      controller.homeState = true;
                    } else {
                      controller.homeState = false;
                      for (var i in controller.rooms.values) {
                        if (i['state'] == true) {
                          controller.homeState = true;
                        }
                      }
                    }

                    builder.addString(pubMassege);
                    client.publishMessage('${controller.homeId}/app/zain',
                        MqttQos.atLeastOnce, builder.payload!);
                  }
                  controller.update();
                }
              },
            ),
          if (controller.rooms[room]['devices'][stackDevices[index]]['device']
              .contains('AC'))
            AC(
              device: controller.rooms[room]['devices'][stackDevices[index]],
              room: room,
              switchState: (bool? state) {
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room;
                  var device =
                      controller.rooms[room]['devices'][stackDevices[index]];

                  if (state == true) {
                    // تشغيل المكيف
                    if (device['state'] == false) {
                      switchTap('state', device['state'], device['id']);
                    }

                    roomState = false;
                    for (var j in controller.rooms[room]['devices'].values) {
                      if (j['state'] == true) {
                        roomState = true;
                      }
                    }

                    controller.rooms[room]['state'] = roomState;
                    if (roomState == true) {
                      controller.homeState = true;
                    } else {
                      controller.homeState = false;
                      for (var i in controller.rooms.values) {
                        if (i['state'] == true) {
                          controller.homeState = true;
                        }
                      }
                    }

                    final builder = MqttClientPayloadBuilder();
                    builder.addString(
                        '${device['id']} AC RUN ${device['type'].toString() == 'مروحه' ? 'X' : device['degree'].toInt().toString()} VAN ${device['speed'].toString()} ${device['type'].toString() == 'تبريد' ? 'AC' : device['type'].toString() == 'تدفئه' ? 'HEAT' : 'VAN'}');
                    client.publishMessage('${controller.homeId}/app/zain',
                        MqttQos.atLeastOnce, builder.payload!);
                  } else {
                    // إيقاف المكيف
                    commandAc(false, device, room);
                  }
                  controller.update();
                }
              },
              sliderState: (double? value) {
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room;
                  var device =
                      controller.rooms[room]['devices'][stackDevices[index]];
                  device['degree'] = value;

                  // تحديث القيمة فقط بدون إرسال MQTT
                  controller.update();
                }
              },
              acTypeState: (int? type) {
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room;
                  var device =
                      controller.rooms[room]['devices'][stackDevices[index]];
                  device['type'] = type == 0
                      ? 'تدفئه'
                      : type == 1
                          ? 'تبريد'
                          : 'مروحه';

                  // تحديث النوع فقط بدون إرسال MQTT
                  controller.update();
                }
              },
              acSwingState: () {
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room;
                  var device =
                      controller.rooms[room]['devices'][stackDevices[index]];
                  switchTap('swing', device['swing'], device['id']);

                  // تحديث حالة التأرجح فقط بدون إرسال MQTT
                  controller.update();
                }
              },
              acSpeedsStateLeft: () {
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room; // تعيين roomId
                  var currentSpeed = controller.rooms[room]['devices']
                      [stackDevices[index]]['speed'];
                  controller.rooms[room]['devices'][stackDevices[index]]
                          ['speed'] =
                      currentSpeed != 1 ? currentSpeed - 1 : currentSpeed;
                  controller.update();
                }
              },
              acSpeedsStateRight: () {
                if (client.connectionStatus!.state.name == 'connected') {
                  roomId = room; // تعيين roomId
                  var currentSpeed = controller.rooms[room]['devices']
                      [stackDevices[index]]['speed'];
                  controller.rooms[room]['devices'][stackDevices[index]]
                          ['speed'] =
                      currentSpeed != 4 ? currentSpeed + 1 : currentSpeed;
                  controller.update();
                }
              },
              acRun: () {
                if (client.connectionStatus!.state.name == 'connected') {
                  commandAc(
                      true,
                      controller.rooms[room]['devices'][stackDevices[index]],
                      room);
                }
              },
            ),
        ],
      ),
    );
  }

  // Build collapsed device widget
  static Widget buildCollapsedDevice(
      String deviceId, String room, int index, List stackDevices) {
    final device = controller.rooms[room]['devices'][stackDevices[index]];
    final bool isConnected = _isDeviceConnected(device);
    final bool isZainMain = device['device'] == 'ZAIN-Main';
    final bool hasSwitch = _hasDeviceSwitch(device);

    return Card(
      color: Colors.transparent,
      elevation: 0,
      child: Center(
        child: Padding(
          padding: EdgeInsets.only(right: controller.sizedWidth * 0.01),
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Top row with device icon and status indicator
                Padding(
                  padding: EdgeInsets.only(
                    top: controller.sizedHight * 0.005,
                    bottom: controller.sizedHight * 0.009,
                    // left: controller.sizedWidth * 0.009,
                  ),
                  child: Row(
                    textDirection: TextDirection.rtl,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Device icon
                      iconStyle(
                        color: AppColors.warningColor.withOpacity(0.7),
                        icon: _getDeviceIcon(device['device']),
                      ),
                      // Status indicator (replaces menu icon)
                      _buildStatusIndicator(
                          device, isZainMain, isConnected, hasSwitch, room),
                    ],
                  ),
                ),
                // Room name row
                Row(
                  textDirection: TextDirection.rtl,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    txtStyle(
                      txt: controller.rooms[room]['privName'],
                      maxLines: 1,
                      align: TextAlign.start,
                    ),
                  ],
                ),
                SizedBox(height: controller.sizedHight * 0.02),
                // Device name row
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: txtStyle(
                        txt: _getDeviceDisplayName(device, room),
                        maxLines: 2,
                        align: TextAlign.start,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: controller.sizedHight * 0.03),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Get device icon based on device type
  static IconData _getDeviceIcon(String deviceType) {
    if (deviceType.contains('ZAIN')) return Icons.flutter_dash_rounded;
    if (deviceType.contains('TV')) return Icons.tv_rounded;
    if (deviceType.contains('SW')) return Icons.power_outlined;
    if (deviceType.contains('AC')) return Icons.ac_unit_rounded;
    return Icons.device_unknown;
  }

  // Get device display name
  static String _getDeviceDisplayName(Map device, String room) {
    if (device['device'].contains('ZAIN')) {
      return device['pubName'];
    } else if (device['device'].contains('SW')) {
      return device['priv'].split('_')[0];
    } else {
      return device['priv'];
    }
  }

  // Check if device is connected
  static bool _isDeviceConnected(Map device) {
    // For ZAIN devices, check if they have connection status
    if (device['device'].contains('ZAIN')) {
      return device.containsKey('connect') ? device['connect'] == true : true;
    }

    // For all other devices, check if they have a connect property first
    if (device.containsKey('connect')) {
      return device['connect'] == true;
    }

    // If no connect property, check based on device type and state
    if (device['device'].contains('SW') ||
        device['device'].contains('AC') ||
        device['device'].contains('TV')) {
      // Device is considered disconnected if state is null
      return device['state'] != null;
    }

    // Default to connected for unknown device types
    return true;
  }

  // Check if device has a switch
  static bool _hasDeviceSwitch(Map device) {
    // SW, AC, and TV devices have switches
    return device['device'].contains('SW') ||
        device['device'].contains('AC') ||
        device['device'].contains('TV');
  }

  // Build status indicator widget
  static Widget _buildStatusIndicator(Map device, bool isZainMain,
      bool isConnected, bool hasSwitch, String room) {
    if (isZainMain) {
      // Star icon for ZAIN-Main devices
      return iconStyle(
        icon: Icons.star_rounded,
        color: AppColors.warningColor,
      );
    } else if (!isConnected) {
      // "غير متصل" text for disconnected devices
      return Padding(
        padding: EdgeInsets.all(controller.sized * 0.005),
        child: txtStyle(
          txt: 'غير متصل',
          color: AppColors.errorColor,
          align: TextAlign.center,
        ),
      );
    } else if (hasSwitch) {
      // Switch style for switchable devices (SW and AC)
      return Directionality(
        textDirection: TextDirection.rtl,
        child: switchStyle(
            size: controller.sized * 0.0007,
            onChanged: (val) {
              if (!controller.canControlDevices()) {
                showNoPermissionDialog(
                    customMessage: 'ليس لديك صلاحية للتحكم في الملحقات');
                return;
              }
              roomId = room;
              sswitch() {
                switchTap(
                    'state',
                    controller.rooms[room]['devices'][device['id']]['state'],
                    controller.rooms[room]['devices'][device['id']]['id']);

                final builder = MqttClientPayloadBuilder();

                String pubMassege = controller.rooms[room]['devices']
                        [device['id']]['id'] +
                    ' SWITCH';

                if (val == false) {
                  for (var j in controller
                      .rooms[room]['devices'][controller.rooms[room]['devices']
                          [device['id']]['id']]
                      .keys
                      .toList()) {
                    if (j != 'id' &&
                        j != 'device' &&
                        j != 'state' &&
                        j != 'pub' &&
                        j != 'priv' &&
                        j != 'pubName' &&
                        j != 'privName') {
                      pubMassege += ' ' + j + '_OFF';
                      // setState(() {
                      controller.rooms[room]['devices'][controller.rooms[room]
                          ['devices'][device['id']]['id']][j]['state'] = val;
                      // });
                    }
                  }

                  roomState = false;
                  for (var j in controller.rooms[room]['devices'].values) {
                    if (j['state'] == true && j['type'] != 'ZAIN') {
                      roomState = true;
                      print(1111111112222222);
                    }
                    // setState(() {

                    // });
                  }
                } else if (val == true) {
                  // في هذه الحاله يجب ان يذهب الى قاعده البيانات و استخراج اخر حاله مخزنه في المساعد
                  roomState = true;
                  for (var j in controller
                      .rooms[room]['devices'][controller.rooms[room]['devices']
                          [device['id']]['id']]
                      .keys
                      .toList()) {
                    if (j != 'id' &&
                        j != 'device' &&
                        j != 'state' &&
                        j != 'pub' &&
                        j != 'priv' &&
                        j != 'pubName' &&
                        j != 'privName') {
                      pubMassege += ' ' + j + '_RUN';
                      // setState(() {
                      controller.rooms[room]['devices'][controller.rooms[room]
                          ['devices'][device['id']]['id']][j]['state'] = val;
                      // });
                    }
                  }
                }
                controller.rooms[room]['state'] = roomState;
                if (val == true) {
                  controller.homeState = true;
                } else {
                  controller.homeState = false;
                  for (var i in controller.rooms.values) {
                    if (i['state'] == true) {
                      controller.homeState = true;
                    }
                  }
                }
                builder.addString(pubMassege);

                client.publishMessage(controller.homeId + "/app/zain",
                    MqttQos.atLeastOnce, builder.payload!);

                controller.update();
              }

              ;
              controller.rooms[room]['devices'][device['id']]['device']
                      .contains('AC')
                  ? commandAc(val!,
                      controller.rooms[room]['devices'][device['id']], room)
                  : controller.rooms[room]['devices'][device['id']]['device']
                          .contains('TV')
                      ? commandTvSw(val!,
                          controller.rooms[room]['devices'][device['id']], room)
                      : sswitch();
            },
            value: controller.rooms[room]['devices'][device['id']]['state'] ??
                false),
      );
    } else {
      // Empty container for devices without status indicators
      return Container();
    }
  }
}
