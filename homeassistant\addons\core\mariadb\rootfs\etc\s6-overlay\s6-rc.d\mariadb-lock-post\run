#!/command/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Lock tables for backups
# ==============================================================================

declare MARIADB_LOCK_CORE_PID
MARIADB_LOCK_CORE_PID=$(s6-svstat -o pid "/run/service/mariadb-lock-core")
# shellcheck disable=SC2086
if bashio::var.equals ${MARIADB_LOCK_CORE_PID} -1; then
    bashio::log.error "Invalid mariadb-lock-core process id ${MARIADB_LOCK_CORE_PID}"
    bashio::exit.nok
fi

# File descriptor &4 is used as stdin for mysql, because &0 is closed after the mariadb-lock-core service is started
# shellcheck disable=SC2086
echo "FLUSH TABLES WITH READ LOCK;" > /proc/${MARIADB_LOCK_CORE_PID}/fd/4

bashio::log.info "MariaDB tables locked"
