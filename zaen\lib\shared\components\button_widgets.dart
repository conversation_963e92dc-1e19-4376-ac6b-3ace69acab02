import 'package:flutter/material.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/components/config.dart';

Widget submitButtom({String text = "موافق", required Function() onPressed}) {
  return OutlinedButton(
      child: Text(
        text,
        textDirection: TextDirection.rtl,
        style: TextStyle(
            color: AppColors.backgroundColor2,
            fontSize: controller.sized * 0.015,
            fontWeight: FontWeight.bold),
      ),
      style: OutlinedButton.styleFrom(
        side: BorderSide.none,
        backgroundColor: AppColors.successColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(20),
          ),
        ),
      ),
      onPressed: onPressed);
}

Widget delButtom({String text = "حذف", required Function() onPressed}) {
  return OutlinedButton(
      child: Text(
        text,
        textDirection: TextDirection.rtl,
        style: TextStyle(
            color: AppColors.backgroundColor2,
            fontSize: controller.sized * 0.015,
            fontWeight: FontWeight.bold),
      ),
      style: OutlinedButton.styleFrom(
        side: BorderSide.none,
        backgroundColor: AppColors.error,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(20),
          ),
        ),
      ),
      onPressed: onPressed);
}

Widget switchStyle(
    {required Function(bool?) onChanged,
    required bool value,
    double size = 0}) {
  if (size == 0) {
    size = controller.sized * 0.0008;
  }
  return Transform.scale(
    scale: size,
    child: Switch(
        activeColor: AppColors.success,
        inactiveThumbColor: AppColors.backgroundColor3,
        activeTrackColor: AppColors.success.withOpacity(0.2),
        inactiveTrackColor: AppColors.textColor2.withOpacity(0.8),
        trackOutlineWidth: WidgetStateProperty.resolveWith((states) {
          return 0.5; // Ширина контура при выключенном состоянии
        }),
        trackOutlineColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.success
                .withOpacity(0.5); // Цвет контура при включенном состоянии
          }
          return AppColors.textSecondary
              .withOpacity(0.2); // Цвет контура при выключенном состоянии
        }),
        value: value,
        onChanged: onChanged),
  );
}

/// مفتاح مخصص مع أيقونة داخلية للثيم - نفس تصميم المفتاح العادي
Widget themeSwitchStyle({
  required Function(bool?) onChanged,
  required bool value,
  double size = 0,
}) {
  if (size == 0) {
    size = controller.sized * 0.001;
  }

  return Transform.scale(
    scale: size,
    child: GestureDetector(
      onTap: () => onChanged(!value),
      child: Container(
        width: 51, // نفس عرض المفتاح العادي
        height: 31, // نفس ارتفاع المفتاح العادي
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.5),
          gradient: LinearGradient(
            colors: value
                ? [
                    const Color(0xFF1A1A2E), // بنفسجي ليلي داكن
                    const Color(0xFF16213E), // أزرق ليلي عميق
                  ]
                : [
                    const Color(0xFFFF8F00), // برتقالي ذهبي
                    const Color(0xFFFFB300), // برتقالي فاتح
                  ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: value
                  ? const Color(0xFF1A1A2E).withOpacity(0.6)
                  : const Color(0xFFFF8F00).withOpacity(0.4),
              blurRadius: 8,
              offset: const Offset(0, 3),
              spreadRadius: 0,
            ),
          ],
        ),
        child: AnimatedAlign(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          alignment: value ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.all(2),
            width: 27,
            height: 27,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.white, // أبيض في كلا الوضعين
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.25),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 150),
              child: Icon(
                value ? Icons.nightlight_round : Icons.wb_sunny_rounded,
                key: ValueKey(value),
                size: 16,
                color: value
                    ? const Color(0xFF1A1A2E) // بنفسجي ليلي داكن للقمر
                    : const Color(0xFFFF8F00), // برتقالي ذهبي للشمس
              ),
            ),
          ),
        ),
      ),
    ),
  );
}
