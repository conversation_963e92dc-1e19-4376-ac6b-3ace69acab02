# C/C++ build system timings
generate_cxx_metadata
  execute-generate-process
    [gap of 16ms]
    exec-configure 4124ms
    [gap of 65ms]
  execute-generate-process completed in 4205ms
  [gap of 42ms]
generate_cxx_metadata completed in 4260ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 17ms
  [gap of 34ms]
generate_cxx_metadata completed in 56ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 15ms
  [gap of 35ms]
generate_cxx_metadata completed in 66ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 22ms
  [gap of 22ms]
generate_cxx_metadata completed in 49ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 13ms
  [gap of 17ms]
generate_cxx_metadata completed in 34ms

