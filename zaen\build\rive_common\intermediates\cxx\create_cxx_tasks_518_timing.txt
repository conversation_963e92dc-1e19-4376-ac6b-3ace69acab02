# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 24ms]
    create-ARM64_V8A-model 11ms
    create-X86-model 14ms
    create-X86_64-model 15ms
    create-module-model 12ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 15ms
    create-X86-model 12ms
    create-X86_64-model 15ms
    create-module-model 14ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    [gap of 18ms]
  create-initial-cxx-model completed in 219ms
create_cxx_tasks completed in 226ms

