# C/C++ build system timings
generate_cxx_metadata
  execute-generate-process
    [gap of 15ms]
    exec-configure 3540ms
    [gap of 45ms]
  execute-generate-process completed in 3600ms
  [gap of 36ms]
generate_cxx_metadata completed in 3649ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 19ms
  [gap of 33ms]
generate_cxx_metadata completed in 56ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 18ms
  [gap of 24ms]
generate_cxx_metadata completed in 46ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 18ms
  [gap of 19ms]
generate_cxx_metadata completed in 42ms

