#!/command/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Start openWakeWord service
# ==============================================================================
flags=()

if bashio::config.true 'debug_logging'; then
    flags+=('--debug')
fi

# shellcheck disable=SC2068
exec python3 -m wyoming_openwakeword \
    --uri 'tcp://0.0.0.0:10400' \
    --preload-model 'ok_nabu' \
    --custom-model-dir /share/openwakeword \
    --threshold "$(bashio::config 'threshold')" \
    --trigger-level "$(bashio::config 'trigger_level')" ${flags[@]}
