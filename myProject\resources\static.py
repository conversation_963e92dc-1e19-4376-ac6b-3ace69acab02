import RPi.GPIO as GPIO
import sys
import random
sys.path.append('/home/<USER>/myProject/modules')
import dht11
import netifaces as ni
from multiprocessing import Manager

def randomSerial():
    hex_chars = "ABCDEF1234567890"
    serial = ":".join(''.join(random.choice(hex_chars) for _ in range(2)) for _ in range(6))
    return serial


GPIO.setwarnings(False)
GPIO.setmode(GPIO.BCM)
instance = dht11.DHT11(pin=4)
timerState='OFF'
ip='zain.local'
pathFiles='/home/<USER>/myProject/resources/sharedFiles/'
pathWaves='/home/<USER>/myProject/resources/wav/'
device_nb=ni.ifaddresses('wlan0')[17][0]['addr']
homeBridgeSerial=open('/home/<USER>/myProject/resources/sharedFiles/homeBridgeSerial.txt','r')
homeBridgeSerial = homeBridgeSerial.readline()

shortcuts = {
    'WEATHER':['WEATHER','HEAT','HUMID','STORM','COLD','RAIN'],
    'TIME':['وقت','ساع','AM','PM','دقاءق','دقايق','دقيق','ساع'],
    'DATE':['Date','DAY','شهر','سن',],
    'PRAY':['PRAY','fajr','asr','maghrib','isha','zuhr',],
    'DEVICE':['AC%','TV%','LIGHT%','SWITCH%','VAN',],
    'DAY':['Sat','Sun','Mon','Tue','Wed','Thu','Fri'],
    'MONTH':['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec',],
    'PLAY':['+','-','*','/','RUN','OFF','STOP','DELETE','WITHOUT','RE','EXCEPT','NOT','SILENTE','CH','RC','CHANGE','VOICE',],
    'ALARM':['ALARM','REMIND','JOB'],
}
filterWords = ['AC%','TV%','LIGHT%','SWITCH%','VAN','ALL',
               'Sat','Sun','Mon','Tue','Wed','Thu','Fri','DAY','Date','MONTH','YEAR',
               'Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec',
               '+','-','*','/','RUN','OFF','STOP','DELETE','WITHOUT','RE','EXCEPT','NOT','SILENTE','CH','RC','CHANGE','JOB','VOICE',
               'PRAY','fajr','asr','maghrib','isha','zuhr','AM','PM','ALARM','REMIND',
               'WEATHER','COLD','HEAT','HUMID','STORM','STATE','AUTO',
               'ساع','دقيق','دقاءق','دقايق','نص','نصف','ربع','ثلث','سن','سنين','شهر','وقت','ثانيه','ثواني','بعد','و','من','الي','في','على',
               'ROOM','HOME','TOMORROW','WEEK','مءقت','عداد',]

NumberS = {
            '٠' : '0', 'صفر' : '0', 'عشره' : '10', 'عشر' : '10','عشرين' : '20', 'عشرون' : '20', 'ثلاثين' : '30', 'ثلاثون' : '30', 'اربعين' : '40', 'اربعون' : '40',
            'خمسين' : '50', 'خمسون' : '50', 'ستين' : '60', 'ستون' : '60', 'سبعين' : '70', 'سبعون' : '70',
            'ثمانين' : '80', 'ثمانون' : '80', 'تسعين' : '90', 'تسعون' : '90', 'مءه' : '100', 'ميه' : '100', 'ميت' : '100', 'مءت' : '100',
            'الف' : '1000', 'مليون' : '1000000', 'ملايين' : '1000000', 'مليار' : '1000000000',
            '١' : '1', 'واحد' : '1', 'حادي' : '1', 'اول' : '1', 'احد' : '1',
            '٢' : '2', 'ثانيه' : '2', 'ثاني' : '2', 'اثنين' : '2', 'اثنان' : '2', 'ثنتين' : '2',
            '٣' : '3', 'ثلاثه' : '3', 'ثالثه' : '3', 'ثالث' : '3', 'ثلاث' : '3',
            '٤' : '4', 'اربعه' : '4', 'رابعه' : '4', 'اربع' : '4', 'رابع' : '4', 
            '٥' : '5',  'خمسه' : '5', 'خامسه' : '5', 'خمس' : '5', 'خامس' : '5',
            '٦' : '6',  'سته' : '6' , 'سادسه' : '6', 'ست' : '6' , 'سادس' : '6',
            '٧' : '7', 'سبعه' : '7', 'سابعه' : '7', 'سبع' : '7', 'سابع' : '7',
            '٨' : '8', 'ثمانيه' : '8', 'ثامنه' : '8', 'ثمان' : '8', 'ثامن' : '8',
            '٩' : '9', 'تسعه' : '9', 'تاسعه' : '9', 'تسع' : '9', 'تاسع' : '9', 
             }

devices={'مكيف' : 'AC%', 'تكييف' : 'AC%', 'كندشن' : 'AC%', 'كوندشن' : 'AC%', 'مزجان' : 'AC%', 'مزجن' : 'AC%',
            'تلفاز' : 'TV%', 'تلفزيون' : 'TV%', 'تيلفزيون' : 'TV%', 'تليفزيون' : 'TV%',
            'ضوء' : 'LIGHT%', 'ضو' : 'LIGHT%', 'ضي' : 'LIGHT%', 'اضاء' : 'LIGHT%', 'انار' : 'LIGHT%','ظي' : 'LIGHT%', 'ظو' : 'LIGHT%' , 'ضواو' : 'ALL LIGHT%', 'ظواو' : 'ALL LIGHT%', 'اضواء' : 'ALL LIGHT%', 'مصباح' : 'LIGHT%' , 'مصابيح' : 'ALL LIGHT%', 'نور' : 'LIGHT%' , 'انوار' : 'ALL LIGHT%',
            'مفتاح' : 'SWITCH%', 'مفاتيح' : 'ALL SWITCH%',
            'مروحه' : 'VAN', 'مراوح' : 'ALL VAN'
            }

wdays = {'سبت' :'Sat','احد' :'Sun', 'اثنين' :'Mon', 'اثنان' :'Mon', 'ثلاثاء' :'Tue', 'اربعاء' :'Wed','خميس' :'Thu', 'جمعه' :'Fri'}

months = {'شهر 1' : 'Jan', 'شهر 2' : 'Feb', 'شهر 3' : 'Mar', 'شهر 4' : 'Apr', 'شهر 5' : 'May', 'شهر 6' : 'Jun',
            'شهر 7' : 'Jul', 'شهر 8' : 'Aug', 'شهر 9' : 'Sep', 'شهر 10' : 'Oct', 'شهر 11' : 'Nov', 'شهر 12' : 'Dec',
          'MONTH 1' : 'Jan', 'MONTH 2' : 'Feb', 'MONTH 3' : 'Mar', 'MONTH 4' : 'Apr', 'MONTH 5' : 'May', 'MONTH 6' : 'Jun',
            'MONTH 7' : 'Jul', 'MONTH 8' : 'Aug', 'MONTH 9' : 'Sep', 'MONTH 10' : 'Oct', 'MONTH 11' : 'Nov', 'MONTH 12' : 'Dec',
          
            'كانون ثاني' : 'Jan', 'شباط' : 'Feb', 'اذار' : 'Mar', 'نيسان' : 'Apr', 'ايار' : 'May', 'حزيران' : 'Jun',
            'تموز' : 'Jul', 'ايلول' : 'Sep', 'تشرين اول' : 'Oct', 'تشرين ثاني' : 'Nov', 'كانون ثاني' : 'Dec',
            'يناير' : 'Jan', 'فبراير' : 'Feb', 'مارس' : 'Mar', 'ابريل' : 'Apr', 'مايو' : 'May', 'يونيو' : 'Jun',
            'يوليو' : 'Jul', 'اغسطس' : 'Aug', 'سبتمبر' : 'Sep', 'اكتوبر' : 'Oct', 'نوفمبر' : 'Nov', 'ديسمبر' : 'Dec'
            }

words={'زاءد':'+', 'زيد':'+', 'زد':'+', 'زياده':'+', 'اضافه':'+', 'ضيف':'+', 'ضف' : '+', 'رفع':'+', 'ضرب' : '*', 'وطي' : '-', 'x' : '*',
        'ضوي' : 'RUN', 'عمل' : 'RUN', 'سوي' : 'RUN', 'ضاوي' : 'RUN',
        'طفي' : 'OFF', 'غلق' : 'OFF','نهي' : 'OFF', 'اطفاء':'OFF','طفء':'OFF', 'اطفء':'OFF',
        'انهاء':'DELETE', 'الغاء':'DELTETE',
        'ايقاف':'STOP','اوقف':'STOP','وقف':'STOP','توقيف':'STOP', 'قف' : 'STOP',
        'استثني' : 'EXCEPT','استثناء' : 'EXCEPT',
        'دون' : 'WITHOUT','بلا' : 'WITHOUT','الا' : 'WITHOUT','عدا' : 'WITHOUT',
        'كل' : 'ALL','جميع' : 'ALL',
        'ليس' : 'NOT', 'مش' : 'NOT', 'حذف' : 'DELETE', 'لغي' : 'DELETE',
        'تكرار' : 'RE', 'داءما' : 'RE', 'كتم' : 'SILENTE',
        'قناه' : 'CH', 'محطه' : 'CH', 'ستلايت' : 'RC', 'رسيفر' : 'RC',
        'صلاه' : 'PRAY', 'اذن' : 'PRAY', 'اذان' : 'PRAY', 
        'مهمه' : 'JOB', 'مهمات' : 'JOB', 'مهام' : 'JOB',
        'صبح' : 'AM','صباح' : 'AM','ليل' : 'PM', 'مسا' : 'PM', 'تاريخ' : 'Date',
        'سنويا':'ALL YEAR','سنه':'YEAR','سنتين':'2 YEAR','سنين':'YEAR','سنوات':'YEAR','شهر يا':'ALL MONTH','شهر':'MONTH','اشهر':'MONTH','شهور':'MONTH',
        'فجر' : 'fajr',  'عصر' : 'asr', 'مغرب' : 'maghrib', 'عشاء' : 'isha',
        'يوم يا' : 'ALL DAY', 'يوم' : 'DAY', 'ايام' : 'DAY', 'حال' : 'STATE', 'وضع' : 'STATE',
        'غرفه' : 'ROOM','غرف' : 'ALL ROOM','بيت' : 'HOME','منزل' : 'HOME','دار' : 'HOME','بكرا' : 'TOMORROW','بكره' : 'TOMORROW','غد' : 'TOMORROW','اسبوعيا' : 'ALL WEEK',  'اسبوع' : 'WEEK','اسابيع' : 'WEEK',
        'اجواء':'WEATHER', 'جو':'WEATHER', 'طقس':'WEATHER', 'حراره':'HEAT', 'حر':'HEAT', 'حار':'HEAT', 'سخن':'HEAT', 'رطوبه':'HUMID',  'عاصف':'STORM', 'عواصف':'STORM', 'اوتو':'AUTO', 'اوتوماتيكي':'AUTO', }

irr ={'شغل' : 'RUN', 'فتح' : 'RUN', 'ضبط' : 'RUN', 'حضر' : 'RUN', 'حط' : 'RUN', 'عير' : 'RUN', 'حدد' : 'RUN',
         'سكر' : 'OFF','غير' : 'CHANGE', 'نقل' : 'CHANGE', 'عدل' : 'CHANGE', 'برد':'COLD', 'دفء':'HEAT',
         'قسم' : '/', 'جمع' : '+', 'قوي' : '+', 'طرح' : '-', 'نقص' : '-', 'قلل' : '-', 'صمت' : 'SILENTE', 'صوت' : 'VOICE',
         'ذكر' : 'REMIND', 'صحي' : 'ALARM', 'يقذ' : 'ALARM','نبه' : 'ALARM',
         'مطر' : 'RAIN', 'ظهر' : 'zuhr',} # فاعل, تفعيل, فعال,مفعول

wordS={'ساع','دقيق','دقاءق','دقايق','ربع','ثلث','نص','نصف','دفء','برد','صوت','وقت','يوم','شهر','جو','ثانيه','ثواني',}
wdayN = {'Sat' :1,'Sun' :2, 'Mon' :3,'Tue' :4, 'Wed' :5,'Thu' :6, 'Fri' :7}
nweeks = {'Sat' :5,'Sun' :6, 'Mon' :0,'Tue' :1, 'Wed' :2,'Thu' :3, 'Fri' :4}
nmonths = {'Jan':1,'Feb':2,'Mar':3,'Apr':4,'May':5,'Jun':6,'Jul':7,'Aug':8,'Sep':9,'Oct':10,'Nov':11,'Dec':12}

doubleChar=['ان','ين','تان','تين']
doubleWord=['ساع','دقيق','رقم','عدد','درج','شهر','يوم']
manager = Manager()
routineWords = manager.list()