import 'package:flutter/material.dart';
import 'package:get/get.dart';

// ===== حركات الانتقال بين الصفحات =====

// حركة انزلاق من اليمين إلى اليسار
class SlideRightToLeftRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlideRightToLeftRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 400),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOutCubic;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        );
}

// حركة انزلاق من اليسار إلى اليمين
class SlideLeftToRightRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlideLeftToRightRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 400),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(-1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOutCubic;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        );
}

// حركة انزلاق من الأسفل إلى الأعلى
class SlideBottomToTopRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlideBottomToTopRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 500),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(0.0, 1.0);
            const end = Offset.zero;
            const curve = Curves.easeOutCubic;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            var fadeTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: Curves.easeIn),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: FadeTransition(
                opacity: animation.drive(fadeTween),
                child: child,
              ),
            );
          },
        );
}

// حركة تكبير مع شفافية
class ScaleRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  ScaleRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 600),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const curve = Curves.elasticOut;

            var scaleTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: curve),
            );

            var fadeTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: Curves.easeIn),
            );

            return ScaleTransition(
              scale: animation.drive(scaleTween),
              child: FadeTransition(
                opacity: animation.drive(fadeTween),
                child: child,
              ),
            );
          },
        );
}

// حركة دوران مع تكبير
class RotationScaleRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  RotationScaleRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 800),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            var rotationTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: Curves.elasticOut),
            );

            var scaleTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: Curves.easeOutBack),
            );

            var fadeTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: Curves.easeIn),
            );

            return RotationTransition(
              turns: animation.drive(rotationTween),
              child: ScaleTransition(
                scale: animation.drive(scaleTween),
                child: FadeTransition(
                  opacity: animation.drive(fadeTween),
                  child: child,
                ),
              ),
            );
          },
        );
}

// حركة انزلاق مع تأثير العمق
class SlideWithDepthRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlideWithDepthRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 500),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;

            var slideTween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: Curves.easeInOutCubic),
            );

            var scaleTween = Tween(begin: 0.8, end: 1.0).chain(
              CurveTween(curve: Curves.easeOutCubic),
            );

            var fadeTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: Curves.easeIn),
            );

            return SlideTransition(
              position: animation.drive(slideTween),
              child: ScaleTransition(
                scale: animation.drive(scaleTween),
                child: FadeTransition(
                  opacity: animation.drive(fadeTween),
                  child: child,
                ),
              ),
            );
          },
        );
}

// حركة مخصصة للدخول إلى الغرف
class RoomEntranceRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  RoomEntranceRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 700),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // حركة انزلاق من اليمين مع تأثير الباب المفتوح
            const slideBegin = Offset(1.0, 0.0);
            const slideEnd = Offset.zero;

            // حركة تكبير تدريجي
            const scaleBegin = 0.7;
            const scaleEnd = 1.0;

            // حركة دوران خفيف
            const rotationBegin = 0.05;
            const rotationEnd = 0.0;

            var slideTween = Tween(begin: slideBegin, end: slideEnd).chain(
              CurveTween(curve: Curves.easeOutCubic),
            );

            var scaleTween = Tween(begin: scaleBegin, end: scaleEnd).chain(
              CurveTween(curve: Curves.elasticOut),
            );

            var rotationTween =
                Tween(begin: rotationBegin, end: rotationEnd).chain(
              CurveTween(curve: Curves.easeOutBack),
            );

            var fadeTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: Curves.easeIn),
            );

            return SlideTransition(
              position: animation.drive(slideTween),
              child: RotationTransition(
                turns: animation.drive(rotationTween),
                child: ScaleTransition(
                  scale: animation.drive(scaleTween),
                  child: FadeTransition(
                    opacity: animation.drive(fadeTween),
                    child: child,
                  ),
                ),
              ),
            );
          },
        );
}

// حركة مخصصة أخرى للغرف - تأثير الباب المنزلق
class SlidingDoorRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlidingDoorRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 600),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return AnimatedBuilder(
              animation: animation,
              builder: (context, child) {
                // تأثير الباب المنزلق من اليمين
                final slideValue =
                    Curves.easeInOutCubic.transform(animation.value);
                final scaleValue = Curves.elasticOut.transform(animation.value);
                final fadeValue = Curves.easeIn.transform(animation.value);

                return Transform.translate(
                  offset: Offset(
                      (1 - slideValue) * MediaQuery.of(context).size.width, 0),
                  child: Transform.scale(
                    scale: 0.8 + (0.2 * scaleValue),
                    child: Opacity(
                      opacity: fadeValue,
                      child: ClipRRect(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(25 * (1 - slideValue)),
                          bottomLeft: Radius.circular(25 * (1 - slideValue)),
                        ),
                        child: child,
                      ),
                    ),
                  ),
                );
              },
            );
          },
        );
}
