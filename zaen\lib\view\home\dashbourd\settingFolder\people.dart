import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/settings/settings.dart';
import 'package:mysql1/mysql1.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:zaen/controller/controller.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/modules/local/mysql_helper.dart';

// دالة لعرض نافذة إدارة المستخدمين
Future<void> showUsersManagementDialog(BuildContext context) async {
  PageController pageController = PageController();
  TextEditingController searchController = TextEditingController();
  List<Map<String, dynamic>> allUsers = [];
  List<Map<String, dynamic>> filteredUsers = [];
  Map<String, dynamic>? selectedUser;

  // متغيرات إدارة الصلاحيات
  List<String> permissions = [];
  bool hasChanges = false;
  String? lastLoadedUserMac; // لتتبع آخر مستخدم تم تحميل صلاحياته

  // دالة لتحليل صلاحيات المستخدم وتحميلها
  void loadUserPermissions(String userAccess, String userMac) {
    // التحقق من أن هذا مستخدم جديد
    if (lastLoadedUserMac == userMac) {
      return; // لا نحتاج لإعادة التحميل
    }

    // إعادة تعيين القائمة
    permissions.clear();
    hasChanges = false;
    lastLoadedUserMac = userMac;

    // إذا كان الوصول كامل أو عدم وصول، لا نحتاج لتحليل
    if (userAccess == 'full' || userAccess == 'never') {
      return;
    }

    // خريطة تحويل من الرموز إلى أسماء الصلاحيات
    Map<String, String> accessCodeToPermission = {
      'data': 'بيانات النظام',
      'dev&rooms': 'الملحقات والغرف',
      'routinesWords': 'الكلمات الروتينية',
      'tasks': 'المهام المجدولة',
      'map': 'الخريطة',
      'wifi': 'إدارة الشبكة والمستخدمين'
    };

    // تقسيم النص وتحليل الصلاحيات
    List<String> accessCodes =
        userAccess.split(',').map((e) => e.trim()).toList();

    for (String code in accessCodes) {
      if (accessCodeToPermission.containsKey(code)) {
        String permissionName = accessCodeToPermission[code]!;
        if (!permissions.contains(permissionName)) {
          permissions.add(permissionName);
        }
      }
    }

    print('تم تحميل صلاحيات المستخدم: $permissions');
  }

  // جلب المستخدمين من قاعدة البيانات
  try {
    final conn = await MySqlConnection.connect(ConnectionSettings(
      host: controller.hostZain.value,
      user: 'root',
      db: 'zain',
      password: 'zain',
      characterSet: CharacterSet.UTF8,
    ));

    var results =
        await conn.query('SELECT name, type, mac, access FROM phones');
    await conn.close();

    for (var row in results) {
      allUsers.add({
        'name': row['name'],
        'type': row['type'],
        'mac': row['mac'],
        'access': row['access'],
      });
    }
    filteredUsers = List.from(allUsers);
  } catch (e) {
    print('خطأ في جلب المستخدمين: $e');
    return;
  }

  AwesomeDialog(
    context: context,
    dialogType: DialogType.noHeader,
    headerAnimationLoop: true,
    animType: AnimType.topSlide,
    dialogBackgroundColor: AppColors.backgroundColor2,
    body: Center(
      child: Material(
        color: Colors.transparent,
        child: SizedBox(
          height: controller.sizedHight * 0.6,
          width: controller.sizedWidth * 0.9,
          child: PageView(
            physics: NeverScrollableScrollPhysics(),
            controller: pageController,
            children: [
              // الصفحة الأولى: قائمة المستخدمين
              StatefulBuilder(
                builder: (context, setState) {
                  return Column(
                    children: [
                      Text(
                        'إدارة المستخدمين',
                        style: TextStyle(
                          color: AppColors.textColor2,
                          fontSize: controller.sized * 0.016,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: controller.sizedHight * 0.02),

                      // حقل البحث
                      Container(
                        width: controller.sizedWidth * 0.85,
                        margin: EdgeInsets.symmetric(
                            horizontal: controller.sizedWidth * 0.02),
                        child: TextFormField(
                          controller: searchController,
                          onChanged: (value) {
                            setState(() {
                              if (value.isEmpty) {
                                filteredUsers = List.from(allUsers);
                              } else {
                                filteredUsers = allUsers
                                    .where((user) => user['name']
                                        .toString()
                                        .toLowerCase()
                                        .contains(value.toLowerCase()))
                                    .toList();
                              }
                            });
                          },
                          textDirection: TextDirection.rtl,
                          style: TextStyle(
                            color: AppColors.textPrimary,
                            fontSize: controller.sized * 0.012,
                          ),
                          decoration: InputDecoration(
                            hintText: 'البحث عن مستخدم...',
                            hintStyle: TextStyle(
                              color: AppColors.textHint,
                              fontSize: controller.sized * 0.012,
                            ),
                            prefixIcon: Icon(Icons.search,
                                color: AppColors.textSecondary),
                            filled: true,
                            fillColor: AppColors.surface,
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.04,
                              vertical: controller.sizedHight * 0.015,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                  color: AppColors.border, width: 1.0),
                            ),
                          ),
                        ),
                      ),

                      // قائمة المستخدمين
                      Expanded(
                        child: filteredUsers.isEmpty
                            ? Center(
                                child: Text(
                                  'لا توجد نتائج',
                                  style: TextStyle(
                                    color: AppColors.textSecondary,
                                    fontSize: controller.sized * 0.015,
                                  ),
                                ),
                              )
                            : ListView.builder(
                                itemCount: filteredUsers.length,
                                itemBuilder: (context, index) {
                                  final user = filteredUsers[index];
                                  String accessText = '';
                                  Color accessColor = AppColors.textSecondary;

                                  switch (user['access']) {
                                    case 'full':
                                      accessText = 'وصـول كامل';
                                      accessColor = AppColors.successColor;
                                      break;
                                    case 'never':
                                      accessText = 'عدم الوصول';
                                      accessColor = AppColors.errorColor;
                                      break;
                                    default:
                                      accessText = 'وصول محدود';
                                      accessColor = AppColors.primaryColor;
                                      break;
                                  }

                                  return Card(
                                    margin: EdgeInsets.symmetric(
                                        vertical: controller.sizedWidth * 0.02,
                                        horizontal:
                                            controller.sizedWidth * 0.02),
                                    color: AppColors.surface,
                                    child: Directionality(
                                      textDirection: TextDirection.rtl,
                                      child: ListTile(
                                        title: Text(
                                          user['name'],
                                          style: TextStyle(
                                            color: AppColors.textPrimary,
                                            fontSize: controller.sized * 0.013,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        subtitle: Text(
                                          user['type'],
                                          style: TextStyle(
                                            color: AppColors.textSecondary,
                                            fontSize: controller.sized * 0.01,
                                          ),
                                        ),
                                        trailing: Container(
                                          padding: EdgeInsets.symmetric(
                                            vertical:
                                                controller.sizedHight * 0.007,
                                          ),
                                          width: controller.sizedWidth * 0.21,
                                          decoration: BoxDecoration(
                                            color: accessColor.withOpacity(0.1),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            border: Border.all(
                                                color: accessColor, width: 1),
                                          ),
                                          child: txtStyle(
                                            txt: accessText,
                                            color: accessColor,
                                            size: controller.sized * 0.01,
                                            align: TextAlign.center,
                                          ),
                                        ),
                                        onTap: () {
                                          selectedUser = user;
                                          pageController.nextPage(
                                            duration:
                                                Duration(milliseconds: 650),
                                            curve: Curves.ease,
                                          );
                                        },
                                      ),
                                    ),
                                  );
                                },
                              ),
                      ),
                    ],
                  );
                },
              ),

              // الصفحة الثانية: تفاصيل المستخدم
              StatefulBuilder(
                builder: (context, setStateDetails) {
                  if (selectedUser == null) {
                    return Center(child: Text('لم يتم اختيار مستخدم'));
                  }

                  String accessText = '';
                  String accessDescription = '';
                  Color accessColor = AppColors.textSecondary;

                  switch (selectedUser!['access']) {
                    case 'full':
                      accessText = 'وصول كامل';
                      accessDescription =
                          'يستطيع هذا المستخدم الوصول والتحكم الكامل في ${controller.home.value}';
                      accessColor = AppColors.successColor;
                      break;
                    case 'never':
                      accessText = 'عدم الوصول';
                      accessDescription =
                          'لا يستطيع هذا المستخدم الدخول إلى ${controller.home.value}';
                      accessColor = AppColors.errorColor;
                      break;
                    default:
                      accessText = 'وصول محدود';
                      accessDescription =
                          'يستطيع هذا المستخدم الوصول والتحكم في ${controller.home.value} بصلاحيات محددة';
                      accessColor = AppColors.primaryColor;
                      break;
                  }

                  // تحليل صلاحيات المستخدم الحالية وتفعيلها
                  loadUserPermissions(
                      selectedUser!['access'], selectedUser!['mac']);

                  print(permissions);
                  return SingleChildScrollView(
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: controller.sizedWidth * 0.1,
                            ),
                            Expanded(
                              child: Text(
                                'تفاصيل المستخدم',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: AppColors.textColor2,
                                  fontSize: controller.sized * 0.015,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            IconButton(
                              onPressed: () {
                                pageController.previousPage(
                                  duration: Duration(milliseconds: 650),
                                  curve: Curves.ease,
                                );
                              },
                              icon: Icon(Icons.arrow_forward_ios_rounded,
                                  color: AppColors.textPrimary),
                            ),
                          ],
                        ),

                        SizedBox(height: controller.sizedHight * 0.02),

                        // معلومات المستخدم
                        Container(
                          width: controller.sizedWidth * 0.85,
                          padding: EdgeInsets.all(controller.sized * 0.005),
                          margin: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.04),
                          decoration: BoxDecoration(
                            color: AppColors.surface,
                            borderRadius: BorderRadius.circular(12),
                            border:
                                Border.all(color: AppColors.border, width: 1),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              _buildInfoRow(
                                  'اسم المستخدم :', selectedUser!['name']),
                              SizedBox(height: 8),
                              _buildInfoRow(
                                  'نوع الجهاز :', selectedUser!['type']),
                              SizedBox(height: 8),
                              _buildInfoRow(
                                  'رمز الجهاز :', selectedUser!['mac']),
                              SizedBox(height: 8),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  txtStyle(
                                      txt: accessText,
                                      color: accessColor,
                                      align: TextAlign.right),
                                  txtStyle(
                                      txt: 'نوع الوصول :       ',
                                      color: AppColors.textColor2),
                                ],
                              ),
                            ],
                          ),
                        ),

                        SizedBox(height: controller.sizedHight * 0.02),

                        // وصف نوع الوصول
                        Container(
                          width: controller.sizedWidth * 0.85,
                          padding: EdgeInsets.all(12),
                          margin: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.04),
                          decoration: BoxDecoration(
                            color: accessColor.withOpacity(0.05),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                color: accessColor.withOpacity(0.3), width: 1),
                          ),
                          child: Text(
                            accessDescription,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: accessColor,
                              fontSize: controller.sized * 0.013,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),

                        SizedBox(height: controller.sizedHight * 0.02),
                        if (selectedUser!['access'] != 'full')
                          // قائمة الصلاحيات
                          Container(
                            width: controller.sizedWidth * 0.85,
                            padding: EdgeInsets.all(controller.sized * 0.01),
                            margin: EdgeInsets.symmetric(
                                horizontal: controller.sizedWidth * 0.04),
                            decoration: BoxDecoration(
                              color: AppColors.surface,
                              borderRadius: BorderRadius.circular(12),
                              border:
                                  Border.all(color: AppColors.border, width: 1),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Center(
                                  child: txtStyle(
                                      txt: 'إدارة الصلاحيات',
                                      color: AppColors.textPrimary,
                                      size: controller.sized * 0.012,
                                      align: TextAlign.center),
                                ),
                                SizedBox(height: 8),

                                // خيارات الصلاحيات
                                ..._buildPermissionCheckboxes(
                                    permissions, setStateDetails, (changed) {
                                  setStateDetails(() {
                                    hasChanges = changed;
                                  });
                                }),

                                SizedBox(height: 12),

                                // خيار الوصول الكامل
                                if (controller.access == 'full')
                                  Container(
                                    decoration: BoxDecoration(
                                      color: permissions.length == 8
                                          ? AppColors.successColor
                                              .withOpacity(0.1)
                                          : AppColors.warningColor
                                              .withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          color: permissions.length == 8
                                              ? AppColors.successColor
                                              : AppColors.warningColor,
                                          width: 1),
                                    ),
                                    child: CheckboxListTile(
                                      title: Text(
                                        'وصول وتحكم كامل',
                                        style: TextStyle(
                                          color: permissions.length == 8
                                              ? AppColors.successColor
                                              : AppColors.warningColor,
                                          fontSize: controller.sized * 0.014,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      value: permissions.length ==
                                          7, // جميع الصلاحيات
                                      onChanged: (bool? value) {
                                        if (value == true) {
                                          _showFullAccessWarning(context, () {
                                            setStateDetails(() {
                                              permissions = [
                                                'بيانات النظام',
                                                'الملحقات والغرف',
                                                'الكلمات الروتينية',
                                                'المهام المجدولة',
                                                'الخريطة',
                                                'إدارة الشبكة والمستخدمين',
                                                'full'
                                              ];
                                              hasChanges = true;
                                            });
                                          });
                                        } else {
                                          setStateDetails(() {
                                            permissions.clear();
                                            hasChanges = true;
                                          });
                                        }
                                      },
                                      activeColor: permissions.length == 8
                                          ? AppColors.successColor
                                          : AppColors.warningColor,
                                    ),
                                  ),
                              ],
                            ),
                          ),

                        SizedBox(height: controller.sizedHight * 0.02),

                        // أزرار التحكم
                        if (controller.access == 'full')
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              // زر إزالة المستخدم
                              TextButton(
                                onPressed: () {
                                  _showRemoveUserDialog(context, selectedUser!);
                                },
                                child: Text(
                                  'إزالة المستخدم',
                                  style: TextStyle(
                                    color: AppColors.errorColor,
                                    fontSize: controller.sized * 0.014,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),

                              // زر التعديل (يظهر عند وجود تغييرات)
                              if (hasChanges)
                                TextButton(
                                  onPressed: () async {
                                    await _updateUserPermissions(pageController,
                                        context, selectedUser!, permissions);
                                  },
                                  child: Text(
                                    'تعديل',
                                    style: TextStyle(
                                      color: AppColors.successColor,
                                      fontSize: controller.sized * 0.014,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    ),
  ).show();
}

// دالة مساعدة لبناء صف المعلومات
Widget _buildInfoRow(String label, String value) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.end,
    children: [
      txtStyle(
          txt: label.contains('رمز الجهاز')
              ? value.replaceAll('-', '-\n')
              : value,
          color: AppColors.textColor3,
          align: TextAlign.right,
          maxLines: 5),
      txtStyle(txt: '$label       ', color: AppColors.textColor2),
    ],
  );
}

// دالة لبناء قائمة الصلاحيات
List<Widget> _buildPermissionCheckboxes(
    List<String> permissions, StateSetter setState, Function(bool) onChanged) {
  List<String> allPermissions = [
    'بيانات النظام',
    'الملحقات والغرف',
    'الكلمات الروتينية',
    'المهام المجدولة',
    'الخريطة',
    'إدارة الشبكة والمستخدمين'
  ];

  return allPermissions.map((permission) {
    return CheckboxListTile(
      title: Text(
        permission,
        style: TextStyle(
          color: AppColors.textPrimary,
          fontSize: controller.sized * 0.013,
        ),
      ),
      value: permissions.contains(permission),
      onChanged: (bool? value) {
        if (controller.access == 'full') {
          setState(() {
            if (value == true) {
              if (!permissions.contains(permission)) {
                permissions.add(permission);
              }
            } else {
              permissions.remove(permission);
            }
            onChanged(true); // إشعار بوجود تغيير
          });
        }
      },
      activeColor: AppColors.primaryColor,
    );
  }).toList();
}

// دالة لعرض تحذير الوصول الكامل
void _showFullAccessWarning(BuildContext context, VoidCallback onConfirm) {
  AwesomeDialog(
    context: context,
    dialogType: DialogType.warning,
    animType: AnimType.topSlide,
    title: 'تحذير',
    desc:
        'لن تستطيع التحكم في هذا المستخدم إن تمت الموافقة على إعطائه جميع الصلاحيات. هل أنت متأكد؟',
    btnCancelText: 'إلغاء',
    btnOkText: 'موافق',
    btnCancelOnPress: () {},
    btnOkOnPress: onConfirm,
  ).show();
}

// دالة لعرض نافذة إزالة المستخدم
void _showRemoveUserDialog(BuildContext context, Map<String, dynamic> user) {
  AwesomeDialog(
    context: context,
    dialogType: DialogType.error,
    animType: AnimType.topSlide,
    title: 'إزالة المستخدم',
    desc: 'هل أنت متأكد من إزالة المستخدم من النظام؟\n"${user['name']}"',
    btnCancelText: 'إلغاء',
    btnOkText: 'إزالة',
    btnCancelOnPress: () {},
    btnOkOnPress: () async {
      try {
        final conn = await MySqlConnection.connect(ConnectionSettings(
          host: controller.hostZain.value,
          user: 'root',
          db: 'zain',
          password: 'zain',
          characterSet: CharacterSet.UTF8,
        ));

        await conn.query('DELETE FROM phones WHERE mac = ?', [user['mac']]);
        await conn.close();

        // إغلاق النافذة وإعادة تحميل القائمة
        Navigator.of(context).pop();
        Navigator.of(context).pop();
        showUsersManagementDialog(context);
      } catch (e) {
        print('خطأ في إزالة المستخدم: $e');
      }
    },
  ).show();
}

// دالة لتحديث صلاحيات المستخدم
Future<void> _updateUserPermissions(pageController, BuildContext context,
    Map<String, dynamic> user, List<String> permissions) async {
  try {
    String newAccess = '';

    // تحديد نوع الوصول بناءً على الصلاحيات المختارة
    if (permissions.length == 7) {
      // جميع الصلاحيات = وصول كامل
      newAccess = 'full';
    } else if (permissions.isEmpty) {
      // لا توجد صلاحيات = عدم الوصول
      newAccess = 'never';
    } else {
      // صلاحيات محددة = وصول محدود
      Map<String, String> permissionMap = {
        'بيانات النظام': 'data',
        'الملحقات والغرف': 'dev&rooms',
        'الكلمات الروتينية': 'routinesWords',
        'المهام المجدولة': 'tasks',
        'الخريطة': 'map',
        'إدارة الشبكة والمستخدمين': 'wifi'
      };

      List<String> accessCodes = [];
      for (String permission in permissions) {
        if (permissionMap.containsKey(permission)) {
          accessCodes.add(permissionMap[permission]!);
        }
      }
      newAccess = accessCodes.join(', ');
    }

    // تحديث قاعدة البيانات باستخدام الاتصال الآمن
    bool success = await executeSafeQueryWithErrorHandling(
        'UPDATE phones SET access = ? WHERE mac = ?',
        [newAccess, user['mac']],
        'خطأ في تحديث صلاحيات المستخدم');

    if (!success) {
      // إظهار رسالة خطأ للمستخدم
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في تحديث الصلاحيات. يرجى المحاولة مرة أخرى.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // إرسال إشعار تحديث للأجهزة المتصلة
    final builder = MqttClientPayloadBuilder();
    builder.addString('1');
    client.publishMessage('edit', MqttQos.atLeastOnce, builder.payload!);
    builder.clear();
    builder.addString('re');
    client.publishMessage(user['mac'], MqttQos.atLeastOnce, builder.payload!);

    // إظهار رسالة نجاح
    AwesomeDialog(
      context: context,
      dialogType: DialogType.success,
      animType: AnimType.topSlide,
      title: 'تم التحديث',
      desc: 'تم تحديث صلاحيات المستخدم بنجاح',
      btnOkOnPress: () {
        // إغلاق النافذة وإعادة تحميل القائمة
        pageController.previousPage(
          duration: Duration(milliseconds: 650),
          curve: Curves.ease,
        );
      },
    ).show();
  } catch (e) {
    print('خطأ في تحديث صلاحيات المستخدم: $e');

    // إظهار رسالة خطأ
    AwesomeDialog(
      context: context,
      dialogType: DialogType.error,
      animType: AnimType.topSlide,
      title: 'خطأ',
      desc: 'حدث خطأ في تحديث صلاحيات المستخدم',
      btnOkOnPress: () {},
    ).show();
  }
}
