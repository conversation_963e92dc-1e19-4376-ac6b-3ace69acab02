/// صفحة إدارة الأتمتة المتقدمة
/// تدعم إنشاء وإدارة قواعد الأتمتة المعقدة

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/advanced_automation_service.dart';
import '../services/notification_service.dart';
import '../models/automation_models.dart';
import 'create_automation_rule_page.dart';

class AdvancedAutomationPage extends StatelessWidget {
  const AdvancedAutomationPage({super.key});

  @override
  Widget build(BuildContext context) {
    final automationService = Get.find<AdvancedAutomationService>();

    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        title: const Text(
          'الأتمتة المتقدمة',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: const Color(0xFF16213E),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Obx(() => Column(
            children: [
              // إحصائيات الأتمتة
              _buildStatisticsSection(automationService),

              // إعدادات الإشعارات
              _buildNotificationSettings(),

              // قائمة قواعد الأتمتة
              Expanded(
                child: _buildAutomationRulesList(automationService),
              ),
            ],
          )),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateRuleDialog(context, automationService),
        backgroundColor: const Color(0xFF16213E),
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  /// بناء قسم الإحصائيات
  Widget _buildStatisticsSection(AdvancedAutomationService service) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatCard(
            'إجمالي القواعد',
            service.totalRules.toString(),
            Icons.rule,
            Colors.blue,
          ),
          _buildStatCard(
            'القواعد النشطة',
            service.activeRules.toString(),
            Icons.play_circle,
            Colors.green,
          ),
          _buildStatCard(
            'تم التفعيل اليوم',
            service.triggeredToday.toString(),
            Icons.flash_on,
            Colors.orange,
          ),
        ],
      ),
    );
  }

  /// بناء قسم إعدادات الإشعارات
  Widget _buildNotificationSettings() {
    try {
      final notificationService = Get.find<NotificationService>();

      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFF16213E),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Obx(() => Row(
              children: [
                const Icon(
                  Icons.notifications,
                  color: Colors.purple,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إشعارات الأتمتة',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'تلقي إشعارات عند تنفيذ قواعد الأتمتة',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: _getGlobalAutomationNotificationSetting(
                      notificationService),
                  onChanged: (value) =>
                      _updateGlobalAutomationNotificationSetting(
                          notificationService, value),
                  activeColor: Colors.purple,
                ),
              ],
            )),
      );
    } catch (e) {
      // إذا لم تكن خدمة الإشعارات متاحة، لا تعرض شيئاً
      return const SizedBox.shrink();
    }
  }

  /// الحصول على إعداد الإشعارات العام للأتمتة
  bool _getGlobalAutomationNotificationSetting(NotificationService service) {
    // إذا كانت جميع قواعد الأتمتة مفعلة للإشعارات، إرجاع true
    final automationNotifications = service.automationRuleNotifications;
    if (automationNotifications.isEmpty) return true;

    // فحص ما إذا كانت معظم القواعد مفعلة
    final enabledCount =
        automationNotifications.values.where((enabled) => enabled).length;
    return enabledCount > automationNotifications.length / 2;
  }

  /// تحديث إعداد الإشعارات العام للأتمتة
  void _updateGlobalAutomationNotificationSetting(
      NotificationService service, bool enabled) {
    // تحديث جميع قواعد الأتمتة
    final automationService = Get.find<AdvancedAutomationService>();

    for (var rule in automationService.automationRules) {
      final hasNotificationAction = rule.actions.any((action) =>
          action.type.toString().contains('notification') ||
          action.type.toString().contains('send_notification'));

      if (hasNotificationAction) {
        service.updateAutomationRuleNotification(rule.id, enabled);
      }
    }

    // إظهار رسالة تأكيد
    Get.snackbar(
      enabled ? 'تم تفعيل الإشعارات' : 'تم إيقاف الإشعارات',
      enabled
          ? 'سيتم إرسال إشعارات عند تنفيذ قواعد الأتمتة'
          : 'لن يتم إرسال إشعارات عند تنفيذ قواعد الأتمتة',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: enabled ? Colors.green : Colors.orange,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          title,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// بناء قائمة قواعد الأتمتة
  Widget _buildAutomationRulesList(AdvancedAutomationService service) {
    if (service.automationRules.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.smart_toy_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد قواعد أتمتة',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 18,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'اضغط على + لإضافة قاعدة جديدة',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: service.automationRules.length,
      itemBuilder: (context, index) {
        final rule = service.automationRules[index];
        return _buildAutomationRuleCard(rule, service);
      },
    );
  }

  /// بناء بطاقة قاعدة الأتمتة
  Widget _buildAutomationRuleCard(
      AdvancedAutomationRule rule, AdvancedAutomationService service) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: rule.enabled
              ? Colors.green.withOpacity(0.3)
              : Colors.grey.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: ExpansionTile(
        leading: Icon(
          rule.enabled ? Icons.play_circle : Icons.pause_circle,
          color: rule.enabled ? Colors.green : Colors.grey,
          size: 32,
        ),
        title: Text(
          rule.name,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              rule.description,
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                _buildChip('${rule.triggers.length} محفز', Colors.blue),
                const SizedBox(width: 8),
                _buildChip('${rule.conditions.length} شرط', Colors.orange),
                const SizedBox(width: 8),
                _buildChip('${rule.actions.length} إجراء', Colors.green),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onSelected: (value) {
            switch (value) {
              case 'toggle':
                service.toggleAutomationRule(rule.id, !rule.enabled);
                break;
              case 'edit':
                _showEditRuleDialog(Get.context!, rule, service);
                break;
              case 'delete':
                _showDeleteConfirmation(Get.context!, rule, service);
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'toggle',
              child: Row(
                children: [
                  Icon(
                    rule.enabled ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    rule.enabled ? 'إيقاف' : 'تفعيل',
                    style: const TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, color: Colors.white),
                  SizedBox(width: 8),
                  Text('تعديل', style: TextStyle(color: Colors.white)),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildRuleDetails(rule),
                const SizedBox(height: 16),
                _buildRuleStats(rule),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رقاقة صغيرة
  Widget _buildChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// بناء تفاصيل القاعدة
  Widget _buildRuleDetails(AdvancedAutomationRule rule) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'تفاصيل القاعدة:',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),

        // المحفزات
        if (rule.triggers.isNotEmpty) ...[
          const Text(
            '🎯 المحفزات:',
            style: TextStyle(color: Colors.blue, fontWeight: FontWeight.bold),
          ),
          ...rule.triggers.map((trigger) => Padding(
                padding: const EdgeInsets.only(left: 16, top: 4),
                child: Text(
                  '• ${_getTriggerDescription(trigger)}',
                  style: const TextStyle(color: Colors.grey),
                ),
              )),
          const SizedBox(height: 8),
        ],

        // الشروط
        if (rule.conditions.isNotEmpty) ...[
          Text(
            '📋 الشروط (${rule.logicOperator == LogicOperator.and ? 'جميع الشروط' : 'شرط واحد على الأقل'}):',
            style: const TextStyle(
                color: Colors.orange, fontWeight: FontWeight.bold),
          ),
          ...rule.conditions.map((condition) => Padding(
                padding: const EdgeInsets.only(left: 16, top: 4),
                child: Text(
                  '• ${_getConditionDescription(condition)}',
                  style: const TextStyle(color: Colors.grey),
                ),
              )),
          const SizedBox(height: 8),
        ],

        // الإجراءات
        if (rule.actions.isNotEmpty) ...[
          const Text(
            '⚡ الإجراءات:',
            style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
          ),
          ...rule.actions.map((action) => Padding(
                padding: const EdgeInsets.only(left: 16, top: 4),
                child: Text(
                  '• ${_getActionDescription(action)}',
                  style: const TextStyle(color: Colors.grey),
                ),
              )),
        ],
      ],
    );
  }

  /// بناء إحصائيات القاعدة
  Widget _buildRuleStats(AdvancedAutomationRule rule) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'عدد مرات التفعيل:',
                style: TextStyle(color: Colors.grey, fontSize: 12),
              ),
              Text(
                rule.triggerCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              const Text(
                'آخر تفعيل:',
                style: TextStyle(color: Colors.grey, fontSize: 12),
              ),
              Text(
                rule.lastTriggered != null
                    ? _formatDateTime(rule.lastTriggered!)
                    : 'لم يتم التفعيل بعد',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// الحصول على وصف المحفز
  String _getTriggerDescription(AutomationTrigger trigger) {
    switch (trigger.type) {
      case TriggerType.entityState:
        final entityType = trigger.config['entity_type'];
        final entityId = trigger.config['entity_id'];
        final state = trigger.config['state'];
        return 'تغيير حالة $entityType.$entityId${state != null ? ' إلى $state' : ''}';

      case TriggerType.sun:
        final event = trigger.config['event'];
        return event == 'sunrise' ? 'شروق الشمس' : 'غروب الشمس';

      case TriggerType.weather:
        final condition = trigger.config['condition'];
        final threshold = trigger.config['threshold'];
        return 'الطقس: $condition ($threshold)';

      case TriggerType.time:
        final time = trigger.config['time'];
        return 'الوقت: $time';

      default:
        return 'محفز غير معروف';
    }
  }

  /// الحصول على وصف الشرط
  String _getConditionDescription(AutomationCondition condition) {
    switch (condition.type) {
      case ConditionType.person:
        final personId = condition.config['person_id'];
        final state = condition.config['state'];
        return 'الشخص $personId: $state';

      case ConditionType.weather:
        return 'شروط الطقس';

      case ConditionType.sun:
        final state = condition.config['state'];
        return 'الشمس: ${state ?? 'أي حالة'}';

      case ConditionType.time:
        return 'شروط الوقت';

      default:
        return 'شرط غير معروف';
    }
  }

  /// الحصول على وصف الإجراء
  String _getActionDescription(AutomationAction action) {
    switch (action.type) {
      case ActionType.deviceControl:
        final deviceId = action.config['device_id'];
        final command = action.config['command'];
        return 'التحكم في الجهاز $deviceId: $command';

      case ActionType.notification:
        final title = action.config['title'];
        return 'إرسال إشعار: $title';

      case ActionType.delay:
        final seconds = action.config['seconds'];
        return 'تأخير لمدة $seconds ثانية';

      case ActionType.scene:
        final sceneId = action.config['scene_id'];
        return 'تفعيل مشهد: $sceneId';

      default:
        return 'إجراء غير معروف';
    }
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    final localDateTime = DateTime(
      dateTime.year,
      dateTime.month,
      dateTime.day,
      dateTime.hour,
      dateTime.minute,
      dateTime.second,
      dateTime.millisecond,
      dateTime.microsecond,
    );
    final now = DateTime.now();

    // حساب الفرق بالمللي ثانية
    final diffMillis =
        now.millisecondsSinceEpoch - localDateTime.millisecondsSinceEpoch;
    print(diffMillis);
    print(localDateTime);
    print(now);
    // تحويل المللي ثانية إلى وحدات زمنية
    final seconds = diffMillis ~/ 1000;
    final minutes = seconds ~/ 60;
    final hours = minutes ~/ 60;
    final days = hours ~/ 24;

    if (seconds < 60) {
      return 'الآن';
    } else if (minutes < 60) {
      return 'منذ $minutes دقيقة';
    } else if (hours < 24) {
      return 'منذ $hours ساعة';
    } else {
      return 'منذ $days يوم';
    }
  }

  /// عرض حوار إنشاء قاعدة جديدة
  void _showCreateRuleDialog(
      BuildContext context, AdvancedAutomationService service) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CreateAutomationRulePage(),
      ),
    );
  }

  /// عرض حوار تعديل القاعدة
  void _showEditRuleDialog(BuildContext context, AdvancedAutomationRule rule,
      AdvancedAutomationService service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: const Text(
          'تعديل قاعدة الأتمتة',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'هذه الميزة قيد التطوير.',
          style: TextStyle(color: Colors.grey),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق', style: TextStyle(color: Colors.blue)),
          ),
        ],
      ),
    );
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation(BuildContext context,
      AdvancedAutomationRule rule, AdvancedAutomationService service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: const Text(
          'تأكيد الحذف',
          style: TextStyle(color: Colors.white),
        ),
        content: Text(
          'هل أنت متأكد من حذف قاعدة الأتمتة "${rule.name}"؟',
          style: const TextStyle(color: Colors.grey),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
          ),
          TextButton(
            onPressed: () {
              service.removeAutomationRule(rule.id);
              Navigator.of(context).pop();
              Get.snackbar(
                'تم الحذف',
                'تم حذف قاعدة الأتمتة بنجاح',
                backgroundColor: Colors.green,
                colorText: Colors.white,
              );
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
