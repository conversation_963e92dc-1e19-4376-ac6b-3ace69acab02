---
configuration:
  voice:
    name: Voice
    description: >-
      The Piper voice to use. Voices are named according to the following
      scheme: `<language>-<name>-<quality>`.
  speaker:
    name: Speaker
    description: >-
      Speaker number to use if the voice supports multiple speakers.
      By default, the first speaker (speaker 0) will be used.
  length_scale:
    name: Length scale
    description: >-
      Speeds up or slows down the voice. A value of 1.0 means to use the voice's
      default speaking rate, with < 1.0 being faster and > 1.0 being slower.
  noise_scale:
    name: Noise scale
    description: >-
      Controls the variability of audio by adding noise during audio generation.
      The effect highly depends on the voice itself, but in general a value of 0
      removes variability and values above 1 will start to degrade audio.
  noise_w:
    name: Speaking cadence
    description: >-
      Controls the variability of speaking cadence (phoneme widths). The effect
      highly depends on the voice itself, but in general a value of 0 removes
      variability and values above 1 produce extreme stutters and pauses.
  max_piper_procs:
    name: Maximum Piper processes
    description: >-
      Number of Piper processes to run simultaneously (default is 1). Increase
      if you need to quickly switch between multiple voices, but beware that
      this will increase RAM usage by the add-on.
  update_voices:
    name: Update voices
    description: >-
      Download the list of new voices automatically every time the add-on
      starts.  You must also reload the Wyoming integration for <PERSON> in Home
      Assistant to see new voices.
  streaming:
    name: Streaming
    description: >-
      Enable support for streaming audio. This breaks apart text at sentence
      boundaries and streams the audio as its being produced. Requires at least
      HA 2025.7.
  debug_logging:
    name: Debug logging
    description: >-
      Print DEBUG level messages to the add-on's log.
network:
  10200/tcp: Piper Wyoming Protocol
