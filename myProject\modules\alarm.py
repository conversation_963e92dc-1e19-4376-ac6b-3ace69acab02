import time
import mysql.connector
import sys
import paho.mqtt.publish as publish
import json
import re
import datetime
from datetime import timedelta
import netifaces as ni
sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/nlt')
sys.path.append('/home/<USER>/myProject/modules')
sys.path.append('/home/<USER>/myProject/processing')
import process as process
import static as st
import handle_device as handle_device


def editAlarm(phrase):
    # تعديل الساعة في المنبة بدون ذكر كلمة ساعة
    
    STT = phrase.split(' ')
    k=0
    for i in STT:
        if i =='ALARM' or i=='REMIND':
            for c in STT:
                if c.isnumeric() and STT[k-1] in ['و','من','الي','في',]:
                    clock = int(c)
                    minute = 00
                    Xclock = STT[k-1]+' '+c
                    Xminute = ''
                    if (STT[k+1]=='10' or (STT[k+1]=='20' and int(c)<5)) and STT[k+2] != 'دقيق' and STT[k+2]!= 'دقاءق':
                        clock += int(STT[k+1])
                        Xclock += ' '+STT[k+1]
                    elif int(c) < 5 and STT[k+1]=='و' and STT[k+2]=='20' and STT[k+3] != 'دقيق' and STT[k+3]!= 'دقاءق':
                        clock += int(STT[k+2])
                        Xclock += ' و '+STT[k+2]
                    phrase2 = phrase.replace(Xclock, STT[k-1]+' '+str(clock) +':00')
                    STT2 = phrase2.split(' ')
                    k2=0
                    Nclock = str(clock)+':00'
                    breaks = ''
                    for end in STT2:
                        if Nclock == STT2[-2]:
                            phrase = phrase2
                            breaks = 'f'
                    
                    for m in STT2:
                        if breaks == 'f':
                            break
                        if m == Nclock:
                            if STT2[k2+1] == 'نص' or STT2[k2+1] == 'نصف':
                                phrase2=phrase2.replace(STT2[k2+1],'30')
                            elif STT2[k2+1] == 'ثلث':
                                phrase2=phrase2.replace(STT2[k2+1],'20')
                            elif STT2[k2+1] == 'ربع':
                                phrase2=phrase2.replace(STT2[k2+1],'15')
                            elif STT2[k2+1]+' '+STT2[k2+2] == 'و نص' or STT2[k2+1]+' '+STT2[k2+2] == 'و نصف':
                                phrase2=phrase2.replace(STT2[k2+1]+' '+STT2[k2+2],'30')
                            elif STT2[k2+1]+' '+STT2[k2+2] == 'و ثلث':
                                phrase2=phrase2.replace(STT2[k2+1]+' '+STT2[k2+2],'20')
                            elif STT2[k2+1]+' '+STT2[k2+2] == 'و ربع':
                                phrase2=phrase2.replace(STT2[k2+1]+' '+STT2[k2+2],'15')
                            STT2= phrase2.split(' ')
                        k2+=1
                    k2=0
                    for m in STT2:
                        if breaks == 'f':
                            break
                        if m == Nclock:
                            if STT2[k2+1].isnumeric():
                                minute += int(STT2[k2+1])
                                Xminute += ' '+STT2[k2+1]
                                if STT2[k2+2].isnumeric() and int(STT2[k2+1]) < 10 and STT2[k2+2][1] == '0' and int(STT2[k2+2]) < 60 :
                                    minute += int(STT2[k2+2])
                                    Xminute += ' ' +STT2[k2+2]
                                elif STT2[k2+2] == 'و' and STT2[k2+3].isnumeric() and STT2[k2+3][1] == '0' and int(STT2[k2+3]) < 60:
                                    minute += int(STT2[k2+3])
                                    Xminute+= ' و '+STT2[k2+3]
                            elif STT2[k2+1] == 'و' and STT2[k2+2].isnumeric():
                                minute+= int(STT2[k2+2])
                                Xminute+= ' و '+STT2[k2+2]
                                if STT2[k2+3].isnumeric() and int(STT2[k2+2]) < 10 and STT2[k2+3][1] == '0' and int(STT2[k2+3]) < 60:
                                    minute+= int(STT2[k2+3])
                                    Xminute+= ' '+STT2[k2+3]
                                elif STT2[k2+3] == 'و' and STT2[k2+4].isnumeric() and STT2[k2+4][1] == '0' and int(STT2[k2+4]) < 60:
                                    minute+= int(STT2[k2+4])
                                    Xminute+= ' و '+STT2[k2+4]
                            if minute < 10:
                                phrase2 = phrase2.replace(':00'+Xminute if Xminute != '' else ':00',':0'+str(minute) if minute != 0 else ':00')
                            else:
                                phrase2 = phrase2.replace(':00'+Xminute if Xminute != '' else ':00',':'+str(minute) if minute != 0 else ':00')
                            if (':00 دقيق' in phrase2 or ':00 دقاءق' in phrase2):
                                phrase2 = phrase2.replace('دقيق ','')
                                phrase2 = phrase2.replace('دقاءق ','')
                            elif (':'+str(minute)+' دقيق' in phrase2 or ':'+str(minute)+' دقاءق' in phrase2):
                                phrase2 = phrase2.replace('دقيق ','')
                                phrase2 = phrase2.replace('دقاءق ','')
                            elif (':0'+str(minute)+' دقيق' in phrase2 or ':0'+str(minute)+' دقاءق' in phrase2):
                                phrase2 = phrase2.replace('دقيق ','')
                                phrase2 = phrase2.replace('دقاءق ','')
                        k2+=1
                    phrase = phrase2
                k+=1
    return phrase

def deviceType(command,type,d):
    print(type)
    if 'TV' in type:
        c={'state':None,'ch':None,'v':None}
        print(command)
        if 'CH' in command:
            if '+' in command or '-' in command:
                c['ch']=command.split(' ')[1]+command.split(' ')[2]
                c['state']=True
            else:
                c['ch']= command.split(' ')[2]
                c['state']=True
        elif 'VOICE' in command:
            if '+' in command or '-' in command:
                c['v']=command.split(' ')[1]+command.split(' ')[2]
                c['state']=True
            else:
                c['v']= command.split(' ')[2]
                c['state']=True
        else :
            if 'POWER-ON' in command:
                c['state']=True
            else:
                c['state']=False
    elif 'AC' in type:
        c={"state":None,"degree":None,"type":None,"speed":None,"swing":None}
        print(command.split)
        if 'OFF' in command:
            c['state']=False
        else:
            c['state']=True
            if command.split(' ')[1]!= '0' and command.split(' ')[1]!= 'X':
                c['degree']=command.split(' ')[1]
            if command.split(' ')[3]!= '0' and command.split(' ')[3]!= 'X':
                c['speed']=command.split(' ')[3]
            if command.split(' ')[4]!= 'XX':
                if command.split(' ')[4]== 'HEAT':
                    c['type']='تدفئة'
                elif command.split(' ')[4]== 'AC':
                    c['type']='تبريد'
                else:
                    c['type']='مروحة'
    elif 'SWITCH' in type:
        c={}
        for s in range(1,int(d['Type'].split('v')[1])):
            c['v'+str(s)]=None  
        for s in command.split(' ')[:-1]:
            c[s.split('_')[0]]= True if 'RUN' in s else False
            
    print(c)
    return c 
def get_say(mday,month,year,awday,nclock,re,room):
    
    say = ''
    clock = ''
        
    if awday == 'None' and re == 'ON' and '#' not in mday and '#' not in month and '#' not in year:
        awday == 'Sat Sun Mon Tue Wed Thu Fri'
    
    if awday == 'Sat Sun Mon Tue Wed Thu Fri' and re == 'ON':
        days='كل يوم'
    elif awday == 'None':
        days=''
    else:
        days=awday.strip()
        days=days.replace('Sat','السَبت')
        days=days.replace('Sun','الاحَد')
        days=days.replace('Mon','الاثنَيّن')
        days=days.replace('Tue','الثُلاثاء')
        days=days.replace('Wed','الاربِعاء')
        days=days.replace('Thu','الخَميس')
        days=days.replace('Fri','الجُمُعَهْ')
        days=days.replace(' ',' و ')
        if re == 'ON':
            days='كل يوم '+days
        else:
                days='يوم '+days

    if '#' not in nclock:
        nclock = nclock.replace('PM','مسائاً')
        nclock = nclock.replace('AM','صباحاً')
        say += nclock
    if days == '' and mday != None and month != None and year != None:
        if '#' not in mday:
            say += f' يَومْ {mday}'
        if '#' not in month:
            say += f' شَهرِ {month}'
        if '#' not in year:
            say += f' سَنَةْ {year}'
    else:
        say += ' '+days
    if room != None:
        
        for i in room:
            if room[i] == 'None':
                room_say = ' في كل أجهِزَةْ زيّنْ'
                return
            elif room[i] != 'None' and room[i] != 'myroom':
                room_say =+ ' '+room[i]
            else:
                room_say = ''
        say+=room_say
    say=say.strip()
    return say
def update(results,alarm,say,state,type):
    if state == 'OFF':
        state = 'بِحَذفِ'
    elif state == 'STOP':
        state = 'بإيقافِ'
    elif state == 'ON':
        state = 'بإعادَةْ تَشغيّلِ'
    edit = ''
    if len(results)==1:
        edit = f'لَقَد قُمتُ {state} {type} '
        if alarm != 'remind' and alarm != None:
            edit += alarm
        if say != '':
            edit += 'المُوافِقْ \n'
            edit += say
        
    else:
        edit = f'لَقَد قُمتُ {state} {type} '
        if say != '':
            edit += 'التي تُوافِقْ \n'
            edit += say
    return edit
    
def remind(self,alarm,year,month,mday,awday,nclock,re,state,room):
    rooms = ''
    dbRoom = ''
    print(room)
    for i in room:
        print(i)
        if room[i] == 'None':
            dbRoom = ''
            
        else:
            rooms = rooms+f"'{i}', "
    if rooms != '':
        rooms = rooms.strip()
        print(rooms)
        rooms = rooms[:-1]
        dbRoom=f'AND room IN ({rooms})'
    print(1)
    self.system_self.cursor.execute("SELECT id,state FROM Calender WHERE id > 0 %s %s %s %s %s %s %s"% (f"AND name LIKE '{alarm}'" if alarm != 'remind' else '', f"AND year LIKE '{year.replace('#','')}'" if awday == 'None' and '#' not in year else '',f"AND month LIKE '{month.replace('#','')}'" if awday == 'None' and '#' not in year else '',f"AND mday LIKE '{mday.replace('#','')}'" if awday == 'None' and '#' not in year else '', f"AND clock LIKE '{nclock}'" if '#' not in nclock else '', f"AND wday LIKE '%{awday}%'" if awday != 'None' else '', dbRoom))
    say = get_say(mday=mday,month=month,year=year,awday=awday,nclock=nclock,re=re,room=room)
    results = self.system_self.cursor.fetchall()
    if results:
        if state == 'OFF':
            for i in results:
                self.system_self.cursor.execute("DELETE FROM Calender WHERE id = '%s'" % (i['id']))
                self.system_self.db.commit()
            say = update(results=results,alarm=alarm,say=say,state=state,type='التَذكيّر' if len(results)==1 else 'التَذكيرات')
            publish.single('edit','1', hostname=st.ip)
            self.system_self.tts.say(say)
            return
        elif state =='STOP':
            for i in results:
                self.system_self.cursor.execute("UPDATE Calender SET state = '%s' WHERE id = '%s'" % (state,i['id']))
                self.system_self.db.commit()
            print(say)
            say = update(results=results,alarm=alarm,say=say,state=state,type='التَذكيّر' if len(results)==1 else 'التَذكيرات')
            publish.single('edit','1', hostname=st.ip)
            self.system_self.tts.say(say)
            return
        elif state =='ON':
            for i in results:
                self.system_self.cursor.execute("UPDATE Calender SET state = '%s' WHERE id = '%s'" % (state,i['id']))
                self.system_self.db.commit()
            say = update(results=results,alarm=alarm,say=say,state=state,type='التَذكيّر' if len(results)==1 else 'التَذكيرات')
            publish.single('edit','1', hostname=st.ip)
            self.system_self.tts.say(say)
            return
        elif state =='_':
            self.system_self.cursor.execute("SELECT * FROM Calender WHERE id > 0  %s %s %s %s %s %s %s"% (f"AND name LIKE '{alarm}'" if alarm != 'remind' else '', f"AND year LIKE '{year.replace('#','')}'" if awday == 'None' else '',f"AND month LIKE '{month.replace('#','')}'" if awday == 'None' else '',f"AND mday LIKE '{mday.replace('#','')}'" if awday == 'None' else '', f"AND clock LIKE '{nclock}'" if '#' not in nclock else '', f"AND wday LIKE '%{awday}%'" if awday != 'None' else '', dbRoom))
            results = self.system_self.cursor.fetchall()       
            if len(results)==1:
                l='يوجد لَدَي تَذكير واحِد \n'
            elif len(results) == 2:
                l='يوجد لَدَي تَذكيران \n'
            else:
                l='يوجد لَدَي %s تَذكيرات \n'%(len(results))
            for r in results:
                if r['room'] in room and room[r['room']] == 'None':
                    myroom = f"في {self.rooms[r['room']]['names'][0]}"
                elif r['room'] in room and room[r['room']] != 'myroom':
                    myroom = f"في {room[r['room']]}"
                else:
                    myroom = ""
                l+='رَقَم %s '%(results.index(r)+1)
                say = get_say(year=r['year'],month=r['month'],mday=r['mday'],awday=r['wday'],nclock=r['clock'],re=r['re'],room=None)    
                l+=say
                l+= f' {myroom}'
                if r['state']=='ON':
                    l+=' , و حالَتَهُ يَعمَل'
                else:
                    l+=' , و حالَتَهُ مُتَوَقِف'
                l+=' \n'
            l=l.replace('Jan','1')
            l=l.replace('Feb','2')
            l=l.replace('Mar','3')
            l=l.replace('Apr','4')
            l=l.replace('May','5')
            l=l.replace('Jun','6')
            l=l.replace('Jul','7')
            l=l.replace('Aug','8')
            l=l.replace('Sep','9')
            l=l.replace('Oct','10')
            l=l.replace('Nov','11')
            l=l.replace('Dec','12')
            
            self.system_self.tts.say(l)
                    
                
    elif state == 'ON':
        if '#' in nclock:
            nclock=nclock.replace('#','')
        if re == 'None':
            re = 'OFF'
        for i in room:
            self.system_self.cursor.execute('INSERT INTO Calender(name,year,month,mday,wday,clock,state,re,room) values(%s, %s, %s,%s,%s, %s, %s, %s, %s)', (alarm,year.replace('#','') if awday == 'None' else 'None',month.replace('#','') if awday == 'None' else 'None',mday.replace('#','') if awday == 'None' else 'None',awday,nclock,state,re,i,))
            self.system_self.db.commit()
        print(year,month,mday,nclock)
        say = get_say(year=year,month=month,mday=mday,awday=awday,nclock=nclock,re=re,room=room)
        publish.single('edit','1', hostname=st.ip)
        self.system_self.tts.say(f'تمَت إضافَة المَوعِد الى قائِمَة التَقويم {say}')
    else:
        self.system_self.tts.say('انتَ لا تَمتَلِك اي تَذكيرات')
    
def alarM(self,awday,nclock,re,state,room):
    rooms = ''
    dbRoom = ''
    print(1)
    for i in room:
        if room[i] == 'None':
            dbRoom = ''
            
        else:
            rooms = rooms+f"'{i}', "
    if rooms != '':
        rooms = rooms.strip()
        rooms = rooms[:-1]
        dbRoom=f'AND room IN ({rooms})'
    print(2)
    self.system_self.cursor.execute("SELECT id,state FROM Alarm WHERE id > 0 %s %s %s"% (f"AND clock LIKE '{nclock}'" if '#' not in nclock else '', f"AND wday LIKE '%{awday}%'" if awday != 'None' else '', dbRoom))
    print("SELECT id,state FROM Alarm WHERE id > 0 %s %s %s"% (f"AND clock LIKE '{nclock}'" if '#' not in nclock else '', f"AND wday LIKE '%{awday}%'" if awday != 'None' else '', dbRoom))
    say = get_say(mday=None,month=None,year=None,awday=awday,nclock=nclock,re=re,room=room)
    results = self.system_self.cursor.fetchall()
    if results:
        if state == 'OFF':
            for i in results:
                self.system_self.cursor.execute("DELETE FROM Alarm WHERE id = '%s'" % (i['id']))
                self.system_self.db.commit()
            say = update(results=results,alarm=None,say=say,state=state,type='مُنَبِهْ' if len(results)==1 else 'المُنَبِهاتْ')
            self.system_self.tts.say(say)
            publish.single('edit','1', hostname=st.ip)
            return
        elif state =='STOP':
            for i in results:
                self.system_self.cursor.execute("UPDATE Alarm SET state = '%s' WHERE id = '%s'" % (state,i['id']))
                self.system_self.db.commit()
            say = update(results=results,alarm=None,say=say,state=state,type='مُنَبِهْ' if len(results)==1 else 'المُنَبِهاتْ')
            self.system_self.tts.say(say)
            publish.single('edit','1', hostname=st.ip)
            return
        elif state =='ON':
            for i in results:
                self.system_self.cursor.execute("UPDATE Alarm SET state = '%s' WHERE id = '%s'" % (state,i['id']))
                self.system_self.db.commit()
            say = update(results=results,alarm=None,say=say,state=state,type='مُنَبِهْ' if len(results)==1 else 'المُنَبِهاتْ')
            self.system_self.tts.say(say)
            publish.single('edit','1', hostname=st.ip)
            return
        elif state =='_':
            self.system_self.cursor.execute("SELECT * FROM Alarm WHERE id > 0  %s %s %s"% (f"AND clock LIKE '{nclock}'" if '#' not in nclock else '', f"AND wday LIKE '%{awday}%'" if awday != 'None' else '', dbRoom))
            results = self.system_self.cursor.fetchall()       
            if len(results)==1:
                l='يوجد لَدَي مُنَبِهْ واحِد \n'
            elif len(results) == 2:
                l='يوجد لَدَي مُنَبِهين \n'
            else:
                l='يوجد لَدَي %s مُنَبِهات \n'%(len(results))
            for r in results:
                if r['room'] in room and room[r['room']] == 'None':
                    myroom = f"في {self.rooms[r['room']]['names'][0]}"
                elif r['room'] in room and room[r['room']] != 'myroom':
                    myroom = f"في {room[r['room']]}"
                else:
                    myroom = ""
                l+='رَقَم %s '%(results.index(r)+1)
                say = get_say(year=None,month=None,mday=None,awday=r['wday'],nclock=r['clock'],re=r['re'],room=None)    
                l+=say
                l+= f' {myroom}'
                if r['state']=='ON':
                    l+=' , و حالَتَهُ يَعمَل'
                else:
                    l+=' , و حالَتَهُ مُتَوَقِف'
                l+=' \n'
            l=l.replace('Jan','1')
            l=l.replace('Feb','2')
            l=l.replace('Mar','3')
            l=l.replace('Apr','4')
            l=l.replace('May','5')
            l=l.replace('Jun','6')
            l=l.replace('Jul','7')
            l=l.replace('Aug','8')
            l=l.replace('Sep','9')
            l=l.replace('Oct','10')
            l=l.replace('Nov','11')
            l=l.replace('Dec','12')
            
            self.system_self.tts.say(l)
                    
                
    elif state == 'ON':
        if '#' in nclock:
            nclock=nclock.replace('#','')
        
        for i in room:
            self.system_self.cursor.execute('INSERT INTO Alarm(wday,clock,state,re,room) values(%s, %s, %s, %s, %s)', (awday,nclock,state,re,i,))
            self.system_self.db.commit()
        say = get_say(year=None,month=None,mday=None,awday=awday,nclock=nclock,re=re,room=room)
        publish.single('edit','1', hostname=st.ip)
        self.system_self.tts.say(f'تمَت إضافَة المَوعِد الى قائِمَة المُنَبِهات {say}')
    else:
        self.system_self.tts.say('انتَ لا تَمتَلِك اي مُنَبِهْ')

def Job(self,awday,nclock,re,state,topics,command):
    if state == 'ON':
        # print(111)  # رسالة تشخيص معطلة
        if '#' in nclock:
            nclock=nclock.replace('#','')
        if re == 'None':
            re = 'OFF'
        if awday == 'None' and re == 'ON':
            awday == 'Sat Sun Mon Tue Wed Thu Fri'
        # print(222222)  # رسالة تشخيص معطلة
        for topic in topics:
            route={}
            t = topic.split('/')
                
            if topic == 'ROOMS/*/*/*':
                route['home']=command
                
            elif t[-1]!='*':
                self.system_self.cursor.execute('SELECT id,Type,Rooms FROM Devices WHERE id = %s',(t[-1],))
                device = self.system_self.cursor.fetchone()
                route[device['Rooms']]={t[-1]:None}
                route[device['Rooms']][t[-1]]=deviceType(command=command,type=device['Type'],d=device)

            elif t[1]!= '*':
                if t[2]=='*' and t[3]=='*':
                    route[t[1]]=command
                        
                elif t[2]!='*' and t[3]=='*':
                    self.system_self.cursor.execute('SELECT id,Type FROM Devices WHERE Type LIKE %s',(t[2].replace('%','')+'%',))
                    device = self.system_self.cursor.fetchall()
                    route[t[1]]={}
                
                    for d in device:
                        print(command)
                        route[t[1]][d['id']]=deviceType(command=command,type=t[2],d=d)                        
            elif t[1]=='*' and t[2]!='*':
                self.system_self.cursor.execute('SELECT id,Type,Rooms FROM Devices WHERE Type LIKE %s',(t[2].replace('%','')+'%',))
                device = self.system_self.cursor.fetchall()

                for d in device:
                    if d['Rooms'] in route:
                        route[d['Rooms']][d['id']]=deviceType(command=command,type=t[2],d=d)                        
                    else:
                        route[d['Rooms']]={}
                        route[d['Rooms']][d['id']]=deviceType(command=command,type=t[2],d=d)

            
                # انشاء نص من اجل الاجهزه المعرفه و الغرف المعرفه
            route = json.dumps(route)
            print(topic,command)
            self.system_self.cursor.execute('INSERT INTO ADevice(route,topic,command,wday,nclock,re,state) values(%s, %s, %s, %s, %s, %s, %s)', (route,topic,command,awday,nclock, re,'ON'))
            self.system_self.db.commit()
        publish.single('edit','1', hostname=st.ip)
    
def alarms(self):

    try:
        # print('3333333333333333')  # رسالة تشخيص معطلة
        self.db.commit()
        self.cursor.execute("SELECT Rooms FROM Devices WHERE id LIKE '%s'"%(ni.ifaddresses('wlan0')[17][0]['addr']))
        result = self.cursor.fetchone()
        # print('8888888888888888')  # رسالة تشخيص معطلة
        if result:
            if result['Rooms'] != 'x':
                room=result['Rooms']
                # print(111)  # رسالة تشخيص معطلة
                try:
                    dt = datetime.datetime.now()
                    # print(44444)  # رسالة تشخيص معطلة
                    if int(dt.strftime('%S'))<55:
                        # print(2222)  # رسالة تشخيص معطلة
                        self.cursor.execute("SELECT * FROM ADevice")
                        results = self.cursor.fetchall()
                        for result in results:
                            if result.get('state') == 'ON':
                                dt = datetime.datetime.now()
                                alarm_clock = str(result.get('nclock'))
                                now_clock = dt.strftime('%l:%M %p').strip()
                                alarm_wday = ''
                                now_wday = dt.strftime('%a')
                                days=['Sat','Sun', 'Mon','Tue', 'Wed','Thu', 'Fri']
                                
                                if result.get('wday') != 'None':

                                    if dt.strftime('%a') in result.get('wday').split(' '):
                                        alarm_wday = dt.strftime('%a')
                                    else:
                                        alarm_wday='None'
                                else:
                                    alarm_wday = dt.strftime('%a')
                                alarm = alarm_wday + ' '  + alarm_clock
                                now = now_wday + ' '+ now_clock
                                print(alarm,now)
                                if alarm == now :
                                    topic= result.get('topic')
                                    command= result.get('command')
                                    route = result.get('route')
                                    route=json.loads(route)
                                    if topic != None:
                                        publish.single(topic,command, hostname=st.ip)
                                        publish.single('edit','1', hostname=st.ip)
                                    else:
                                        if 'home' in route:
                                            print(11111)
                                            handle_device.commandRooms(self,command=route['home'])
                                            print(22222)
                                        else:
                                            for r in route:
                                                if route[r]==True or route[r]==False:
                                                    handle_device.commandRooms(self,command=route[r],rooms=[r])
                                                else:
                                                    for d in route[r]:
                                                        print(444444444444444444444444444444444)
                                                        if 'degree' in route[r][d]:
                                                            command='RUN ' if route[r][d]['state']==True else 'OFF '
                                                            if route[r][d]['state']==True:
                                                                if route[r][d]['degree']!=None:
                                                                    command+=str(route[r][d]['degree'])+' '
                                                                else:
                                                                    command+='0 '
                                                                command+='VAN '
                                                                if route[r][d]['speed']!=None:
                                                                    command+=str(route[r][d]['speed'])+' '
                                                                else:
                                                                    command+='0 '
                                                                if route[r][d]['type']!=None:
                                                                    command+='AC' if route[r][d]['type']=='تبريد' else 'HEAT' if route[r][d]['type']=='تدفئة' else 'VAN'
                                                                else:
                                                                    command+='XX '
                                                            command+='XX:XX:XX:XX:XX:XX'
                                                            topic='ROOMS/%s/AC%%/%s'%(r,d,)
                                                            print(command)
                                                            print(topic)
                                                            publish.single(topic,command, hostname=st.ip)
                                                        elif 'ch' in route[r][d]:
                                                            topic='ROOMS/%s/TV%%/%s'%(r,d,)
                                                            if route[r][d]['state']==False:
                                                                command='POWER-OFF XX:XX:XX:XX:XX:XX'
                                                                publish.single(topic,command, hostname=st.ip)
                                                            else:
                                                                if route[r][d]['ch']!=None:
                                                                    if '+' in route[r][d]['ch'] or '-' in route[r][d]['ch']:
                                                                        command='CH '+ route[r][d]['ch'][0]+ ' '+route[r][d]['ch'][1:]+' '
                                                                    else:
                                                                        command='CH = '+route[r   ][d]['ch']+' '
                                                                    command+='XX:XX:XX:XX:XX:XX'
                                                                    publish.single(topic,command, hostname=st.ip)
                                                                if route[r][d]['v']!=None:
                                                                    if route[r][d]['v']==True:
                                                                        command='SIL-ON'
                                                                        publish.single(topic,command, hostname=st.ip)
                                                                    elif route[r][d]['v']==False:
                                                                        command='SIL-OFF'
                                                                        publish.single(topic,command, hostname=st.ip)
                                                                    if '+' in route[r][d]['v'] or '-' in route[r][d]['ch']:
                                                                        command='VOICE '+ route[r][d]['v'][0]+ ' '+route[r][d]['ch'][1:]+' '
                                                                    else:
                                                                        command='VOICE = '+route[r   ][d]['v']+' '
                                                                    command+='XX:XX:XX:XX:XX:XX'
                                                                    publish.single(topic,command, hostname=st.ip)
                                                                if route[r][d]['ch']==None and route[r][d]['v']!=None:
                                                                    command='POWER-ON XX:XX:XX:XX:XX:XX'
                                                                    publish.single(topic,command, hostname=st.ip)
                                                        else :
                                                            for v in route[r][d]:
                                                                if route[r][d][v]!=None:
                                                                    command+=v+'_RUN ' if route[r][d][v]==True else '_OFF '
                                                            command+='XX:XX:XX:XX:XX:XX'
                                                            publish.single(topic,command, hostname=st.ip)
                                    self.tts.say('done')
                                    print('تم تنفيذ المهمه')
                                    if result.get('re')=='OFF':
                                        if result.get('wday') != 'None'  and len(result.get('wday').split(' ')) != 1:
                                                wday = result.get('wday').replace(now_wday,'')
                                                wday = wday.replace('  ',' ')
                                                wday = wday.strip()
                                                self.cursor.execute("UPDATE ADevice SET wday = '%s' WHERE id = '%s'" % (wday,str(result.get('id')),))
                                        else:
                                            self.cursor.execute("DELETE FROM ADevice WHERE id = '%s'" % (str(result.get('id')),))
                                        self.db.commit()
                                    publish.single('edit','1', hostname=st.ip)
                        # print(333333)  # رسالة تشخيص معطلة
                        self.cursor.execute("SELECT * FROM Alarm WHERE room LIKE '%s'"%(room))
                        # print(room)  # رسالة تشخيص معطلة
                        results = self.cursor.fetchall()
                        # print(3332222)  # رسالة تشخيص معطلة
                        for result in results:
                            # print(5555555)  # رسالة تشخيص معطلة
                            if result.get('state')=='ON':
                                print(66)
                                dt = datetime.datetime.now()
                                alarm_clock = str(result.get('clock'))
                                now_clock = dt.strftime('%l:%M %p').strip()
                                alarm_wday = ''
                                now_wday = dt.strftime('%a')
                                days=['Sat','Sun', 'Mon','Tue', 'Wed','Thu', 'Fri']
                                
                                if result.get('wday') != 'None':

                                    if dt.strftime('%a') in result.get('wday').split(' '):
                                        alarm_wday = dt.strftime('%a')
                                    else:
                                        alarm_wday='None'
                                else:
                                    alarm_wday = dt.strftime('%a')
                                alarm = alarm_wday + ' '  + alarm_clock
                                now = now_wday + ' '+ now_clock
                                now = now.strip()
                                alarm = alarm.strip()
                                print(now,alarm)
                                if alarm == now :
                                    self.tts.say('alarm')
                                    
                                
                                    if result.get('re')=='OFF':
                                        
                                        if result.get('wday') != 'None'  and len(result.get('wday').split(' ')) != 1:
                                            wday = result.get('wday').replace(now_wday,'')
                                            wday = wday.replace('  ',' ')
                                            wday = wday.strip()
                                            self.cursor.execute("UPDATE Alarm SET wday = '%s' WHERE id = '%s'" % (wday,str(result.get('id')),))
                                        else:
                                            self.cursor.execute("UPDATE Alarm SET state = 'STOP' WHERE id = '%s'" % (str(result.get('id')),))
                                        self.db.commit()
                                    publish.single('edit','1', hostname=st.ip)
                                            
#                         dt = datetime.now()
#                         time.sleep((55-int(dt.strftime('%S')))+5)
                except:
                    print(777777)
                    
            
    except:
        print(3333)
        