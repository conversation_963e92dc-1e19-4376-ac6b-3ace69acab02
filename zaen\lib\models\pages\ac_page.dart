import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:sleek_circular_slider/sleek_circular_slider.dart';
import 'dart:math' as math;
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Widget ACPage({
  String? id,
  var deviceState,
  bool swingState = false,
  var speedState = 2,
  var typeState = 1,
  var degree = 25.0,
  String? roomN,
  required bool connect,
  String? acPrivName,
  required Function() acRun,
  required Function() del,
  required Function() Dfavorite,
  required Function() editRoom,
  required Function(String?) editNames,
  required Function(bool?, String?) editPrivName,
  required Function(bool?) switchState,
  required Function(double?) sliderState,
  required Function(int?) acTypeState,
  required Function() acSwingState,
  required Function(int?) acSpeedsState,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  TextEditingController editPriv = TextEditingController(
    text: acPrivName != 'x' ? acPrivName : 'X',
  );
  bool privN = false;

  return pageSlide(
    content: GestureDetector(
      onTap: () {
        if (privN) {
          if (editPriv.text == '' ||
              editPriv.text == null ||
              editPriv.text == 'X' ||
              editPriv.text == 'x') {
            editPriv.text = acPrivName != null ? acPrivName : 'X';
            privN = false;
          } else {
            for (var i = 0; i < editPriv.text.length; i++) {
              if (arabic.contains(editPriv.text[i]) ||
                  editPriv.text[i].isNumericOnly) {
                privN = true;
              } else {
                editPriv.text = acPrivName != null ? acPrivName : 'X';
                privN = false;
                break;
              }
            }

            if (privN) {
              editPrivName(privN, editPriv.text);
              privN = false;
            }
          }
        }
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: PageView(
        scrollDirection: Axis.vertical,
        // shrinkWrap: true,
        onPageChanged: (i) {
          FocusManager.instance.primaryFocus?.unfocus();
          editPriv.text = acPrivName != null ? acPrivName : 'X';
        },
        physics: BouncingScrollPhysics(),
        children: connect == true &&
                client.connectionStatus!.state.name == 'connected'
            ? [
                Container(
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: sizedWidth * 0.035),
                        child: Row(mainAxisSize: MainAxisSize.min, children: [
                          Directionality(
                            textDirection: TextDirection.rtl,
                            child: switchStyle(
                                value: deviceState, onChanged: switchState),
                          ),
                          Expanded(
                            child: Container(
                              alignment: Alignment.bottomRight,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Container(
                                      width: controller.sizedWidth * 0.44,
                                      padding: EdgeInsets.only(
                                          right: controller.sized * 0.01),
                                      child: FittedBox(
                                        alignment: Alignment.centerRight,
                                        fit: BoxFit.scaleDown,
                                        child: txtStyle(
                                            align: TextAlign.right,
                                            txt: acPrivName != 'x'
                                                ? acPrivName!
                                                : 'لا يوجد اسم'),
                                      )),
                                  Container(
                                      padding: EdgeInsets.only(
                                          left: controller.sizedWidth * 0.01),
                                      decoration: BoxDecoration(
                                          border: Border(
                                              left: BorderSide(
                                                  color: AppColors.textColor
                                                      .withOpacity(0.25),
                                                  width: 1.5))),
                                      child: txtStyle(
                                          align: TextAlign.right,
                                          txt: 'مكيف هواء',
                                          color: AppColors.textColor3)),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            width: sizedWidth * 0.01,
                          ),
                          iconStyle(
                            icon: Icons.ac_unit,
                            color: AppColors.warningColor,
                            size: sized * 0.035,
                          ),
                        ]),
                      ),
                      Column(
                        children: [
                          SizedBox(
                            height: sizedHeight * 0.02,
                          ),
                          Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                IconButton(
                                  onPressed: () {
                                    acTypeState(2);
                                  },
                                  icon: Icon(Icons.air_rounded),
                                  color: typeState == 2
                                      ? Color.fromARGB(255, 61, 182, 222)
                                      : AppColors.textColor2.withOpacity(0.2),
                                  iconSize: sized * 0.052,
                                ),
                                SizedBox(
                                  width: sizedWidth * 0.075,
                                ),
                                IconButton(
                                  onPressed: () {
                                    acTypeState(1);
                                  },
                                  icon: Icon(Icons.ac_unit_rounded),
                                  color: typeState == 1
                                      ? Colors.cyan
                                      : AppColors.textColor2.withOpacity(0.2),
                                  iconSize: sized * 0.052,
                                ),
                                SizedBox(
                                  width: sizedWidth * 0.075,
                                ),
                                IconButton(
                                  onPressed: () {
                                    acTypeState(0);
                                  },
                                  icon: Icon(Icons.wb_sunny_rounded),
                                  color: typeState == 0
                                      ? AppColors.warningColor
                                      : AppColors.textColor2.withOpacity(0.2),
                                  iconSize: sized * 0.052,
                                ),
                              ]),
                          Directionality(
                            textDirection: TextDirection.rtl,
                            child: Container(
                              margin: EdgeInsets.only(top: sizedHeight * 0.025),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  SleekCircularSlider(
                                    min: 16,
                                    max: 30,
                                    initialValue: degree.toDouble(),
                                    appearance: CircularSliderAppearance(
                                        infoProperties: InfoProperties(
                                            modifier: (percentage) =>
                                                '${percentage.toInt()}°',
                                            mainLabelStyle: TextStyle(
                                                color: AppColors.textColor2,
                                                fontSize: sized * 0.05,
                                                fontWeight: FontWeight.bold)),
                                        size: (sized) * 0.16,
                                        customColors: CustomSliderColors(
                                            hideShadow: true,
                                            trackColor: AppColors.textColor2
                                                .withOpacity(0.2),
                                            progressBarColors: <Color>[
                                              Color(0xFF1AB600),
                                              Color(0xFF6DD400),
                                            ])),
                                    onChangeEnd: sliderState,
                                  )
                                ],
                              ),
                            ),
                          ),
                          Text(
                            'سرعه المروحة',
                            style: TextStyle(
                                color: AppColors.textColor3,
                                fontSize: sized * 0.012),
                          ),
                          Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Text(
                                  '1',
                                  style: TextStyle(
                                      color: speedState == 1
                                          ? Colors.blue.shade600
                                          : AppColors.textColor2
                                              .withOpacity(0.2),
                                      fontSize: sized * 0.015),
                                ),
                                IconButton(
                                  onPressed: () {
                                    acSpeedsState(1);
                                  },
                                  icon: Icon(Icons.air_rounded),
                                  color: speedState == 1
                                      ? Colors.blue.shade600
                                      : AppColors.textColor2.withOpacity(0.2),
                                  iconSize: sized * 0.043,
                                ),
                                SizedBox(
                                  width: sizedWidth * 0.05,
                                ),
                                Text(
                                  '2',
                                  style: TextStyle(
                                      color: speedState == 2
                                          ? Colors.blue.shade600
                                          : AppColors.textColor2
                                              .withOpacity(0.2),
                                      fontSize: sized * 0.015),
                                ),
                                IconButton(
                                  onPressed: () {
                                    acSpeedsState(2);
                                  },
                                  icon: Icon(Icons.air_rounded),
                                  color: speedState == 2
                                      ? Colors.blue.shade600
                                      : AppColors.textColor2.withOpacity(0.2),
                                  iconSize: sized * 0.043,
                                ),
                                SizedBox(
                                  width: sizedWidth * 0.05,
                                ),
                                Text(
                                  '3',
                                  style: TextStyle(
                                      color: speedState == 3
                                          ? Colors.blue.shade600
                                          : AppColors.textColor2
                                              .withOpacity(0.2),
                                      fontSize: sized * 0.015),
                                ),
                                IconButton(
                                  onPressed: () {
                                    acSpeedsState(3);
                                  },
                                  icon: Icon(Icons.air_rounded),
                                  color: speedState == 3
                                      ? Colors.blue.shade600
                                      : AppColors.textColor2.withOpacity(0.2),
                                  iconSize: sized * 0.043,
                                ),
                                SizedBox(
                                  width: sizedWidth * 0.045,
                                ),
                                IconButton(
                                  onPressed: () {
                                    acSpeedsState(4);
                                  },
                                  icon: Icon(Icons.autorenew_rounded),
                                  color: speedState == 4
                                      ? Colors.blue.shade600
                                      : AppColors.textColor2.withOpacity(0.2),
                                  iconSize: sized * 0.043,
                                ),
                              ]),
                          containerIconsOption(
                            color: AppColors.backgroundColor3.withOpacity(0.7),
                            padding: EdgeInsets.only(right: sizedWidth * 0.02),
                            margin: EdgeInsets.only(top: sizedHeight * 0.015),
                            content: Row(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                IconButton(
                                  onPressed: acSwingState,
                                  icon: swingState
                                      ? Icon(Icons.check_box_rounded)
                                      : Icon(Icons
                                          .check_box_outline_blank_rounded),
                                  color: swingState
                                      ? AppColors.primaryColor
                                      : AppColors.current.onSurfaceVariant,
                                  iconSize: sized * 0.032,
                                ),
                                SizedBox(
                                  width: sizedWidth * 0.085,
                                ),
                                Text(
                                  'التأرجح',
                                  style: TextStyle(
                                      color: AppColors.textColor3,
                                      fontSize: sized * 0.015,
                                      fontWeight: FontWeight.bold),
                                )
                              ],
                            ),
                          ),
                          SizedBox(
                            height: sizedHeight * 0.025,
                          ),
                          Transform.rotate(
                            angle: 180 * math.pi / 180,
                            child: IconButton(
                              padding: EdgeInsets.zero,
                              onPressed: acRun,
                              icon: Icon(
                                Icons.play_circle_fill_rounded,
                                size: sized * 0.06,
                                color: AppColors.primaryColor.withOpacity(0.85),
                              ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                pageSetting(
                    del: del,
                    id: id,
                    Dfavorite: Dfavorite,
                    editRoom: editRoom,
                    editNames: editNames,
                    editPrivName: editPrivName,
                    roomN: roomN,
                    privName: acPrivName,
                    type: 'مكيف هواء')
              ]
            : [
                pageSetting(
                    del: del,
                    id: id,
                    Dfavorite: Dfavorite,
                    editRoom: editRoom,
                    editNames: editNames,
                    editPrivName: editPrivName,
                    roomN: roomN,
                    privName: acPrivName,
                    type: 'مكيف هواء',
                    connect: false)
              ],
      ),
    ),
  );
}
