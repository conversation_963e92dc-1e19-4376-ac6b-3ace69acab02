import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:sleek_circular_slider/sleek_circular_slider.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'routine_variables.dart';

Widget addRoutineAc({
  required Map device,
  required Map roomData,
  setState1,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  var acPrivName = device['priv'];

  if (controller.addRoutine.containsKey(roomData['id']) &&
      controller.addRoutine[roomData['id']] != true &&
      controller.addRoutine[roomData['id']] != false &&
      controller.addRoutine[roomData['id']].containsKey(device['id'])) {
    if (controller.addRoutine[roomData['id']][device['id']]['state'] != null) {
      deviceState =
          controller.addRoutine[roomData['id']][device['id']]['state'];
    } else {
      deviceState = device['state'];
    }
    if (controller.addRoutine[roomData['id']][device['id']]['degree'] != null) {
      degree = controller.addRoutine[roomData['id']][device['id']]['degree'];
    } else {
      degree = device['degree'];
    }
    if (degree < 16) {
      degree = 16;
    }
    if (controller.addRoutine[roomData['id']][device['id']]['speed'] != null) {
      speedState = controller.addRoutine[roomData['id']][device['id']]['speed']
          .toString();
    } else {
      speedState = device['speed'].toString();
    }
    if (controller.addRoutine[roomData['id']][device['id']]['swing'] != null) {
      swingState = controller.addRoutine[roomData['id']][device['id']]['swing'];
    } else {
      swingState = device['swing'];
    }
    if (controller.addRoutine[roomData['id']][device['id']]['type'] != null) {
      typeState = controller.addRoutine[roomData['id']][device['id']]['type'];
    } else {
      typeState = device['type'];
    }
  } else {
    deviceState = device['state'];
    degree = device['degree'];
    speedState = device['speed'].toString();
    swingState = device['swing'];
    typeState = device['type'];
  }
  return StatefulBuilder(builder: ((context, setState) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Column(
        children: [
          SizedBox(
            height: controller.sizedHight * 0.02,
          ),
          Row(mainAxisSize: MainAxisSize.min, children: [
            containerIconsOption(
              content: Row(
                children: [
                  Directionality(
                    textDirection: TextDirection.rtl,
                    child: switchStyle(
                        size: controller.sized * 0.001,
                        value: deviceState,
                        onChanged: (s) {
                          setState(
                            () {
                              deviceState = s;
                            },
                          );

                          if (controller.addRoutine
                              .containsKey(roomData['id'])) {
                            if (controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id'])) {
                              if (controller.addRoutine[roomData['id']]
                                      [device['id']]['state'] !=
                                  null) {
                                controller.addRoutine[roomData['id']]
                                    [device['id']]['state'] = s;
                                if (s == false) {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['degree'] = null;
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['speed'] = null;
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['swing'] = null;
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['type'] = null;
                                }
                              }
                            }
                          }
                        }),
                  ),
                  IconButton(
                      onPressed: () {
                        if (controller.addRoutine.containsKey('home')) {
                          controller.addRoutine = {};
                        }
                        if (controller.addRoutine.containsKey(roomData['id'])) {
                          if (controller.addRoutine[roomData['id']] == true ||
                              controller.addRoutine[roomData['id']] == false) {
                            controller.addRoutine[roomData['id']] = {};
                            controller.addRoutine[roomData['id']]
                                [device['id']] = {
                              'state': deviceState,
                              'degree': null,
                              'type': null,
                              'speed': null,
                              'swing': null
                            };
                          } else {
                            if (controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                              if (controller.addRoutine[roomData['id']]
                                      [device['id']]['state'] ==
                                  null) {
                                controller.addRoutine[roomData['id']]
                                    [device['id']]['state'] = deviceState;
                              } else {
                                controller.addRoutine[roomData['id']]
                                    [device['id']]['state'] = null;

                                if (controller
                                    .addRoutine[roomData['id']][device['id']]
                                    .values
                                    .every((element) => element == null)) {
                                  controller.addRoutine[roomData['id']]
                                      .remove(device['id']);
                                  if (controller
                                      .addRoutine[roomData['id']].isEmpty) {
                                    controller.addRoutine
                                        .remove(roomData['id']);
                                  }
                                }
                              }
                            } else {
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {
                                'state': deviceState,
                                'degree': null,
                                'type': null,
                                'speed': null,
                                'swing': null
                              };
                            }
                          }
                        } else {
                          controller.addRoutine[roomData['id']] = {};
                          controller.addRoutine[roomData['id']]
                              [device['id']] = {
                            'state': deviceState,
                            'degree': null,
                            'type': null,
                            'speed': null,
                            'swing': null
                          };
                        }
                        setState1(
                          () {
                            controller.addRoutine;
                          },
                        );
                        print(controller.addRoutine);
                      },
                      icon: iconStyle(
                        icon: controller.addRoutine
                                    .containsKey(roomData['id']) &&
                                controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id']) &&
                                controller.addRoutine[roomData['id']]
                                        [device['id']]['state'] !=
                                    null
                            ? Icons.check_circle_rounded
                            : Icons.add_circle_outline_rounded,
                        color: controller.addRoutine
                                    .containsKey(roomData['id']) &&
                                controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id']) &&
                                controller.addRoutine[roomData['id']]
                                        [device['id']]['state'] !=
                                    null
                            ? AppColors.primaryColor
                            : AppColors.warningColor,
                      ))
                ],
              ),
            ),
            Expanded(
              child: Container(
                margin: EdgeInsets.zero,
                alignment: Alignment.bottomRight,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                        child: txtStyle(
                      txt: acPrivName!,
                    )),
                    SizedBox(
                      width: sized * 0.01,
                    ),
                    Container(
                      padding: EdgeInsets.only(left: sizedWidth * 0.01),
                      decoration: BoxDecoration(
                          border: Border(
                              left: BorderSide(
                                  color: AppColors.textColor.withOpacity(0.25),
                                  width: 1.5))),
                      child: txtStyle(
                        txt: 'مكيف هواء',
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              width: sizedWidth * 0.01,
            ),
            iconStyle(
              icon: Icons.ac_unit,
              color: AppColors.warningColor,
              size: sized * 0.035,
            ),
          ]),
          SizedBox(
            height: sizedHeight * 0.02,
          ),
          containerIconsOption(
            content: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  IconButton(
                    onPressed: () {
                      setState(
                        () {
                          typeState = 'مروحة';
                        },
                      );
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        print('1111111111111111111111111111');
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          print('2222222222222222222222222');
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['type'] !=
                              null) {
                            print('33333333333333333333333333333333333');
                            controller.addRoutine[roomData['id']][device['id']]
                                ['type'] = 'مروحة';
                            print(controller.addRoutine);
                          }
                        }
                      }
                      // acTypeState(2);
                    },
                    padding: EdgeInsets.symmetric(
                        horizontal: controller.sizedWidth * 0.05),
                    icon: iconStyle(
                      icon: Icons.air_rounded,
                      color: typeState == 'مروحة'
                          ? Color.fromARGB(255, 61, 182, 222)
                          : AppColors.textColor.withOpacity(0.25),
                      size: sized * 0.04,
                    ),
                  ),
                  SizedBox(
                    width: sizedWidth * 0.01,
                  ),
                  IconButton(
                    onPressed: () {
                      setState(
                        () {
                          typeState = 'تبريد';
                        },
                      );
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['type'] !=
                              null) {
                            controller.addRoutine[roomData['id']][device['id']]
                                ['type'] = 'تبريد';
                          }
                        }
                      }
                      // acTypeState(1);
                    },
                    padding: EdgeInsets.symmetric(
                        horizontal: controller.sizedWidth * 0.05),
                    icon: iconStyle(
                      icon: Icons.ac_unit_rounded,
                      color: typeState == 'تبريد'
                          ? Colors.blue.shade600
                          : AppColors.textColor.withOpacity(0.25),
                      size: sized * 0.04,
                    ),
                  ),
                  SizedBox(
                    width: sizedWidth * 0.01,
                  ),
                  IconButton(
                    onPressed: () {
                      setState(
                        () {
                          typeState = 'تدفئة';
                        },
                      );
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['type'] !=
                              null) {
                            controller.addRoutine[roomData['id']][device['id']]
                                ['type'] = 'تدفئة';
                          }
                        }
                      }
                      // acTypeState(0);
                    },
                    padding: EdgeInsets.symmetric(
                        horizontal: controller.sizedWidth * 0.05),
                    icon: iconStyle(
                      icon: Icons.wb_sunny_rounded,
                      color: typeState == 'تدفئة'
                          ? AppColors.warningColor
                          : AppColors.textColor.withOpacity(0.25),
                      size: sized * 0.04,
                    ),
                  ),
                  SizedBox(
                      width: sizedWidth * 0.05, height: sizedHeight * 0.08),
                  Expanded(
                      child: IconButton(
                          onPressed: () {
                            if (controller.addRoutine.containsKey('home')) {
                              controller.addRoutine = {};
                            }
                            if (controller.addRoutine
                                .containsKey(roomData['id'])) {
                              if (controller.addRoutine[roomData['id']] ==
                                      true ||
                                  controller.addRoutine[roomData['id']] ==
                                      false) {
                                controller.addRoutine[roomData['id']] = {};
                                controller.addRoutine[roomData['id']]
                                    [device['id']] = {
                                  'state': true,
                                  'degree': null,
                                  'type': typeState,
                                  'speed': null,
                                  'swing': null
                                };
                              } else {
                                if (controller.addRoutine[roomData['id']]
                                    .containsKey(device['id'])) {
                                  if (controller.addRoutine[roomData['id']]
                                          [device['id']]['type'] ==
                                      null) {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['type'] = typeState;
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['state'] = true;
                                  } else {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['type'] = null;
                                    if (controller
                                        .addRoutine[roomData['id']]
                                            [device['id']]
                                        .values
                                        .every((element) => element == null)) {
                                      controller.addRoutine[roomData['id']]
                                          .remove(device['id']);
                                      if (controller
                                          .addRoutine[roomData['id']].isEmpty) {
                                        controller.addRoutine
                                            .remove(roomData['id']);
                                      }
                                    }
                                  }
                                } else {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']] = {
                                    'state': true,
                                    'degree': null,
                                    'type': typeState,
                                    'speed': null,
                                    'swing': null
                                  };
                                }
                              }
                            } else {
                              controller.addRoutine[roomData['id']] = {};
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {
                                'state': true,
                                'degree': null,
                                'type': typeState,
                                'speed': null,
                                'swing': null
                              };
                            }
                            setState1(
                              () {
                                controller.addRoutine;
                              },
                            );
                            print('66666666666666666666666666');
                            print(controller.addRoutine);
                          },
                          padding: EdgeInsets.only(
                              right: controller.sizedWidth * 0.0001),
                          icon: Center(
                            child: iconStyle(
                              icon: controller.addRoutine
                                          .containsKey(roomData['id']) &&
                                      controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id']) &&
                                      controller.addRoutine[roomData['id']]
                                              [device['id']]['type'] !=
                                          null
                                  ? Icons.check_circle_rounded
                                  : Icons.add_circle_outline_rounded,
                              color: controller.addRoutine
                                          .containsKey(roomData['id']) &&
                                      controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id']) &&
                                      controller.addRoutine[roomData['id']]
                                              [device['id']]['type'] !=
                                          null
                                  ? AppColors.primaryColor
                                  : AppColors.warningColor,
                            ),
                          ))),
                  SizedBox(
                    width: sizedWidth * 0.01,
                  ),
                ]),
          ),
          Directionality(
            textDirection: TextDirection.rtl,
            child: containerIconsOption(
              padding: EdgeInsets.only(
                  top: sizedHeight * 0.025, left: sizedWidth * 0.025),
              margin: EdgeInsets.only(top: sizedHeight * 0.025),
              content: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                      onPressed: () {
                        if (controller.addRoutine.containsKey('home')) {
                          controller.addRoutine = {};
                        }
                        if (controller.addRoutine.containsKey(roomData['id'])) {
                          if (controller.addRoutine[roomData['id']] == true ||
                              controller.addRoutine[roomData['id']] == false) {
                            controller.addRoutine[roomData['id']] = {};
                            controller.addRoutine[roomData['id']]
                                [device['id']] = {
                              'state': true,
                              'degree': degree.toInt(),
                              'type': null,
                              'speed': null,
                              'swing': null
                            };
                          } else {
                            if (controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                              if (controller.addRoutine[roomData['id']]
                                      [device['id']]['degree'] ==
                                  null) {
                                controller.addRoutine[roomData['id']]
                                    [device['id']]['degree'] = degree.toInt();
                                controller.addRoutine[roomData['id']]
                                    [device['id']]['state'] = true;
                              } else {
                                controller.addRoutine[roomData['id']]
                                    [device['id']]['degree'] = null;
                                if (controller
                                    .addRoutine[roomData['id']][device['id']]
                                    .values
                                    .every((element) => element == null)) {
                                  controller.addRoutine[roomData['id']]
                                      .remove(device['id']);
                                  if (controller
                                      .addRoutine[roomData['id']].isEmpty) {
                                    controller.addRoutine
                                        .remove(roomData['id']);
                                  }
                                }
                              }
                            } else {
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {
                                'state': true,
                                'degree': degree.toInt(),
                                'type': null,
                                'speed': null,
                                'swing': null
                              };
                            }
                          }
                        } else {
                          controller.addRoutine[roomData['id']] = {};
                          controller.addRoutine[roomData['id']]
                              [device['id']] = {
                            'state': true,
                            'degree': degree.toInt(),
                            'type': null,
                            'speed': null,
                            'swing': null
                          };
                        }
                        setState1(
                          () {
                            controller.addRoutine;
                          },
                        );
                        print(controller.addRoutine);
                      },
                      padding: EdgeInsets.zero,
                      icon: iconStyle(
                        icon: controller.addRoutine
                                    .containsKey(roomData['id']) &&
                                controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id']) &&
                                controller.addRoutine[roomData['id']]
                                        [device['id']]['degree'] !=
                                    null
                            ? Icons.check_circle_rounded
                            : Icons.add_circle_outline_rounded,
                        color: controller.addRoutine
                                    .containsKey(roomData['id']) &&
                                controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id']) &&
                                controller.addRoutine[roomData['id']]
                                        [device['id']]['degree'] !=
                                    null
                            ? AppColors.primaryColor
                            : AppColors.warningColor,
                      )),
                  SleekCircularSlider(
                    min: 16,
                    max: 30,
                    initialValue: degree.toDouble(),
                    appearance: CircularSliderAppearance(
                        infoProperties: InfoProperties(
                            modifier: (percentage) => '${percentage.toInt()}°',
                            mainLabelStyle: TextStyle(
                                color: AppColors.textColor.withOpacity(0.35),
                                fontSize: sized * 0.05,
                                fontWeight: FontWeight.bold)),
                        size: (sized) * 0.14,
                        customColors: CustomSliderColors(
                            hideShadow: true,
                            trackColor: AppColors.textColor.withOpacity(0.25),
                            progressBarColors: <Color>[
                              Color(0xFF1AB600),
                              Color(0xFF6DD400),
                            ])),
                    onChangeEnd: (d) {
                      degree = d.toInt();
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['degree'] !=
                              null) {
                            controller.addRoutine[roomData['id']][device['id']]
                                ['degree'] = degree.toInt();
                          }
                        }
                      }
                    },
                  )
                ],
              ),
            ),
          ),
          txtStyle(
            txt: 'سرعه المروحة',
            color: AppColors.textColor.withOpacity(0.6),
            size: sized * 0.012,
          ),
          containerIconsOption(
            content: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  SizedBox(
                    width: sizedWidth * 0.01,
                  ),
                  txtStyle(
                    txt: '1',
                    color: speedState == '1'
                        ? Colors.blue.shade600
                        : AppColors.textColor.withOpacity(0.25),
                    size: sized * 0.013,
                  ),
                  IconButton(
                    onPressed: () {
                      setState(
                        () {
                          speedState = '1';
                        },
                      );
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        print('1111111111111111111111111111');
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          print('2222222222222222222222222');
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['speed'] !=
                              null) {
                            print('33333333333333333333333333333333333');
                            controller.addRoutine[roomData['id']][device['id']]
                                ['speed'] = '1';
                            print(controller.addRoutine);
                          }
                        }
                      }
                      // acSpeedsState(1);
                    },
                    padding: EdgeInsets.zero,
                    icon: iconStyle(
                      icon: Icons.air_rounded,
                      color: speedState == '1'
                          ? Colors.blue.shade600
                          : AppColors.textColor.withOpacity(0.25),
                      size: sized * 0.035,
                    ),
                  ),
                  SizedBox(
                    width: sizedWidth * 0.023,
                  ),
                  txtStyle(
                    txt: '2',
                    color: speedState == '2'
                        ? Colors.blue.shade600
                        : AppColors.textColor.withOpacity(0.25),
                    size: sized * 0.013,
                  ),
                  IconButton(
                    onPressed: () {
                      setState(
                        () {
                          speedState = '2';
                        },
                      );
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        print('1111111111111111111111111111');
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          print('2222222222222222222222222');
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['speed'] !=
                              null) {
                            print('33333333333333333333333333333333333');
                            controller.addRoutine[roomData['id']][device['id']]
                                ['speed'] = '2';
                            print(controller.addRoutine);
                          }
                        }
                      }
                      // acSpeedsState(2);
                    },
                    padding: EdgeInsets.zero,
                    icon: iconStyle(
                        icon: Icons.air_rounded,
                        color: speedState == '2'
                            ? Colors.blue.shade600
                            : AppColors.textColor.withOpacity(0.25),
                        size: sized * 0.035),
                  ),
                  SizedBox(
                    width: sizedWidth * 0.023,
                  ),
                  txtStyle(
                      txt: '3',
                      color: speedState == '3'
                          ? Colors.blue.shade600
                          : AppColors.textColor.withOpacity(0.25),
                      size: sized * 0.013),
                  IconButton(
                    onPressed: () {
                      setState(
                        () {
                          speedState = '3';
                        },
                      );
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        print('1111111111111111111111111111');
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          print('2222222222222222222222222');
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['speed'] !=
                              null) {
                            print('33333333333333333333333333333333333');
                            controller.addRoutine[roomData['id']][device['id']]
                                ['speed'] = '3';
                            print(controller.addRoutine);
                          }
                        }
                      }
                      // acSpeedsState(3);
                    },
                    padding: EdgeInsets.zero,
                    icon: iconStyle(
                      icon: Icons.air_rounded,
                      color: speedState == '3'
                          ? Colors.blue.shade600
                          : AppColors.textColor.withOpacity(0.25),
                      size: sized * 0.035,
                    ),
                  ),
                  SizedBox(
                    width: sizedWidth * 0.023,
                  ),
                  IconButton(
                    onPressed: () {
                      setState(
                        () {
                          speedState = '4';
                        },
                      );
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        print('1111111111111111111111111111');
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          print('2222222222222222222222222');
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['speed'] !=
                              null) {
                            print('33333333333333333333333333333333333');
                            controller.addRoutine[roomData['id']][device['id']]
                                ['speed'] = '4';
                            print(controller.addRoutine);
                          }
                        }
                      }
                      // acSpeedsState(4);
                    },
                    padding: EdgeInsets.zero,
                    icon: iconStyle(
                      icon: Icons.autorenew_rounded,
                      color: speedState == '4'
                          ? Colors.blue.shade600
                          : AppColors.textColor.withOpacity(0.25),
                      size: sized * 0.035,
                    ),
                  ),
                  SizedBox(
                    width: sizedWidth * 0.032,
                  ),
                  Expanded(
                    child: IconButton(
                        onPressed: () {
                          if (controller.addRoutine.containsKey('home')) {
                            controller.addRoutine = {};
                          }
                          if (controller.addRoutine
                              .containsKey(roomData['id'])) {
                            if (controller.addRoutine[roomData['id']] == true ||
                                controller.addRoutine[roomData['id']] ==
                                    false) {
                              controller.addRoutine[roomData['id']] = {};
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {
                                'state': true,
                                'degree': null,
                                'type': null,
                                'speed': speedState,
                                'swing': null
                              };
                            } else {
                              if (controller.addRoutine[roomData['id']]
                                  .containsKey(device['id'])) {
                                if (controller.addRoutine[roomData['id']]
                                        [device['id']]['speed'] ==
                                    null) {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['speed'] = speedState;
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['state'] = true;
                                } else {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['speed'] = null;
                                  if (controller
                                      .addRoutine[roomData['id']][device['id']]
                                      .values
                                      .every((element) => element == null)) {
                                    controller.addRoutine[roomData['id']]
                                        .remove(device['id']);
                                    if (controller
                                        .addRoutine[roomData['id']].isEmpty) {
                                      controller.addRoutine
                                          .remove(roomData['id']);
                                    }
                                  }
                                }
                              } else {
                                controller.addRoutine[roomData['id']]
                                    [device['id']] = {
                                  'state': true,
                                  'degree': null,
                                  'type': null,
                                  'speed': speedState,
                                  'swing': null
                                };
                              }
                            }
                          } else {
                            controller.addRoutine[roomData['id']] = {};
                            controller.addRoutine[roomData['id']]
                                [device['id']] = {
                              'state': true,
                              'degree': null,
                              'type': null,
                              'speed': speedState,
                              'swing': null
                            };
                          }
                          setState1(
                            () {
                              controller.addRoutine;
                            },
                          );
                          print(controller.addRoutine);
                        },
                        padding: EdgeInsets.zero,
                        icon: iconStyle(
                          icon: controller.addRoutine
                                      .containsKey(roomData['id']) &&
                                  controller.addRoutine[roomData['id']] !=
                                      true &&
                                  controller.addRoutine[roomData['id']] !=
                                      false &&
                                  controller.addRoutine[roomData['id']]
                                      .containsKey(device['id']) &&
                                  controller.addRoutine[roomData['id']]
                                          [device['id']]['speed'] !=
                                      null
                              ? Icons.check_circle_rounded
                              : Icons.add_circle_outline_rounded,
                          color: controller.addRoutine
                                      .containsKey(roomData['id']) &&
                                  controller.addRoutine[roomData['id']] !=
                                      true &&
                                  controller.addRoutine[roomData['id']] !=
                                      false &&
                                  controller.addRoutine[roomData['id']]
                                      .containsKey(device['id']) &&
                                  controller.addRoutine[roomData['id']]
                                          [device['id']]['speed'] !=
                                      null
                              ? AppColors.primaryColor
                              : AppColors.warningColor,
                        )),
                  )
                ]),
          ),
          containerIconsOption(
            padding: EdgeInsets.only(right: sizedWidth * 0.02),
            margin: EdgeInsets.only(top: sizedHeight * 0.015),
            content: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () {
                    setState(
                      () {
                        swingState = !swingState;
                      },
                    );

                    if (controller.addRoutine.containsKey(roomData['id'])) {
                      if (controller.addRoutine[roomData['id']] != true &&
                          controller.addRoutine[roomData['id']] != false &&
                          controller.addRoutine[roomData['id']]
                              .containsKey(device['id'])) {
                        if (controller.addRoutine[roomData['id']][device['id']]
                                ['swing'] !=
                            null) {
                          controller.addRoutine[roomData['id']][device['id']]
                              ['swing'] = swingState;
                        }
                      }
                    }
                  },
                  padding: EdgeInsets.zero,
                  icon: swingState
                      ? iconStyle(
                          icon: Icons.check_box_rounded,
                          color: AppColors.primaryColor)
                      : iconStyle(
                          icon: Icons.check_box_outline_blank_rounded,
                          color: AppColors.textColor2.withOpacity(0.25)),
                  iconSize: sized * 0.032,
                ),
                SizedBox(
                  width: sizedWidth * 0.01,
                ),
                txtStyle(
                  txt: 'التأرجح',
                  color: AppColors.textColor2.withOpacity(0.65),
                  size: sized * 0.015,
                ),
                SizedBox(
                  width: sizedWidth * 0.06,
                ),
                IconButton(
                    onPressed: () {
                      if (controller.addRoutine.containsKey('home')) {
                        controller.addRoutine = {};
                      }
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        if (controller.addRoutine[roomData['id']] == true ||
                            controller.addRoutine[roomData['id']] == false) {
                          controller.addRoutine[roomData['id']] = {};
                          controller.addRoutine[roomData['id']]
                              [device['id']] = {
                            'state': true,
                            'degree': null,
                            'type': null,
                            'speed': null,
                            'swing': swingState
                          };
                        } else {
                          if (controller.addRoutine[roomData['id']]
                              .containsKey(device['id'])) {
                            if (controller.addRoutine[roomData['id']]
                                    [device['id']]['swing'] ==
                                null) {
                              controller.addRoutine[roomData['id']]
                                  [device['id']]['swing'] = swingState;
                              controller.addRoutine[roomData['id']]
                                  [device['id']]['state'] = true;
                            } else {
                              controller.addRoutine[roomData['id']]
                                  [device['id']]['swing'] = null;

                              if (controller
                                  .addRoutine[roomData['id']][device['id']]
                                  .values
                                  .every((element) => element == null)) {
                                controller.addRoutine[roomData['id']]
                                    .remove(device['id']);
                                if (controller
                                    .addRoutine[roomData['id']].isEmpty) {
                                  controller.addRoutine.remove(roomData['id']);
                                }
                              }
                            }
                          } else {
                            controller.addRoutine[roomData['id']]
                                [device['id']] = {
                              'state': true,
                              'degree': null,
                              'type': null,
                              'speed': null,
                              'swing': swingState
                            };
                          }
                        }
                      } else {
                        controller.addRoutine[roomData['id']] = {};
                        controller.addRoutine[roomData['id']][device['id']] = {
                          'state': true,
                          'degree': null,
                          'type': null,
                          'speed': null,
                          'swing': swingState
                        };
                      }
                      setState1(
                        () {
                          controller.addRoutine;
                        },
                      );
                      print(controller.addRoutine);
                    },
                    iconSize: controller.sized * 0.03,
                    icon: iconStyle(
                      icon: controller.addRoutine.containsKey(roomData['id']) &&
                              controller.addRoutine[roomData['id']] != true &&
                              controller.addRoutine[roomData['id']] != false &&
                              controller.addRoutine[roomData['id']]
                                  .containsKey(device['id']) &&
                              controller.addRoutine[roomData['id']]
                                      [device['id']]['swing'] !=
                                  null
                          ? Icons.check_circle_rounded
                          : Icons.add_circle_outline_rounded,
                      color: controller.addRoutine
                                  .containsKey(roomData['id']) &&
                              controller.addRoutine[roomData['id']] != true &&
                              controller.addRoutine[roomData['id']] != false &&
                              controller.addRoutine[roomData['id']]
                                  .containsKey(device['id']) &&
                              controller.addRoutine[roomData['id']]
                                      [device['id']]['swing'] !=
                                  null
                          ? AppColors.primaryColor
                          : AppColors.warningColor,
                    ))
              ],
            ),
          ),
          SizedBox(
            height: sizedHeight * 0.045,
          )
        ],
      ),
    );
  }));
}
