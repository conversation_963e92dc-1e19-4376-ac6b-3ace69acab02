---
version: 1.5.0
slug: dhcp_server
name: "DHCP server [deprecated]"
description: A simple DHCP server
url: https://home-assistant.io/addons/dhcp_server/
codenotary: <EMAIL>
advanced: true
arch:
  - armhf
  - armv7
  - aarch64
  - amd64
  - i386
host_network: true
image: homeassistant/{arch}-addon-dhcp_server
options:
  default_lease: 86400
  dns:
    - *******
    - *******
  ntp: []
  domain: null
  hosts: []
  max_lease: 172800
  networks:
    - broadcast: *************
      gateway: ***********
      interface: eth0
      netmask: *************
      range_end: *************
      range_start: ***********00
      subnet: ***********
schema:
  default_lease: int
  dns:
    - str
  ntp:
    - str
  domain: str
  hosts:
    - ip: str
      mac: str
      name: str
  max_lease: int
  networks:
    - broadcast: str
      gateway: str
      interface: str
      netmask: str
      range_end: str
      range_start: str
      subnet: str
startup: system
init: false
stage: deprecated
