import 'dart:convert';
import 'package:get/get.dart';
import 'package:mysql1/mysql1.dart';
import 'package:zaen/shared/components/config.dart';

/// نموذج بيانات الشمس
class SunData {
  final String state; // above_horizon, below_horizon
  final double? elevation;
  final double? azimuth;
  final bool? rising;
  final DateTime? nextDawn;
  final DateTime? nextDusk;
  final DateTime? nextMidnight;
  final DateTime? nextNoon;
  final DateTime? nextRising;
  final DateTime? nextSetting;
  final DateTime lastUpdated;

  SunData({
    required this.state,
    this.elevation,
    this.azimuth,
    this.rising,
    this.nextDawn,
    this.nextDusk,
    this.nextMidnight,
    this.nextNoon,
    this.nextRising,
    this.nextSetting,
    required this.lastUpdated,
  });

  factory SunData.fromJson(Map<String, dynamic> json) {
    return SunData(
      state: json['state'] ?? 'unknown',
      elevation: json['elevation']?.toDouble(),
      azimuth: json['azimuth']?.toDouble(),
      rising: json['rising'],
      nextDawn: json['next_dawn'] != null
          ? DateTime.tryParse(json['next_dawn'])
          : null,
      nextDusk: json['next_dusk'] != null
          ? DateTime.tryParse(json['next_dusk'])
          : null,
      nextMidnight: json['next_midnight'] != null
          ? DateTime.tryParse(json['next_midnight'])
          : null,
      nextNoon: json['next_noon'] != null
          ? DateTime.tryParse(json['next_noon'])
          : null,
      nextRising: json['next_rising'] != null
          ? DateTime.tryParse(json['next_rising'])
          : null,
      nextSetting: json['next_setting'] != null
          ? DateTime.tryParse(json['next_setting'])
          : null,
      lastUpdated: json['last_updated'] != null
          ? DateTime.tryParse(json['last_updated']) ?? DateTime.now()
          : DateTime.now(),
    );
  }
}

/// خدمة إدارة بيانات الشمس
class SunService extends GetxController {
  static SunService get instance => Get.find();

  // البيانات التفاعلية
  final Rx<SunData?> currentSunData = Rx<SunData?>(null);
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadSunData();
  }

  /// تحميل بيانات الشمس من قاعدة البيانات
  Future<void> loadSunData() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final conn = await MySqlConnection.connect(ConnectionSettings(
          host: controller.hostZain.value,
          // port: 80,
          user: 'root',
          db: 'zain',
          password: 'zain',
          characterSet: CharacterSet.UTF8));

      // جلب كيان الشمس الرئيسي
      var results = await conn.query(
          'SELECT entity_id, state, friendly_name, attributes_json FROM observer_entities WHERE entity_id = ?',
          ['sun.sun']);

      if (results.isNotEmpty) {
        final row = results.first;
        final attributesJson = row['attributes_json'] as String?;

        if (attributesJson != null) {
          final attributes = json.decode(attributesJson);

          // جلب مستشعرات الشمس الإضافية
          var sensorResults = await conn.query('''
            SELECT entity_id, state FROM observer_entities 
            WHERE entity_id IN (?, ?, ?, ?, ?, ?)
          ''', [
            'sensor.sun_next_dawn',
            'sensor.sun_next_dusk',
            'sensor.sun_next_midnight',
            'sensor.sun_next_noon',
            'sensor.sun_next_rising',
            'sensor.sun_next_setting'
          ]);

          // إضافة بيانات المستشعرات
          for (var sensorRow in sensorResults) {
            final entityId = sensorRow['entity_id'] as String;
            final state = sensorRow['state'] as String?;

            if (state != null) {
              final sensorType = entityId.replaceAll('sensor.sun_next_', '');
              attributes['next_$sensorType'] = state;
            }
          }

          // إنشاء بيانات الشمس
          final sunData = SunData.fromJson({
            'state': row['state'],
            'elevation': attributes['elevation'],
            'azimuth': attributes['azimuth'],
            'rising': attributes['rising'],
            'next_dawn': attributes['next_dawn'],
            'next_dusk': attributes['next_dusk'],
            'next_midnight': attributes['next_midnight'],
            'next_noon': attributes['next_noon'],
            'next_rising': attributes['next_rising'],
            'next_setting': attributes['next_setting'],
            'last_updated': DateTime.now().toIso8601String(),
          });

          currentSunData.value = sunData;
        }
      }

      await conn.close();
    } catch (e) {
      errorMessage.value = 'خطأ في تحميل بيانات الشمس: $e';
      print('❌ خطأ في تحميل بيانات الشمس: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تنسيق الوقت للعرض
  String formatTime(DateTime? dateTime) {
    if (dateTime == null) return 'غير متاح';

    // تحويل إلى التوقيت المحلي إذا كان UTC
    DateTime localTime = dateTime;
    if (dateTime.isUtc) {
      localTime = dateTime.add(const Duration(hours: 3)); // UTC+3 للرياض
    }

    return '${localTime.hour.toString().padLeft(2, '0')}:${localTime.minute.toString().padLeft(2, '0')}';
  }

  /// الحصول على حالة الشمس بالعربية
  String getSunStateArabic(String state) {
    switch (state) {
      case 'above_horizon':
        return 'فوق الأفق';
      case 'below_horizon':
        return 'تحت الأفق';
      default:
        return state;
    }
  }

  /// الحصول على أيقونة الشمس حسب الحالة
  String getSunIcon(String state) {
    switch (state) {
      case 'above_horizon':
        return '☀️';
      case 'below_horizon':
        return '🌙';
      default:
        return '🌅';
    }
  }
}
