---
version: 3.13.0
hassio_api: true
slug: nginx_proxy
name: NGINX Home Assistant SSL proxy
description: An SSL/TLS proxy
url: https://github.com/home-assistant/addons/tree/master/nginx_proxy
arch:
  - armhf
  - armv7
  - aarch64
  - amd64
  - i386
image: homeassistant/{arch}-addon-nginx_proxy
init: false
map:
  - ssl
  - share
options:
  domain: null
  hsts: max-age=31536000; includeSubDomains
  certfile: fullchain.pem
  keyfile: privkey.pem
  cloudflare: false
  customize:
    active: false
    default: nginx_proxy_default*.conf
    servers: nginx_proxy/*.conf
  real_ip_from: []
ports:
  443/tcp: 443
  80/tcp: null
schema:
  domain: str
  hsts: str
  certfile: str
  keyfile: str
  cloudflare: bool
  customize:
    active: bool
    default: str
    servers: str
  real_ip_from:
    - str
