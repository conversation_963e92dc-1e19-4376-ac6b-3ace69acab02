[mysqld]
port=3306
log_error=mariadb.err

# Persistent storage location
datadir=/data/databases

# Use a proper collation set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Do not resolve DNS names
skip-name-resolve

# Tune for low-end devices (Like a Raspberry Pi)
key_buffer_size = 16M
max_connections = 64
myisam_recover_options = FORCE
myisam_sort_buffer_size = 8M
net_buffer_length = 16K
read_buffer_size = 256K
read_rnd_buffer_size = 512K
sort_buffer_size = 512K
join_buffer_size = 128K
table_open_cache = 64
thread_cache_size = 8
thread_stack = 192K
tmp_table_size = 16M

# Disable query cache
query_cache_limit = 1M
query_cache_size = 0M
query_cache_type = 0

# InnoDB Tweaks
innodb_buffer_pool_size = 128M
innodb_log_buffer_size = 8M
innodb_log_file_size = 48M
max_binlog_size = 96M
