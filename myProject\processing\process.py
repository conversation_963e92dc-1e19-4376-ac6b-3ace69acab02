import mysql.connector
import datetime
from datetime import timedelta, date
import time
import threading
import netifaces as ni
import sys
import re
import json
import paho.mqtt.publish as publish
sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/nlt')
sys.path.append('/home/<USER>/myProject/modules')
import static as st
import weather as weatherr
import prayer as prayer
import editClock as editClock
import editNumber as editNumber
import tts as tts
import nlt as nlt
import handle_alarm as handle_alarm
import handle_device as handle_device
class CommandProcessor():
    def __init__(self,system_self):
        self.system_self = system_self
        self.words = {}
        self.devices_names = []
        self.devices_cache = {}
        self.myroom = ''
        self.rooms = {}
        self.text_devices = {}
        self.text_rooms = []
        self.text_names = []
        self.devices_err = []
        self._load_rooms()
        self._load_words()
        
    def _load_words(self):
        routineWords = []
        try:
            # فتح الملف وقراءة البيانات
            with open(st.pathFiles + 'words.txt', 'r', encoding='utf-8') as words_file:
                routineWords = json.load(words_file)
                
            # التحقق من أن البيانات قائمة
            if not isinstance(routineWords, list):
                print("تنبيه: البيانات المقروءة ليست قائمة")
        except FileNotFoundError:
            print("الملف غير موجود")
        except json.JSONDecodeError:
            print("خطأ في تنسيق ملف JSON")
        except Exception as e:
            print(f"خطأ غير متوقع: {e}")
        for word in routineWords:
            name = nlt.edit_char(str(word.get('word')))
            name = nlt.edit_word(name)
            self.words[name] = word.get('route')
            
            
    def _load_devices(self):
        """تحميل بيانات الأجهزة من قاعدة البيانات"""
        self.system_self.cursor.execute("SELECT id,Type,rooms FROM Devices WHERE Type NOT LIKE 'ZAIN%'")
        devices = self.system_self.cursor.fetchall()
        for device in devices:
            self.devices_cache[device['id']] = device
            listNames = []
            edited_names = []
            if device['Type'] == 'AC' or device['Type'] == 'TV':
                self.devices_cache[device['id']]['names'] = []
                self.system_self.cursor.execute("SELECT %s FROM NDevice"%(device['id']))
                names = self.system_self.cursor.fetchall()
                if names:
                    for name in names:
                        if name[device['id']] != None and name[device['id']] != 'None':
                            listNames.append(name[device['id']])
                self.devices_cache[device['id']]['names']=listNames
                
                if listNames:
                    for name in listNames:
                        ename = nlt.edit_char(name)
                        ename = nlt.edit_word(ename)
                        edited_names.append(ename)
                        self.devices_names.append({device['Type']:{'edit':ename, 'name':name}})
                        
                self.devices_cache[device['id']]['edited_names']=edited_names
            elif 'SWITCH' in device['Type']:
                self.system_self.cursor.execute("SELECT * FROM %s_SWITCH"%(device['id']))
                names = self.system_self.cursor.fetchall()
                switches = {}
                if names:
                    for v in names[0]:
                        
                        if v != 'id':
                            switches[v]={}
                            switches[v]['Type'] = names[0][v]
                            switches[v]['names'] = []
                            switches[v]['edited_names'] = []
                    for r in names[1:]:
                        for v in r:
                            if v != 'id':
                                if r[v]!=None and r[v]!='None':
                                    switches[v]['names'].append(r[v])
                if switches:
                    for v in switches:
                        if switches[v]['names'] != []:
                            for name in switches[v]['names']:
                                ename = nlt.edit_char(name)
                                ename = nlt.edit_word(ename)
                                switches[v]['edited_names'].append(ename)
                                self.devices_names.append({switches[v]['Type']:{'edit':ename, 'name':name}})
                                
                self.devices_cache[device['id']]['switches'] = switches
            if device['Type'] == 'TV':
                self.system_self.cursor.execute("SELECT * FROM %s_TV"%(device['id']))
                names = self.system_self.cursor.fetchall()
                ch = {}
                edited_ch ={}
                if names:
                    for r in names:
                        ch[r['number']] = r['chaneel']
                        chaneel = nlt.edit_char(r['chaneel'])
                        chaneel = nlt.edit_word(chaneel)
                        edited_ch[r['number']] = chaneel
                self.devices_cache[device['id']]['chaneels'] = ch
                self.devices_cache[device['id']]['edited_chaneels'] = edited_ch
    
    def _load_rooms(self):
        """تحميل بيانات الغرف من قاعدة البيانات"""
        try:
            self.system_self.cursor.execute("SELECT rooms FROM Devices WHERE id LIKE '%s'"% (ni.ifaddresses('wlan0')[17][0]['addr']))
            reslt = self.system_self.cursor.fetchone()
            if reslt and 'rooms' in reslt:
                self.myroom = reslt['rooms']
            else:
                self.myroom = 'room_01'  # قيمة افتراضية
        except Exception as e:
            print(f"خطأ في تحميل الغرف: {e}")
            self.myroom = 'room_01'  # قيمة افتراضية
        self.system_self.cursor.execute("SELECT * FROM Rooms")
        results = self.system_self.cursor.fetchall()
        for room in results[0]:
            self.rooms[room] = {'names':[], 'edited_names':[]}
        names = []
        edited_names = []
        for roomS in results:
            for room in roomS:
                if roomS[room] != None and roomS[room] != 'None':
                    self.rooms[room]['names'].append(roomS[room])
                    edited = nlt.edit_char(roomS[room])
                    edited = nlt.edit_word(edited)
                    self.rooms[room]['edited_names'].append(edited)    
    
    def process_command(self, text):
        """معالجة الأمر النصي وتنفيذ الإجراء المناسب"""
        text = nlt.edit_char(text)
        nlt_result = nlt.edit_word(text)
        # ايجاد كلمات روتينيه
        for routine in self.words:
            if routine in nlt_result:
                route = self.words[routine]
                route=json.loads(route)
                if 'home' in route:
                    handle_device.commandRooms(self,command=route['home'])
                else:
                    
                    for rrr in route:
                        if route[rrr]==True or route[rrr]==False:
                            handle_device.commandRooms(self,command=route[rrr],rooms=[rrr])
                        else:
                            for d in route[rrr]:
                                if 'degree' in route[rrr][d]:
                                    command='RUN ' if route[rrr][d]['state']==True else 'OFF '
                                    if route[rrr][d]['state']==True:
                                        if route[rrr][d]['degree']!=None:
                                            command+=str(route[rrr][d]['degree'])+' '
                                        else:
                                            command+='0 '
                                        command+='VAN '
                                        if route[rrr][d]['speed']!=None:
                                            command+=str(route[rrr][d]['speed'])+' '
                                        else:
                                            command+='0 '
                                        if route[rrr][d]['type']!=None:
                                            command+='AC' if route[rrr][d]['type']=='تبريد' else 'HEAT' if route[rrr][d]['type']=='تدفئة' else 'VAN'
                                        else:
                                            command+='XX '
                                    command+='XX:XX:XX:XX:XX:XX'
                                    topic='ROOMS/%s/AC%%/%s'%(rrr,d,)
                                    publish.single(topic,command, hostname=st.ip)
                                elif 'ch' in route[rrr][d]:
                                    topic='ROOMS/%s/TV%%/%s'%(rrr,d,)
                                    if route[rrr][d]['state']==False:
                                        command='POWER-OFF XX:XX:XX:XX:XX:XX'
                                        publish.single(topic,command, hostname=st.ip)
                                    else:
                                        if route[rrr][d]['ch']!=None:
                                            if '+' in route[rrr][d]['ch'] or '-' in route[rrr][d]['ch']:
                                                command='CH '+ route[rrr][d]['ch'][0]+ ' '+route[rrr][d]['ch'][1:]+' '
                                            else:
                                                command='CH = '+route[rrr][d]['ch']+' '
                                            command+='XX:XX:XX:XX:XX:XX'
                                            publish.single(topic,command, hostname=st.ip)
                                        if route[rrr][d]['v']!=None:
                                            if route[rrr][d]['v']==True:
                                                command='SIL-ON'
                                                publish.single(topic,command, hostname=st.ip)
                                            elif route[rrr][d]['v']==False:
                                                command='SIL-OFF'
                                                publish.single(topic,command, hostname=st.ip)
                                            if '+' in route[rrr][d]['v'] or '-' in route[rrr][d]['ch']:
                                                command='VOICE '+ route[rrr][d]['v'][0]+ ' '+route[rrr][d]['ch'][1:]+' '
                                            else:
                                                command='VOICE = '+route[rrr][d]['v']+' '
                                            command+='XX:XX:XX:XX:XX:XX'
                                            publish.single(topic,command, hostname=st.ip)
                                        if route[rrr][d]['ch']==None and route[rrr][d]['v']!=None:
                                            command='POWER-ON XX:XX:XX:XX:XX:XX'
                                            publish.single(topic,command, hostname=st.ip)
                                else :
                                    for v in route[rrr][d]:
                                        if route[rrr][d][v]!=None:
                                            command+=v+'_RUN ' if route[rrr][d][v]==True else '_OFF '
                                    command+='XX:XX:XX:XX:XX:XX'
                                    publish.single(topic,command, hostname=st.ip)
                return
        # استخراج اسماء الغرف
        if 'HOME' in nlt_result or 'ALL ROOM' in nlt_result:
            self.text_rooms = '*'
        else:
            for room in self.rooms:
                for name in self.rooms[room]['edited_names']:
                    if name in nlt_result:
                        texted_name = self.rooms[room]['names'][self.rooms[room]['edited_names'].index(name)]
                        texted_name = texted_name.replace(' ','_')
                        nlt_result = nlt_result.replace(name,f' {room}({texted_name}) ')
                        self.text_rooms.append(room)
        # ايجاد ان كان هناك اسماء اجهزه
        if 'AC%' in nlt_result or 'TV%' in nlt_result or 'LIGHT' in nlt_result or 'SWITCH%' in nlt_result or 'VAN' in nlt_result:
            self._load_devices()
            for name in self.devices_names:
                if 'AC' in name and 'AC' in nlt_result:
                    if 'AC% '+name['AC']['edit'] in nlt_result:
                        self.text_names.append(name)
                if 'TV' in name and 'TV' in nlt_result:
                    if 'TV% '+name['TV']['edit'] in nlt_result:
                        self.text_names.append(name)
                if 'SWITCH' in name and 'SWITCH' in nlt_result:
                    if 'SWITCH% '+name['SWITCH']['edit'] in nlt_result:
                        self.text_names.append(name)
                if 'LIGHT' in name and 'LIGHT' in nlt_result:
                    if 'LIGHT '+name['LIGHT']['edit'] in nlt_result:
                        self.text_names.append(name)
                if 'VAN' in name and 'VAN' in nlt_result:
                    if 'VAN '+name['VAN']['edit'] in nlt_result:
                        self.text_names.append(name)
            if self.text_names != []:
                for i in self.devices_cache:
                    if self.devices_cache[i]['Type'] == 'TV' and 'TV%' in nlt_result and (self.text_rooms == '*' or self.devices_cache[i]['rooms'] in self.text_rooms or (self.text_rooms == [] and self.myroom == self.devices_cache[i]['rooms'])):
                        if self.devices_cache[i]['edited_names'] != []:
                            for name in self.devices_cache[i]['edited_names']:
                                if self.devices_cache[i]['Type']+'% '+name in nlt_result:
                                    texted_name = self.devices_cache[i]['names'][self.devices_cache[i]['edited_names'].index(name)]
                                    texted_name = texted_name.replace(' ','_')
                                    nlt_result = nlt_result.replace(name,f'({texted_name})')
                                    self.text_devices[i]={'name' : texted_name,'room': self.devices_cache[i]['rooms'],'type':self.devices_cache[i]['Type']}
                        if self.devices_cache[i]['edited_chaneels'] == {}:
                            for number in self.devices_cache[i]['edited_chaneels']:
                                if self.devices_cache[i]['edited_chaneels'][number] in nlt_result:
                                    nlt_result = nlt_result.replace(self.devices_cache[i]['edited_chaneels'][number],f' {number}(chaneel) ')
                    elif self.devices_cache[i]['Type'] == 'AC' and 'AC%' in nlt_result and (self.text_rooms == '*' or self.devices_cache[i]['rooms'] in self.text_rooms or (self.text_rooms == [] and self.myroom == self.devices_cache[i]['rooms'])):
                        if self.devices_cache[i]['edited_names'] != []:
                            for name in self.devices_cache[i]['edited_names']:
                                if self.devices_cache[i]['Type']+'% '+name in nlt_result:
                                    texted_name = self.devices_cache[i]['names'][self.devices_cache[i]['edited_names'].index(name)]
                                    texted_name = texted_name.replace(' ','_')
                                    nlt_result = nlt_result.replace(name,f'({texted_name})')
                                    self.text_devices[i] = {'name' : texted_name,'room': self.devices_cache[i]['rooms'],'type':self.devices_cache[i]['Type']}
                    elif 'SWITCH' in self.devices_cache[i]['Type'] and ('LIGHT' in nlt_result or 'SWITCH%' in nlt_result or ('VAN' in nlt_result and 'AC%' not in nlt_result)) and (self.text_rooms == '*' or self.devices_cache[i]['rooms'] in self.text_rooms or (self.text_rooms == [] and self.myroom == self.devices_cache[i]['rooms'])):
                        if self.devices_cache[i]['switches'] != {}:
                            sw = {}
                            for switch in self.devices_cache[i]['switches']:
                                
                                if self.devices_cache[i]['switches'][switch]['Type'] in nlt_result and self.devices_cache[i]['switches'][switch]['edited_names'] != []:
                                    for name in self.devices_cache[i]['swetches'][switch]['edited_names']:
                                        if self.devices_cache[i]['switches'][switch]['Type']+'% '+name in nlt_result:
                                            texted_name = self.devices_cache[i]['switches'][switch]['names'][self.devices_cache[i]['switches'][switch]['edited_names'].index(name)]
                                            texted_name = texted_name.replace(' ','_')
                                            nlt_result = nlt_result.replace(name,f'({texted_name})')
                                            sw[switch]=texted_name
                                
                            if sw != {}:
                                self.text_devices[i] = {'name' : sw,'room': self.devices_cache[i]['rooms'],'type':self.devices_cache[i]['Type']}
                    nlt_result = nlt_result.strip()   
                for name in self.text_names:
                    
                    if 'AC' in name and f"AC% ({name['AC']['edit']})" not in nlt_result:
                        self.devices_err.append(name)  
        
        # فلتره النص و حذف الكلمات الغير لازمه
        ph = nlt_result.split()
        listPh = []
        for i in ph:
            if i.isnumeric() or i in st.filterWords or '_' in i or ':' in i or '(' in i or ')' in i:
                listPh.append(i)
        nlt_result = ' '.join(listPh)
        # الفرق بين اثنين و احد كرقم و يوم
        k=0
        ph = nlt_result.split()
        for i in ph:
            if 'Mon' in i or 'Sun' in i:
                if k !=0 and (ph[k-1].isnumeric() or ph[k-1] in ['ساع','دقيق','دقايق','دقاءق','علي','MONTH','YEAR','+','-','*','/','الي']):
                    nlt_result=nlt_result.replace(ph[k-1]+' Mon',ph[k-1]+' 2')
                elif k+1 < len(ph) and ph[k+1] in ['+','-','*','/','10','20','30','40','50','60','70','80','90']:  
                    nlt_result=nlt_result.replace('Mon '+ph[k+1],'2 '+ph[k+1])
                elif k+2 < len(ph) and ph[k+1] in ['و','من','على',] and (ph[k+2].isnumeric() or ph[k+2] in ['+','-','*','/','10','20','30','40','50','60','70','80','90']):  
                    nlt_result=nlt_result.replace(' Mon '+ph[k+1]+' '+ph[k+2],' 2 '+ph[k+1]+' '+ph[k+2])
            k+=1
            
        nlt_result = ' '+nlt_result+' '
        # استخراج صيغه الساعه
        nlt_result=editClock.editClock(nlt_result)
        nlt_result= nlt_result.strip()
        ph = nlt_result.split()
        # الفرق بين احد و اثنين كرقم و يوم بعد استخراج صيغه الساعه
        k=0
        for i in ph:
            if 'Mon' in i or 'Sun' in i:
                if k+1 < len(ph) and ph[k+1] in ['ساع','دقيق','دقايق','دقاءق','ربع','نص','نصف','MONTH','YEAR','+','-','*','/','10','20','30','40','50','60','70','80','90']:  
                    nlt_result=nlt_result.replace('Mon '+ph[k+1],'2 '+ph[k+1])
                elif k+2 < len(ph) and ph[k+1] in ['و','من','على',] and (ph[k+2].isnumeric() or ph[k+2] in ['ساع','دقيق','دقايق','دقاءق','ربع','نص','نصف','MONTH','YEAR','+','-','*','/','10','20','30','40','50','60','70','80','90']):  
                    nlt_result=nlt_result.replace(' Mon '+ph[k+1]+' '+ph[k+2],' 2 '+ph[k+1]+' '+ph[k+2])
            k+=1
        
        nlt_result = ' '+nlt_result+' '
        # تعديل الارقام
        nlt_result=editNumber.editNumber(nlt_result)
        
        nlt_result = nlt_result.strip()
        # عمل التوكينز
        nlp = [nlt_result,text]
        for i in nlt_result.split():
            if i.isnumeric() and 'NUMBER' not in nlp:
                nlp.append('NUMBER')
            elif ':' in i  and 'TIME' not in nlp:
                nlp.append('TIME')
            elif '_' in i or '(' in i:
                if i.split('(')[0] in self.rooms and 'ROOMS' not in nlp:
                    nlp.append('ROOMS')
                elif i.split('(')[0] in self.devices_cache and 'DEVICE' not in nlp:
                    nlp.append('DEVICE')
            else:
                for s in st.shortcuts:
                    if i in st.shortcuts[s] and s not in nlp:
                        nlp.append(s)
        
        if len(nlp) < 3:
            return "لم أفهم الأمر، يرجى إعادة صياغته"
        
        edited_text = nlp[0]  # النص بعد المعالجة
        original_text = nlp[1]  # النص الأصلي
        tokens = nlp[2:]  # الرموز المستخرجة
        
        print(f"Processed NLT: {nlp}")
        print(f"Tokens: {tokens}")
        
        # تحديد نوع الأمر
        if self._has_any_token(tokens, ['DEVICE']):
            return handle_device._handle_device_control(self,tokens, edited_text)
        elif self._has_any_token(tokens, ['WEATHER']):
            return self._handle_weather(tokens, edited_text)
        elif self._has_any_token(tokens, ['ALARM']):
            return handle_alarm._handle_alarm(self,tokens, edited_text)
        elif self._has_any_token(tokens, ['PRAY']):
            return self._handle_prayer_times(tokens, edited_text)
        elif ('مءقت' in edited_text or 'عداد' in edited_text):
            return self._handle_timer(edited_text)
        elif self._has_any_token(tokens, ['TIME']):
            return self._handle_time()
        elif self._has_any_token(tokens, ['DATE','DAY']):
            return self._handle_date(tokens,edited_text)
        elif self._has_any_token(tokens, ['NUMBER']) and ('+' in edited_text or '-' in edited_text or '*' in edited_text or '/' in edited_text):
            return self._handle_math(edited_text)
        elif self._has_any_token(tokens, ['PLAY']) and self.text_rooms == '*':
            handle_device.commandRooms(command=True if 'RUN' in nlt_result else False,serial=ni.ifaddresses('wlan0')[17][0]['addr'])
        elif self._has_any_token(tokens, ['PLAY']) and self.text_rooms != []:
            handle_device.commandRooms(command=True if 'RUN' in nlt_result else False,rooms=self.text_rooms,serial=ni.ifaddresses('wlan0')[17][0]['addr'])
        
        else:
            return "لم أتمكن من تحديد نوع الأمر"
    
    def _has_any_token(self, tokens, token_list):
        """التحقق مما إذا كانت الرموز تحتوي على أي من الرموز المحددة"""
        return any(token in tokens for token in token_list)
    
    def _handle_weather(self,tokens,text):
        """معالجة استعلامات الطقس"""
        # استخدام DHT11 للحصول على درجة الحرارة والرطوبة
        try :
            for i in text:
                if i in st.shortcuts['WEATHER']:
                    weather=i

            if 'DAY' not in tokens:
                if 'TOMORROW' in tokens:
                    day = 1
                else:
                    day = 'current'
            else:
                for i in text.split():
                    if i in st.shortcuts['DAY']:
                        day = i
            
            if 'CITY' not in tokens:
                city='here'
            else:
                cities = []
                self.system_self.cursor.execute("SELECT city FROM City")
                results = self.system_self.cursor.fetchall()
                for r in results:
                    cities.append(r[0])
                for i in text.split():
                    if i in cities:
                        city = i
            
            if 'نصف DAY' in text or 'نص DAY' in text or 'منتصف DAY' in text or 'zuhr' in text :
                    timeE = 'eve'
                
            elif 'AM' in text:
                timeE='AM'
            elif 'PM' in text:
                timeE = 'PM'
            else:
                timeE = 'now'
            weatherr.weather(city,day,timeE,weather)
        except :
            
            self.system_self.tts.say('للأسَف, لا يَتَوَفَر لَدَي إنتَرنِت , لَقَد تَعَذَر العُثور على معلومات')
            pass
    
    def _handle_time(self):
        """معالجة استعلامات الوقت"""
        now = datetime.datetime.now()
        time_str = now.strftime("%I:%M")
        time_str = time_str+' مَسَائَاً' if now.strftime("%p")=='PM' else time_str+' صَباحَاً'
        self.system_self.tts.say(f"الوقت الآن هو {time_str}")
    
    def _handle_date(self,tokens,text):
        """معالجة استعلامات التاريخ"""
        
        if 'DAY' in tokens:
            for i in text.split():
                if i in st.shortcuts['DAY']:
                    nday = i
            
            nday= st.nweeks[nday]
            today= datetime.today()
            day=nday-today.weekday()
            if day <= 0:
                day+=7
            result = today+timedelta(days=day)
            for i in st.wdays:
                if st.wdays[i]==result.strftime('%a'):
                    day=i
            self.system_self.tts.say('يوم ال%s هو %s من شَهرِ %s سَنَةْ %s'%(day,str(result.day),str(result.month),str(result.year)))
        elif 'TOMORROW' in text:
            result=datetime.datetime.today()+timedelta(days=1)
            for i in st.wdays:
                if st.wdays[i]==result.strftime('%a'):
                    day=i
            self.system_self.tts.say('يَومُ الغَد هوَ ال%s و يَكون التاريخ %s من شَهرِ %s سَنَةْ %s'%(day,str(result.day),str(result.month),str(result.year)))
        elif 'MONTH' in tokens or 'MONTH' in text:
            today= datetime.datetime.today()
            mday = None
            month = None
            year = None
            for i in text.split():
                if i in st.shortcuts['MONTH'] and month == None:
                    month = i
                elif 'MONTH' in text and month == None:
                    month = today.month
                if i.isnumeric():
                    if i > '32' and mday == None:
                        mday = int(i)    
                    elif i != mday and year == None:
                        year = int(i)

            if year == None:
                if st.nmonths[month]<today.month:
                    year=today.year+1
                else:
                    year=today.year
            if mday == None:
                mday = today.day
            if month == None:
                month = today.month
            Date=datetime.datetime(year,st.nmonths[month],mday)
            result= Date.strftime('%a')
            for i in st.wdays:
                if st.wdays[i]==result:
                    result=i
            self.system_self.tts.say('يكون التاريخ %s من شَهرِ %s سَنَةْ %s هو يوم ال%s'%(str(mday),str(st.nmonths[month]),str(year),result))
            return
        else:
            today= datetime.datetime.today()
            for i in st.wdays:
                if st.wdays[i]==today.strftime('%a'):
                    day=i
            self.system_self.tts.say('اليوم هو ال%s تاريخ %s من شهر %s %s'%(str(day),str(today.day),str(today.month),str(today.year)))

    def _handle_math(self,text):
        math = ' '
        for i in text.split():
            if i.isnumeric() or i in ['+','-','*','/',] or ('-' in i and i.replace('-','').isnumeric()):
                math+=i+' '
        math = math.strip()
        if math[-1] in ['+','-','*','/',]:
            math = math[:-1]
            
        try:
            mathS = math.replace(' - ',' ناقِصْ ')
            mathS = mathS.replace(' * ',' ضَرّبْ ')
            mathS = mathS.replace(' / ',' تَقسيّمْ ')
            self.system_self.tts.say(mathS+' '+'= %s'%(str(eval(math))))
        except:
            self.system_self.tts.say('لَم افهمِ المقصود') 
    
    def _handle_timer(self,text):
        
        if self.system_self.timerState == True:
            if 'STOP' in text.split() or 'DELETE' in text.split() or 'OFF' in text.split():
                self.system_self.timer.cancel()
                self.system_self.timerState=False
                self.system_self.tts.say('تم إيقاف المؤقِت')
            else:
                self.system_self.tts.say('يوجد مؤقِت يَعمَل الان')
        else:
            hour=0
            minute=0
            second=0
            text = text.replace('ربع','15').replace('ثلث','20').replace('نص','30').replace('نصف','30').strip()

            # استخراج عدد الساعات (يدعم المفرد بدون رقم)
            h = re.search(r'(?:\b(\d+)\s*)?(?:ساع)', text)
            if h:
                hour = int(h.group(1))*60*60 if h.group(1) else 1*60*60

            # استخراج عدد الدقائق (يدعم المفرد بدون رقم)
            m = re.search(r'(?:\b(\d+)\s*)?(?:دقيق|دقاءق|دقايق)', text)
            if m:
                minute = int(m.group(1))*60 if m.group(1) else 1*60

            # استخراج عدد الثواني (يدعم المفرد بدون رقم)
            s = re.search(r'(?:\b(\d+)\s*)?(?:ثاني|ثواني)', text)
            if s:
                second = int(s.group(1)) if s.group(1) else 1

#             q={'ربع':'15','ثلث':'20','نص':'30','نصف':'30'}
#                 
#             if 'ساع' in text.split() and text.split()[text.split().index('ساع')-1].isnumeric():
#                 hour = int(text.split()[text.split().index('ساع')-1])*60*60
#                 if text.split()[text.split().index('ساع')+1] in q:
#                     minute=int(q[text.split()[text.split().index('ساع')+1]]*60)
#             elif 'ساع' in text.split() and text.split()[text.split().index('ساع')-1] in q:
#                 minute = int(q[text.split()[text.split().index('ساع')-1]])*60
#             elif 'ساع' in text.split():
#                 hour = 60*60
#                 if text.split()[text.split().index('ساع')+1] in q:
#                     minute=int(q[text.split()[text.split().index('ساع')+1]]*60)
#             elif ':' in text.split():
#                 for i in text.split():
#                     if ':' in i:
#                         i = i.split(':')
#                         minute = int(i[0]+i[1])
#                         minute = minute*60
#                         return
#             
#             if 'دقاءق' in text.split() and text.split()[text.split().index('دقاءق')-1].isnumeric():
#                 minute = int(text.split()[text.split().index('دقاءق')-1])*60
#                 if text.split()[text.split().index('دقاءق')+1] in q:
#                     second=int(q[text.split()[text.split().index('دقاءق')+1]])
#             elif 'دقيق' in text.split() and text.split()[text.split().index('دقيق')-1].isnumeric():
#                 minute = int(text.split()[text.split().index('دقيق')-1])*60
#                 if text.split()[text.split().index('دقيق')+1] in q:
#                     second=int(q[text.split()[text.split().index('دقيق')+1]])
#             elif 'دقاءق' in text.split() and text.split()[text.split().index('دقاءق')-1] in q:
#                 second = int(q[text.split()[text.split().index('دقاءق')-1]])
#             elif 'دقيق' in text.split() and text.split()[text.split().index('دقيق')-1] in q:
#                 second = int(q[text.split()[text.split().index('دقيق')-1]])
#             elif 'دقاءق' in text.split() or 'دقيق' in text.split():
#                 minute = 60
#                 if 'دقاءق' in text.split() and text.split()[text.split().index('دقاءق')+1] in q:
#                     second=int(q[text.split()[text.split().index('دقاءق')+1]])
#                 elif 'دقيق' in text.split() and text.split()[text.split().index('دقيق')+1] in q:
#                     second=int(q[text.split()[text.split().index('دقيق')+1]])
#             
#             if 'sec' in text.split() and text.split()[text.split().index('sec')-1].isnumeric():
#                 second = int(text.split()[text.split().index('sec')-1])
#             elif 'ثواني' in text.split() and text.split()[text.split().index('ثواني')-1].isnumeric():
#                 second = int(text.split()[text.split().index('ثواني')-1])
#             elif 'sec' in text.split():
#                 second = 1
            iTimer=hour+minute+second
            self.system_self.timerState=True
            def mytimer():
                
                self.system_self.timerState=False
                self.system_self.tts.say('t_2')
                self.system_self.tts.say('لقد إنتهى المؤقت')
            self.system_self.timer = threading.Timer(iTimer,mytimer)
            self.system_self.tts.say('سَيَتِم تَشغيل المؤقِت عَندَ الصافِره مدَةْ %s %s %s'%(str(int(hour/60/60))+' ساعه' if hour != 0 else '',str(int(minute/60))+' دقيقه' if minute != 0 else '',str(second)+' ثانيه' if second != 0 else ''))
            
            time.sleep(7)
            self.system_self.tts.say('t_1')
            self.system_self.timer.start()
            print('لقد بدأ المؤقت')
            
    def _handle_prayer_times(self, tokens, text):
        """معالجة استعلامات أوقات الصلاة"""
        

        if 'CITY' not in tokens:
            self.system_self.cursor.execute("SELECT lat,lon FROM City WHERE mycity='here'")
            coords = self.system_self.cursor.fetchone()
            if coords:
                lat = coords[0]
                lon=coords[1]
            else:
                self.system_self.tts.say('لم يَتِم تَحديد مَدينة بَعد, الرَجاء التَوَجُه الى الاعدادات لإختيار مَدينه')
                return
        else:
            cities = []
            self.system_self.cursor.execute("SELECT city FROM City")
            results = self.system_self.cursor.fetchall()
            for r in results:
                cities.append(r[0])
            for i in text.split():
                if i in cities:
                    city = i
        
            self.system_self.cursor.execute("SELECT lat,lon FROM City WHERE city='%s'"%(city))
            coords = self.system_self.cursor.fetchone()
            if coords:
                lat = coords[0]
                lon=coords[1]
        T=open(st.pathFiles+'timing.txt','r')
        timing=T.readline()
        T.close()
        if 'PRAY' in tokens and ('fajr' in text or 'zuhr' in text or 'asr' in text or 'maghrib' in text or 'isha' in text or 'Fri' in text):
            pt = prayer.Prayertime(float(lon),float(lat), 2, date.today().year, date.today().month, date.today().day, prayer.Calendar.UmmAlQuraUniv, prayer.Mazhab.Default, int(timing))
            pt.calculate()
            if 'fajr' in text:
                mypray=pt.fajr_time()
                praytime = 'فَجّر'
            if 'zuhr' in text or 'Fri' in text:
                mypray=pt.zuhr_time()
                praytime = 'ظُهرْ'
                if 'Fri' in text:
                    praytime = 'الجُمعَه'
            if 'asr' in text:
                mypray=pt.asr_time()
                praytime = 'عَصّرْ'
            if 'maghrib' in text:
                mypray=pt.maghrib_time()
                praytime = 'مَغرِب'
            if 'isha' in text:
                mypray=pt.isha_time()
                praytime = 'عِشاءْ'
            if 'PM' in mypray and int(mypray.split(':')[0]) != 12:
                isPM=int(mypray.split(':')[0])+12
                x=mypray.replace(mypray.split(':')[0]+':',str(isPM)+':')
            else:
                x=mypray
            x =x.replace(' AM','')
            x =x.replace(' PM','')
            if len(x.split(':')[0])==1:
                x='0'+x
            X = x.split(':')
            print(X)
            y= datetime.now()
            if city != '':
                self.system_self.tts.say('يَكون مَوعِد صَلاةْ ال%s في %s حَوالي %s'%(praytime,city.replace(' ','-'),mypray))
            elif timedelta(hours=int(X[0]),minutes=int(X[1])) > timedelta(hours=y.hour,minutes=y.minute):
                z= datetime(y.year,y.month,y.day,int(x[0:2]),int(x[3:5]))
                v = z-y
                timeE=str(v).split(':')
                if int(timeE[0]) == 2:
                    Ttime='ساعتان'
                elif int(timeE[0]) == 1 or int(timeE[0]) > 10:
                    Ttime=timeE[0]+' ساعه'
                elif int(timeE[0]) == 0:
                    Ttime=''
                elif int(timeE[0]) < 11:
                    Ttime=timeE[0]+' ساعات'
                
                if Ttime != '':
                    Ttime+=' و '
                if timeE[1] == 2:
                    Ttime+='دقيقتان'
                elif timeE[1] == 1 or int(timeE[1]) > 10:
                    Ttime+=timeE[1]+' دقيقه'
                elif int(timeE[1]) < 11 and int(timeE[1]) != 0:
                    Ttime+=timeE[1]+' دقائق'
                
                self.system_self.tts.say('يَكون مَوعِد صَلاةْ ال%s في حَوالي %s , حَيثُ باقي مِنَ الوقت %s'%(praytime,mypray,Ttime))
            elif timedelta(hours=int(X[0]),minutes=int(X[1])) < timedelta(hours=y.hour,minutes=y.minute):
                self.system_self.tts.say('مَوعِد صَلاةْ ال%s غَداً في حَوالي %s'%(praytime,mypray))
            else:
                self.system_self.tts.say('مِنَ المُفتَرَض أن يَكونَ مَوعِد الصلاهْ الان او في الدقائِق القادِمَهْ')

        else:
            pt = prayer.Prayertime(float(lon),float(lat), 2, date.today().year, date.today().month, date.today().day, prayer.Calendar.UmmAlQuraUniv, prayer.Mazhab.Default, int(timing))
            pt.calculate()
            prays =[pt.fajr_time(),pt.zuhr_time(),pt.asr_time(),pt.maghrib_time(),pt.isha_time()]
            t=['فَجّر','ظُهرْ','عَصّرْ','مَغرِب','عِشاءْ']
            saY='اوقات الصَلَواتِ الخمس %s تَاتِي في المَواعيْد التاليَهْ \n'%('في '+city.replace(' ','-') if city != '' else '')
            for i in range(5):
                saY+='ال'+t[i]+' في '+prays[i]+' \n'
                
            self.system_self.tts.say(saY)
        
        prayer_type = None
        
        # تحديد نوع الصلاة من الرموز
        for token in tokens:
            if token in ['fajr', 'asr', 'maghrib', 'isha', 'zuhr']:
                prayer_type = token
                break
        
        # استخدام API وهمي لأوقات الصلاة (يجب استبداله بمكتبة حقيقية أو API)
        prayer_times = {
            'fajr': '04:30',
            'zuhr': '12:15',
            'asr': '15:45',
            'maghrib': '18:30',
            'isha': '20:00'
        }
        
        prayer_names_ar = {
            'fajr': 'الفجر',
            'zuhr': 'الظهر',
            'asr': 'العصر',
            'maghrib': 'المغرب',
            'isha': 'العشاء'
        }
        
        if prayer_type:
            time_str = prayer_times.get(prayer_type)
            prayer_name = prayer_names_ar.get(prayer_type)
            return f"وقت صلاة {prayer_name} هو {time_str}"
        else:
            # إرجاع جميع أوقات الصلاة
            result = "أوقات الصلاة اليوم:\n"
            for prayer, time_str in prayer_times.items():
                result += f"{prayer_names_ar[prayer]}: {time_str}\n"
            return result


# مثال للاستخدام
def process_voice_command(text,system_self):
    processor = CommandProcessor(system_self)
    processor.process_command(text)


# ملف تنفيذي إذا تم تشغيل البرنامج مباشرة
# if __name__ == "__main__":
#     if len(sys.argv) > 1:
#         command_text = sys.argv[1]
#         result = process_voice_command(command_text)
#         print(result)
#     else:
#         # اختبارات
#         test_commands = [
#             "شغل تذكير يوم 26 شهر 6",
#             
#         ]
#         
#         for cmd in test_commands:
#             print(f"الأمر: {cmd}")
#             result = process_voice_command(cmd)
#             print(f"النتيجة: {result}")
#             print("-" * 50)