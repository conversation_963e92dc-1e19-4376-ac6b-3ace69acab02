import mysql.connector
import datetime
from datetime import timedelta, date
import time
import paho.mqtt.publish as publish
import threading
import netifaces as ni
import sys
import re
sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/nlt')
sys.path.append('/home/<USER>/myProject/modules')
import static as st
import alarm as alarmm

import handle_alarm as handle_alarm


def _handle_device_control(self, tokens, text):
    """معالجة التحكم بالأجهزة"""
    # تحديد الجهاز المطلوب
    
    ac_topic = []
    tv_topic = []
    sw_topic = []
    err = ''
    if self.text_devices:
        for device in self.text_devices:
            
            if 'AC' in self.text_devices[device]['type']:
                topic = 'ROOMS/*/AC%/'
                topic += device
                ac_topic.append(topic)
            elif 'TV' in self.text_devices[device]['type']:
                topic = 'ROOMS/*/TV%/'
                topic += device
                tv_topic.append(topic)
            elif 'SWITCH' in self.text_devices[device]['type']:
                topic = 'ROOMS/*/SWITCH%/'
                topic += device
                sw_topic.append(topic)
        if self.devices_err != []:
            for name in self.devices_err:
                if 'AC' in name:
                    err += 'مُكَيف '+name['AC']['name']+' '
                elif 'TV' in name:
                    err += 'تِلفاز '+name['TV']['name']+' '
                elif 'SWITCH' in name:
                    err += 'مُفتاح '+name['SWITCH']['name']+' '
                elif 'LIGHT' in name:
                    err += 'إضائَةْ '+name['LIGHT']['name']+' '
                else:
                    err += 'مِروَحِةْ '+name['VAN']['name']+' '
            if self.text_rooms == []:
                err+= 'في هذه الغُرفَهْ'
            elif self.text_rooms != '*':
                err+= 'في '
                for room in self.text_rooms:
                    err+= text[text.index(room)+len(room)+1:text.index(')')].replace('_',' ')
    elif self.devices_err != []:       
        for name in self.devices_err:
            if 'AC' in name:
                err += 'مُكَيف '+name['AC']['name']+' '
            elif 'TV' in name:
                err += 'تِلفاز '+name['TV']['name']+' '
            elif 'SWITCH' in name:
                err += 'مُفتاح '+name['SWITCH']['name']+' '
            elif 'LIGHT' in name:
                err += 'إضائَةْ '+name['LIGHT']['name']+' '
            else:
                err += 'مِروَحِةْ '+name['VAN']['name']+' '
        if self.text_rooms == []:
            err+= 'في هذه الغُرفَهْ'
        elif self.text_rooms != '*':
            err+= 'في '
            for room in self.text_rooms:
                err+= text[text.index(room)+len(room)+1:text.index(')')].replace('_',' ')
    else:
        def findSwitch(results,device):
            for result in results:
                sw = {}
                try:
                    if 'switches' in self.devices_cache[result['id']]:
                        for switch in self.devices_cache[result['id']]['switches']:
                            if 'Type' in self.devices_cache[result['id']]['switches'][switch]:
                                if self.devices_cache[result['id']]['switches'][switch]['Type'] in device:
                                    sw[switch] = 'None'
                    if sw != {}:
                        self.text_devices[result['id']] = {'name':sw,'room':self.devices_cache[result['id']]['rooms'],'type':'SWITCH%'}
                        topic = f"ROOMS/*/SWITCH%/{result['id']}"
                        sw_topic.append(topic)
                except (KeyError, TypeError) as e:
                    print(f"خطأ في معالجة switch {result.get('id', 'unknown')}: {e}")
                    continue
        def addTopic(device):
            check = True
            if self.text_rooms == '*':
                self.system_self.cursor.execute("SELECT id FROM Devices WHERE Type LIKE '%s'"%(device if device not in ['LIGHT','VAN'] else 'SWITCH%',))
                results = self.system_self.cursor.fetchall()
                if results:
                    check = False
                    topic = f'ROOMS/*/{device}/*'
                    if device == 'AC%':
                        ac_topic.append(topic)
                    elif device == 'TV%':
                        tv_topic.append(topic)
                    elif device == 'SWITCH%' or device == 'LIGHT' or device == 'VAN':
                        findSwitch(results,device)     
            elif self.text_rooms == []:
                self.system_self.cursor.execute("SELECT id FROM Devices WHERE Type LIKE '%s' and rooms = '%s'"%(device if device not in ['LIGHT','VAN'] else 'SWITCH%',self.myroom))
                results = self.system_self.cursor.fetchall()
                if results:
                    check = False
                    topic = f'ROOMS/{self.myroom}/{device}/*'
                    if device == 'AC%':
                        ac_topic.append(topic)
                    elif device == 'TV%':
                        tv_topic.append(topic)
                    elif device == 'SWITCH%' or device == 'LIGHT' or device == 'VAN':
                        findSwitch(results,device)
                    
            else:
                for room in self.text_rooms:
                    self.system_self.cursor.execute("SELECT id FROM Devices WHERE Type LIKE '%s' and rooms = '%s'"%(device if device not in ['LIGHT','VAN'] else 'SWITCH%',room))
                    results = self.system_self.cursor.fetchall()
                    if results:
                        check = False
                        topic = f'ROOMS/{room}/{device}/*'
                        if device == 'AC%':
                            ac_topic.append(topic)
                        elif device == 'TV%':
                            tv_topic.append(topic)
                        elif device == 'SWITCH%' or device == 'LIGHT' or device == 'VAN':
                            findSwitch(results,device)
            err1 = ''
            if check == True:
                if device == 'AC%':
                    err1 ='مُلحَق مُكَيف'
                elif device == 'TV%':
                    err1 = 'مُلحَق تِلفاز'
                elif device == 'SWITCH%':
                    err1 = 'مُلحَق مُفتاح'
                elif device == 'LIGHT':
                    err1 = 'مُلحَق إضائهْ'
                elif device == 'VAN':
                    err1 = 'مُلحَق مِروُحُهْ'
                return err1
            else:
                return ''
        if 'AC%' in text:
            get_err = addTopic('AC%')
            err += get_err+' '
        elif 'TV%' in text:
            get_err = addTopic('TV%')
            err += get_err+' '
        elif 'LIGHT' in text:
            get_err = addTopic('LIGHT')
            err += get_err+' '
        elif 'SWITCH%' in text:
            get_err = addTopic('SWITCH%')
            err += get_err+' '
        elif 'VAN' in text and 'AC%' not in text:
            get_err = addTopic('VAN')
            err += get_err+' '
        err = err.strip()
        if err != '':
            if self.text_rooms == '*':
                err += ' في النِظام'
            elif self.text_rooms == []:
                err += ' في هذهِ الغُرفَهْ'
            elif self.text_rooms != []:
                room_err = ' في '
                for room in self.text_rooms:
                    room_err += text[text.index(room)+len(room)+1:text.index(')')].replace('_',' ')+' '
            err = err.strip()
    time = None
    say = ''
    if (ac_topic != [] or tv_topic != [] or sw_topic != []) and ('TIME' in tokens or 'DAY' in tokens or 'ALARM' in tokens):
        time = handle_alarm._handle_alarm(self, tokens, text)
        say += 'تم جَدولَةْ مُهِمَةٌ جَديدَهْ في '
        if '#' in time[1]:
                time[1]=time[1].replace('#','')
        clock=time[1].replace('PM','مسائاً')
        clock=time[1].replace('AM','صباحاً')
        say += clock
        if time[0] == 'None' and re == 'ON':
                time[0] == 'Sat Sun Mon Tue Wed Thu Fri'
        if time[0] == 'Sat Sun Mon Tue Wed Thu Fri' and time[2] == 'ON':
            days=' كل يوم'
        elif time[0] == 'None':
            days=''
        else:
            days=time[0].strip()
            days=days.replace('Sat','السَبت')
            days=days.replace('Sun','الاحَد')
            days=days.replace('Mon','الاثنَيّن')
            days=days.replace('Tue','الثُلاثاء')
            days=days.replace('Wed','الاربِعاء')
            days=days.replace('Thu','الخَميس')
            days=days.replace('Fri','الجُمُعَهْ')
            days=days.replace(' ',' و ')
            if re == 'ON':
                days=' كل يوم '+days
            else:
                    days=' يوم '+days
        say += days
        if err:
            say += '\n'
            say += 'لَم يَتِم العُثورْ على '
            say += err
    elif err != '':
        say += 'لَم يَتِم العُثورْ على '
        say += err
    if ac_topic != []:
        control_ac(self,ac_topic, tokens, text,time)
        
    if tv_topic != []:
        control_tv(self,tv_topic, tokens, text,time)
    if sw_topic != []:
        control_sw(self,sw_topic, tokens, text,time)
    if say != '':
        self.system_self.tts.say(say)
    
def control_ac(self, topic, tokens, text,time):
    """التحكم في المكيف"""
    command = ''
    split = text.split()
    
    if 'OFF' in split:
        command += 'OFF'
    else :
        if ('+' in split or '-' in split) and not (('الى' in split and split[split.index('الى')+1].isnumeric()) or ('على' in split and split[split.index('على')+1].isnumeric())):
            command += '+' if '+' in split else '-'
        else:
            command += 'RUN'
        if 'VAN' in split and (split.index('VAN')<split.index('AC%') or 'على VAN' in text):
            if ('NUMBER' in tokens and ('1' in split or '2' in split or '3' in split or '4' in split)) or 'AUTO' in split:
                command += ' X VAN '
                command += '1' if '1' in split else '2' if '2' in split else '3' if '3' in split else '4' if '4' in split else '4'
                command += ' VAN'
            else:
                if '+' in split or '-' in split:
                    command += ' 0 VAN 1 VAN'
                else:
                    command += ' X VAN 0 VAN'
        elif 'NUMBER' in tokens:
            for number in range(len(split)):
                if split[number].isnumeric():
                    command += f' {split[number]} VAN'
                    if 'VAN' in split and number+1 != len(split):
                        for van in split[number+1:]:
                            if van in ['1','2','3','4','AUTO'] and (f'VAN {van}' in text or f'VAN على {van}' in text or f'VAN الى {van}' in text or f'VAN في {van}' in text or f'{van} VAN' in text or f'{van} الى VAN' in text or f'{van} على VAN' in text or f'{van} في VAN' in text):
                                command +=f' {van}' if van != 'AUTO' else ' 4'
                    else:
                        command += ' 0'
                    command +=' XX'
                    break
        else:
            command += ' 0 VAN 0 XX'
        command = command.strip()
        if command == 'RUN':
            command += ' 0 VAN 0 XX'
        if 'COLD' in split or 'HEAT' in split:
            command = command.replace('XX','AC' if 'COLD' in split else 'HEAT')
    command += f" {ni.ifaddresses('wlan0')[17][0]['addr']}"
    print(command,topic,'1123342')
    if time:
        for t in topic:
            print(123456789)
            alarmm.Job(self,time[0],time[1],time[2],'ON',topic,command)
    else:
        for t in topic:
            publish.single(t,command, hostname=st.ip)

def control_tv(self, topic, tokens, text,time):
    """التحكم في التلفاز"""
    split = text.split()
    if ('SILENTE' in text and 'DELETE' not in split and 'OFF' not in split and 'STOP' not in split) or (('OFF' in split or 'STOP' in split) and 'VOICE' in split):
        command = 'SIL'
    elif 'SILENTE' in text and ('DELETE' in split or 'OFF' in split or 'STOP' in split):
        command = 'VOICE + 1'
    elif 'VOICE' in text:
        
        command = 'VOICE -' if '-' in text else 'VOICE +'
        if 'NUMBER' in tokens:
            for number in split:
                if number.isnumeric() and int(number)< 16:
                    command += f' {number}'
                elif number.isnumeric():
                    command += ' 15'
        else :
            command += ' 2'
    elif 'CH' in text or 'CHANGE' in text or 'NUMBER' in tokens:
        command = 'CH '
        if '(CH)' in text:
            for ch in split:
                if '(CH)' in ch:
                    command += '= '+ ch[:'(']
        elif 'NUMBER' in tokens:
            for number in split:
                if number.isnumeric():
                    command += f'- {number}' if '-' in split else f'- {number}' if '+' in split else f'= {number}'
        else:
            command += ' + 1'
    elif 'OFF' in split or 'STOP' in split:
        command = 'POWER-OFF'
    else:
        command = 'POWER-ON'
    command += f" {ni.ifaddresses('wlan0')[17][0]['addr']}"
    if time:
        for t in topic:
            alarmm.Job(self,time[0],time[1],time[2],'ON',topic,command)
    else:
        for t in topic:
            publish.single(t,command, hostname=st.ip)

def control_sw(self, topic, tokens, text,time):
    
    if 'OFF' in text or 'STOP' in text:
        c ='_OFF'
    else:
        c = '_RUN'
        
    for t in topic:
        command = ''
        for sw in self.text_devices[t.split('/')[-1]]['name']:
            command += sw+c+' '
        command = command.strip()
        command += f" {ni.ifaddresses('wlan0')[17][0]['addr']}"
        command = command.strip()
        if time:
            alarmm.Job(self,time[0],time[1],time[2],'ON',[t],command)
        else:
            publish.single(t,command, hostname=st.ip)


def commandRooms(self,command,type = 'all',serial = 'XX:XX:XX:XX:XX:XX',rooms = []):
    print(1)
    self.db.commit()
    self.cursor.execute("SELECT * FROM Devices")
    print(2)
    result = self.cursor.fetchall()
    print(3)
    if result:
        if rooms !=[]:
            for room in rooms:
                if type=='all' or type =='AC':
                    topic = 'ROOMS/%s/AC%%/*' %(room,)
                    if command == True :
                        ac = 'RUN 0 VAN 0 XX '+serial
                    else :
                        ac = 'OFF '+serial
                    publish.single(topic,ac, hostname=st.ip)
                if type == 'all' or type == 'TV':
                    topic = 'ROOMS/%s/TV%%/*' %(room,)
                    if command == True :
                        tv = 'POWER-ON '+serial
                    else :
                        tv = 'POWER-OFF '+serial
                    publish.single(topic,tv, hostname=st.ip)
                if type == 'all' or type == 'SWITCH' or type == 'LIGHT' or type == 'VAN':
                    topic = 'ROOMS/%s/SWITCH%%/*'%(room,)
                    sw=''
                    self.cursor.execute("SELECT * FROM Devices WHERE Type LIKE 'SWITCH%' AND Rooms = %s",(room,))
                    mySw=self.cursor.fetchall()
                    for s in mySw:
                        
                        if type == 'all':    
                            for s in mySw:
                                for v in range(int(s['Type'].split('v')[1])):
                                    if command :
                                        sw+='v'+str(v+1)+'_RUN '
                                    else: 
                                        sw+='v'+str(v+1)+'_OFF '
                            sw+=serial
                            publish.single(topic,sw, hostname=st.ip)
                        else:
                            
                            self.cursor.execute("SELECT * FROM %s_SWITCH WHERE id = 1"%(s['id']))
                            swType = cursor.fetchone()
                            for ss in swType:
                                if ss != 'id' and swType[ss]== type:
                                    if command:
                                        sw+=ss+'_RUN '
                                    else :
                                        sw+=ss+'_OFF '
                            sw+=serial
                            publish.single(topic,sw, hostname=st.ip)
        
        else :
            print(1)
            if type=='all' or type=='AC':
                topic = 'ROOMS/*/AC%/*'
                if command == True :
                    ac = 'RUN 0 VAN 0 XX '+serial
                else:
                    ac = 'OFF '+serial
                publish.single(topic,ac, hostname=st.ip)
            print(2)
            if type=='all' or type=='TV':
                topic = 'ROOMS/*/TV%/*'
                if command == True :
                    tv = 'POWER-ON '+serial
                else:
                    tv = 'POWER-OFF '+serial
                publish.single(topic,tv, hostname=st.ip)
            print(3)
            if type=='all' or type=='SWITCH' or type=='LIGHT' or type=='VAN':
                topic = 'ROOMS/*/SWITCH%/*'
                sw=''
                self.cursor.execute("SELECT * FROM Devices WHERE Type LIKE 'SWITCH%'")
                mySw=self.cursor.fetchall()
                for s in mySw:
                    
                    if type == 'all':    
                        
                        for v in range(int(s['Type'].split('v')[1])):
                            if command :
                                sw+='v'+str(v+1)+'_RUN '
                            else: 
                                sw+='v'+str(v+1)+'_OFF '
                        sw+=serial
                        publish.single(topic,sw, hostname=st.ip)
                    else:
                        
                        self.cursor.execute("SELECT * FROM %s_SWITCH WHERE id = 1"%(s['id']))
                        swType = self.cursor.fetchone()
                        for ss in swType:
                            if ss != 'id' and swType[ss]== type:
                                if command:
                                    sw+=ss+'_RUN '
                                else :
                                    sw+=ss+'_OFF '
                        sw+=serial
                        publish.single(topic,sw, hostname=st.ip)
                
        print(4)    
        publish.single('edit','1', hostname=st.ip)
