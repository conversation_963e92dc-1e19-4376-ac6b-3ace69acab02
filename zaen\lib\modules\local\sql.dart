import 'dart:convert';

import 'package:mysql1/mysql1.dart';
import 'package:sqflite/sqflite.dart';
import 'package:zaen/modules/local/ip.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/controller/controller.dart';

getDevices() async {
  while (client.connectionStatus!.state.name == 'connected') {
    try {
      Map rooms = {};
      Map roomDevices = {};
      late Results Devices;
      late Results Rooms;

      var home;
      var homeType;
      var homeImage;
      String? homeId;
      var homeState;
      var appDB = await openDatabase('${controller.system}.db', version: 3);
      homeState = null;
      // Open a connection (testdb should already exist)
      var myHome;

      myHome = await appDB.rawQuery('SELECT * FROM home');
      controller.home.value = myHome[0]['name'];
      controller.homeType.value = myHome[0]['type'];
      controller.homeImage.value = myHome[0]['image'];
      final conn = await MySqlConnection.connect(ConnectionSettings(
          host: controller.hostZain.value,
          // port: 80,
          user: 'root',
          db: 'zain',
          password: 'zain',
          characterSet: CharacterSet.UTF8));
      // Devices = await conn.query('select * from Devices');
      print('111111111');
      await conn.query(
        'UPDATE phones SET name = ?, type = ? WHERE mac = ?',
        [controller.deviceName, controller.deviceModel, controller.uuid],
      );
      controller.routines = await appDB.rawQuery('SELECT * FROM routine');
      controller.tasks = await conn.query("SELECT * FROM ADevice");
      controller.routineWords = await conn.query("SELECT * FROM RDevice");
      List fav = await appDB.rawQuery('SELECT id FROM favorite');
      controller.favorite.clear();
      for (var i in fav.toList()) {
        controller.favorite.add(i['id'].toString());
      }
      Devices = await conn.query("SELECT id,Type FROM Devices");

      print(Devices);
      List<Map<String, Object?>> appdevices =
          await appDB.rawQuery("SELECT id from devices");
      Map myDevices = {};
      List appDevices = [];
      String NULLDevices = '';
      // for (var i in Devices.toList()) {
      //   myDevices[i.fields['id']] = i.fields['Type'];
      // }
      for (var i in Devices.toList()) {
        if (i.fields['Rooms'] != 'x') {
          myDevices[i.fields['id']] = i.fields['Type'];
        }
        if (i.fields['Type'].toString().contains('TV') ||
            i.fields['Type'].toString().contains('AC')) {
          if (NULLDevices.isEmpty) {
            NULLDevices += i.fields['id'] + ' IS NULL';
          } else {
            NULLDevices += ' AND ' + i.fields['id'] + ' IS NULL';
          }
        }
        if (i.fields['Type'] == 'ZAIN-Main') {
          homeId = i.fields['id'];
        }
      }
      // print(myDevices);
      print('7777777777777777777777777777777777777777777777');
      for (var i in appdevices) {
        if (myDevices.containsKey(i['id']) == false) {
          appDB.rawQuery('DELETE FROM devices WHERE id = "${i['id']}"');
        } else {
          appDevices.add(i['id']);
        }
      }
      for (var i in myDevices.keys.toList()) {
        if (appDevices.contains(i) == false) {
          var p = '';
          // print(myDevices[i].toString());
          print('44444444477777777777777777777777777777777777777');
          if (myDevices[i].toString().contains('SWITCH')) {
            for (var s = 0;
                s <
                    int.parse(
                        myDevices[i].toString().replaceAll('SWITCHv', ''));
                s++) {
              p += 'x_';
            }
            p += 'x';
            print('333333333333333333333333333333333333333');
            // print(p);
          } else {
            p = 'x';
          }
          appDB.rawQuery('insert into devices(id,name) values(?,?)', [i, p]);
        }
      }

      Rooms = await conn.query(
          "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME LIKE '%Rooms'");
      // // print(Rooms.toList()[0]);
      List<Map<String, Object?>> myroom =
          await appDB.rawQuery("SELECT id from rooms");
      List myrooms = [];
      List approoms = [];
      String NULLRooms = '';
      for (var i in Rooms.toList()) {
        myrooms.add(i.values?.first);
        if (NULLRooms.isEmpty) {
          NULLRooms += i.values!.first.toString() + ' is NULL';
        } else {
          NULLRooms += ' AND ' + i.values!.first.toString() + ' is NULL';
        }
        Results sqlAlarm = await conn
            .query("select * from Alarm where room = '${i.values?.first}'");
        controller.alarms[i.values?.first] = [];
        for (var alarm in sqlAlarm.toList()) {
          controller.alarms[i.values?.first].add(alarm.fields);
        }
      }
      for (var i in myroom) {
        if (myrooms.contains(i['id']) == false) {
          appDB.rawQuery('DELETE FROM rooms WHERE id = "${i['id']}"');
        } else {
          approoms.add(i['id']);
        }
      }
      print('11111111144444444444444');

      if (NULLDevices.isNotEmpty) {
        await conn.query("DELETE FROM NDevice where ${NULLDevices}");
      }
      if (NULLRooms.isNotEmpty) {
        await conn.query("DELETE FROM Rooms where ${NULLRooms}");
      }
      print('11111111144444444444444555555');
      for (var i in Rooms.toList()) {
        if (approoms.contains(i.values?.first) == false) {
          appDB.rawQuery('insert into rooms(id,name,image) values(?,?,?)', [
            i.values?.first,
            'x',
            'assets/images/places/home/<USER>'
          ]);
        }
        print('11111111144444444444111111');
        // // print(i.values?.first);
        List<Map<String, Object?>> myroom;

        myroom = await appDB.rawQuery(
            'SELECT name,image FROM rooms WHERE id = "${i.values?.first}"');

        // await appDB.close;
        // print(myroom[0]);
        for (var roomId in i) {
          // جلب البيانات من الداتا بيز الخاصه بالتطبيق
          var roomImage = myroom[0]['image'];
          var roomPrivName = myroom[0]['name'];
          var roompubName;
          Results x = await conn.query("select $roomId from Rooms");
          // // print(x);
          print('1111111114444444444442222222222222222');
          for (var e = 0; e < x.length; e++) {
            if (x.elementAt(e).fields.values.first != null &&
                x.elementAt(e).fields.values.first != 'null' &&
                x.elementAt(e).fields.values.first != 'None') {
              roompubName = x.elementAt(e).fields.values.first;
              // // print(roompubName);
            }
          }

          var roomstate = null;
          // // print(roomId);
          Devices =
              await conn.query("select * from Devices where Rooms = '$roomId'");
          roomDevices = {};
          // print(Devices);

          for (var j in Devices.toList()) {
            List<Map<String, Object?>> mydevice;
            // await appDB.transaction((txn) async {
            //   int id2 = await txn.rawInsert(
            //       'INSERT INTO devices(id, name) VALUES(?, ?)',
            //       ['${j['id']}', 'None']);
            // }).then((value) {
            //   // print(value);
            // }).catchError((onError) {
            //   // print(onError);
            // });
            mydevice = await appDB
                .rawQuery('SELECT name FROM devices WHERE id = "${j['id']}"');

            // await appDB.close;
            print(mydevice[0]['name']);

            var deviceState;
            var date;
            if (j['connect'] != null &&
                j['connect'].contains('-') &&
                j['type'].toString().contains('ZAIN') == false) {
              date = j['connect'].split('-');
              if (int.parse(date[0]) == DateTime.now().year &&
                  int.parse(date[1]) == DateTime.now().month &&
                  int.parse(date[2]) == DateTime.now().day &&
                  (int.parse(date[3]) == DateTime.now().hour ||
                      (DateTime.now().hour - int.parse(date[3]) == 1 &&
                          DateTime.now().minute == 0 &&
                          int.parse(date[4]) == 59)) &&
                  ((DateTime.now().minute + (DateTime.now().second / 100)) -
                          int.parse(date[4])) <
                      1.2) {
                controller.devices[j['id']] = true;
              } else {
                controller.devices[j['id']] = false;
                deviceState = 'x';
              }
            }

            if (j['Type'].toString().contains("AC")) {
              var devicePrivName = mydevice[0]['name'] != 'None' &&
                      mydevice[0]['name'] != 'null' &&
                      mydevice[0]['name'] != null
                  ? mydevice[0]['name']
                  : null;

              deviceState = deviceState == 'x'
                  ? null
                  : j['state'].toString().contains("OFF")
                      ? false
                      : true;
              roomDevices[j['id']] = {
                'id': j['id'],
                'device': j['Type'],
                'priv': devicePrivName,
                'state': deviceState,
                'degree': j['state'] == 'OFF' ||
                        j['state'].toString().split(' ')[1] == 'X'
                    ? 16
                    : int.parse(j['state'].toString().split(' ')[1]),
                'type': j['state'] == 'OFF' ||
                        j['state'].toString().split(' ')[4] == 'AC'
                    ? 'تبريد'
                    : j['state'].toString().split(' ')[4] == 'HEAT'
                        ? 'تدفئه'
                        : 'مروحه',
                'speed': j['state'] == 'OFF'
                    ? 2
                    : int.parse(j['state'].toString().split(' ')[3]),
                'swing': false,
              };

              if (deviceState == true) {
                roomstate = true;
                homeState = true;
              } else if (deviceState == false && roomstate == null) {
                roomstate = false;
                if (homeState == null) {
                  homeState = false;
                }
              }
            } else if (j['Type'].toString().contains("TV")) {
              var devicePrivName = mydevice[0]['name'] != 'None' &&
                      mydevice[0]['name'] != 'null' &&
                      mydevice[0]['name'] != null
                  ? mydevice[0]['name']
                  : null;
              Results c = await conn.query("SELECT * FROM ${j['id']}_TV");
              Map ch = {};
              for (var e in c.toList()) {
                ch[e.fields['chaneel']] = e.fields['number'];
              }

              deviceState = deviceState == 'x'
                  ? null
                  : j['state'].toString().contains("POWER-OFF")
                      ? false
                      : true;
              roomDevices[j['id']] = {
                'device': j['Type'],
                'id': j['id'],
                'priv': devicePrivName,
                'state': deviceState,
                'sil': j['state'].toString().contains("SIL-OFF") ? true : false,
                'ch': ch
              };

              if (deviceState == true) {
                roomstate = true;
                homeState = true;
              } else if (deviceState == false && roomstate == null) {
                roomstate = false;
                if (homeState == null) {
                  homeState = false;
                }
              }
            } else if (j['Type'].toString().contains("SWITCH")) {
              print('rrrrrrrrrrrrrrrrrrrrrrrr');

              // await appDB.transaction((txn) async {
              //   // print(12345);
              //   await txn.rawUpdate('UPDATE devices SET name = ? WHERE id = ?',
              //       ['x_x_x_x_اصفر', j['id']]);
              //   // print(123456);
              // });

              Results sw = await conn.query("SELECT * FROM ${j['id']}_SWITCH");
              // // print(sw.elementAt(1).fields);
              // // print(sw.length);
              print('eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee');
              var devicePrivName = mydevice[0]['name'] != 'None' &&
                      mydevice[0]['name'] != 'null' &&
                      mydevice[0]['name'] != null
                  ? mydevice[0]['name']
                  : null;
              // var devicePubName = 'يونيفيرسال';
              deviceState = deviceState == 'x' ? null : false;
              String NULLSw = '';
              Map swList = {};
              swList[j['id']] = {
                // 'id': j['id'],
                // 'device': 'SWITCH',
                // 'priv': devicePrivName,
                // 'state': deviceState,
              };
              print('aaaaaaaaaaaaaaaaaaaaaaaaaaaaa');
              for (var e in j['state'].toString().split(' ')) {
                var swId = e.toString().split('_')[0];

                if (NULLSw.isEmpty) {
                  NULLSw += swId + ' IS NULL';
                } else {
                  NULLSw += ' AND ' + swId + ' IS NULL';
                }
                print(1);
                print(e);
                var state = deviceState == null
                    ? null
                    : e.toString().split('_')[1] == 'OFF'
                        ? false
                        : true;
                print(2);
                var type = sw.elementAt(0).fields[swId];
                if (state != null && state) {
                  deviceState = true;
                }
                swList[j['id']][swId] = {
                  'state': state,
                  'type': type,
                };
                print(3);
                // swList.addEntries({
                //   swId: {
                //     'state': state,
                //     'type': type,
                //   }
                // }.entries);
              }
              print('eeeFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF');
              await conn.query("DELETE FROM ${j['id']}_SWITCH where ${NULLSw}");
              swList[j['id']].addEntries({
                'device': 'SWITCH',
                'id': j['id'],
                'priv': devicePrivName,
                // 'pub': devicePubName,
                'state': deviceState,
              }.entries);

              roomDevices.addEntries(swList.entries);
              if (deviceState == true) {
                roomstate = true;
                homeState = true;
              } else if (deviceState == false && roomstate == null) {
                roomstate = false;
                if (homeState == null) {
                  homeState = false;
                }
              }
              print('qqqqqqqqqqqqqqqqqqqqqqqq');
            } else if (j['Type'].toString().contains("ZAIN")) {
              var devicePrivName = mydevice[0]['name'] != 'None' &&
                      mydevice[0]['name'] != 'null' &&
                      mydevice[0]['name'] != null
                  ? mydevice[0]['name']
                  : null;
              var devicePubName = 'زين';
              roomDevices[j['id']] = {
                'id': j['id'],
                'device': j['Type'].toString().contains("ZAIN-Main")
                    ? 'ZAIN-Main'
                    : 'ZAIN',
                'pubName': devicePubName,
                // يتم عمل zainTV
                'hearing': 21.0,
                'volume': 13.0,
                'sil': true,
                'lestin': true,
                'play': true,
              };

              homeId = j['id'].toString();
            }
          }
          // // print(roomDevices);
          rooms[roomId] = {
            'id': roomId,
            'state': roomstate,
            'image': roomImage,
            'pubName': roompubName,
            'privName': roomPrivName,
            'devices': roomDevices
          };
        }
      }
      // // print(rooms);

      // var databasesPath = await getDatabasesPath();
      // String path = join(databasesPath, 'demo.db');

      // // print(myHome);
      // getData = await conn.query("SELECT id,type,connect FROM Devices");
      // var date;
      // for (var i in getData) {
      //   if (i.fields['connect'] != null &&
      //       i.fields['connect'].contains('-') &&
      //       i.fields['type'].contains('ZAIN') == false) {
      //     date = i.fields['connect'].split('-');
      //     if (int.parse(date[0]) == DateTime.now().year &&
      //         int.parse(date[1]) == DateTime.now().month &&
      //         int.parse(date[2]) == DateTime.now().day &&
      //         (int.parse(date[3]) == DateTime.now().hour ||
      //             (DateTime.now().hour - int.parse(date[3]) == 1 &&
      //                 DateTime.now().minute == 0 &&
      //                 int.parse(date[4]) == 59)) &&
      //         ((DateTime.now().minute + (DateTime.now().second / 100)) -
      //                 int.parse(date[4])) <
      //             1.2) {
      //       controller.devices[i.fields['id']] = true;
      //     } else {
      //       controller.devices[i.fields['id']] = false;
      //     }
      //   }
      // }
      print('1111111114444444444444433333333333333333');
      var homeConnect = false;
      for (var r in rooms.values) {
        bool room = false;
        for (var d in r['devices'].values) {
          if (controller.devices[d['id']] == true) {
            room = true;
            homeConnect = true;
          }
        }
        controller.devices[r['id']] = room;
      }
      for (int i = controller.routines.length - 1; i >= 0; i--) {
        Map routine = controller.routines[i];
        Map routineData;

        try {
          // التعامل مع الحالات المختلفة للروتين
          if (routine['routine'] is String) {
            routineData = jsonDecode(routine['routine']);
          } else if (routine['routine'] is Map) {
            routineData = routine['routine'];
          } else {
            // إزالة الروتين إذا كان التنسيق غير صحيح
            controller.routines.removeAt(i);
            continue;
          }

          bool routineModified = false;
          List<String> roomKeys = List.from(routineData.keys);

          for (String roomKey in roomKeys) {
            // التحقق من وجود الغرفة
            if (!rooms.containsKey(roomKey) && roomKey != 'home') {
              routineData.remove(roomKey);
              routineModified = true;
              continue;
            }

            // التعامل مع القيم المختلفة للروتين
            if (routineData[roomKey] is bool) {
              // إذا كانت القيمة بوليانية
              continue;
            }

            if (routineData[roomKey] is Map) {
              Map roomRoutine = routineData[roomKey];
              List<String> deviceKeys = List.from(roomRoutine.keys);

              for (String deviceKey in deviceKeys) {
                // التحقق من وجود الجهاز في الغرفة
                if (!rooms[roomKey]['devices'].containsKey(deviceKey)) {
                  roomRoutine.remove(deviceKey);
                  routineModified = true;
                }
              }

              // إذا أصبحت الغرفة فارغة
              if (roomRoutine.isEmpty) {
                routineData.remove(roomKey);
                routineModified = true;
              }
            }
          }

          // التحقق من فراغ الروتين
          if (routineData.isEmpty) {
            // حذف الروتين من قاعدة البيانات
            await appDB
                .delete('routine', where: 'id = ?', whereArgs: [routine['id']]);
            controller.routines.removeAt(i);
          } else if (routineModified) {
            // تحديث الروتين في قاعدة البيانات
            await appDB.update('routine', {'routine': jsonEncode(routineData)},
                where: 'id = ?', whereArgs: [routine['id']]);

            // تحديث الروتين في الذاكرة
            controller.routines[i]['routine'] = jsonEncode(routineData);
          }
        } catch (e) {
          print('Error processing routine: $e');
          // إزالة الروتين في حالة حدوث خطأ
          controller.routines.removeAt(i);
        }
      }
      for (var task in controller.tasks!) {
        Map<String, dynamic> routeData;

        try {
          // التعامل مع البيانات في حقل route
          if (task['route'] is String) {
            routeData = jsonDecode(task['route']);
          } else if (task['route'] is Map) {
            routeData = task['route'];
          } else {
            print('Invalid route format for task: ${task['id']}');
            continue;
          }

          bool taskModified = false;
          List<String> roomKeys = List.from(routeData.keys);

          for (String roomKey in roomKeys) {
            // التحقق من وجود الغرفة
            if (!rooms.containsKey(roomKey) && roomKey != 'home') {
              routeData.remove(roomKey);
              taskModified = true;
              continue;
            }

            if (routeData[roomKey] is Map) {
              Map roomDevices = routeData[roomKey];
              List<String> deviceKeys = List.from(roomDevices.keys);

              for (String deviceKey in deviceKeys) {
                // التحقق من وجود الجهاز في الغرفة
                if (!rooms[roomKey]['devices'].containsKey(deviceKey)) {
                  roomDevices.remove(deviceKey);
                  taskModified = true;
                } else {
                  // التحقق من صحة بيانات الجهاز
                  Map deviceData = roomDevices[deviceKey];
                  if (deviceData != null && deviceData is Map) {
                    // التحقق من وجود الحقول المطلوبة
                    if (!deviceData.containsKey('state') ||
                        !deviceData.containsKey('degree') ||
                        !deviceData.containsKey('type')) {
                      roomDevices.remove(deviceKey);
                      taskModified = true;
                    }
                  } else {
                    roomDevices.remove(deviceKey);
                    taskModified = true;
                  }
                }
              }

              // إذا أصبحت الغرفة فارغة
              if (roomDevices.isEmpty) {
                routeData.remove(roomKey);
                taskModified = true;
              }
            }
          }

          // التحقق من فراغ المهمة
          if (routeData.isEmpty) {
            // حذف المهمة من قاعدة البيانات
            await conn.query('DELETE FROM ADevice WHERE id = ?', [task['id']]);
            print('Task ${task['id']} deleted due to empty route data');
          } else if (taskModified) {
            // تحديث المهمة في قاعدة البيانات
            await conn.query('UPDATE ADevice SET route = ? WHERE id = ?',
                [jsonEncode(routeData), task['id']]);
            print('Task ${task['id']} updated successfully');
          }
        } catch (e) {
          print('Error processing task ${task['id']}: $e');
        }
      }
      // for (var i in rooms) {
      //   var room = false;
      //   // print(i['devices']);
      //   // print('4444444444444444444422222222222');
      //   for (var j in i['devices']) {
      //     if (controller.devices[j['id']] == true) {
      //       room = true;
      //       homeConnect = true;
      //     }
      //   }
      //   controller.devices[i['id']] = room;
      // }
      controller.devices['home'] = homeConnect;

      await conn.close();
      appDB.close;

      // appDatabase();
      controller.start(myHome[0]['name'], myHome[0]['type'], myHome[0]['image'],
          homeState, rooms, homeId);
      print(homeId);
      break;
    } catch (e) {
      print(e);
    }
  }
}
