# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 101ms
create_cxx_tasks completed in 105ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 16ms]
    create-ARMEABI_V7A-model 12ms
    [gap of 82ms]
    create-ARMEABI_V7A-model 14ms
    create-X86-model 10ms
  create-initial-cxx-model completed in 148ms
create_cxx_tasks completed in 153ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 83ms]
    create-ARMEABI_V7A-model 69ms
    [gap of 10ms]
    create-X86-model 19ms
  create-initial-cxx-model completed in 187ms
create_cxx_tasks completed in 193ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    [gap of 119ms]
  create-initial-cxx-model completed in 129ms
create_cxx_tasks completed in 136ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 103ms
create_cxx_tasks completed in 107ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-ARMEABI_V7A-model 31ms
    [gap of 88ms]
  create-initial-cxx-model completed in 127ms
create_cxx_tasks completed in 136ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 91ms]
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 104ms
create_cxx_tasks completed in 108ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 14ms
    create-X86-model 12ms
    create-X86_64-model 12ms
    create-module-model 10ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 16ms
    create-X86-model 15ms
    create-X86_64-model 13ms
    create-module-model 10ms
    [gap of 18ms]
    create-ARM64_V8A-model 10ms
    create-X86-model 14ms
    create-X86_64-model 53ms
  create-initial-cxx-model completed in 258ms
create_cxx_tasks completed in 266ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 12ms]
    create-ARMEABI_V7A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 13ms
    [gap of 27ms]
    create-ARM64_V8A-model 11ms
    create-X86-model 14ms
    create-X86_64-model 13ms
    [gap of 41ms]
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 173ms
create_cxx_tasks completed in 181ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 102ms
create_cxx_tasks completed in 109ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 15ms
    create-X86-model 13ms
    create-X86_64-model 14ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 14ms
    create-X86-model 12ms
    create-X86_64-model 12ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 28ms
  create-initial-cxx-model completed in 256ms
create_cxx_tasks completed in 263ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 12ms
    create-X86-model 11ms
    create-X86_64-model 10ms
    create-module-model 12ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 15ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 13ms
    [gap of 18ms]
  create-initial-cxx-model completed in 201ms
create_cxx_tasks completed in 211ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 37ms]
    create-X86_64-model 13ms
    [gap of 18ms]
    create-ARMEABI_V7A-model 10ms
    create-X86-model 11ms
    [gap of 45ms]
  create-initial-cxx-model completed in 142ms
create_cxx_tasks completed in 145ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 13ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 15ms
    create-X86-model 18ms
    create-X86_64-model 18ms
    create-module-model 15ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 16ms
    create-X86-model 23ms
    create-X86_64-model 38ms
    create-module-model 25ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 14ms
    create-X86-model 19ms
    create-X86_64-model 17ms
  create-initial-cxx-model completed in 352ms
  [gap of 13ms]
create_cxx_tasks completed in 365ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 18ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    create-X86_64-model 15ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 11ms
    [gap of 10ms]
    create-X86_64-model 12ms
    [gap of 13ms]
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 10ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 209ms
  [gap of 17ms]
create_cxx_tasks completed in 226ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    [gap of 10ms]
    create-X86_64-model 12ms
    [gap of 34ms]
    create-X86-model 11ms
    create-X86_64-model 11ms
    [gap of 49ms]
  create-initial-cxx-model completed in 173ms
create_cxx_tasks completed in 180ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 21ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 21ms
    create-ARM64_V8A-model 25ms
    create-X86-model 16ms
    create-X86_64-model 14ms
    [gap of 20ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 12ms
    create-X86-model 16ms
    create-X86_64-model 13ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 271ms
create_cxx_tasks completed in 280ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 19ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 12ms
    create-X86-model 13ms
    create-X86_64-model 15ms
    create-module-model 14ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 14ms
    create-X86-model 16ms
    create-X86_64-model 12ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 12ms
    create-X86-model 13ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 256ms
create_cxx_tasks completed in 269ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 14ms
    [gap of 12ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 12ms
    [gap of 10ms]
    create-X86_64-model 10ms
    create-module-model 11ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 10ms
    create-X86-model 13ms
    create-X86_64-model 11ms
    create-module-model 11ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 10ms
    [gap of 10ms]
    create-X86-model 11ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 218ms
create_cxx_tasks completed in 223ms

