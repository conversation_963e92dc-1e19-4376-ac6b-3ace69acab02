<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<style>
        	.dropdown {
		  position: relative;
		  display: inline-block;
		}
		
		.dropdown-content {
		  display: none;
		  position: absolute;
		  background-color: #f9f9f9;
		  min-width: 160px;
		  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
		  padding: 12px 16px;
		  z-index: 1;
		}
		
		.dropdown:hover .dropdown-content {
		  display: block;
		}
    	</style>
    	<script src="/scripts/snippet-javascript-console.min.js?v=1"></script>
</head>
<body>


<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
<script src="https://cdn.rawgit.com/harvesthq/chosen/gh-pages/chosen.jquery.min.js"></script>
<link href="https://cdn.rawgit.com/harvesthq/chosen/gh-pages/chosen.min.css" rel="stylesheet"/>

<details>
    <summary> apple homekit </summary> 
	<img src="static/images/homebridge.jpg" width="250" height="250">
</details>
<br>
<br>
<details>
    <summary> الاجهزة </summary>

	{% for result in results %}
		<form name="{{ result[0] }}" id="{{ result[0] }}" method="POST" action="/devices">

		{% if result[1]=="AC" %}
			مكيف
		{% elif result[1]=="TV" %}
			تلفاز
		{% elif "SWITCH" in result[1] %}
			مفتاح
		{% elif "ZAIN" in result[1] %}
			مساعد صوتي
		{%endif%}
<br>
------
			رمز الجهاز : {{ result[0].replace("_a_",":") }}
<br>
------
اختيار الغرفة : 
		<select name="room {{ result[0] }}">
			<option value="{{ result[2] }}"> {{ result[2] }}</option>
			<option value="x" {% if result[2] == "x" %} hidden {%endif%}> x </option>
			{% for room in rooms %}
 				<option value="{{room}}" {%if result[2] == room%} hidden {%endif%}>  {{room}} </option>
			{%endfor%}
		</select>
		<input type="submit" value="submit">
		</form>
		<br>
		{% if "ZAIN" not in result[1]  and "SWITCH" not in result[1] %}
-----
			<form name="{{ result[0] }}" method="POST" action="/devices">
	
						الاسماء و الصفات المختارة
	<br>
				<select data-placeholder="SELECT {{result[0]}}" multiple class="chosen-select" name="device {{result[0]}}">
			    		<option value="x" selected hidden></option>
					{% for d in NDevice %}
						{% if d == result[0] and NDevice[d] != None and NDevice[d] != "None"%}
							{% for s in NDevice[d] %}
				    				{% if s != None and s != "None" %}
				    					<option value="{{s}}" selected >{{s}}</option>
				    				{%endif%}
							{%endfor%}
				    		{%endif%}
					{%endfor%}
				</select>
	<br>
			
			<input type="submit" value="submit">
			</form>
	<br>
		<form name="{{ result[0] }}" method="POST" action="/devices">
			اضافة اسم او صفة (مثلا قم بإضافة الرقم(1) ليكون الجهاز ان كان نوعة مكيف باسم "مكيف واحد" او "المكيف الاول" او "التكييف رقم واحد" او قم باضافة كلمة "صغير يصبح "المكيف الصغير"
			<br>
			اضف اسم جديد هنا :-  
			<input type="text" name="name {{result[0]}}">
			<input type="submit" value="submit">
		</form>
	{%endif%}
	<br>
	<br>
	{% if result[1] == "TV" %}
-----
	   <form method="POST" action="/devices">
		<div class="dropdown">
		  <span>قائمة القنوات</span>
		  <div class="dropdown-content">
		    <table>
			<tr>
			    <th>القناة</th>
			    <th>الرقم</th>
			</tr>
				 <input type="text" name="TV" value="{{result[0]+"_TV"}}" hidden>
				{%for i in TVs[result[0]+"_TV"]%}
				    <tr>
			    		<td> <input type="text" name="{{i[0]}}" value="{{i[0]}}"></td>
			    		<td> <input type="text" name="{{i[1]}}" value="{{i[1]}}"></td>
				    </tr>
				{%endfor%}
		    </table>
		  </div>
		</div>
		<input type="submit" value="submit">
	    </form>
<br>
	    <form method="POST" action="/devices">
		<input type="text" name="add-TV" value="{{result[0]+"_TV"}}" hidden>
	اسم القناة :<input type="text" name="ch">
<br>
	رقم القناة :<input type="text" name="number">
			<input type="submit" value="اضافة قناة جديدة">
	    </form>
<br>
 	    <form method="POST" action="/devices">
		<input type="text" name="del-TV" value="{{result[0]+"_TV"}}" hidden>
		حذف قناه :<input type="text" name="x">
	   	<input type="submit" value="حذف القناة">
	    </form>
	

	{% elif  "SWITCH" in result[1]%}

		نوع المفاتيح
		{% for i in range(result[1].split("v")[1]|int) %}
			<form method="POST" action="/devices">
	<br>
				
				<input type="text" name="TYPE-SWITCH" value="{{result[0]+"_SWITCH"}}" hidden>
المفتاح {{i+1}}  <select name="v{{i+1}}">
				<option value="{{ SWITCHS_TYPE[result[0]+"_SWITCH"]['v'+(i+1)|string] }}"> {% if SWITCHS_TYPE[result[0]+"_SWITCH"]['v'+(i+1)|string] == "SWITCH"%} مفتاح {%elif SWITCHS_TYPE[result[0]+"_SWITCH"]['v'+(i+1)|string] == "LIGHT"%} ضوء {%elif SWITCHS_TYPE[result[0]+"_SWITCH"]['v'+(i+1)|string] == "VAN"%}  مروحه او شفاط {%endif%} </option>
				<option value="SWITCH"> مفتاح </option>
				<option value="LIGHT"> ضوء </option>
				<option value="VAN"> مروحه او شفاط </option>
				</select><br>
				<input type="submit" value="تغيير">
				<br><br>
			</form>
		{%endfor%}

<br>
<br>
	    <form method="POST" action="/devices">
			<div class="dropdown">
			  <span>اسماء المفاتيح</span>
			  <div class="dropdown-content">
			    <table>
				<tr>
				{% for i in range(result[1].split("v")[1]|int)%}
				         <th>{{i+1}}</th>
				{%endfor%}
				</tr>
				<input type="text" name="list-SWITCH" value="{{result[0]+"_SWITCH"}}" hidden>
				{% if SWITCHS[result[0]+"_SWITCH"] != [] %}
					{%for i in SWITCHS[result[0]+"_SWITCH"]%}
					    <tr>
					    {% for s in i %}
				    		<td> <input type="text" name="{{s}}" value="{{i[s]}}"></td>
					    {%endfor%}
					    </tr>
					{%endfor%}
				{%endif%}
			    </table>
			  </div>
			</div>
		<input type="submit" value="submit">
	    </form>
<br>
	    <form method="POST" action="/devices">

		<button type="submit" name="add-SWITCH" value="{{result[0]+"_SWITCH"}}"> اضافة غرفة اخرى </button>
	    </form>
<br>


	{%endif%}
	
	<form method="POST" action="/devices">
		{%if 'Main' not in result[1] %}
			{{ result[3] }} <br>
			<button type="submit" name="del-device" value="{{result[0]}} {{result[1]}}"> حذف الجهاز </button>
		{%else%}
			الجهاز الرئيسي
		{%endif%}
	    </form>
	<br>
	<br>
	------------------------------------------------------------------------------------------------
	{%endfor%}



<br>
</details>
<br>
<br>
<br>
<details>
    <summary> تعديل التوقيت </summary>
<form method="POST" action="/date">
سيتم تعديل التوقيت في الاجهزه المتصله في النظام فقط
	<br>
التوقيت <select name="timing">
		<option value="{{ timing }}"> {% if timing == "1"%} صيفي {%else%} شتوي {%endif%} </option>
		<option value="1"> صيفي </option>
		<option value="0"> شتوي </option>
	</select><br>
<input type="submit" value="submit">
</form>
</details>

<br>
<br>
<br>
<details>
    <summary> مكان الاقامة </summary>
<form method="POST" action="/place">
	قم باختيار المدينة
<br>
<select name="city">
	<option value="mycity">{{countreis["mycity"]}}</option>
	{% for country in countreis %}
		{% if country != "mycity" %}
		<optgroup label="{{country}}">

			{% for city in countreis[country] %}
				<option value="{{city+','+country}}">{{city}}</option>
			{%endfor%}
		</optgroup>
		{%endif%}
	{%endfor%}
</select>
<input type="submit" value="submit">
</form>
<img src="/home/<USER>/MyProject/devices/templates/homebridge.jpg">
</details>
<br>
<br>
<br>




    {% for room in rooms %}
	{{room}}
	<br>
	<form action="/rooms" method="POST">
	   <select data-placeholder="SELECT {{room}}" multiple class="chosen-select" name="list {{room}}">
    		<option value="x" selected hidden></option>
		{% if rooms[room] != None and rooms[room] != "None" %}	
			{% for iroom in rooms[room] %}
				<option value="{{iroom}}" selected >{{iroom}}</option>
			{%endfor%}
		{%endif%}
	   </select>
        <br>
	<input type="submit">
	</form>
<br>
	<form action="/rooms" method="POST">
ادخال اسم غرفة

		<input type="text" name="add {{room}}">
		<input type="submit" value="submit">
	</form>
	<br>
	<br>
-------
<br>
    {%endfor%}
<br>
    <form action="/rooms" method="POST">
	<button type="submit" name="ADD" value="ADD"> اضافة غرفة اخرى </button>
<br><br>
    </form>
    <form action="/rooms" method="POST">
حذف غرفة
	<select name="del">
		{% for room in rooms %}
 			<option value="{{room}}"> {{room}} </option>
		{%endfor%}
	</select>
	<input type="submit" value="submit">

    </form>

    <script type="text/javascript">
        $(".chosen-select").chosen({
  no_results_text: "Oops, nothing found!"
})
    </script>





</body>
</html>