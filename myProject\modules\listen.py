#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import subprocess
import signal
import speech_recognition as sr
import threading
import time
from pydub import AudioSegment
import sys
import json
import paho.mqtt.publish as publish
import logging

# إضافة المسارات للمكتبات المطلوبة
sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/nlt')
sys.path.append('/home/<USER>/myProject/modules')
sys.path.append('/home/<USER>/myProject/processing')

# استيراد المكتبات المخصصة
import process as process
import nlt as nlt
import static as st
import tts as tts
# from process_voice import process_voice_command

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("voice_assistant.log"),
        logging.StreamHandler()
    ]
)

class VoiceAssistant:
    def __init__(self,system_self):
        # إعداد متغيرات الصوت والتعرف على الكلام
        self.system_self = system_self
        self.recognizer = sr.Recognizer()
        self.sphinx_recognizer = sr.Recognizer()
        self.google_recognizer = sr.Recognizer()
    
        # إعداد معلمات التعرف على الكلام
        self.sphinx_recognizer.dynamic_energy_threshold = True
        self.sphinx_recognizer.dynamic_energy_adjustment_ratio = 3.5
        
        # إعداد متغيرات التسجيل
        self.audio_file = "recording.wav"
        self.recording_process = None
        self.is_listening = False
        self.last_activity_time = 0
        self.max_recording_time = 70 * 60  # 20 دقيقة بالثواني
        
        # الكلمات المفتاحية
        self.wake_word = "يا زين"
        self.keywords = [
            (self.wake_word, 1),
            ('زين', 1),
            ('مرحبا زين', 1)
        ]

    def start_recording(self):
        """بدء تسجيل الصوت في ملف"""
        try:
            if self.recording_process:
                self.stop_recording()
            
            self.recording_process = subprocess.Popen(
                f"arecord --format=S16_LE --rate=44100 -D hw:3,0 {self.audio_file}", 
                stdout=subprocess.PIPE, 
                shell=True, 
                preexec_fn=os.setsid
            )
            self.last_activity_time = time.time()
            time.sleep(2.5)  # انتظار لبدء التسجيل
            logging.info("جاهز للاستماع")
        except Exception as e:
            logging.error(f"خطأ في بدء التسجيل: {e}")
            if self.recording_process:
                os.killpg(os.getpgid(self.recording_process.pid), signal.SIGTERM)

    def stop_recording(self):
        """إيقاف تسجيل الصوت"""
        if self.recording_process:
            logging.info("إيقاف التسجيل")
            os.killpg(os.getpgid(self.recording_process.pid), signal.SIGTERM)
            self.recording_process = None
            time.sleep(1)

    def restart_recording(self):
        """إعادة تشغيل التسجيل"""
        self.stop_recording()
        self.start_recording()
        logging.info("تم إعادة تشغيل التسجيل")

    def check_keyword(self):
        """التحقق من وجود الكلمة المفتاحية في آخر ثانيتين من التسجيل"""
        try:
            # تحميل الملف الصوتي
            sound = AudioSegment.from_wav(self.audio_file)
            total_duration = len(sound) / 1000  # التحويل إلى ثواني
            
            # إذا كان طول التسجيل أقل من ثانيتين، لا حاجة للتحقق
            if total_duration < 2:
                return False
            
            # استخراج آخر ثانيتين من التسجيل
            with sr.AudioFile(self.audio_file) as source:
                # ضبط مستوى الضوضاء
                self.sphinx_recognizer.adjust_for_ambient_noise(source, duration=0.5)
                self.sphinx_recognizer.energy_threshold = 1500
                
                # تسجيل آخر ثانيتين
                offset = total_duration - 2
                audio_data = self.sphinx_recognizer.record(source, offset=offset)
                
            # استخدام sphinx للتعرف على الكلمة المفتاحية
            try:
                text = self.sphinx_recognizer.recognize_sphinx(audio_data, language='ar')
                
                # التحقق من وجود الكلمة المفتاحية
                if (self.wake_word in text or 
                    'ي زين' in text or 
                    'هي زين' in text or 
                    'مرحبا زين' in text):
                    logging.info("تم اكتشاف الكلمة المفتاحية!")
                    logging.info(f"تم سماع: {text}")
                    return True
                    
            except sr.UnknownValueError:
                logging.debug("لم يتم التعرف على أي كلام")
            except Exception as e:
                logging.error(f"خطأ في التعرف على الكلام: {e}")
                
            return False
                
        except Exception as e:
            logging.error(f"خطأ في التحقق من الكلمة المفتاحية: {e}")
            return False

    def listen_for_command(self):
        """الاستماع للأوامر بعد سماع الكلمة المفتاحية"""
        try:
            self.system_self.tts.say('beep_1')
            logging.info("بدء الاستماع للأمر")
            
            # التحقق من توفر الإنترنت
            if not self.is_internet_available():
                self.system_self.tts.say('للأسَف, لا يَتَوَفَر لَدَي إنتَرنِت , لَقَد تَعَذَر العُثور على معلومات')
                logging.warning("لا يوجد اتصال بالإنترنت")
                return
            
            # تحليل الصوت لتحديد متى ينتهي الكلام
            sound = AudioSegment.from_wav(self.audio_file)
            total_duration = len(sound) / 1000
            
            # تحديد متوسط مستوى الصوت في التسجيل للمقارنة
            base_volume = self.calculate_average_volume(sound)
            current_end = total_duration
            silence_counter = 0
            max_silence = 2  # عدد مرات التحقق قبل اعتبار أن الكلام قد انتهى
            
            # الاستمرار في الاستماع حتى اكتشاف الصمت
            while silence_counter < max_silence:
                # اقرأ الملف الصوتي المحدث
                updated_sound = AudioSegment.from_wav(self.audio_file)
                new_duration = len(updated_sound) / 1000
                
                # إذا كان هناك محتوى صوتي جديد
                if new_duration > current_end + 0.5:  # نصف ثانية من المحتوى الجديد
                    # تحقق من مستوى الصوت في المحتوى الجديد
                    new_segment = updated_sound[int((current_end - 0.2) * 1000):]
                    current_volume = new_segment.max
                    
                    logging.debug(f"الحجم الحالي: {current_volume}, الحجم الأساسي: {base_volume}")
                    
                    # إذا كان مستوى الصوت منخفضًا بما يكفي، زد عداد الصمت
                    if current_volume - base_volume < 100:
                        silence_counter += 1
                    else:
                        silence_counter = 0  # إعادة ضبط عداد الصمت لأن هناك كلامًا
                        
                    current_end = new_duration
                
                time.sleep(0.2)
            
            self.system_self.tts.say('beep_2')
            logging.info("انتهى الاستماع، بدء تحويل الكلام إلى نص")
            
            # استخدام Google للتعرف على الكلام بعد الكلمة المفتاحية
            with sr.AudioFile(self.audio_file) as source:
                # التعرف على الكلام بدءًا من بعد الكلمة المفتاحية
                # نفترض أن الكلمة المفتاحية موجودة في آخر ثانيتين، لذا نبدأ من قبل تلك النقطة بقليل
                command_start = total_duration - 3  # ثانية قبل الكلمة المفتاحية لضمان التقاط الكلام بالكامل
                if command_start < 0:
                    command_start = 0
                    
                command_audio = self.google_recognizer.record(source, offset=command_start)
                
            try:
                command_text = self.google_recognizer.recognize_google(command_audio, language='ar-AR')
                logging.info(f"تم التعرف على الأمر: {command_text}")
                self.process_command(command_text)
                
            except sr.UnknownValueError:
                logging.warning("لم يتم التعرف على الأمر")
            except Exception as e:
                logging.error(f"خطأ في التعرف على الأمر: {e}")
                
        except Exception as e:
            logging.error(f"خطأ في الاستماع للأمر: {e}")

    def calculate_average_volume(self, sound):
        """حساب متوسط مستوى الصوت في التسجيل"""
        if len(sound) < 5000:  # أقل من 5 ثوانٍ
            return sound.max - 100  # قيمة تقريبية
            
        # حساب متوسط الحجم في آخر 5 ثوانٍ
        last_5sec = sound[-5000:]
        chunks = [last_5sec[i:i+1000] for i in range(0, len(last_5sec), 1000)]
        return sum(chunk.max for chunk in chunks) / len(chunks)

    def is_internet_available(self):
        """التحقق من توفر اتصال الإنترنت"""
        try:
            check_file = open(st.pathFiles + 'check.txt', 'r')
            status = check_file.readline()
            check_file.close()
            return status == '1'
        except:
            return False

    def process_command(self, command_text):
        """معالجة الأمر المنطوق"""
        try:
            # تنظيف النص وإرساله للمعالجة
            
            logging.info(f"معالجة الأمر: {command_text}")
            
            # استدعاء دالة معالجة الأوامر الصوتية
            process_thread = threading.Thread(
                target= process.process_voice_command,
                args=(command_text,self.system_self)
            )
            process_thread.daemon = True
            process_thread.start()
            
        except Exception as e:
            logging.error(f"خطأ في معالجة الأمر: {e}")

    def run(self):
        """تشغيل المساعد الصوتي"""
        try:
            self.system_self.tts.say('listening')
            self.start_recording()
            
            while True:
                # التحقق من مدة التسجيل وإعادة تشغيله إذا تجاوزت المدة المحددة
                current_time = time.time()
                if current_time - self.last_activity_time > self.max_recording_time:
                    logging.info("تجديد التسجيل (تجاوز 20 دقيقة)")
                    self.restart_recording()
                
                # التحقق من وجود الكلمة المفتاحية
                if self.check_keyword():
                    # إذا تم اكتشاف الكلمة المفتاحية، استمع للأمر
                    self.listen_for_command()
                    
                # انتظار قصير قبل التحقق مرة أخرى
                time.sleep(0.5)
                
        except KeyboardInterrupt:
            logging.info("تم إيقاف المساعد الصوتي بواسطة المستخدم")
        except Exception as e:
            logging.error(f"خطأ في تشغيل المساعد الصوتي: {e}")
        finally:
            self.stop_recording()

