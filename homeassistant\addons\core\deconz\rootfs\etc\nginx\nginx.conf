worker_processes  1;
pid /var/run/nginx.pid;
error_log /dev/stdout info;
daemon off;

events {
    worker_connections 1024;
}

http {
    include             mime.types;
    default_type        application/octet-stream;
    sendfile            on;
    keepalive_timeout   65;
    proxy_read_timeout  1200;
    gzip                on;
    gzip_disable        "msie6";

    map $http_upgrade $connection_upgrade {
        default upgrade;
        ''      close;
    }

    # Ingress
    server {
        listen 8099 default_server;

        allow   ***********;
        deny    all;

        server_name _;
        access_log /dev/stdout combined;

        client_max_body_size 4G;
        keepalive_timeout 5;

        root /dev/null;

        location /websocket {
            proxy_pass http://127.0.0.1:8081;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
        }

        location /novnc {
            proxy_pass http://127.0.0.1:5901/;
            proxy_redirect default;

            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
        }

        location =/ingress.html {
            root /usr/share/www/;
        }

        location / {
            proxy_pass http://127.0.0.1:40850;
            proxy_redirect default;

            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;

            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Host $http_host;
            proxy_set_header X-NginX-Proxy true;

            sub_filter_once off;
            sub_filter_types *;
            
            # Tell Phoscon app where the gateway is and stop scan of other hosts.
            sub_filter ',-1!==document.location.hash.indexOf("nocache")' ',app.probeWorker.postMessage({add:[document.location.host+\'%%ingress_entry%%\']}),app.probeWorker.postMessage({scan:!0}),true';
            
            # Disable Discovery from Dresden
            sub_filter 'queryDresdenDiscovery(url){' 'queryDresdenDiscovery(url){ return Promise.resolve([]);';

            # Block attempt to probe subnets
            sub_filter '} else if (state === STATE_CHECKSUBNETS) {\n        if' '} else if (state === STATE_CHECKSUBNETS) { if (true) {needCheckSubnets = false;state = STATE_IDLE;queueStateAction(E_TIMEOUT, 1000);} else if';

            # Block attempt at loading gateway api by adding port
            sub_filter 'if (ip.indexOf(\':\') === -1) // no port was specified' 'if (false)';

            # Correctly handle redirect to login
            sub_filter 'window.location.href = \'/pwa/login.html\';' 'window.location.href = \'%%ingress_entry%%/pwa/login.html\';';

            # Patch Websocket support
            sub_filter 'WebSocket(prot+host+":"+port)' 'WebSocket(prot+window.location.host+\'%%ingress_entry%%/websocket\')';

            # The Manifest is useless and causes unneeded failing requests @ the Home Assistant server
            sub_filter '<link rel="manifest" href="/pwa/manifest.json">' '';
        }
    }
}
