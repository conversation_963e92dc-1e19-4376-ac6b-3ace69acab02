---
configuration:
  log_level:
    name: Matter Server Log Level
    description: Logging level of the Matter Server component.
  log_level_sdk:
    name: Matter SDK Log Level
    description: Logging level of the Matter SDK.
  beta:
    name: Use the latest beta version
    description: >-
      Installs the latest beta of the Python Matter Server. It is recommended
      to create a backup before starting the add-on with this flag enabled!
  enable_test_net_dcl:
    name: Enable test-net DCL usage.
    description: >-
      Enable PAA root certificates and other device information from test-net
      DCL. This is meant for development and testing purposes.
  bluetooth_adapter_id:
    name: Bluetooth Adapter ID
    description: >-
      BlueZ Bluetooth Adapter Controller ID used by the Matter Server. This is
      required if local commissioning via Bluetooth is used. Note: Using the
      Home Assistant Companion app commissioning method is recommended as it is
      better tested and allows to commission directly in proximity of the device
      itself.
  matter_server_args:
    name: Extra Matter Server arguments
    description: >-
      This allows to pass additional command line arguments to the Python Matter
      Server. Use `--help` to get a list of possible arguments. Note that
      arguments are also added by the startup script controlled by other add-on
      options.
  matter_server_version:
    name: Matter Server version
    description: >-
      Install custom Matter Server version. WARNING: An older version might have
      an incompatible storage format! Use this feature with caution! Make sure
      you have a recent backup of the add-on!
  matter_sdk_wheels_version:
    name: Matter SDK wheels version
    description: >-
      Install custom Matter SDK wheels version. NOTE: The API might not be
      compatible with the Python Matter server.
network:
  5580/tcp: Matter Server WebSocket server port.
