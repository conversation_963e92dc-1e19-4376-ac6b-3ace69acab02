#!/usr/bin/env python3
"""
محرك الأتمتة الذكي
يستخدم بيانات الطقس والكيانات لتنفيذ أتمتة ذكية
"""

import sys
import json
import time
import re
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# إضافة المسارات المطلوبة
sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/mqtt')
sys.path.append('/home/<USER>')

try:
    from ha import HomeAssistantIntegration
    import static as st
    from database_helper import DatabaseHelper
    import paho.mqtt.client as mqtt
    import mysql.connector
    # استيراد مدير الرسائل
    sys.path.append('/home/<USER>/myProject/utils')
    from logging_config import should_show_disconnect
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

class AutomationEngine:
    """محرك الأتمتة الذكي"""
    
    def __init__(self, shared_mqtt_client=None):
        self.ha_integration = HomeAssistantIntegration()
        self.db_helper = DatabaseHelper()
        self.mqtt_client = shared_mqtt_client  # استخدام العميل المشترك
        self.running = False

        # قواعد الأتمتة المحملة
        self.automation_rules = []
        self.active_automations = {}

        # إعدادات MQTT
        self.mqtt_host = st.ip if hasattr(st, 'ip') else 'zain.local'
        self.mqtt_port = 1883

        # إعداد MQTT فقط إذا لم يتم توفير عميل مشترك
        if not self.mqtt_client:
            self.setup_mqtt()
        else:
            self.setup_shared_mqtt()

        self.load_automation_rules()
        self.create_automation_tables()
    
    def setup_mqtt(self):
        """إعداد اتصال MQTT"""
        try:
            self.mqtt_client = mqtt.Client(client_id="automation_engine")
            self.mqtt_client.on_connect = self.on_mqtt_connect
            self.mqtt_client.on_disconnect = self.on_disconnect
            self.mqtt_client.on_message = self.on_mqtt_message
            
            self.mqtt_client.connect(self.mqtt_host, self.mqtt_port, 60)
            self.mqtt_client.loop_start()
            
        except Exception as e:
            print(f"خطأ في إعداد MQTT: {e}")

    def setup_shared_mqtt(self):
        """إعداد العميل المشترك"""
        if self.mqtt_client:
            # إضافة معالجات الرسائل للعميل المشترك
            self.mqtt_client.message_callback_add("automation/+", self.on_mqtt_message)
            self.mqtt_client.message_callback_add("automation/+/+", self.on_mqtt_message)
            self.mqtt_client.message_callback_add("homeassistant/+/+", self.on_mqtt_message)

            # الاشتراك في المواضيع المطلوبة

            self.mqtt_client.subscribe("homeassistant/+/+")

            print("🤖 تم إعداد محرك الأتمتة مع العميل المشترك")

    def on_mqtt_connect(self, client, userdata, flags, rc):
        """عند الاتصال بـ MQTT - للعميل المنفصل فقط"""
        if self.mqtt_client and self.mqtt_client != client:
            return  # تجاهل إذا كان هذا عميل مشترك

        if rc == 0:
            # تسجيل الاتصال مرة واحدة فقط عند بدء التشغيل
            if not hasattr(self, '_automation_mqtt_logged'):
                print("✅ محرك الأتمتة متصل بـ MQTT")
                print("🤖 تم الاشتراك في مواضيع الأتمتة المتقدمة")
                self._automation_mqtt_logged = True

            # الاشتراك في المواضيع المطلوبة (للعميل المنفصل فقط)
            if not hasattr(self, 'mqtt_client') or self.mqtt_client is None:
                client.subscribe("homeassistant/+/+")  # جميع كيانات Home Assistant

            self._mqtt_connected = True
        else:
            print(f"❌ فشل اتصال محرك الأتمتة بـ MQTT: {rc}")
            self._mqtt_connected = False

    def on_disconnect(self, client, userdata, rc):
        """عند قطع الاتصال مع MQTT"""
        self._mqtt_connected = False
        if rc != 0:
            # تقليل رسائل قطع الاتصال المتكررة بشكل جذري
            try:
                if should_show_disconnect("automation_mqtt_disconnect"):
                    print(f"⚠️ مشكلة في اتصال MQTT - محرك الأتمتة (كود: {rc})")
                    print("🔄 سيتم إعادة المحاولة تلقائياً...")
            except:
                pass  # تجاهل أخطاء الاستيراد
        # إزالة رسالة القطع الطبيعي لتقليل الضوضاء

    def on_mqtt_message(self, client, userdata, msg, properties=None):
        """معالجة رسائل MQTT"""
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')

            # تسجيل الرسائل المهمة فقط
            if topic.startswith('automation/'):
                print(f"📨 رسالة MQTT: {topic}")

            # معالجة طلبات الأتمتة المتقدمة
            if topic.startswith("homeassistant/"):
                self.process_entity_update(topic, payload)

        except Exception as e:
            print(f"❌ خطأ في معالجة رسالة MQTT: {e}")

    
    def create_automation_tables(self):
        """إنشاء جداول الأتمتة"""
        connection = None
        cursor = None
        
        try:
            connection = self.db_helper.get_connection()
            cursor = connection.cursor()
            
            # جدول قواعد الأتمتة المتقدم
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS advanced_automation_rules (
                id VARCHAR(255) PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                triggers JSON NOT NULL,
                conditions JSON NOT NULL,
                actions JSON NOT NULL,
                logic_operator ENUM('AND', 'OR') DEFAULT 'AND',
                enabled BOOLEAN DEFAULT TRUE,
                priority ENUM('low', 'normal', 'high', 'urgent', 'critical') DEFAULT 'normal',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                last_triggered TIMESTAMP NULL,
                trigger_count INT DEFAULT 0,
                INDEX idx_enabled (enabled),
                INDEX idx_name (name),
                INDEX idx_priority (priority)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)


            
            # جدول سجل تنفيذ الأتمتة
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS automation_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                rule_id VARCHAR(255),
                triggered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                conditions_met JSON,
                actions_executed JSON,
                success BOOLEAN DEFAULT TRUE,
                error_message TEXT,
                execution_time_ms INT,
                FOREIGN KEY (rule_id) REFERENCES advanced_automation_rules(id) ON DELETE CASCADE,
                INDEX idx_triggered_at (triggered_at),
                INDEX idx_rule_id (rule_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)
            
            connection.commit()
            print("✅ تم إنشاء جداول الأتمتة بنجاح")

            # لا نحتاج لإدراج أمثلة - سيتم إنشاء القواعد من التطبيق
            print("✅ جداول الأتمتة المتقدمة جاهزة للاستخدام")

        except mysql.connector.Error as e:
            print(f"❌ خطأ في إنشاء جداول الأتمتة: {e}")
            if connection:
                connection.rollback()
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    


    def load_automation_rules(self):
        """تحميل قواعد الأتمتة من قاعدة البيانات"""
        try:
            # استخدام الجدول الجديد فقط
            self.automation_rules = self.get_all_automation_rules()
            print(f"✅ تم تحميل {len(self.automation_rules)} قاعدة أتمتة متقدمة")

        except Exception as e:
            print(f"❌ خطأ في تحميل قواعد الأتمتة: {e}")

    def process_entity_update(self, topic, payload):
        """معالجة تحديث الكيانات وفحص قواعد الأتمتة"""
        try:
            # استخراج معلومات الكيان من الموضوع
            topic_parts = topic.split('/')
            if len(topic_parts) >= 3:
                entity_type = topic_parts[1]
                entity_id = topic_parts[2]
                
                # فحص جميع قواعد الأتمتة
                for rule in self.automation_rules:
                    if self.check_rule_conditions_with_entity(rule, entity_type, entity_id, payload):
                        self.execute_rule_actions(rule)
                        
        except Exception as e:
            print(f"خطأ في معالجة تحديث الكيان: {e}")
    
    def check_rule_conditions_with_entity(self, rule, entity_type, entity_id, payload):
        """فحص شروط قاعدة الأتمتة المتقدمة"""
        try:
            # فحص المحفزات أولاً
            if not self.check_rule_triggers(rule, entity_type, entity_id, payload):
                return False

            conditions = rule['conditions']
            logic_operator = rule.get('logic_operator', 'AND')

            if not conditions:
                return True

            condition_results = []

            # فحص شروط الطقس
            if 'weather' in conditions:
                weather_data = self.ha_integration.get_weather_data()
                condition_results.append(self.check_weather_conditions(conditions['weather'], weather_data))

            # فحص شروط الشمس
            if 'sun' in conditions:
                condition_results.append(self.check_sun_conditions(conditions['sun']))

            # فحص شروط الأشخاص
            if 'person' in conditions:
                condition_results.append(self.check_person_conditions(conditions['person']))

            # فحص شروط الوقت
            if 'time' in conditions:
                condition_results.append(self.check_time_conditions(conditions['time']))

            # فحص شروط الكيانات
            if 'entities' in conditions:
                condition_results.append(self.check_entity_conditions(conditions['entities'], entity_type, entity_id, payload))

            # فحص شروط الأجهزة
            if 'devices' in conditions:
                condition_results.append(self.check_device_conditions(conditions['devices']))

            # تطبيق المنطق (AND/OR)
            if logic_operator == 'AND':
                return all(condition_results)
            else:  # OR
                return any(condition_results)

        except Exception as e:
            print(f"خطأ في فحص شروط القاعدة: {e}")
            return False

    def check_rule_triggers(self, rule, entity_type, entity_id, payload):
        """فحص محفزات قاعدة الأتمتة"""
        try:
            triggers = rule.get('triggers', [])
            if not triggers:
                return True

            for trigger in triggers:
                trigger_type = trigger.get('type')

                if trigger_type == 'entity_state':
                    # محفز تغيير حالة الكيان
                    if (trigger.get('entity_type') == entity_type and
                        trigger.get('entity_id') == entity_id):

                        expected_state = trigger.get('state')
                        if expected_state and payload == expected_state:
                            return True
                        elif not expected_state:  # أي تغيير في الحالة
                            return True

                elif trigger_type == 'time':
                    # محفز الوقت
                    return self.check_time_trigger(trigger)

                elif trigger_type == 'sun':
                    # محفز الشمس
                    return self.check_sun_trigger(trigger, entity_type, entity_id)

                elif trigger_type == 'weather':
                    # محفز الطقس
                    return self.check_weather_trigger(trigger, entity_type, entity_id)

            return False

        except Exception as e:
            print(f"خطأ في فحص محفزات القاعدة: {e}")
            return False

    def check_sun_conditions(self, sun_conditions):
        """فحص شروط الشمس"""
        try:
            # جلب بيانات الشمس من Home Assistant
            sun_data = self.ha_integration.get_entity_state('sun.sun')
            if not sun_data:
                return False

            for condition, value in sun_conditions.items():
                if condition == 'state':
                    # above_horizon أو below_horizon
                    if sun_data.get('state') != value:
                        return False
                elif condition == 'elevation':
                    # زاوية الشمس
                    elevation = sun_data.get('attributes', {}).get('elevation', 0)
                    if not self.compare_numeric_value(elevation, value):
                        return False

            return True

        except Exception as e:
            print(f"خطأ في فحص شروط الشمس: {e}")
            return False

    def check_person_conditions(self, person_conditions):
        """فحص شروط الأشخاص"""
        try:
            for person_id, conditions in person_conditions.items():
                person_data = self.ha_integration.get_entity_state(f'person.{person_id}')
                if not person_data:
                    continue

                for condition, value in conditions.items():
                    if condition == 'state':
                        # home, not_home, unknown
                        if person_data.get('state') != value:
                            return False
                    elif condition == 'zone':
                        # التحقق من المنطقة
                        current_zone = person_data.get('attributes', {}).get('zone')
                        if current_zone != value:
                            return False

            return True

        except Exception as e:
            print(f"خطأ في فحص شروط الأشخاص: {e}")
            return False

    def check_time_trigger(self, trigger):
        """فحص محفز الوقت"""
        try:
            from datetime import datetime
            now = datetime.now()

            trigger_time = trigger.get('time')
            if trigger_time:
                # تحويل الوقت المحدد إلى datetime
                hour, minute = map(int, trigger_time.split(':'))
                if now.hour == hour and now.minute == minute:
                    return True

            return False

        except Exception as e:
            print(f"خطأ في فحص محفز الوقت: {e}")
            return False

    def check_sun_trigger(self, trigger, entity_type, entity_id):
        """فحص محفز الشمس"""
        try:
            if entity_type == 'sun' and entity_id == 'sun':
                event_type = trigger.get('event')

                # جلب بيانات الشمس
                sun_data = self.ha_integration.get_entity_state('sun.sun')
                if not sun_data:
                    return False

                current_state = sun_data.get('state')

                if event_type == 'sunrise' and current_state == 'above_horizon':
                    return True
                elif event_type == 'sunset' and current_state == 'below_horizon':
                    return True

            return False

        except Exception as e:
            print(f"خطأ في فحص محفز الشمس: {e}")
            return False

    def check_weather_trigger(self, trigger, entity_type, entity_id):
        """فحص محفز الطقس"""
        try:
            if entity_type == 'weather':
                weather_data = self.ha_integration.get_weather_data()
                if not weather_data:
                    return False

                condition_type = trigger.get('condition')
                threshold = trigger.get('threshold')

                if condition_type == 'temperature_above':
                    return weather_data.get('temperature', 0) > threshold
                elif condition_type == 'temperature_below':
                    return weather_data.get('temperature', 0) < threshold
                elif condition_type == 'humidity_above':
                    return weather_data.get('humidity', 0) > threshold
                elif condition_type == 'rain_probability':
                    # فحص احتمالية المطر من التوقعات
                    forecast = weather_data.get('forecast_data', {})
                    if isinstance(forecast, dict):
                        rain_prob = forecast.get('precipitation_probability', 0)
                        return rain_prob > threshold

            return False

        except Exception as e:
            print(f"خطأ في فحص محفز الطقس: {e}")
            return False

    def compare_numeric_value(self, actual_value, condition):
        """مقارنة القيم الرقمية مع الشروط"""
        try:
            if isinstance(condition, dict):
                operator = condition.get('operator', '==')
                value = condition.get('value', 0)

                if operator == '>':
                    return actual_value > value
                elif operator == '<':
                    return actual_value < value
                elif operator == '>=':
                    return actual_value >= value
                elif operator == '<=':
                    return actual_value <= value
                elif operator == '==':
                    return actual_value == value
                elif operator == '!=':
                    return actual_value != value
            else:
                # مقارنة مباشرة
                return actual_value == condition

            return False

        except Exception as e:
            print(f"خطأ في مقارنة القيم: {e}")
            return False
    
    def check_weather_conditions(self, weather_conditions, weather_data):
        """فحص شروط الطقس"""
        if not weather_data:
            return False
        
        for condition, value in weather_conditions.items():
            if condition == 'temperature_min' and weather_data.get('temperature', 0) < value:
                return False
            elif condition == 'temperature_max' and weather_data.get('temperature', 100) > value:
                return False
            elif condition == 'humidity_min' and weather_data.get('humidity', 0) < value:
                return False
            elif condition == 'humidity_max' and weather_data.get('humidity', 100) > value:
                return False
            elif condition == 'condition' and weather_data.get('weather_condition') != value:
                return False
        
        return True
    
    def check_time_conditions(self, time_conditions):
        """فحص شروط الوقت"""
        now = datetime.now()
        
        for condition, value in time_conditions.items():
            if condition == 'hour_min' and now.hour < value:
                return False
            elif condition == 'hour_max' and now.hour > value:
                return False
            elif condition == 'weekday' and now.weekday() not in value:
                return False
            elif condition == 'date_range':
                start_date = datetime.strptime(value['start'], '%Y-%m-%d').date()
                end_date = datetime.strptime(value['end'], '%Y-%m-%d').date()
                if not (start_date <= now.date() <= end_date):
                    return False
        
        return True
    
    def check_entity_conditions(self, entity_conditions, entity_type, entity_id, payload):
        """فحص شروط الكيانات"""
        for condition in entity_conditions:
            if condition.get('type') == entity_type and condition.get('id') == entity_id:
                expected_state = condition.get('state')
                if expected_state and payload != expected_state:
                    return False
        
        return True
    
    def check_device_conditions(self, device_conditions):
        """فحص شروط الأجهزة"""
        for condition in device_conditions:
            device_id = condition.get('device_id')
            expected_state = condition.get('state')
            
            # جلب حالة الجهاز من قاعدة البيانات
            device_info = self.db_helper.get_device_info(device_id)
            if device_info and device_info[4] != expected_state:  # العمود الخامس هو الحالة
                return False
        
        return True
    
    def execute_rule_actions(self, rule):
        """تنفيذ إجراءات قاعدة الأتمتة"""
        start_time = time.time()
        executed_actions = []
        success = True
        error_message = None
        
        try:
            actions = rule['actions']
            
            for action in actions:
                action_type = action.get('type')
                
                if action_type == 'device_control':
                    self.execute_device_control(action)
                elif action_type == 'notification':
                    self.execute_notification(action)
                elif action_type == 'scene':
                    self.execute_scene(action)
                elif action_type == 'delay':
                    time.sleep(action.get('seconds', 1))
                
                executed_actions.append(action)
            
            # تحديث إحصائيات القاعدة
            self.update_rule_stats(rule['id'])
            
        except Exception as e:
            success = False
            error_message = str(e)
            print(f"❌ خطأ في تنفيذ إجراءات القاعدة {rule['name']}: {e}")
        
        # تسجيل التنفيذ
        execution_time = int((time.time() - start_time) * 1000)
        self.log_automation_execution(
            rule['id'], 
            rule['conditions'], 
            executed_actions, 
            success, 
            error_message, 
            execution_time
        )
        
        if success:
            print(f"✅ تم تنفيذ قاعدة الأتمتة: {rule['name']}")
    
    def execute_device_control(self, action):
        """تنفيذ التحكم في الأجهزة"""
        device_id = action.get('device_id')
        command = action.get('command')
        room = action.get('room', '*')
        device_type = action.get('device_type', '*')
        
        # إرسال أمر التحكم عبر MQTT
        control_topic = f"ROOMS/{room}/{device_type}/{device_id}"
        self.mqtt_client.publish(control_topic, command)
    
    def execute_notification(self, action):
        """تنفيذ إرسال الإشعارات"""
        rule_id = action.get('rule_id')

        if rule_id:
            notification_data = {
                'rule_id': rule_id
            }

            self.mqtt_client.publish("notifications/send", json.dumps(notification_data))
            print(f"🔔 تم إرسال طلب إشعار لقاعدة: {rule_id}")
        else:
            print("❌ لم يتم توفير rule_id في إجراء الإشعار")
    
    def execute_scene(self, action):
        """تنفيذ مشهد"""
        scene_name = action.get('scene_name')
        scene_actions = action.get('actions', [])
        
        for scene_action in scene_actions:
            if scene_action.get('type') == 'device_control':
                self.execute_device_control(scene_action)
    
    def update_rule_stats(self, rule_id):
        """تحديث إحصائيات القاعدة"""
        query = """
        UPDATE automation_rules 
        SET last_triggered = NOW(), trigger_count = trigger_count + 1 
        WHERE id = %s
        """
        self.db_helper.execute_query(query, (rule_id,))
    
    def log_automation_execution(self, rule_id, conditions, actions, success, error_message, execution_time):
        """تسجيل تنفيذ الأتمتة"""
        query = """
        INSERT INTO automation_log 
        (rule_id, conditions_met, actions_executed, success, error_message, execution_time_ms)
        VALUES (%s, %s, %s, %s, %s, %s)
        """
        
        params = (
            rule_id,
            json.dumps(conditions),
            json.dumps(actions),
            success,
            error_message,
            execution_time
        )
        
        self.db_helper.execute_query(query, params)
    
    def start_engine(self):
        """بدء محرك الأتمتة"""
        print("🚀 بدء محرك الأتمتة الذكي...")
        self.running = True

        # بدء التحديث الدوري لقواعد الأتمتة
        def periodic_check():
            while self.running:
                try:
                    
                    now = datetime.now()
    #                     print(now.second)
                    if now.second > 40:
                        time.sleep(62 - now.second)
                        self.load_automation_rules()
                        # فحص المحفزات الزمنية
                        self.check_time_triggers()
                    else:

                        time.sleep(40 - now.second)  # فحص كل دقيقة
                except Exception as e:
                    print(f"خطأ في الفحص الدوري: {e}")
                    time.sleep(60)

        check_thread = threading.Thread(target=periodic_check, daemon=True)
        check_thread.start()

        # بدء مراقبة الطقس والكيانات
        def weather_monitor():
            while self.running:
                try:
                    self.check_weather_triggers()
                    self.check_sun_triggers()
                    time.sleep(300)  # فحص كل 5 دقائق
                except Exception as e:
                    print(f"خطأ في مراقبة الطقس: {e}")
                    time.sleep(60)

        weather_thread = threading.Thread(target=weather_monitor, daemon=True)
        weather_thread.start()

        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⏹️ إيقاف محرك الأتمتة...")
            self.stop_engine()
    
    def stop_engine(self):
        """إيقاف محرك الأتمتة"""
        self.running = False

        if self.mqtt_client:
            self.mqtt_client.loop_stop()
            self.mqtt_client.disconnect()

        print("✅ تم إيقاف محرك الأتمتة بنجاح")

    def check_time_triggers(self):
        """فحص المحفزات الزمنية"""
        try:
            current_time = datetime.now()
            current_hour = current_time.hour
            current_minute = current_time.minute
            current_weekday = current_time.strftime('%A').lower()

            for rule in self.automation_rules:
                if not rule.get('enabled', True):
                    continue

                triggers = rule.get('triggers', [])
                for trigger in triggers:
                    if trigger.get('type') == 'time':
                        config = trigger.get('config', {})
                        trigger_time = config.get('time', '')
                        trigger_days = config.get('days', [])

                        if trigger_time:
                            try:
                                hour, minute = map(int, trigger_time.split(':'))

                                # فحص الوقت
                                if hour == current_hour and minute == current_minute:
                                    # فحص الأيام
                                    if not trigger_days or current_weekday in trigger_days:
                                        print(f"🕐 تم تفعيل محفز الوقت: {trigger_time} للقاعدة: {rule.get('name')}")
                                        self.execute_automation_rule(rule)
                            except ValueError:
                                continue

        except Exception as e:
            print(f"❌ خطأ في فحص المحفزات الزمنية: {e}")

    def check_weather_triggers(self):
        """فحص محفزات الطقس"""
        try:
            # الحصول على بيانات الطقس الحالية
            weather_data = self.ha_integration.get_weather_data()
            if not weather_data:
                return

            current_temp = weather_data.get('temperature', 0)
            current_humidity = weather_data.get('humidity', 0)
            current_condition = weather_data.get('condition', '')

            for rule in self.automation_rules:
                if not rule.get('enabled', True):
                    continue

                triggers = rule.get('triggers', [])
                for trigger in triggers:
                    if trigger.get('type') == 'weather':
                        config = trigger.get('config', {})
                        condition = config.get('condition', '')
                        threshold = config.get('threshold', 0)

                        triggered = False

                        if condition == 'temperature_above' and current_temp > threshold:
                            triggered = True
                        elif condition == 'temperature_below' and current_temp < threshold:
                            triggered = True
                        elif condition == 'humidity_above' and current_humidity > threshold:
                            triggered = True
                        elif condition == 'humidity_below' and current_humidity < threshold:
                            triggered = True
                        elif condition == 'rain_start' and 'rain' in current_condition.lower():
                            triggered = True
                        elif condition == 'rain_stop' and 'rain' not in current_condition.lower():
                            triggered = True

                        if triggered:
                            print(f"🌤️ تم تفعيل محفز الطقس: {condition} للقاعدة: {rule.get('name')}")
                            self.execute_automation_rule(rule)

        except Exception as e:
            print(f"❌ خطأ في فحص محفزات الطقس: {e}")

    def check_sun_triggers(self):
        """فحص محفزات الشمس"""
        try:
            # الحصول على بيانات الشمس
            sun_data = self.ha_integration.get_entity_state('sun.sun')
            if not sun_data:
                return

            current_time = datetime.now()

            for rule in self.automation_rules:
                if not rule.get('enabled', True):
                    continue

                triggers = rule.get('triggers', [])
                for trigger in triggers:
                    if trigger.get('type') == 'sun':
                        config = trigger.get('config', {})
                        event = config.get('event', '')

                        # هنا يمكن إضافة منطق أكثر تعقيداً لفحص أوقات الشروق والغروب
                        # للآن سنتركها بسيطة
                        if event in ['sunrise', 'sunset']:
                            print(f"☀️ تم تفعيل محفز الشمس: {event} للقاعدة: {rule.get('name')}")
                            # يمكن تنفيذ القاعدة هنا بناءً على منطق أكثر تعقيداً

        except Exception as e:
            print(f"❌ خطأ في فحص محفزات الشمس: {e}")

    def execute_automation_rule(self, rule):
        """تنفيذ قاعدة أتمتة"""
        try:
            print(f"🚀 تنفيذ قاعدة الأتمتة: {rule.get('name')}")

            # فحص الشروط أولاً
            if not self.check_rule_conditions(rule):
                print(f"❌ لم تتحقق شروط القاعدة: {rule.get('name')}")
                return

            # تنفيذ الإجراءات
            actions = rule.get('actions', [])
            rule_id = rule.get('id')
            for action in actions:
                self.execute_action(action, rule_id)

            # تحديث عداد التفعيل في قاعدة البيانات
            self.update_rule_execution_stats(rule_id)

            # تحديث إحصائيات القاعدة
            self.update_rule_execution_stats(rule.get('id'))

            # إرسال إشعار بالتنفيذ
            self.mqtt_client.publish(
                'automation/executed',
                json.dumps({
                    'rule_id': rule.get('id'),
                    'rule_name': rule.get('name'),
                    'executed_at': datetime.now().isoformat()
                }, ensure_ascii=False)
            )

            print(f"✅ تم تنفيذ قاعدة الأتمتة بنجاح: {rule.get('name')}")

        except Exception as e:
            print(f"❌ خطأ في تنفيذ قاعدة الأتمتة: {e}")

    def check_rule_conditions(self, rule):
        """فحص شروط القاعدة"""
        try:
            conditions = rule.get('conditions', [])
            if not conditions:
                return True

            logic_operator = rule.get('logic_operator', 'AND')
            condition_results = []

            for condition in conditions:
                result = self.evaluate_condition(condition)
                condition_results.append(result)

            if logic_operator == 'AND':
                return all(condition_results)
            else:  # OR
                return any(condition_results)

        except Exception as e:
            print(f"❌ خطأ في فحص شروط القاعدة: {e}")
            return False

    def evaluate_condition(self, condition):
        """تقييم شرط واحد"""
        try:
            condition_type = condition.get('type')
            config = condition.get('config', {})

            if condition_type == 'weather':
                return self.evaluate_weather_condition(config)
            elif condition_type == 'sun':
                return self.evaluate_sun_condition(config)
            elif condition_type == 'person':
                return self.evaluate_person_condition(config)
            elif condition_type == 'time':
                return self.evaluate_time_condition(config)

            return True

        except Exception as e:
            print(f"❌ خطأ في تقييم الشرط: {e}")
            return False

    def evaluate_weather_condition(self, config):
        """تقييم شرط الطقس"""
        try:
            weather_data = self.ha_integration.get_weather_data()
            if not weather_data:
                return False

            condition = config.get('condition', '')
            threshold = config.get('threshold', 0)
            current_temp = weather_data.get('temperature', 0)
            current_humidity = weather_data.get('humidity', 0)

            if condition == 'temperature_above':
                return current_temp > threshold
            elif condition == 'temperature_below':
                return current_temp < threshold
            elif condition == 'humidity_above':
                return current_humidity > threshold
            elif condition == 'humidity_below':
                return current_humidity < threshold

            return True

        except Exception as e:
            print(f"❌ خطأ في تقييم شرط الطقس: {e}")
            return False

    def evaluate_sun_condition(self, config):
        """تقييم شرط الشمس"""
        try:
            sun_data = self.ha_integration.get_entity_state('sun.sun')
            if not sun_data:
                return False

            condition = config.get('condition', '')
            current_time = datetime.now()

            # منطق بسيط للشمس - يمكن تطويره أكثر
            if condition in ['after_sunset', 'after_sunrise', 'before_sunset', 'before_sunrise']:
                return True  # للآن نعتبر الشرط محقق

            return True

        except Exception as e:
            print(f"❌ خطأ في تقييم شرط الشمس: {e}")
            return False

    def evaluate_person_condition(self, config):
        """تقييم شرط الشخص"""
        try:
            person_id = config.get('person_id', 'zaen')
            expected_state = config.get('state', 'home')

            person_data = self.ha_integration.get_entity_state(f'person.{person_id}')
            if not person_data:
                return False

            current_state = person_data.get('state', 'unknown')
            return current_state == expected_state

        except Exception as e:
            print(f"❌ خطأ في تقييم شرط الشخص: {e}")
            return False

    def evaluate_time_condition(self, config):
        """تقييم شرط الوقت"""
        try:
            condition_time = config.get('time', '')
            if not condition_time:
                return True

            current_time = datetime.now()
            hour, minute = map(int, condition_time.split(':'))

            # فحص بسيط للوقت - يمكن تطويره أكثر
            return current_time.hour >= hour and current_time.minute >= minute

        except Exception as e:
            print(f"❌ خطأ في تقييم شرط الوقت: {e}")
            return True

    def execute_action(self, action, rule_id=None):
        """تنفيذ إجراء واحد"""
        try:
            action_type = action.get('type')
            config = action.get('config', {})

            if action_type == 'device_control':
                self.execute_device_control_action(config)
            elif action_type == 'notification':
                self.execute_notification_action(config, rule_id)
            elif action_type == 'scene':
                self.execute_scene_action(config)
            elif action_type == 'delay':
                delay_seconds = config.get('seconds', 1)
                time.sleep(delay_seconds)
                print(f"⏱️ تم تنفيذ تأخير: {delay_seconds} ثانية")

        except Exception as e:
            print(f"❌ خطأ في تنفيذ الإجراء: {e}")

    def execute_device_control_action(self, config):
        """تنفيذ إجراء التحكم في الجهاز"""
        try:
            device_id = config.get('device_id', '')
            device_type = config.get('device_type', '')
            room = config.get('room', '*')
            command = config.get('command', '')

            if device_type == 'AC':
                temperature = config.get('temperature', 24)
                fan_speed = config.get('fan_speed', 'auto')
                topic = f"ROOMS/{room}/AC/{device_id}"
                payload = f"{command},{temperature},{fan_speed}"
            elif device_type == 'TV':
                topic = f"ROOMS/{room}/TV/{device_id}"
                payload = command
            elif device_type == 'SWITCH':
                topic = f"ROOMS/{room}/SWITCH/{device_id}"
                payload = command
            else:
                topic = f"ROOMS/{room}/{device_type}/{device_id}"
                payload = command

            self.mqtt_client.publish(topic, payload)
            print(f"📡 تم إرسال أمر التحكم: {topic} -> {payload}")

        except Exception as e:
            print(f"❌ خطأ في تنفيذ إجراء التحكم في الجهاز: {e}")

    def execute_notification_action(self, config, rule_id=None):
        """تنفيذ إجراء الإشعار"""
        try:
            if not rule_id:
                print("❌ لم يتم توفير rule_id في إجراء الإشعار")
                return

            notification_data = {
                'rule_id': rule_id
            }

            self.mqtt_client.publish(
                'notifications/send',
                json.dumps(notification_data, ensure_ascii=False)
            )

            print(f"🔔 تم إرسال طلب إشعار لقاعدة: {rule_id}")

        except Exception as e:
            print(f"❌ خطأ في تنفيذ إجراء الإشعار: {e}")

    def execute_scene_action(self, config):
        """تنفيذ إجراء المشهد"""
        try:
            scene_name = config.get('scene_name', '')
            service_data = config.get('service_data', {})

            # إرسال أمر تنفيذ المشهد إلى Home Assistant
            scene_data = {
                'scene': scene_name,
                'data': service_data
            }

            self.mqtt_client.publish(
                'homeassistant/scene/activate',
                json.dumps(scene_data, ensure_ascii=False)
            )
            print(f"🎬 تم تنفيذ مشهد: {scene_name}")

        except Exception as e:
            print(f"❌ خطأ في تنفيذ إجراء المشهد: {e}")

    def update_rule_execution_stats(self, rule_id):
        """تحديث إحصائيات تنفيذ القاعدة"""
        try:
            connection = mysql.connector.connect(
                host='localhost',
                user='root',
                password='zain',
                database='zain'
            )
            cursor = connection.cursor()

            query = """
            UPDATE advanced_automation_rules
            SET last_triggered = NOW(), trigger_count = trigger_count + 1
            WHERE id = %s
            """

            cursor.execute(query, (rule_id,))
            connection.commit()

            print(f"📊 تم تحديث إحصائيات القاعدة: {rule_id}")

        except Exception as e:
            print(f"❌ خطأ في تحديث إحصائيات القاعدة: {e}")
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def reset_daily_trigger_counts(self):
        """إعادة تصفير عدادات التفعيل اليومية في منتصف الليل"""
        try:
            connection = mysql.connector.connect(
                host='localhost',
                user='root',
                password='zain',
                database='zain'
            )
            cursor = connection.cursor()

            # إعادة تصفير عدادات التفعيل اليومية لجميع القواعد
            query = """
            UPDATE advanced_automation_rules
            SET daily_trigger_count = 0
            """

            cursor.execute(query)
            connection.commit()

            print("🔄 تم إعادة تصفير عدادات التفعيل اليومية")

        except Exception as e:
            print(f"❌ خطأ في إعادة تصفير العدادات اليومية: {e}")
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def get_all_automation_rules(self):
        """الحصول على جميع قواعد الأتمتة"""
        connection = None
        cursor = None
        rules = []

        try:
            connection = mysql.connector.connect(
                host='localhost',
                user='root',
                password='zain',
                database='zain'
            )
            cursor = connection.cursor(dictionary=True)

            query = """
            SELECT id, name, description, triggers, conditions, actions,
                   enabled, priority, created_at, updated_at
            FROM advanced_automation_rules
            ORDER BY priority DESC, created_at DESC
            """

            cursor.execute(query)
            results = cursor.fetchall()

            for row in results:
                rule = {
                    'id': row['id'],
                    'name': row['name'],
                    'description': row['description'],
                    'triggers': json.loads(row['triggers']) if row['triggers'] else [],
                    'conditions': json.loads(row['conditions']) if row['conditions'] else [],
                    'actions': json.loads(row['actions']) if row['actions'] else [],
                    'enabled': bool(row['enabled']),
                    'priority': row['priority'],
                    'created_at': row['created_at'].isoformat() if row['created_at'] else None,
                    'updated_at': row['updated_at'].isoformat() if row['updated_at'] else None
                }
                rules.append(rule)

            print(f"📋 تم تحميل {len(rules)} قاعدة أتمتة من قاعدة البيانات")

        except Exception as e:
            print(f"❌ خطأ في تحميل قواعد الأتمتة: {e}")
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

        return rules


def main():
    """الدالة الرئيسية"""
    # engine = AutomationEngine()
    # engine.start_engine()

if __name__ == "__main__":
    main()
