import 'package:flutter/material.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/components/config.dart';

Widget pageSlide({
  required Widget content,
  bool isClosing = false,
}) {
  return AnimatedPageSlide(
    content: content,
    isClosing: isClosing,
  );
}

// نسخة بسيطة بدون حركات معقدة لتجنب المشاكل
Widget simplePageSlide({
  required Widget content,
}) {
  return TweenAnimationBuilder<double>(
    duration: const Duration(milliseconds: 400),
    tween: Tween<double>(begin: 0.0, end: 1.0),
    curve: Curves.easeOutCubic,
    builder: (context, value, child) {
      return Transform.translate(
        offset: Offset(0, (1 - value) * 100),
        child: Opacity(
          opacity: value,
          child: Container(
            height: double.infinity,
            width: double.infinity,
            margin: EdgeInsets.only(top: controller.sizedHight * 0.1),
            decoration: BoxDecoration(
              color: AppColors.backgroundColor2.withOpacity(0.989),
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(25.5)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                pageSlidingStyle(),
                SizedBox(height: controller.sizedHight * 0.01),
                Expanded(child: content),
              ],
            ),
          ),
        ),
      );
    },
  );
}

// متغير لتتبع حالة الـ bottom sheet
bool _isBottomSheetOpen = false;

// دالة مساعدة لإظهار pageSlide مع حركات جميلة
Future<T?> showAnimatedBottomSheet<T>({
  required BuildContext context,
  required Widget content,
  bool enableDrag = true,
  bool isDismissible = true,
}) async {
  // منع فتح عدة bottom sheets في نفس الوقت
  if (_isBottomSheetOpen) {
    return null;
  }

  _isBottomSheetOpen = true;

  try {
    final result = await showModalBottomSheet<T>(
      context: context,
      backgroundColor: Colors.transparent,
      enableDrag: enableDrag,
      isDismissible: isDismissible,
      isScrollControlled: true,
      builder: (context) => pageSlide(content: content),
    );

    return result;
  } finally {
    _isBottomSheetOpen = false;
  }
}

// دالة بديلة أكثر أماناً لـ showBottomSheet العادي
PersistentBottomSheetController? showSafeBottomSheet({
  required BuildContext context,
  required Widget content,
  bool enableDrag = true,
}) {
  if (_isBottomSheetOpen) {
    return null;
  }

  _isBottomSheetOpen = true;

  final controller = showBottomSheet(
    context: context,
    enableDrag: enableDrag,
    backgroundColor: Colors.transparent,
    builder: (context) => pageSlide(content: content),
  );

  // إعادة تعيين المتغير عند إغلاق الـ sheet
  controller.closed.then((_) {
    _isBottomSheetOpen = false;
  });

  return controller;
}

class AnimatedPageSlide extends StatefulWidget {
  final Widget content;
  final bool isClosing;

  const AnimatedPageSlide({
    Key? key,
    required this.content,
    this.isClosing = false,
  }) : super(key: key);

  @override
  AnimatedPageSlideState createState() => AnimatedPageSlideState();
}

class AnimatedPageSlideState extends State<AnimatedPageSlide>
    with TickerProviderStateMixin {
  AnimationController? _slideController;
  AnimationController? _fadeController;
  AnimationController? _scaleController;

  Animation<Offset>? _slideAnimation;
  Animation<double>? _fadeAnimation;
  Animation<double>? _scaleAnimation;
  Animation<double>? _shadowAnimation;

  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    if (_isDisposed) return;

    // إعداد controllers للحركات
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 700),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // إعداد الحركات
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController!,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController!,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController!,
      curve: Curves.elasticOut,
    ));

    _shadowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController!,
      curve: Curves.easeInOut,
    ));

    // بدء الحركات
    _startOpeningAnimations();
  }

  void _startOpeningAnimations() async {
    if (_isDisposed || _slideController == null) return;

    try {
      await Future.delayed(const Duration(milliseconds: 50));
      if (!_isDisposed && _slideController != null && mounted) {
        _slideController!.forward();
        _fadeController!.forward();
      }
      await Future.delayed(const Duration(milliseconds: 100));
      if (!_isDisposed && _scaleController != null && mounted) {
        _scaleController!.forward();
      }
    } catch (e) {
      // تجاهل الأخطاء إذا تم التخلص من الـ widget
    }
  }

  void _startClosingAnimations() async {
    if (_isDisposed || _scaleController == null) return;

    try {
      if (!_isDisposed && _scaleController != null && mounted) {
        _scaleController!.reverse();
      }
      await Future.delayed(const Duration(milliseconds: 100));
      if (!_isDisposed && _fadeController != null && mounted) {
        _fadeController!.reverse();
        _slideController!.reverse();
      }
    } catch (e) {
      // تجاهل الأخطاء إذا تم التخلص من الـ widget
    }
  }

  @override
  void didUpdateWidget(AnimatedPageSlide oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isClosing && !oldWidget.isClosing) {
      _startClosingAnimations();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _slideController?.dispose();
    _fadeController?.dispose();
    _scaleController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isDisposed ||
        _slideController == null ||
        _fadeController == null ||
        _scaleController == null) {
      return Container(); // إرجاع container فارغ إذا تم التخلص من الـ controllers
    }

    return AnimatedBuilder(
      animation: Listenable.merge(
          [_slideController!, _fadeController!, _scaleController!]),
      builder: (context, child) {
        // الحصول على حجم منطقة أزرار النظام الفعلي
        final bottomPadding = MediaQuery.of(context).padding.bottom;

        return SlideTransition(
          position:
              _slideAnimation ?? AlwaysStoppedAnimation(const Offset(0, 1)),
          child: FadeTransition(
            opacity: _fadeAnimation ?? AlwaysStoppedAnimation(0.0),
            child: ScaleTransition(
              scale: _scaleAnimation ?? AlwaysStoppedAnimation(0.8),
              child: Container(
                height: double.infinity,
                width: double.infinity,
                margin: EdgeInsets.only(top: controller.sizedHight * 0.1),
                padding: EdgeInsets.only(
                  bottom: bottomPadding > 0
                      ? bottomPadding // إضافة 10 بكسل إضافية للمساحة
                      : controller.sizedHight *
                          0.05, // قيمة احتياطية للأجهزة القديمة
                ),
                decoration: BoxDecoration(
                  color: AppColors.backgroundColor2
                      .withOpacity(0.989 * (_fadeAnimation?.value ?? 0.0)),
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(25.5)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black
                          .withOpacity(0.3 * (_shadowAnimation?.value ?? 0.0)),
                      blurRadius: 25 * (_shadowAnimation?.value ?? 0.0),
                      offset: Offset(0, -8 * (_shadowAnimation?.value ?? 0.0)),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    AnimatedBuilder(
                      animation: _scaleController!,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _scaleAnimation?.value ?? 0.8,
                          child: Opacity(
                            opacity: _fadeAnimation?.value ?? 0.0,
                            child: pageSlidingStyle(),
                          ),
                        );
                      },
                    ),
                    SizedBox(
                      height: controller.sizedHight * 0.01,
                    ),
                    Expanded(
                      child: AnimatedBuilder(
                        animation: _fadeController!,
                        builder: (context, child) {
                          return Transform.translate(
                            offset: Offset(
                                0, (1 - (_fadeAnimation?.value ?? 0.0)) * 30),
                            child: Opacity(
                              opacity: _fadeAnimation?.value ?? 0.0,
                              child: widget.content,
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

Widget pageSlidingStyle() => FractionallySizedBox(
      widthFactor: controller.sizedWidth * 0.0008,
      // heightFactor: controller.sizedHight * 0.0008,
      child: Container(
        margin: EdgeInsets.symmetric(
          vertical: controller.sizedHight * 0.025,
        ),
        child: Container(
          height: controller.sizedHight * 0.007,
          decoration: BoxDecoration(
            color: AppColors.containerPageColor.withOpacity(0.99),
            borderRadius: const BorderRadius.all(Radius.circular(2.5)),
          ),
        ),
      ),
    );
