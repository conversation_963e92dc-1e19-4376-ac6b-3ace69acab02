# تقسيم ملف mainRoutine.dart

تم تقسيم ملف `mainRoutine.dart` الكبير (1737 سطر) إلى عدة ملفات أصغر لتحسين التنظيم وسهولة الصيانة.

## الملفات الجديدة:

### 1. `mainRoutine.dart` (الملف الرئيسي المبسط)
- يحتوي على الدالة الرئيسية `mainRoutine()`
- يستدعي جميع المكونات الفرعية
- حوالي 120 سطر

### 2. `main_routine_task_display.dart`
- عرض المهام المختارة
- دوال عرض أجهزة التكييف والتلفزيون والمفاتيح
- حوالي 430 سطر

### 3. `main_routine_shortcut_section.dart`
- قسم الاختصارات
- اختيار الأيقونات
- حوالي 315 سطر

### 4. `main_routine_words_section.dart`
- قسم الكلمات الروتينية
- إدخال وتحقق من الكلمات
- حوالي 150 سطر

### 5. `main_routine_task_scheduler.dart`
- قسم الجدولة
- اختيار الأوقات والأيام
- حوالي 300 سطر

### 6. `main_routine_action_buttons.dart`
- أزرار الحفظ والحذف
- معالجة قاعدة البيانات
- حوالي 340 سطر

## المزايا:

1. **تنظيم أفضل**: كل مكون في ملف منفصل
2. **سهولة الصيانة**: يمكن تعديل كل جزء بشكل مستقل
3. **إعادة الاستخدام**: يمكن استخدام المكونات في أماكن أخرى
4. **تحسين الأداء**: تحميل أسرع للكود
5. **تطوير الفريق**: يمكن للمطورين العمل على ملفات مختلفة

## الاستخدام:

```dart
import 'package:zaen/view/home/<USER>/mainRoutine.dart';

// استخدام الدالة كما هو معتاد
mainRoutine(context: context, setState: setState, edit: edit)
```

## ملاحظات:

- تم الحفاظ على جميع الوظائف الأصلية
- لم يتم تغيير التصميم أو المظهر
- جميع المتغيرات العامة تعمل كما هو متوقع
- تم تحسين بعض الأخطاء البرمجية البسيطة

## المتغيرات المطلوبة:

يجب أن تكون المتغيرات التالية متاحة في النطاق العام:
- `add`, `isShortcut`, `isWords`, `isTask`, `isScheduler`
- `routineIcon`, `name1`, `name2`
- `h`, `m`, `isAM`, `days`, `re`
- `myId`, `isSetting`, `p`, `pageController`
- `weekDays`, `arabic`, `routineIcons`
