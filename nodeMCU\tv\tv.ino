#include <ESP8266WiFi.h>
#include <PubSubClient.h>
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include <String.h>
#include <DNSServer.h>
#include <ESP8266WebServer.h>
#include <ArduinoJson.h>
#include <Preferences.h>

const uint16_t kIrLed = 4;
IRsend irsend(kIrLed);
int led = 14;
int LedOn=13;
int PIN_RESET_BUTTON= 12;
int RESET = 0;
const char* mqttServer = "zain.local";    // IP adress Raspberry Pi
const int mqttPort = 1883;
//const char* mqttUser = "username";      // if you don't have MQTT Username, no need input
//const char* mqttPassword = "12345678";  // if you don't have MQTT Password, no need input
int co = 55;
char* lasttopic = "input/TV";
String code = "";
String clientId;
char* response;
String mytopic;
String message;
char* topic1;
char* topic2;
char* topic3;
char* topic4;
String Topic1;
String Topic2;
String Topic3;
String Topic4;
String Response;
String key = "False";
String myState = "POWER-OFF SIL-OFF";
int khztv = 32;

uint16_t POWER[75] = {8972, 4452, 572, 556, 576, 524, 604, 524, 576, 552, 568, 532, 600, 528, 572, 552, 580, 524, 596, 1656, 572, 1656, 576, 1652, 596, 1660, 572, 1656, 576, 1652, 596, 1656, 576, 1652, 576, 552, 580, 520, 600, 1656, 576, 1652, 576, 1652, 600, 528, 572, 556, 572, 528, 604, 1648, 572, 1660, 572, 552, 576, 528, 604, 520, 580, 1648, 600, 1656, 576, 1652, 580, 39688, 8960, 2236, 572, 30084, 8968, 2228, 572};
uint16_t SIL[75] = {8968, 4460, 576, 580, 548, 524, 600, 528, 580, 548, 572, 528, 604, 524, 576, 552, 576, 524, 596, 1660, 572, 1656, 576, 1652, 596, 1660, 572, 1656, 572, 1656, 596, 1656, 572, 1656, 576, 580, 552, 524, 596, 528, 580, 1648, 604, 524, 576, 552, 576, 524, 596, 532, 580, 1648, 604, 1652, 576, 1652, 580, 548, 572, 1656, 572, 1656, 596, 1660, 572, 1656, 572, 39700, 8972, 2228, 568, 30080, 8968, 2228, 576};
uint16_t P[71] = {9268, 4552, 616, 540, 612, 544, 608, 544, 608, 548, 608, 544, 608, 548, 616, 540, 616, 536, 616, 1688, 616, 1688, 608, 1700, 608, 1696, 608, 1696, 612, 1692, 616, 1688, 616, 1688, 608, 1696, 612, 1692, 612, 1692, 616, 540, 612, 544, 612, 544, 608, 544, 608, 548, 604, 548, 616, 540, 616, 540, 612, 1688, 620, 1688, 608, 1696, 608, 1696, 612, 1692, 612, 40112, 9272, 2252, 612};
uint16_t M[71] = {9256, 4556, 612, 540, 616, 536, 616, 540, 612, 540, 616, 536, 616, 540, 612, 540, 612, 544, 612, 1692, 612, 1692, 604, 1696, 612, 1692, 612, 1692, 616, 1688, 608, 1696, 608, 1696, 612, 1692, 612, 1688, 608, 1700, 608, 544, 608, 544, 608, 548, 608, 1696, 608, 544, 612, 544, 608, 544, 608, 544, 608, 1696, 612, 1692, 616, 1688, 616, 540, 616, 1688, 604, 40116, 9260, 2256, 604};
uint16_t CH_P[71] = {9260, 4540, 608, 544, 608, 544, 612, 540, 612, 540, 616, 536, 608, 544, 608, 544, 612, 540, 612, 1684, 616, 1684, 612, 1688, 612, 1688, 616, 1684, 616, 1684, 612, 1688, 608, 1692, 608, 544, 608, 1692, 608, 544, 608, 1692, 608, 1692, 612, 540, 616, 536, 608, 544, 608, 1688, 612, 544, 608, 1688, 612, 544, 608, 544, 612, 1684, 612, 1688, 608, 1692, 616, 40020, 9256, 2248, 616};
uint16_t CH_M[71] = {9248, 4548, 612, 568, 588, 564, 580, 572, 580, 572, 584, 568, 584, 568, 588, 564, 588, 564, 580, 1692, 616, 1684, 616, 1684, 612, 1684, 612, 1688, 612, 1688, 620, 1680, 616, 1684, 616, 564, 588, 564, 592, 560, 580, 1692, 616, 564, 580, 572, 584, 1688, 608, 572, 584, 1688, 608, 1692, 616, 1684, 612, 568, 588, 1684, 612, 1684, 616, 564, 588, 1684, 616, 40020, 9260, 2244, 608};
uint16_t N0[75] = {8968, 4468, 568, 560, 572, 528, 604, 524, 576, 552, 576, 524, 596, 536, 576, 548, 572, 532, 600, 1656, 572, 1656, 576, 1656, 596, 1660, 568, 1660, 572, 1656, 604, 1652, 580, 1652, 576, 552, 568, 532, 600, 1656, 576, 1652, 576, 556, 576, 524, 604, 524, 576, 552, 580, 1648, 572, 1660, 600, 528, 572, 556, 576, 1656, 572, 1656, 596, 1660, 572, 1656, 572, 39720, 8976, 2232, 572, 30132, 8968, 2236, 568};
uint16_t N1[75] = {8976, 4460, 576, 552, 580, 524, 596, 532, 576, 552, 568, 532, 600, 532, 568, 556, 576, 528, 604, 1652, 576, 1652, 580, 1652, 596, 1660, 572, 1656, 576, 1656, 604, 1652, 580, 1648, 580, 548, 572, 532, 600, 1656, 572, 528, 604, 1652, 580, 524, 596, 1660, 568, 532, 600, 1656, 572, 1660, 572, 556, 576, 1652, 576, 552, 580, 1652, 576, 552, 572, 1656, 572, 39724, 8972, 2236, 572, 30148, 8976, 2228, 576};
uint16_t N2[75] = {8976, 4460, 576, 552, 580, 524, 596, 532, 580, 548, 572, 528, 600, 528, 572, 556, 576, 528, 604, 1652, 576, 1652, 580, 1652, 600, 1656, 572, 1656, 576, 1656, 604, 1652, 580, 1648, 580, 552, 568, 1660, 572, 1656, 604, 528, 572, 1656, 604, 524, 576, 552, 632, 472, 608, 1648, 580, 524, 596, 532, 580, 1648, 604, 524, 576, 1656, 604, 1652, 576, 1652, 580, 39720, 8972, 2236, 584, 30144, 8972, 2232, 576};
uint16_t N3[75] = {8980, 4456, 580, 548, 572, 532, 600, 528, 572, 556, 572, 528, 604, 528, 572, 556, 576, 524, 604, 1652, 580, 1652, 576, 1652, 600, 1656, 576, 1656, 572, 1656, 608, 1648, 580, 1652, 580, 1652, 600, 528, 568, 1660, 604, 528, 572, 1656, 604, 524, 576, 552, 580, 524, 596, 532, 576, 1652, 600, 528, 572, 1660, 600, 528, 572, 1656, 608, 1648, 580, 1652, 580, 39724, 8972, 2236, 568, 30156, 8976, 2232, 576};
uint16_t N4[75] = {8976, 4460, 576, 580, 552, 524, 596, 532, 580, 548, 572, 532, 600, 528, 568, 560, 572, 528, 604, 1652, 576, 1656, 576, 1652, 600, 1656, 572, 1660, 572, 1656, 604, 1652, 580, 1652, 576, 580, 552, 524, 596, 532, 580, 572, 548, 1656, 572, 584, 548, 1656, 572, 584, 548, 1656, 576, 1652, 600, 1656, 572, 1660, 572, 584, 544, 1656, 576, 580, 552, 1652, 576, 39728, 8980, 2228, 576, 30160, 8972, 2236, 572};
uint16_t N5[75] = {8984, 4460, 576, 580, 552, 556, 576, 548, 552, 580, 548, 552, 580, 548, 552, 580, 552, 548, 580, 1652, 580, 1652, 576, 1656, 596, 1664, 576, 1652, 580, 1652, 600, 1660, 580, 1652, 576, 580, 552, 1652, 580, 576, 544, 556, 576, 1656, 572, 560, 572, 556, 552, 576, 544, 1660, 572, 584, 548, 1656, 572, 1660, 604, 552, 548, 1660, 600, 1656, 576, 1656, 572, 39756, 8984, 2232, 572, 30220, 8984, 2232, 576};
uint16_t N6[75] = {8976, 4464, 572, 556, 576, 528, 600, 528, 572, 556, 576, 524, 608, 524, 576, 552, 576, 524, 596, 1660, 572, 1660, 572, 1656, 604, 1656, 572, 1656, 576, 1656, 604, 1652, 580, 1652, 576, 1652, 600, 532, 568, 560, 572, 528, 600, 1656, 576, 528, 604, 524, 576, 552, 576, 528, 604, 1652, 580, 1652, 576, 1652, 600, 532, 576, 1652, 600, 1656, 576, 1656, 572, 39736, 8980, 2228, 580, 30160, 8976, 2232, 576};
uint16_t N7[75] = {8976, 4464, 572, 584, 548, 556, 576, 552, 548, 580, 548, 552, 580, 552, 548, 580, 552, 548, 580, 1652, 580, 1652, 576, 1652, 600, 1660, 572, 1656, 572, 1660, 604, 1652, 576, 1656, 576, 580, 548, 556, 576, 1652, 580, 1652, 576, 552, 580, 552, 580, 1648, 580, 548, 572, 1660, 572, 1660, 580, 576, 544, 556, 576, 1656, 576, 1656, 572, 556, 576, 1656, 572, 39740, 8976, 2236, 580, 30184, 8976, 2232, 576};
uint16_t N8[75] = {8976, 4468, 568, 560, 572, 532, 600, 528, 572, 556, 572, 532, 600, 528, 572, 556, 576, 528, 600, 1660, 572, 1656, 572, 1660, 604, 1656, 572, 1660, 572, 1660, 600, 1656, 576, 1656, 572, 556, 576, 1656, 572, 1660, 604, 1656, 572, 528, 604, 528, 572, 556, 576, 524, 604, 1656, 576, 528, 604, 524, 572, 556, 576, 1656, 572, 1660, 604, 1656, 572, 1660, 572, 39756, 8980, 2232, 572, 30224, 8988, 2228, 576};
uint16_t N9[75] = {8980, 4464, 572, 556, 576, 528, 604, 524, 576, 552, 576, 528, 604, 528, 572, 556, 576, 524, 604, 1656, 576, 1656, 572, 1660, 604, 1652, 576, 1656, 576, 1656, 604, 1656, 576, 1652, 576, 1656, 604, 528, 572, 1660, 604, 1652, 576, 528, 604, 552, 548, 552, 576, 556, 576, 552, 548, 1656, 604, 528, 572, 556, 576, 1656, 572, 1660, 604, 1652, 576, 1656, 576, 39760, 8984, 2232, 572, 30236, 8984, 2232, 572};

WiFiClient espClient;
PubSubClient client(espClient);
ESP8266WebServer server(5000);
String macAddress;
Preferences preferences;
// دالة للحصول على عنوان MAC
String getMacAddress() {
  uint8_t baseMac[6];
  WiFi.softAPmacAddress(baseMac);
  char baseMacChr[18] = {0};
  sprintf(baseMacChr, "%02X:%02X:%02X:%02X:%02X:%02X", baseMac[0], baseMac[1], baseMac[2], baseMac[3], baseMac[4], baseMac[5]);
  return String(baseMacChr);
}
void AP(String addressWiFi){
  IPAddress local_IP(192, 144, 4, 1);
  IPAddress gateway(192, 144, 4, 1);
  IPAddress subnet(255, 255, 255, 0);
  WiFi.softAPConfig(local_IP, gateway, subnet);
  Serial.println("Access Point Started");
  Serial.print("IP Address: ");
  Serial.println(WiFi.softAPIP());
  WiFi.softAP(addressWiFi.c_str(),"");
  server.on("/wifi", HTTP_POST, handlePost);

  // بدء الخادم
  server.begin();
  Serial.println("Server started");
  Serial.println("تشغيل نقطه الوصول مده دقيقة واحده");
  
  }
void handlePost() {
  Serial.println("sssss");
  if (server.hasArg("plain") == false) {
    server.send(400, "text/plain", "Body not received");
    return;
  }

  String body = server.arg("plain");
  DynamicJsonDocument doc(1024);
  deserializeJson(doc, body);

  const char* ssid = doc["ssid"];
  const char* password = doc["password"];
  Serial.println(ssid);
  WiFi.begin(ssid, password);

  int timeout = 10; // 10 ثواني كحد أقصى للاتصال
  while (WiFi.status() != WL_CONNECTED && timeout > 0) {
    delay(1000);
    timeout--;
  }

  if (WiFi.status() == WL_CONNECTED) {
    server.send(200, "text/plain", "Configuration successful");
    preferences.putString("ssid", ssid);
    preferences.putString("password", password);
    ESP.reset();
  } else {
    server.send(500, "text/plain", "Configuration failed");
  }
}


String getValue(String data, char separator, int index)
{
  int found = 0;
  int strIndex[] = {0, -1};
  int maxIndex = data.length() - 1;

  for (int i = 0; i <= maxIndex && found <= index; i++) {
    if (data.charAt(i) == separator || i == maxIndex) {
      found++;
      strIndex[0] = strIndex[1] + 1;
      strIndex[1] = (i == maxIndex) ? i + 1 : i;
    }
  }

  return found > index ? data.substring(strIndex[0], strIndex[1]) : "";
}

void setup_topic() {
  client.publish(lasttopic, code.c_str());
  client.subscribe(code.c_str());
  delay(500);
}
void setup() {
  irsend.begin();
  Serial.begin(115200);
  pinMode(led, OUTPUT);
  digitalWrite(led,LOW);
  pinMode(LedOn, OUTPUT);
  digitalWrite(LedOn,LOW);
  pinMode(PIN_RESET_BUTTON, INPUT);
  delay(2000);
  preferences.begin("wifi-creds", false);
  
  delay(5000);                                                                
  String savedSSID = preferences.getString("ssid", "");
  String savedPassword = preferences.getString("password", "");
  Serial.println("ddddddddddddddddd");
  Serial.println(savedSSID);
  if(savedSSID == ""){
    WiFi.mode(WIFI_AP_STA);
    macAddress = getMacAddress();
    Serial.print("MAC Address: ");
    Serial.println(macAddress);
    String addressWiFi="تلفاز/" +macAddress;
  
    Serial.println("fffffffffffffff");
    AP(addressWiFi);
    delay(1000);
    }
  else if(savedSSID != "" and WiFi.status() != WL_CONNECTED){
    WiFi.mode(WIFI_STA);
    WiFi.softAPdisconnect(true);
    WiFi.begin(savedSSID.c_str(), savedPassword.c_str());
    delay(5000);
    }                                                                    
  if(savedSSID != "" and WiFi.status() == WL_CONNECTED) {
    unsigned char mac[6];
    WiFi.macAddress(mac);
    code+=String(mac[0],16)+String(":")+String(mac[1],16)+String(":")+String(mac[2],16)+String(":")+String(mac[3],16)+String(":")+String(mac[4],16)+String(":")+String(mac[5],16);
    String codeAP = code;
    Serial.println(code);
    
    code.replace(":","_a_");
    
    Serial.println("connected"); //A-عند الانتهاء من الاتصال يظهر هذة الاسم علي السريال

    clientId="zain.local/"+String(code);
    client.setServer(mqttServer, mqttPort);
    client.setCallback(callback);
  
    while (!client.connected()) {
      Serial.println("Connecting to MQTT...");
      if (client.connect(clientId.c_str())) {
  
        Serial.println("connected");
  
      } else {
  
        Serial.print("failed with state ");
        //Serial.print(client.state());
        delay(2000);
       
      }
      resetESP();
    }
//  client.publish("esp8266", "Hello Raspberry Pi");
    //  client.subscribe("esp8266");
  }
}
void resetESP(){
  RESET = digitalRead(PIN_RESET_BUTTON);
  if( RESET == 1) {                                 
    Serial.println("Erase settings and restart ...");
    const char* ssid = "";                   // wifi ssid
    const char* password =  "";
    WiFi.begin(ssid, password);
    preferences.putString("ssid", "");
    preferences.putString("password", "");
    delay(1000);
    //wifiManager.resetSettings();  
    ESP.reset();
  }
}

void callback(char* topic, byte* payload, unsigned int length) {

  Serial.print("Message arrived in topic: ");
  Serial.println(topic);

  Serial.print("Message:");


  if ((String)topic == (String)code) {
    mytopic = "";
    for (int i = 0; i < length; i++) {
      mytopic = mytopic + (char)payload[i];
      //  Serial.println(newtopic);
      Serial.println((char)payload[i]);
    }
    if (mytopic=="remove"){
        Serial.println("Erase settings and restart ...");
        const char* ssid = "";                   // wifi ssid
        const char* password =  "";
        WiFi.begin(ssid, password);
        preferences.putString("ssid", "");
        preferences.putString("password", "");
        delay(1000);
        //wifiManager.resetSettings();  
        ESP.reset();
        delay(5000);
    }
    else if (mytopic=="re"){
      delay(1000);
      //wifiManager.resetSettings();  
      ESP.reset();
    }
    else if (mytopic.indexOf("state") != -1){
        mytopic.replace("state POWER","POWER");
        myState= mytopic;
        if (myState.indexOf("POWER-ON") != -1) {
          digitalWrite(LedOn,HIGH);
        }
        else{
          digitalWrite(LedOn,LOW);
        }
        Serial.println(myState);
    }
    Serial.println(message);
  }
  else if ((String)topic == (String)topic1 or (String)topic == (String)topic2 or (String)topic == (String)topic3 or (String)topic == (String)topic4) {
    message = "";
    for (int i = 0; i < length; i++) {
      message = message + (char)payload[i];
      //  Serial.println(newtopic);
      Serial.println((char)payload[i]);
    }

    //      if (message.indexOf("+")== 0 or message.indexOf("-")== 0){
    //        if (message.indexOf("+")== 0){
    //          message.replace(getValue(message, ' ', 1),String(getValue(message, ' ', 1).toInt()+getValue(myState, ' ', 1).toInt()) );
    //        }
    //        else{
    //          message.replace(getValue(message, ' ', 1),String(getValue(myState, ' ', 1).toInt()-getValue(message, ' ', 1).toInt()) );
    //        }
    //        message.replace(getValue(message, ' ', 0), "RUN");
    //        Serial.println(message);
    //      }
    //      if (message.indexOf("RUN")== 0 and getValue(message, ' ', 1) == "0"){
    //        message.replace("0 VAN", getValue(myState, ' ', 1)+ " VAN");
    //        if (getValue(myState, ' ', 1)=="X"){
    //          message.replace("X VAN", "24 VAN");
    //        }
    //      }
    //      if (message.indexOf("VAN") != -1 and getValue(message, ' ', 3) == "0"){
    //        message.replace("VAN 0", "VAN "+getValue(myState, ' ', 3));
    //      }
    if (message.indexOf("POWER-ON") != -1 or message.indexOf("POWER-OFF") != -1 or message.indexOf("SIL") != -1 or message.indexOf("state") != -1) {
      Response =  getValue(message, ' ', 1);
      message = getValue(message, ' ', 0);
    }
    else {
      Response =  getValue(message, ' ', 3);
      message = getValue(message, ' ', 0) + " " + getValue(message, ' ', 1) + " " + getValue(message, ' ', 2);
    }
    response = const_cast<char*>(Response.c_str());
    Serial.println(message);
    //uint16_t Send[147];
    //for (int i = 0; i < 147;i++) {
    //Send[i] = c16v1[i];
    //}
    if (message.indexOf("SIL") != -1) {
      irsend.sendRaw(SIL , 75, 38);
      Serial.println("SILENTE");
      if (myState.indexOf("SIL-ON") != -1) {
        myState.replace("SIL-ON","SIL-OFF");;
      }
      else{
        myState.replace("SIL-OFF","SIL-ON");
      }
      client.publish(response, (code+" response "+myState).c_str());
    }
    else if (message == "state") {
      if (myState.indexOf("POWER-ON") != -1) {
        myState.replace("POWER-ON","POWER-OFF");
        digitalWrite(LedOn,LOW);
      }
      else{
        myState.replace("POWER-OFF","POWER-ON");
        digitalWrite(LedOn,HIGH);
      }
      Serial.println(myState);
      client.publish(response, (code+" response "+myState).c_str());
    }
    else if (message.indexOf("POWER-OFF") != -1) {
      if (myState.indexOf("POWER-ON") != -1) {
        irsend.sendRaw(POWER , 75, 38);
        myState.replace("POWER-ON","POWER-OFF");
        digitalWrite(LedOn,LOW);
      }
      Serial.println(myState);
      client.publish(response, (code+" response "+myState).c_str());
    }
    else if (message.indexOf("POWER-ON") != -1) {
      if (myState.indexOf("POWER-OFF") != -1) {
        irsend.sendRaw(POWER , 75, 38);
        myState.replace("POWER-OFF","POWER-ON");
        digitalWrite(LedOn,HIGH);
      }
      Serial.println(myState);
      client.publish(response, (code+" response "+myState).c_str());
    }

    //else if (message == "POWER") {
     // irsend.sendRaw(POWER , 75, 38);
      //if (myState == "مغلق") {
       // myState = "مفتوح";
     // }
     // else {
      //  myState = "مغلق";
     // }
     // Serial.println(myState);
     // client.publish(response, (code+" response "+myState).c_str());
    //}

    else if (message.indexOf("VOICE") != -1) {
      if (message.indexOf("+") != -1) {
        for (int i = 0; i <= getValue(message, ' ', 2).toInt(); i++) {
          irsend.sendRaw(P , 71, 38);
          delay(200);
        }
      }
      else if (message.indexOf("-") != -1) {
        for (int i = 0; i <= getValue(message, ' ', 2).toInt(); i++) {
          irsend.sendRaw(M , 71, 38);
          delay(200);
        }
      }
      if (myState.indexOf("SIL-ON") != -1) {
        myState.replace("SIL-ON","SIL-OFF");;
      }
      client.publish(response, (code+" response "+myState).c_str());
    }

    else if (message.indexOf("CH") != -1) {

      if (myState.indexOf("POWER-OFF") != -1) {
        myState.replace("POWER-OFF","POWER-ON");
        digitalWrite(LedOn,HIGH);
        irsend.sendRaw(POWER , 75, 38);
        client.publish(response, (code+" response "+myState).c_str());
        delay(15000);
      }
      if (message.indexOf("+") != -1) {
        for (int i = 1; i <= getValue(message, ' ', 2).toInt(); i++) {
          irsend.sendRaw(CH_P , 71, 38);
          delay(200);
        }
      }
      else if (message.indexOf("-") != -1) {
        for (int i = 1; i <= getValue(message, ' ', 2).toInt(); i++) {
          irsend.sendRaw(CH_M , 71, 38);
          delay(200);
        }
      }
      else if (message.indexOf("=") != -1) {
        String number = getValue(message, ' ', 2);
        for (int i = 0; i <= getValue(message, ' ', 2).length(); i++) {
          if (String(number[i]) == "0") {
            irsend.sendRaw(N0 , 75, 38);
            Serial.println(number[i]);
          }
          else if (String(number[i]) == "1") {
            irsend.sendRaw(N1 , 75, 38);
            Serial.println(number[i]);
          }
          else if (String(number[i]) == "2") {
            irsend.sendRaw(N2 , 75, 38);
            Serial.println(number[i]);
          }
          else if (String(number[i]) == "3") {
            irsend.sendRaw(N3 , 75, 38);
            Serial.println(number[i]);
          }
          else if (String(number[i]) == "4") {
            irsend.sendRaw(N4 , 75, 38);
            Serial.println(number[i]);
          }
          else if (String(number[i]) == "5") {
            irsend.sendRaw(N5 , 75, 38);
            Serial.println(number[i]);
          }
          else if (String(number[i]) == "6") {
            irsend.sendRaw(N6 , 75, 38);
            Serial.println(number[i]);
          }
          else if (String(number[i]) == "7") {
            irsend.sendRaw(N7 , 75, 38);
            Serial.println(number[i]);
          }
          else if (String(number[i]) == "8") {
            irsend.sendRaw(N8 , 75, 38);
            Serial.println(number[i]);
          }
          else if (String(number[i]) == "9") {
            irsend.sendRaw(N9 , 75, 38);
            Serial.println(number[i]);
          }
        }
        Serial.println(getValue(message, ' ', 2));
      }
      
    }

    else {
      client.publish("RESPONSE", "ERROR");
      delay(3000);
    }
    message = "";

  }
  else {
    Serial.println("xxx");
  }

  Serial.println();
  Serial.println("-----------------------");
  Serial.println(myState);



}
char* string2char(String command) {
  if (command.length() == 0) {
    Serial.println("ccc");
    setup_topic();
  }
  if (command.length() != 0) {
    delay(200);
    Topic1 = "ROOMS/" + command + "/TV%/" + String(code);
    Topic2 = "ROOMS/*/TV%/" + String(code);
    Topic3 = "ROOMS/*/TV%/*";
    Topic4 = "ROOMS/" + command + "/TV%/*";
    //Serial.println((String)p);
    //client.publish("199", "Hello Raspberry Pi");
    topic1 = const_cast<char*>(Topic1.c_str());
    topic2 = const_cast<char*>(Topic2.c_str());
    topic3 = const_cast<char*>(Topic3.c_str());
    topic4 = const_cast<char*>(Topic4.c_str());
    Serial.println(topic1);
    Serial.println(topic2);
    Serial.println(topic3);
    Serial.println(topic4);
    digitalWrite(led,HIGH);
    client.subscribe(topic1);
    client.subscribe(topic2);
    client.subscribe(topic3);
    client.subscribe(topic4);
    key = "True";
  }
}


void reconnect() {
  if (!client.connected()) {
    //if (WiFi.status() != WL_CONNECTED) return;
    if (client.connect(clientId.c_str())) {
      Serial.println("connected");
      mytopic="";
      key = "False";
      string2char(mytopic);

      }
    else {
      Serial.print("failed, rc=");
      Serial.print(client.state());
      Serial.println(" try again in 5 seconds");
      // Wait 5 seconds before retrying
 //     delay(5000);
    }
  }
}



void loop() {
  String ssid = preferences.getString("ssid", "");
  String password = preferences.getString("password", "");
//  Serial.println(ssid);
//  Serial.println(password);
  if (ssid != "" and WiFi.status() != WL_CONNECTED){
    Serial.println("11111111");
    Serial.println(WiFi.status());
    digitalWrite(led,LOW);
    WiFi.begin(ssid.c_str(), password.c_str());
    delay(5000);
    if (WiFi.status() == WL_CONNECTED){
      ESP.reset();
    }
  }
  else if (ssid != "" and WiFi.status() == WL_CONNECTED and !client.connected()) {
    digitalWrite(led,LOW);
    reconnect();
    delay(200);
  }
  else if (client.connected()){
    client.loop();
    if (key != "True") {
      string2char(mytopic);
    }
    if (co == 60){
      client.publish("connect", code.c_str());
      co=0;  
    }
    else{
      co=co+1;
    }
  }
  else{
    delay(200);
    digitalWrite(led,LOW);
  }
  //Serial.println(WiFi.status());
  server.handleClient();
  resetESP();
  delay(200);
}
