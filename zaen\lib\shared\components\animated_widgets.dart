import 'package:flutter/material.dart';

// ===== حركات انتقال للـ PageView =====

class AnimatedPageView extends StatefulWidget {
  final List<Widget> children;
  final PageController? controller;
  final ValueChanged<int>? onPageChanged;
  final ScrollPhysics? physics;
  final Axis scrollDirection;
  final Duration animationDuration;
  final Curve animationCurve;

  const AnimatedPageView({
    Key? key,
    required this.children,
    this.controller,
    this.onPageChanged,
    this.physics,
    this.scrollDirection = Axis.horizontal,
    this.animationDuration = const Duration(milliseconds: 500),
    this.animationCurve = Curves.easeInOutCubic,
  }) : super(key: key);

  @override
  AnimatedPageViewState createState() => AnimatedPageViewState();
}

class AnimatedPageViewState extends State<AnimatedPageView>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _animation;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = widget.controller ?? PageController();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    if (widget.controller == null) {
      _pageController.dispose();
    }
    super.dispose();
  }

  void animateToPage(int page) {
    _animationController.forward().then((_) {
      _pageController.animateToPage(
        page,
        duration: widget.animationDuration,
        curve: widget.animationCurve,
      );
      _animationController.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 - (_animation.value * 0.05),
          child: Opacity(
            opacity: 1.0 - (_animation.value * 0.3),
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
                widget.onPageChanged?.call(index);
              },
              physics: widget.physics,
              scrollDirection: widget.scrollDirection,
              children: widget.children,
            ),
          ),
        );
      },
    );
  }
}
