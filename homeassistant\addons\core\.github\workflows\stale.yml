---
# yamllint disable rule:truthy
name: <PERSON>ale

on:
  schedule:
    - cron: "0 * * * *"
  workflow_dispatch:

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - name: 30 days stale issues
        uses: actions/stale@v9.1.0
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          days-before-issue-stale: 30
          days-before-issue-close: 7
          operations-per-run: 150
          remove-stale-when-updated: true
          stale-issue-label: "stale"
          exempt-issue-labels: "pinned,security,Help wanted"
          stale-issue-message: >
            This issue has been automatically marked as stale because it has
            not had recent activity. It will be closed if no further activity
            occurs. Thank you for your contributions.
