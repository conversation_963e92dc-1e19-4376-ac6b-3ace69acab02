import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/services/notification_service.dart';
import 'package:zaen/shared/themes/app_colors.dart';

/// ويدجت لعرض الإشعارات السريعة في أعلى الشاشة
class NotificationBanner extends StatelessWidget {
  const NotificationBanner({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<NotificationService>(
      init: NotificationService.instance,
      builder: (notificationService) {
        final activeNotifications = notificationService.activeNotifications;

        if (activeNotifications.isEmpty) {
          return const SizedBox.shrink();
        }

        // عرض أحدث إشعار فقط
        final latestNotification = activeNotifications.first;

        return Container(
          margin: const EdgeInsets.all(8.0),
          child: Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [
                    _getNotificationColor(latestNotification.type)
                        .withOpacity(0.1),
                    _getNotificationColor(latestNotification.type)
                        .withOpacity(0.05),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Row(
                  children: [
                    // أيقونة الإشعار
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _getNotificationColor(latestNotification.type)
                            .withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getNotificationIcon(latestNotification.type),
                        color: _getNotificationColor(latestNotification.type),
                        size: 20,
                      ),
                    ),

                    const SizedBox(width: 12),

                    // محتوى الإشعار
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            latestNotification.title,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            latestNotification.body,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),

                    // عدد الإشعارات الإضافية
                    if (activeNotifications.length > 1)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.primaryColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '+${activeNotifications.length - 1}',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primaryColor,
                          ),
                        ),
                      ),

                    const SizedBox(width: 8),

                    // زر الإغلاق
                    GestureDetector(
                      onTap: () {
                        notificationService
                            .removeNotification(latestNotification.id);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Icon(
                          Icons.close,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.weatherChange:
        return Colors.blue;
      case NotificationType.extremeWeather:
        return Colors.red;
      case NotificationType.smartSuggestion:
        return Colors.green;
      case NotificationType.weatherAlert:
        return Colors.orange;
      case NotificationType.automationNotification:
        return Colors.purple;
    }
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.weatherChange:
        return Icons.wb_sunny;
      case NotificationType.extremeWeather:
        return Icons.warning;
      case NotificationType.smartSuggestion:
        return Icons.lightbulb;
      case NotificationType.weatherAlert:
        return Icons.info;
      case NotificationType.automationNotification:
        return Icons.smart_toy;
    }
  }
}
