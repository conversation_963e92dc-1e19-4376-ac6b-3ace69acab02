import requests
import mysql.connector
import json
import sys
import time
from datetime import datetime, timedelta
import threading
import logging

# إضافة مسارات النظام
sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/mqtt')
sys.path.append('/home/<USER>/myProject/nlt')

try:
    import static as st
    from database_helper import DatabaseHelper
    import tts as tts_module
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد بعض الوحدات: {e}")
    # إعدادات افتراضية في حالة عدم توفر الوحدات
    class MockStatic:
        ip = "zain.local"
    st = MockStatic()

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# إعدادات Home Assistant
BASE_URL = "http://zain.local:8123/api"
HEADERS = {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJhMTZkYTViNTBjOTg0NDczODUyNjkxMDdkNWI4ZjQ3MCIsImlhdCI6MTc1MzcxMzQyMiwiZXhwIjoyMDY5MDczNDIyfQ.ASA2ze-qC_9jj71Ih7FBygGDyZRVS5by8-G4P_SbTlo",
    "Content-Type": "application/json"
}

# إعدادات قاعدة البيانات
DB_CONFIG = {
    "user": "root",
    "host": st.ip if hasattr(st, 'ip') else "zain.local",
    "passwd": "zain",
    "database": "zain"
}

# أنواع الكيانات التي نهتم بها مع تصنيفاتها
WATCHED_TYPES = {
    "weather": {
        "types": ["weather"],
        "table": "weather_data",
        "update_interval": 300  # 5 دقائق
    },
    "sensor": {
        "types": ["sensor"],
        "table": "sensor_data",
        "update_interval": 60   # دقيقة واحدة
    },
    "sun": {
        "types": ["sun"],
        "table": "sun_data",
        "update_interval": 3600  # ساعة واحدة
    },
    "person": {
        "types": ["person"],
        "table": "person_data",
        "update_interval": 30    # 30 ثانية
    },
    "zone": {
        "types": ["zone"],
        "table": "zone_data",
        "update_interval": 300   # 5 دقائق
    },
    "automation": {
        "types": ["automation"],
        "table": "automation_data",
        "update_interval": 60    # دقيقة واحدة
    }
}

class HomeAssistantIntegration:
    """فئة رئيسية لإدارة التكامل مع Home Assistant"""

    def __init__(self):
        self.db_helper = DatabaseHelper() if 'DatabaseHelper' in globals() else None
        self.tts = None
        self.last_update = {}
        self.running = False
        self.update_thread = None

        # تهيئة TTS إذا كان متوفراً
        try:
            if 'tts_module' in globals():
                self.tts = tts_module.SmartHomeTTSCache()
        except Exception as e:
            logger.warning(f"لا يمكن تهيئة TTS: {e}")

        # إنشاء الجداول المطلوبة
        self.create_required_tables()

    def get_all_entities(self):
        """جلب كل الكيانات من Home Assistant"""
        try:
            response = requests.get(f"{BASE_URL}/states", headers=HEADERS, timeout=10)
            response.raise_for_status()
            entities = response.json()
            # تسجيل فقط عند وجود تغيير كبير
            if len(entities) != getattr(self, '_last_entity_count', 0):
                logger.info(f"تم جلب {len(entities)} كيان من Home Assistant")
                self._last_entity_count = len(entities)
            return entities
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ فشل في الاتصال بـ Home Assistant: {e}")
            return []
        except Exception as e:
            logger.error(f"❌ خطأ غير متوقع: {e}")
            return []

    def connect_to_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            db = mysql.connector.connect(**DB_CONFIG)
            # تسجيل الاتصال فقط عند الحاجة
            return db
        except mysql.connector.Error as err:
            logger.error(f"❌ فشل الاتصال بقاعدة البيانات: {err}")
            return None

    def create_required_tables(self):
        """إنشاء الجداول المطلوبة"""
        connection = None
        cursor = None

        try:
            connection = self.connect_to_database()
            if not connection:
                return False

            cursor = connection.cursor()

            # جدول الكيانات العام (محسن)
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS observer_entities (
                id INT AUTO_INCREMENT PRIMARY KEY,
                entity_id VARCHAR(255) UNIQUE,
                type VARCHAR(50),
                state VARCHAR(500),
                friendly_name VARCHAR(255),
                attributes_json TEXT,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_entity_type (type),
                INDEX idx_entity_id (entity_id),
                INDEX idx_last_updated (last_updated)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)

            # جدول بيانات الطقس المفصل
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS weather_data (
                id INT AUTO_INCREMENT PRIMARY KEY,
                entity_id VARCHAR(255),
                temperature DECIMAL(5,2),
                humidity INT,
                pressure DECIMAL(7,2),
                wind_speed DECIMAL(5,2),
                wind_direction VARCHAR(10),
                weather_condition VARCHAR(100),
                weather_description VARCHAR(255),
                visibility DECIMAL(5,2),
                uv_index DECIMAL(3,1),
                forecast_data JSON,
                location VARCHAR(255),
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (entity_id) REFERENCES observer_entities(entity_id) ON DELETE CASCADE,
                INDEX idx_weather_updated (last_updated),
                INDEX idx_weather_location (location)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)

            # جدول بيانات أجهزة الاستشعار
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS sensor_data (
                id INT AUTO_INCREMENT PRIMARY KEY,
                entity_id VARCHAR(255),
                sensor_type VARCHAR(100),
                value DECIMAL(10,4),
                unit VARCHAR(50),
                device_class VARCHAR(100),
                location VARCHAR(255),
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (entity_id) REFERENCES observer_entities(entity_id) ON DELETE CASCADE,
                INDEX idx_sensor_type (sensor_type),
                INDEX idx_sensor_updated (last_updated)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)

            connection.commit()
            logger.info("✅ تم إنشاء الجداول المطلوبة بنجاح")
            return True

        except mysql.connector.Error as e:
            logger.error(f"❌ خطأ في إنشاء الجداول: {e}")
            if connection:
                connection.rollback()
            return False
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def process_weather_entity(self, entity):
        """معالجة كيانات الطقس"""
        try:
            entity_id = entity["entity_id"]
            attrs = entity.get("attributes", {})

            weather_data = {
                'entity_id': entity_id,
                'temperature': attrs.get('temperature'),
                'humidity': attrs.get('humidity'),
                'pressure': attrs.get('pressure'),
                'wind_speed': attrs.get('wind_speed'),
                'wind_direction': attrs.get('wind_bearing'),
                'weather_condition': entity.get('state'),
                'weather_description': attrs.get('friendly_name', ''),
                'visibility': attrs.get('visibility'),
                'uv_index': attrs.get('uv_index'),
                'forecast_data': json.dumps(attrs.get('forecast', []), ensure_ascii=False),
                'location': attrs.get('location', 'unknown')
            }

            return self.insert_weather_data(weather_data)

        except Exception as e:
            logger.error(f"خطأ في معالجة كيان الطقس {entity.get('entity_id', 'unknown')}: {e}")
            return False

    def process_sensor_entity(self, entity):
        """معالجة كيانات أجهزة الاستشعار"""
        try:
            entity_id = entity["entity_id"]
            attrs = entity.get("attributes", {})

            # تحديد نوع المستشعر
            sensor_type = attrs.get('device_class', 'generic')
            if not sensor_type and 'temperature' in entity_id.lower():
                sensor_type = 'temperature'
            elif not sensor_type and 'humidity' in entity_id.lower():
                sensor_type = 'humidity'

            sensor_data = {
                'entity_id': entity_id,
                'sensor_type': sensor_type,
                'value': self.safe_float_convert(entity.get('state')),
                'unit': attrs.get('unit_of_measurement', ''),
                'device_class': attrs.get('device_class', ''),
                'location': attrs.get('friendly_name', '').split(' ')[0] if attrs.get('friendly_name') else 'unknown'
            }

            return self.insert_sensor_data(sensor_data)

        except Exception as e:
            logger.error(f"خطأ في معالجة كيان المستشعر {entity.get('entity_id', 'unknown')}: {e}")
            return False

    def safe_float_convert(self, value):
        """تحويل آمن للقيم الرقمية"""
        try:
            if value is None or value == 'unknown' or value == 'unavailable':
                return None
            return float(value)
        except (ValueError, TypeError):
            return None

    def insert_weather_data(self, weather_data):
        """إدراج بيانات الطقس"""
        connection = None
        cursor = None

        try:
            connection = self.connect_to_database()
            if not connection:
                return False

            cursor = connection.cursor()

            query = """
            INSERT INTO weather_data
            (entity_id, temperature, humidity, pressure, wind_speed, wind_direction,
             weather_condition, weather_description, visibility, uv_index, forecast_data, location)
            VALUES (%(entity_id)s, %(temperature)s, %(humidity)s, %(pressure)s, %(wind_speed)s,
                    %(wind_direction)s, %(weather_condition)s, %(weather_description)s,
                    %(visibility)s, %(uv_index)s, %(forecast_data)s, %(location)s)
            ON DUPLICATE KEY UPDATE
                temperature = VALUES(temperature),
                humidity = VALUES(humidity),
                pressure = VALUES(pressure),
                wind_speed = VALUES(wind_speed),
                wind_direction = VALUES(wind_direction),
                weather_condition = VALUES(weather_condition),
                weather_description = VALUES(weather_description),
                visibility = VALUES(visibility),
                uv_index = VALUES(uv_index),
                forecast_data = VALUES(forecast_data),
                location = VALUES(location)
            """

            cursor.execute(query, weather_data)
            connection.commit()
            return True

        except mysql.connector.Error as e:
            logger.error(f"خطأ في إدراج بيانات الطقس: {e}")
            if connection:
                connection.rollback()
            return False
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def insert_sensor_data(self, sensor_data):
        """إدراج بيانات المستشعر"""
        connection = None
        cursor = None

        try:
            connection = self.connect_to_database()
            if not connection:
                return False

            cursor = connection.cursor()

            query = """
            INSERT INTO sensor_data
            (entity_id, sensor_type, value, unit, device_class, location)
            VALUES (%(entity_id)s, %(sensor_type)s, %(value)s, %(unit)s, %(device_class)s, %(location)s)
            ON DUPLICATE KEY UPDATE
                sensor_type = VALUES(sensor_type),
                value = VALUES(value),
                unit = VALUES(unit),
                device_class = VALUES(device_class),
                location = VALUES(location)
            """

            cursor.execute(query, sensor_data)
            connection.commit()
            return True

        except mysql.connector.Error as e:
            logger.error(f"خطأ في إدراج بيانات المستشعر: {e}")
            if connection:
                connection.rollback()
            return False
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def insert_or_update_entity(self, entity):
        """إدراج أو تحديث كيان في الجدول الرئيسي"""
        connection = None
        cursor = None

        try:
            connection = self.connect_to_database()
            if not connection:
                return False

            cursor = connection.cursor()

            entity_id = entity["entity_id"]
            type_ = entity_id.split(".")[0]
            state = entity.get("state", "")
            attrs = entity.get("attributes", {})
            friendly_name = attrs.get("friendly_name", "")
            attributes_json = json.dumps(attrs, ensure_ascii=False)

            cursor.execute("""
                INSERT INTO observer_entities (entity_id, type, state, friendly_name, attributes_json)
                VALUES (%s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    state = VALUES(state),
                    friendly_name = VALUES(friendly_name),
                    attributes_json = VALUES(attributes_json);
            """, (entity_id, type_, state, friendly_name, attributes_json))

            connection.commit()
            return True

        except mysql.connector.Error as e:
            logger.error(f"خطأ في إدراج/تحديث الكيان {entity_id}: {e}")
            if connection:
                connection.rollback()
            return False
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def process_entities(self):
        """معالجة جميع الكيانات"""
        entities = self.get_all_entities()
        if not entities:
            return 0

        processed_count = 0
        weather_count = 0
        sensor_count = 0

        for entity in entities:
            entity_id = entity["entity_id"]
            type_ = entity_id.split(".")[0]

            # التحقق من أن النوع مراقب
            category = None
            for cat, config in WATCHED_TYPES.items():
                if type_ in config["types"]:
                    category = cat
                    break

            if not category:
                continue

            # إدراج في الجدول الرئيسي
            if self.insert_or_update_entity(entity):
                processed_count += 1

                # معالجة متخصصة حسب النوع
                if category == "weather":
                    if self.process_weather_entity(entity):
                        weather_count += 1
                elif category == "sensor":
                    if self.process_sensor_entity(entity):
                        sensor_count += 1

        logger.info(f"✅ تم معالجة {processed_count} كيان (طقس: {weather_count}, مستشعرات: {sensor_count})")
        return processed_count

    def get_weather_data(self, location=None):
        """الحصول على بيانات الطقس من جدول observer_entities"""
        connection = None
        cursor = None

        try:
            connection = self.connect_to_database()
            if not connection:
                return None

            cursor = connection.cursor(dictionary=True)

            # البحث عن كيان الطقس في جدول observer_entities
            query = """
            SELECT entity_id, state, friendly_name, attributes_json
            FROM observer_entities
            WHERE type = 'weather'
            ORDER BY id DESC
            LIMIT 1
            """
            cursor.execute(query)
            weather_entity = cursor.fetchone()

            if weather_entity and weather_entity['attributes_json']:
                try:
                    # تحليل JSON attributes
                    attributes = json.loads(weather_entity['attributes_json'])

                    # إنشاء بيانات الطقس المنسقة
                    weather_data = {
                        'entity_id': weather_entity['entity_id'],
                        'weather_condition': weather_entity['state'],
                        'friendly_name': weather_entity['friendly_name'],
                        'temperature': attributes.get('temperature'),
                        'humidity': attributes.get('humidity'),
                        'pressure': attributes.get('pressure'),
                        'wind_speed': attributes.get('wind_speed'),
                        'wind_bearing': attributes.get('wind_bearing'),
                        'cloud_coverage': attributes.get('cloud_coverage'),
                        'uv_index': attributes.get('uv_index'),
                        'dew_point': attributes.get('dew_point'),
                        'visibility': attributes.get('visibility'),
                        'temperature_unit': attributes.get('temperature_unit', '°C'),
                        'pressure_unit': attributes.get('pressure_unit', 'hPa'),
                        'wind_speed_unit': attributes.get('wind_speed_unit', 'km/h'),
                        'attribution': attributes.get('attribution'),
                        'last_updated': datetime.now()
                    }

                    return weather_data

                except json.JSONDecodeError as e:
                    logger.error(f"خطأ في تحليل JSON للطقس: {e}")
                    return None

            return None

        except mysql.connector.Error as e:
            logger.error(f"خطأ في جلب بيانات الطقس: {e}")
            return None
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def get_sensor_data(self, sensor_type=None, location=None):
        """الحصول على بيانات المستشعرات"""
        connection = None
        cursor = None

        try:
            connection = self.connect_to_database()
            if not connection:
                return []

            cursor = connection.cursor(dictionary=True)

            query = "SELECT * FROM sensor_data WHERE 1=1"
            params = []

            if sensor_type:
                query += " AND sensor_type = %s"
                params.append(sensor_type)

            if location:
                query += " AND location LIKE %s"
                params.append(f"%{location}%")

            query += " ORDER BY last_updated DESC LIMIT 10"

            cursor.execute(query, params)
            return cursor.fetchall()

        except mysql.connector.Error as e:
            logger.error(f"خطأ في جلب بيانات المستشعرات: {e}")
            return []
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def get_entity_state(self, entity_id):
        """الحصول على حالة كيان محدد"""
        connection = None
        cursor = None

        try:
            connection = self.connect_to_database()
            if not connection:
                return None

            cursor = connection.cursor(dictionary=True)

            query = """
            SELECT entity_id, state, friendly_name, attributes_json
            FROM observer_entities
            WHERE entity_id = %s
            ORDER BY id DESC
            LIMIT 1
            """

            cursor.execute(query, (entity_id,))
            entity = cursor.fetchone()

            if entity:
                # تحليل attributes إذا كانت موجودة
                attributes = {}
                if entity['attributes_json']:
                    try:
                        attributes = json.loads(entity['attributes_json'])
                    except json.JSONDecodeError:
                        attributes = {}

                return {
                    'entity_id': entity['entity_id'],
                    'state': entity['state'],
                    'attributes': attributes,
                    'friendly_name': entity['friendly_name'],
                    'last_updated': datetime.now()
                }

            return None

        except mysql.connector.Error as e:
            logger.error(f"خطأ في جلب حالة الكيان {entity_id}: {e}")
            return None
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def start_continuous_update(self, interval=300):
        """بدء التحديث المستمر"""
        self.running = True

        def update_loop():
            while self.running:
                try:
                    self.process_entities()
                    time.sleep(interval)
                except Exception as e:
                    logger.error(f"خطأ في حلقة التحديث: {e}")
                    time.sleep(60)  # انتظار دقيقة قبل المحاولة مرة أخرى

        self.update_thread = threading.Thread(target=update_loop, daemon=True)
        self.update_thread.start()
        logger.info(f"✅ تم بدء التحديث المستمر كل {interval} ثانية")

    def stop_continuous_update(self):
        """إيقاف التحديث المستمر"""
        self.running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        logger.info("✅ تم إيقاف التحديث المستمر")

# دالة للتوافق مع النظام القديم
def main():
    """الدالة الرئيسية للتوافق مع النظام القديم"""
    integration = HomeAssistantIntegration()
    processed = integration.process_entities()
    print(f"✅ تم حفظ {processed} كيانًا مراقبًا في قاعدة البيانات.")

if __name__ == "__main__":
    main()
