import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mysql1/mysql1.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/models/pages/pages_index.dart';
import 'package:zaen/models/shortcuts.dart';
import 'package:zaen/modules/local/ip.dart';
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/shared/commands/room.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/commands/home.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/settings/settings.dart';
import 'dashbourd/favorite/favorite.dart';
import 'package:zaen/view/home/<USER>/routineDevices/routine.dart';
import 'package:zaen/services/notification_service.dart';
import 'package:zaen/pages/notifications_settings_page.dart';
import 'package:zaen/widgets/notification_banner.dart';
import 'package:zaen/view/home/<USER>/setting.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/view/home/<USER>/weather/weather_widget.dart';
import 'package:zaen/view/home/<USER>/weather/weather_details_page.dart';

class Home extends StatefulWidget {
  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  var hostZain;
  HomeController controller = Get.put(HomeController(), permanent: true);
  int p = 1;
  bool scroll = false;
  bool menublur = false;
  bool editFavorite = false;

  // أضف تحكم التمرير
  late ScrollController _scrollController;
  double expandedHeight = 0;
  double collapsedHeight = kToolbarHeight;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_handleScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_handleScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _handleScroll() {
    // احسب ارتفاع AppBar في حالة التكبير
    final newExpandedHeight = controller.sizedHight * 0.07;

    // احسب العتبة التي يتغير عندها سلوك AppBar
    final threshold = newExpandedHeight - collapsedHeight;

    // تحقق إذا كان المستخدم قد مرر بما يكفي لتغيير حالة AppBar
    final newScrollState = _scrollController.offset >= threshold;
    print(_scrollController.offset);
    // تحديث الحالة فقط إذا تغيرت
    if (newScrollState != scroll && p == 0) {
      setState(() => scroll = newScrollState);
    }
    if (p == 1) {
      scroll = false;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(0.0);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    controller.sizedHight = MediaQuery.of(context).size.height;
    controller.sizedWidth = MediaQuery.of(context).size.width;
    controller.sized = controller.sizedHight + controller.sizedWidth;
    PageController _pageController = PageController(initialPage: p);
    print('Home screen initialized');

    print(controller.home.value);

    return Scaffold(
        extendBody: true,
        extendBodyBehindAppBar: true,
        body: GetBuilder<HomeController>(
            builder: (controller) => GetBuilder<SettingsController>(
                  builder: (settingsController) => ConditionalBuilder(
                    condition: controller.home.value.isNotEmpty,
                    fallback: (context) {
                      print('fffffffffffffff');
                      return const Center(child: CircularProgressIndicator());
                    },
                    builder: (context) => Stack(
                      children: [
                        controller.homeImage.value.contains('com.example.zaen')
                            ? Image.file(
                                File(controller.homeImage.value),
                                height: MediaQuery.of(context).size.height,
                                width: MediaQuery.of(context).size.width,
                                fit: BoxFit.cover,
                                filterQuality: FilterQuality.high,
                              )
                            : Image.asset(
                                "${controller.homeImage.value}",
                                height: MediaQuery.of(context).size.height,
                                width: MediaQuery.of(context).size.width,
                                filterQuality: FilterQuality.high,
                                fit: BoxFit.cover,
                              ),
                        BackdropFilter(
                          blendMode: BlendMode.srcIn,
                          filter: ImageFilter.blur(
                            sigmaX: 40,
                            sigmaY: 40,
                          ),
                          child: Container(
                              height: MediaQuery.of(context).size.height,
                              width: MediaQuery.of(context).size.width,
                              color: AppColors.textColor.withOpacity(0.05)),
                        ),
                        NestedScrollView(
                          controller: _scrollController, // أضف تحكم التمرير هنا
                          physics: p == 1
                              ? NeverScrollableScrollPhysics()
                              : BouncingScrollPhysics(),
                          headerSliverBuilder:
                              (BuildContext context, bool innerBoxIsScrolled) {
                            return <Widget>[
                              Directionality(
                                textDirection: TextDirection.rtl,
                                child: SliverAppBar(
                                  expandedHeight: p != 1
                                      ? controller.sizedHight * 0.3
                                      : controller.sizedHight * 0.22,
                                  backgroundColor: p != 1
                                      ? AppColors.backgroundColor
                                          .withOpacity(0.65)
                                      : scroll
                                          ? AppColors.backgroundColor
                                              .withOpacity(0.65)
                                          : Colors.transparent,
                                  leadingWidth: controller.sizedWidth * 0.4,
                                  actions: [
                                    IconButton(
                                      padding: EdgeInsets.zero,
                                      onPressed: () {
                                        if (controller.homeId.isNotEmpty &&
                                            client.connectionStatus!.state
                                                    .name ==
                                                'connected') {
                                          isSetting = true;
                                          addRoutine(context: context);
                                        }
                                      },
                                      icon: Icon(
                                        Icons.settings_rounded,
                                      ),
                                      color: const Color.fromARGB(
                                          255, 239, 235, 235),
                                      iconSize: controller.sized * 0.025,
                                    ),
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal:
                                              controller.sizedWidth * 0.01),
                                      child: controller.devices['home'] ==
                                                  false ||
                                              client.connectionStatus!.state
                                                      .name !=
                                                  'connected'
                                          ? Padding(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal:
                                                      controller.sizedWidth *
                                                          0.01,
                                                  vertical:
                                                      controller.sizedHight *
                                                          0.015),
                                              child: Text(
                                                'غير متصل',
                                                textDirection:
                                                    TextDirection.rtl,
                                                style: TextStyle(
                                                    color: Color.fromARGB(
                                                        255, 238, 19, 3),
                                                    fontSize: controller.sized *
                                                        0.0135,
                                                    fontWeight:
                                                        FontWeight.bold),
                                              ))
                                          : switchStyle(
                                              value: controller.homeState,
                                              onChanged: (val) {
                                                commandHome(val!);
                                              }),
                                    ),
                                  ],
                                  leading: Row(
                                    children: [
                                      PopupMenuButton<String>(
                                        onOpened: () {
                                          setState(() {
                                            menublur = true;
                                          });
                                        },
                                        onCanceled: () {
                                          setState(() {
                                            menublur = false;
                                          });
                                        },
                                        padding: EdgeInsets.zero,
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(15)),
                                        color: AppColors.backgroundColor2
                                            .withOpacity(0.85),
                                        shadowColor: Colors.transparent,
                                        icon: Icon(
                                          Icons.home_work,
                                          color: const Color.fromARGB(
                                              255, 239, 235, 235),
                                          size: controller.sized * 0.027,
                                        ),
                                        offset: Offset(-25, 45),
                                        itemBuilder: (context) {
                                          final menuItems =
                                              <PopupMenuEntry<String>>[
                                            PopupMenuItem<String>(
                                              enabled: true,
                                              value: 'new',
                                              height:
                                                  controller.sizedHight * 0.035,
                                              child: Container(
                                                color: Colors.transparent,
                                                child: Row(
                                                  textDirection:
                                                      TextDirection.rtl,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Text(
                                                      'العثور على نظام جديد',
                                                      textDirection:
                                                          TextDirection.rtl,
                                                      style: TextStyle(
                                                          color: AppColors
                                                              .textColor
                                                              .withOpacity(0.8),
                                                          fontSize:
                                                              controller.sized *
                                                                  0.012,
                                                          fontWeight:
                                                              FontWeight.bold),
                                                    ),
                                                    iconStyle(
                                                        icon: Icons
                                                            .add_home_rounded,
                                                        color: AppColors
                                                            .warningColor,
                                                        size: controller.sized *
                                                            0.02),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            PopupMenuItem<String>(
                                                enabled: false,
                                                height: 0,
                                                padding: EdgeInsets.zero,
                                                child: Divider(
                                                  color: AppColors
                                                      .backgroundColor2
                                                      .withOpacity(0.5),
                                                  thickness: 6,
                                                )),
                                            PopupMenuItem<String>(
                                              value: controller.home.value,
                                              height:
                                                  controller.sizedHight * 0.035,
                                              child: Container(
                                                child: Row(
                                                  textDirection:
                                                      TextDirection.rtl,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Text(
                                                      controller.home.value,
                                                      textDirection:
                                                          TextDirection.rtl,
                                                      style: TextStyle(
                                                          color: AppColors
                                                              .textColor
                                                              .withOpacity(0.8),
                                                          fontSize:
                                                              controller.sized *
                                                                  0.012,
                                                          fontWeight:
                                                              FontWeight.bold),
                                                    ),
                                                    Icon(Icons.check,
                                                        color: AppColors
                                                            .primaryColor,
                                                        size: 20),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            if (controller.systems.length != 1)
                                              PopupMenuItem<String>(
                                                  enabled: false,
                                                  height: 0,
                                                  padding: EdgeInsets.zero,
                                                  child: Divider(
                                                      color: AppColors.textColor
                                                          .withOpacity(0.2))),
                                          ];
                                          for (int i = 0;
                                              i < controller.systems.length;
                                              i++) {
                                            final house = controller.systems[i];
                                            if (house['title'] !=
                                                controller.home.value) {
                                              menuItems.add(
                                                PopupMenuItem<String>(
                                                  value: house['title'],
                                                  height:
                                                      controller.sizedHight *
                                                          0.03,
                                                  child: Container(
                                                    child: Row(
                                                      textDirection:
                                                          TextDirection.rtl,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        Text(
                                                          house['title']
                                                              .toString(),
                                                          textDirection:
                                                              TextDirection.rtl,
                                                          style: TextStyle(
                                                              color: AppColors
                                                                  .textColor
                                                                  .withOpacity(
                                                                      0.5),
                                                              fontSize: controller
                                                                      .sized *
                                                                  0.012,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              );
                                              if (i <
                                                  controller.systems.length -
                                                      1) {
                                                menuItems.add(
                                                  PopupMenuItem<String>(
                                                      enabled: false,
                                                      height: 0,
                                                      padding: EdgeInsets.zero,
                                                      child: Divider(
                                                        color: AppColors
                                                            .textColor
                                                            .withOpacity(0.2),
                                                      )),
                                                );
                                              }
                                            }
                                          }

                                          return menuItems;
                                        },
                                        onSelected: (value) async {
                                          print(controller.tasks);
                                          setState(() {
                                            menublur = false;
                                          });
                                          if (value == 'new') {
                                            Get.toNamed('wait');
                                          } else if (value != 'x') {
                                            print('x');
                                          }
                                        },
                                        constraints: BoxConstraints(
                                          maxHeight:
                                              controller.sizedHight * 0.3,
                                          minWidth: controller.sizedWidth * 0.6,
                                          maxWidth: controller.sizedWidth * 0.6,
                                        ),
                                      ),
                                      if (Get.isRegistered<
                                          NotificationService>())
                                        GetBuilder<NotificationService>(
                                          init: NotificationService.instance,
                                          builder: (notificationService) {
                                            final activeCount =
                                                notificationService
                                                    .activeNotifications.length;
                                            return Stack(
                                              children: [
                                                IconButton(
                                                  padding: EdgeInsets.zero,
                                                  onPressed: () {
                                                    Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                        builder: (context) =>
                                                            const NotificationsSettingsPage(),
                                                      ),
                                                    );
                                                  },
                                                  icon: Icon(
                                                    Icons
                                                        .notifications_outlined,
                                                  ),
                                                  color: const Color.fromARGB(
                                                      255, 239, 235, 235),
                                                  iconSize:
                                                      controller.sized * 0.025,
                                                ),
                                                if (activeCount > 0)
                                                  Positioned(
                                                    right: 8,
                                                    top: 8,
                                                    child: Container(
                                                      padding:
                                                          EdgeInsets.all(2),
                                                      decoration: BoxDecoration(
                                                        color: Colors.red,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(10),
                                                      ),
                                                      constraints:
                                                          BoxConstraints(
                                                        minWidth: 16,
                                                        minHeight: 16,
                                                      ),
                                                      child: Text(
                                                        activeCount > 9
                                                            ? '9+'
                                                            : activeCount
                                                                .toString(),
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                          fontSize:
                                                              controller.sized *
                                                                  0.008,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                        textAlign:
                                                            TextAlign.center,
                                                      ),
                                                    ),
                                                  ),
                                              ],
                                            );
                                          },
                                        ),
                                    ],
                                  ),
                                  flexibleSpace: ClipRect(
                                    child: BackdropFilter(
                                      filter: ImageFilter.blur(
                                        sigmaX: p == 0 || scroll ? 75 : 0,
                                        sigmaY: p == 0 || scroll ? 75 : 0,
                                      ),
                                      child: FlexibleSpaceBar(
                                          centerTitle: true,
                                          titlePadding: EdgeInsets.only(
                                            bottom: p == 1
                                                ? controller.sizedHight * 0.06
                                                : scroll
                                                    ? controller.sizedHight *
                                                        0.015
                                                    : controller.sizedHight *
                                                        0.07,
                                          ),
                                          expandedTitleScale:
                                              controller.sized * 0.0015,
                                          title: Text(
                                            controller.home.value,
                                            style: TextStyle(
                                                color: const Color.fromARGB(
                                                    255, 239, 235, 235),
                                                fontSize:
                                                    controller.sized * 0.02,
                                                fontWeight: FontWeight.w700),
                                          ),
                                          background: p == 1
                                              ? Container(
                                                  child: Align(
                                                    alignment:
                                                        Alignment.bottomLeft,
                                                    child: Padding(
                                                      padding: EdgeInsets.all(
                                                          controller.sized *
                                                              0.005),
                                                      child: Row(
                                                        children: [
                                                          WeatherWidget(
                                                            isCompact: true,
                                                            onTap: () {
                                                              Get.to(() =>
                                                                  const WeatherDetailsPage());
                                                            },
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                )
                                              : Container(
                                                  decoration: BoxDecoration(
                                                    image: DecorationImage(
                                                      image: controller
                                                              .homeImage.value
                                                              .contains(
                                                                  'com.example.zaen')
                                                          ? FileImage(File(
                                                                  controller
                                                                      .homeImage
                                                                      .value))
                                                              as ImageProvider
                                                          : AssetImage(
                                                                  controller
                                                                      .homeImage
                                                                      .value)
                                                              as ImageProvider,
                                                      colorFilter:
                                                          ColorFilter.mode(
                                                        AppColors
                                                            .backgroundColor
                                                            .withOpacity(0.2),
                                                        BlendMode.darken,
                                                      ),
                                                      fit: BoxFit.cover,
                                                      filterQuality:
                                                          FilterQuality.high,
                                                    ),
                                                  ),
                                                  child: controller.homeId
                                                              .isNotEmpty &&
                                                          client.connectionStatus!
                                                                  .state.name ==
                                                              'connected'
                                                      ? Align(
                                                          alignment: Alignment
                                                              .bottomLeft,
                                                          child:
                                                              SingleChildScrollView(
                                                            scrollDirection:
                                                                Axis.horizontal,
                                                            child: Padding(
                                                              padding: EdgeInsets
                                                                  .all(controller
                                                                          .sized *
                                                                      0.005),
                                                              child: Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .spaceAround,
                                                                children: [
                                                                  WeatherWidget(
                                                                    isCompact:
                                                                        true,
                                                                    onTap: () {
                                                                      Get.to(() =>
                                                                          const WeatherDetailsPage());
                                                                    },
                                                                  ),
                                                                  SizedBox(
                                                                    width: controller
                                                                            .sizedWidth *
                                                                        0.02,
                                                                  ),
                                                                  WeatherWidget(
                                                                    isCompact:
                                                                        true,
                                                                    onTap: () {
                                                                      Get.to(() =>
                                                                          const WeatherDetailsPage());
                                                                    },
                                                                  ),
                                                                  SizedBox(
                                                                    width: controller
                                                                            .sizedWidth *
                                                                        0.02,
                                                                  ),
                                                                  WeatherWidget(
                                                                    isCompact:
                                                                        true,
                                                                    onTap: () {
                                                                      Get.to(() =>
                                                                          const WeatherDetailsPage());
                                                                    },
                                                                  ),
                                                                  SizedBox(
                                                                    width: controller
                                                                            .sizedWidth *
                                                                        0.02,
                                                                  ),
                                                                  WeatherWidget(
                                                                    isCompact:
                                                                        true,
                                                                    onTap: () {
                                                                      Get.to(() =>
                                                                          const WeatherDetailsPage());
                                                                    },
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        )
                                                      : Container(),
                                                )),
                                    ),
                                  ),
                                  pinned: true,
                                  floating: false,
                                  snap: false,
                                ),
                              )
                            ];
                          },
                          body: ConditionalBuilder(
                            condition: controller.homeId.isNotEmpty &&
                                client.connectionStatus!.state.name ==
                                    'connected',
                            fallback: (context) {
                              return const Center(
                                  child: CircularProgressIndicator());
                            },
                            builder: (context) {
                              client.subscribe(controller.homeId + "/app/phone",
                                  MqttQos.atLeastOnce);
                              client.subscribe("edit", MqttQos.atLeastOnce);
                              client.subscribe(
                                  controller.uuid, MqttQos.atLeastOnce);

                              return PageView(
                                  onPageChanged: ((value) {
                                    setState(() {
                                      p = value;
                                    });
                                    if (p == 1) {
                                      scroll = false;
                                      WidgetsBinding.instance
                                          .addPostFrameCallback((_) {
                                        if (_scrollController.hasClients) {
                                          _scrollController.jumpTo(0.0);
                                        }
                                      });
                                    }
                                  }),
                                  controller: _pageController,
                                  children: [
                                    SingleChildScrollView(
                                      physics: BouncingScrollPhysics(),
                                      child: Column(
                                        children: [
                                          const NotificationBanner(),
                                          for (dynamic i
                                              in controller.rooms.values)
                                            (shortcutRoom(
                                              sizedWidth: controller.sizedWidth,
                                              sizedHeight:
                                                  controller.sizedHight,
                                              sized: controller.sized,
                                              connect:
                                                  controller.devices[i['id']],
                                              onTap: () {
                                                controller.roomData = i;
                                                Get.toNamed('room');
                                              },
                                              roomName: i['pubName'],
                                              roomPrivName: i['privName'],
                                              roomState: i['state'],
                                              switchState: (val) {
                                                commandRoom(val!, i['id']);
                                              },
                                              image: i['image']
                                                      .toString()
                                                      .contains(
                                                          'com.example.zaen')
                                                  ? FileImage(File(i['image']))
                                                  : AssetImage("${i['image']}"),
                                            )),
                                          const SizedBox(
                                            height: 30,
                                          ),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal:
                                              controller.sizedWidth * 0.02),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          Text(
                                            'اختصارات سريعة',
                                            textAlign: TextAlign.justify,
                                            textDirection: TextDirection.rtl,
                                            style: TextStyle(
                                                color: AppColors
                                                    .backgroundColor2
                                                    .withOpacity(0.65),
                                                fontSize:
                                                    controller.sized * 0.011,
                                                fontWeight: FontWeight.bold),
                                          ),
                                          SizedBox(
                                            height:
                                                controller.sizedHight * 0.005,
                                          ),
                                          routine(context),
                                          Divider(
                                            color: AppColors.backgroundColor
                                                .withOpacity(0.2),
                                            endIndent:
                                                controller.sizedWidth * 0.1,
                                          ),
                                          Padding(
                                            padding: EdgeInsets.zero,
                                            child: Row(
                                              textDirection: TextDirection.rtl,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Expanded(
                                                  child: Text(
                                                    'الملحقات المفضلة',
                                                    textAlign:
                                                        TextAlign.justify,
                                                    textDirection:
                                                        TextDirection.rtl,
                                                    style: TextStyle(
                                                        color: AppColors
                                                            .backgroundColor2
                                                            .withOpacity(0.65),
                                                        fontSize:
                                                            controller.sized *
                                                                0.011,
                                                        fontWeight:
                                                            FontWeight.bold),
                                                  ),
                                                ),
                                                editFavorite
                                                    ? CircularPercentIndicator(
                                                        radius:
                                                            controller.sized *
                                                                0.0175,
                                                        lineWidth:
                                                            controller.sized *
                                                                0.01,
                                                        backgroundWidth:
                                                            controller.sized *
                                                                0.001,
                                                        backgroundColor:
                                                            AppColors
                                                                .primaryColor
                                                                .withOpacity(
                                                                    0.7),
                                                        center: IconButton(
                                                            padding:
                                                                EdgeInsets.zero,
                                                            color: AppColors
                                                                .primaryColor
                                                                .withOpacity(
                                                                    0.8),
                                                            iconSize: controller
                                                                    .sized *
                                                                0.0225,
                                                            onPressed:
                                                                () async {
                                                              print(controller
                                                                  .rooms);
                                                              setState(() {
                                                                editFavorite =
                                                                    false;
                                                              });
                                                              var appDB =
                                                                  await openDatabase(
                                                                      '${controller.system}.db',
                                                                      version:
                                                                          3);
                                                              await appDB.rawQuery(
                                                                  "DELETE FROM favorite");
                                                              for (var i
                                                                  in controller
                                                                      .favorite) {
                                                                await appDB
                                                                    .rawQuery(
                                                                        'insert into favorite(id) values(?)',
                                                                        [i]);
                                                              }
                                                            },
                                                            icon: Icon(Icons
                                                                .check_circle)),
                                                      )
                                                    : CircularPercentIndicator(
                                                        radius:
                                                            controller.sized *
                                                                0.0175,
                                                        lineWidth:
                                                            controller.sized *
                                                                0.01,
                                                        backgroundWidth:
                                                            controller.sized *
                                                                0.001,
                                                        backgroundColor:
                                                            AppColors
                                                                .backgroundColor
                                                                .withOpacity(
                                                                    0.3),
                                                        center: IconButton(
                                                            padding:
                                                                EdgeInsets.zero,
                                                            color: AppColors
                                                                .backgroundColor
                                                                .withOpacity(
                                                                    0.5),
                                                            iconSize: controller
                                                                    .sized *
                                                                0.0225,
                                                            onPressed: () {
                                                              setState(() {
                                                                editFavorite =
                                                                    true;
                                                              });
                                                            },
                                                            icon: Icon(Icons
                                                                .change_circle)),
                                                      ),
                                                editFavorite
                                                    ? CircularPercentIndicator(
                                                        radius:
                                                            controller.sized *
                                                                0.0175,
                                                        lineWidth:
                                                            controller.sized *
                                                                0.01,
                                                        backgroundWidth:
                                                            controller.sized *
                                                                0.001,
                                                        backgroundColor:
                                                            AppColors
                                                                .errorColor
                                                                .withOpacity(
                                                                    0.7),
                                                        center: IconButton(
                                                            padding:
                                                                EdgeInsets.zero,
                                                            color: AppColors
                                                                .errorColor
                                                                .withOpacity(
                                                                    0.8),
                                                            iconSize: controller
                                                                    .sized *
                                                                0.0225,
                                                            onPressed: () {
                                                              setState(() {
                                                                editFavorite =
                                                                    false;
                                                              });
                                                              final builder =
                                                                  MqttClientPayloadBuilder();
                                                              builder.addString(
                                                                  're');
                                                              builder.addString(
                                                                  '1');
                                                              client.publishMessage(
                                                                  'edit',
                                                                  MqttQos
                                                                      .atLeastOnce,
                                                                  builder
                                                                      .payload!);
                                                            },
                                                            icon: Icon(
                                                                Icons.cancel)),
                                                      )
                                                    : Container()
                                              ],
                                            ),
                                          ),
                                          SizedBox(
                                            height:
                                                controller.sizedHight * 0.005,
                                          ),
                                          editFavorite
                                              ? EditFavorite()
                                              : favorite(),
                                          Divider(
                                            color: AppColors.border
                                                .withOpacity(0.4),
                                            endIndent:
                                                controller.sizedWidth * 0.1,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ]);
                            },
                          ),
                        ),
                        if (menublur)
                          BackdropFilter(
                            blendMode: BlendMode.srcOver,
                            filter: ImageFilter.blur(
                              sigmaX: 10,
                              sigmaY: 10,
                            ),
                            child: Container(
                              height: MediaQuery.of(context).size.height,
                              width: MediaQuery.of(context).size.width,
                            ),
                          ),
                      ],
                    ),
                  ),
                )));
  }
}
