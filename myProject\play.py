import os
import subprocess
import signal
import threading
import time
from datetime import datetime
import mysql.connector
import paho.mqtt.publish as publish
from wireless import Wireless
import netifaces as ni
import requests
import RPi.GPIO as GPIO
import sys
import json

sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/nlt')
sys.path.append('/home/<USER>/myProject/modules')
sys.path.append('/home/<USER>/myProject/processing')
sys.path.append('/home/<USER>/myProject/mqtt')
sys.path.append('/home/<USER>')
import process as process
import listen as listen
import nw as nw
import tts as tts
import static as st
import serialNumber as serialNumber
import alarm as alarm

# استيراد نظام التكامل مع Home Assistant
try:
    from ha import HomeAssistantIntegration
    from ha_service import HAService
    from automation_engine import AutomationEngine
    HA_INTEGRATION_AVAILABLE = True
    print("✅ تم تحميل نظام التكامل مع Home Assistant")
except ImportError as e:
    print(f"⚠️ نظام التكامل مع Home Assistant غير متاح: {e}")
    HA_INTEGRATION_AVAILABLE = False


class run_system:
    def __init__(self):
        self.db = mysql.connector.connect(user='root', host=st.ip, passwd='zain', database='zain')
        self.cursor = self.db.cursor(dictionary=True)
        self.tts = tts.SmartHomeTTSCache()
        self.timerState = False
        self.timer=None
        self.ap=None
        self.main=None
        self.check=None

        # متغيرات نظام التكامل مع Home Assistant
        self.ha_integration = None
        self.ha_service = None
        self.automation_engine = None
        self.ha_running = False

    def start_ha_integration(self):
        """بدء نظام التكامل مع Home Assistant"""
        if not HA_INTEGRATION_AVAILABLE:
            print("⚠️ نظام التكامل مع Home Assistant غير متاح")
            return False

        try:
            print("🚀 بدء نظام التكامل مع Home Assistant...")

            # إنشاء كائنات النظام (بدون MQTT منفصل)
            self.ha_integration = HomeAssistantIntegration()

            # إنشاء الجداول المطلوبة فقط
            print("📊 إنشاء جداول قاعدة البيانات...")
            self.ha_integration.create_required_tables()

            # ملاحظة: خدمات MQTT يتم تشغيلها من run.py لتجنب التداخل
            print("ℹ️ خدمات MQTT ستعمل من النظام الرئيسي (run.py)")

            # بدء المزامنة الدورية في خيط منفصل
            print("🔄 بدء المزامنة الدورية...")
            sync_thread = threading.Thread(
                target=self.periodic_ha_sync,
                name="HASync",
                daemon=True
            )
            sync_thread.start()


            self.ha_running = True
            print("✅ تم بدء نظام التكامل مع Home Assistant بنجاح")
            return True

        except Exception as e:
            print(f"❌ فشل في بدء نظام التكامل مع Home Assistant: {e}")
            return False

    def periodic_ha_sync(self):
        """المزامنة الدورية مع Home Assistant"""
        while self.ha_running:
            try:
                if self.ha_integration:
                    processed = self.ha_integration.process_entities()
                    current_time = datetime.now().strftime("%H:%M:%S")
                    print(f"🔄 [{current_time}] تم معالجة {processed} كيان من Home Assistant")

                # انتظار 5 دقائق قبل المزامنة التالية
                time.sleep(300)

            except Exception as e:
                print(f"❌ خطأ في المزامنة الدورية: {e}")
                time.sleep(60)  # انتظار دقيقة قبل المحاولة مرة أخرى

    
    def reset(self):
        if (GPIO.input(17)):
            print('اعاده تشغيل نقطه الوصول')
            self.tts.say('beep_lo')
            nw.start_hotspot()
            AP=open(st.pathFiles+'ap.txt','w')
            AP.write('1')
            AP.close()
            time.sleep(4)
    def checkInternetRequests(self,url='http://www.google.com/', timeout=12):
        
        try:
            #r = requests.get(url, timeout=timeout)
            r = requests.head(url, timeout=timeout)
            check=open(st.pathFiles+'check.txt','w')
            check.write('1')
            check.close()
            self.check=True
        except requests.ConnectionError as ex:
#             print(ex)
            check=open(st.pathFiles+'check.txt','w')
            check.write('0')
            check.close()
            self.check=False
        time.sleep(2)

    def wav_listen(self):
        IP = ni.ifaddresses('wlan0')[ni.AF_INET][0]['addr']
                
        while 1:
            # تقليل رسائل التشخيص المتكررة
            if not hasattr(self, '_debug_counter'):
                self._debug_counter = 0

            self._debug_counter += 1

            # طباعة رسائل التشخيص كل 10 مرات فقط
            if self._debug_counter % 10 == 1:
                print("🔄 فحص دوري للنظام...")

            try:
                self.reset()
                AP=open(st.pathFiles+'ap.txt','r')
                self.ap=AP.readline()
                AP.close()

                ip=ni.ifaddresses('wlan0')[ni.AF_INET][0]['addr']

                # طباعة IP فقط عند التغيير
                if not hasattr(self, '_last_ip') or self._last_ip != ip:
                    print(f"📡 عنوان IP: {ip}")
                    self._last_ip = ip
                if IP != ip:
                    IP = ni.ifaddresses('wlan0')[ni.AF_INET][0]['addr']
                    self.WIFI()
                else:
                    self.checkInternetRequests()
                    now = datetime.now()
    #                     print(now.second)
                    if now.second > 40 and self.check:
    #                         print(62 - now.second)
                        time.sleep(62 - now.second)
                        alarm.alarms(self,)
                        self.updateRoutineWords
                    elif self.check:
                        self.updateRoutineWords
                    else:
                        time.sleep(2)
                        
                    
            except Exception as e:
                # طباعة الأخطاء فقط عند الحاجة
                if self._debug_counter % 10 == 1:
                    print(f"⚠️ خطأ في النظام: {e}")
                time.sleep(10)
            
    def updateRoutineWords(self):
        try:
            self.cursor.execute("SELECT * FROM RDevice")
            
            results = self.cursor.fetchall()
            with open(st.pathFiles + 'words.txt', 'w', encoding='utf-8') as words_file:
                json.dump(results, words_file, ensure_ascii=False, indent=4)
        except:
            print('321')
        time.sleep(2)
    def app(self):
        """تشغيل تطبيق MQTT والخادم"""

        # يمكن اختيار النظام الجديد أو الأصلي
        try:
            # محاولة تشغيل النظام الجديد المحسن أولاً
            if os.path.exists("/home/<USER>/myProject/mqtt/run.py"):
                print("تشغيل النظام الجديد المحسن...")
                subprocess.run("python3 /home/<USER>/myProject/mqtt/run.py", shell=True)
            
        except Exception as e:
            print(f"خطأ في تشغيل التطبيق: {e}")
            # محاولة أخيرة مع النظام الأصلي
            
        

    def WIFI(self):
        
        wire = Wireless()
        wire.interface('wlan0')
        
        if self.ap != '1':
            while wire.current()==None:
                time.sleep(9)
                if wire.current()!=None:
                    break
                if wire.current()==None:
                    subprocess.run("aplay --format=S16_LE --rate=44100 /home/<USER>/myProject/resources/wav/error.wav", shell=True)
        if self.main == '1':
            print("🔧 إعداد النظام الرئيسي...")
            while 1:
                try:
                    IP = ni.ifaddresses('wlan0')[ni.AF_INET][0]['addr']
                    break
                except:
                    continue
            print("📡 تم الحصول على عنوان IP")
            c=open(st.pathFiles+'hosts1.txt','w')
            c.write('''127.0.0.1    localhost
    ::1          localhost ip6-localhost ip6-loopback
    ff02::1      ip6-allnodes
    ff02::2      ip6-allrouters
    %s           zain
        '''%(IP))

            c.close()
        else :
            c=open(st.pathFiles+'hosts1.txt','w')
            c.write('''127.0.0.1    localhost
    ::1          localhost ip6-localhost ip6-loopback
    ff02::1      ip6-allnodes
    ff02::2      ip6-allrouters
    127.0.0.1    zain
        ''')

            c.close()
        time.sleep(1)
        subprocess.Popen("sudo cp "+st.pathFiles+"hosts1.txt /etc/hosts", shell=True , preexec_fn=os.setsid)
        time.sleep(1)
        subprocess.Popen("sudo systemctl restart dnsmasq", shell=True , preexec_fn=os.setsid)
        time.sleep(5)
        print("🗄️ الاتصال بقاعدة البيانات...")
        self.db = mysql.connector.connect(user='root', host=st.ip, passwd='zain', database='zain')
        self.cursor = self.db.cursor(dictionary=True)
        time.sleep(20)

    def close(self):
            """إغلاق الاتصال بقاعدة البيانات ونظام التكامل"""
            print("⏹️ إيقاف نظام زين للبيت الذكي...")

            # إيقاف نظام التكامل مع Home Assistant
            self.ha_running = False

            if self.ha_service and hasattr(self.ha_service, 'stop_service'):
                try:
                    self.ha_service.stop_service()
                    print("✅ تم إيقاف خدمة Home Assistant")
                except Exception as e:
                    print(f"❌ خطأ في إيقاف خدمة Home Assistant: {e}")

            if self.automation_engine and hasattr(self.automation_engine, 'stop_engine'):
                try:
                    self.automation_engine.stop_engine()
                    print("✅ تم إيقاف محرك الأتمتة")
                except Exception as e:
                    print(f"❌ خطأ في إيقاف محرك الأتمتة: {e}")

            # إغلاق قاعدة البيانات
            if self.cursor:
                self.cursor.close()
            if self.db:
                self.db.close()

            print("✅ تم إيقاف جميع الخدمات بنجاح")

    def start(self):
        serialNumber.serialN()

        GPIO.setmode(GPIO.BCM)
        GPIO.setup(17, GPIO.IN, pull_up_down=GPIO.PUD_DOWN)


        M=open(st.pathFiles+'main.txt','r')
        self.main=M.readline()
        M.close()

        IP = ni.ifaddresses('wlan0')[ni.AF_INET][0]['addr']
        print(IP)
        self.WIFI()
        print("🔊 تشغيل صوت البدء...")
        self.tts.say('beep_hi')

        # بدء نظام التكامل مع Home Assistant
        print("🏠 بدء نظام زين للبيت الذكي...")
        ha_started = self.start_ha_integration()
        if ha_started:
            print("✅ نظام التكامل مع Home Assistant جاهز")
        else:
            print("⚠️ سيتم تشغيل النظام بدون تكامل Home Assistant")

        s = threading.Thread(target=self.app)

        assistant = listen.VoiceAssistant(self)
        process_thread = threading.Thread(
            target= assistant.run,)
        process_thread.daemon = True
        process_thread.start()
        s.start()
        print("🚀 جميع خدمات النظام تعمل الآن")
        while 1:
            try:
                self.cursor.execute("SELECT Rooms FROM Devices WHERE id LIKE '%s'"% (st.device_nb))
                result = self.cursor.fetchone()
            except:
                continue
            print(result)
            if result:
                room=result['Rooms']
            else:
                room='x'
                try:
                    self.cursor.execute("INSERT INTO Devices(id,type,rooms) values(%s,%s,%s)", (st.device_nb,'ZAIN-Main',room))
                    self.db.commit()
                except:
                    continue
            break
        self.wav_listen()

play = run_system()
play.start()