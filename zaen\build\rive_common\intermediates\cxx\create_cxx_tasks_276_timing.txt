# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 12ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    [gap of 85ms]
    create-X86-model 10ms
    [gap of 11ms]
  create-initial-cxx-model completed in 140ms
create_cxx_tasks completed in 144ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 95ms
create_cxx_tasks completed in 98ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 17ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 27ms
    create-ARM64_V8A-model 20ms
    create-X86-model 58ms
    create-X86_64-model 21ms
    create-module-model 19ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 19ms
    create-X86-model 13ms
    create-X86_64-model 34ms
    create-module-model 11ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 15ms
    create-X86-model 15ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 373ms
create_cxx_tasks completed in 382ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-ARMEABI_V7A-model 13ms
    create-X86-model 14ms
    create-X86_64-model 11ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 20ms
    create-X86-model 14ms
    [gap of 50ms]
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 199ms
create_cxx_tasks completed in 206ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 10ms
    create-X86-model 16ms
    create-X86_64-model 12ms
    create-module-model 11ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 11ms
    create-X86-model 10ms
    create-X86_64-model 13ms
    [gap of 34ms]
    create-X86-model 10ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 205ms
create_cxx_tasks completed in 211ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 13ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 15ms
    create-X86_64-model 10ms
    create-module-model
      [gap of 17ms]
      create-ndk-meta-abi-list 21ms
    create-module-model completed in 38ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 50ms
    [gap of 15ms]
    create-X86-model 18ms
    create-X86_64-model 36ms
    create-module-model
      create-project-model 11ms
      create-ndk-meta-abi-list 12ms
    create-module-model completed in 30ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 16ms
    create-X86-model 24ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 363ms
  [gap of 15ms]
create_cxx_tasks completed in 378ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    [gap of 24ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    [gap of 10ms]
    create-X86_64-model 10ms
    create-module-model 10ms
    [gap of 34ms]
  create-initial-cxx-model completed in 161ms
create_cxx_tasks completed in 168ms

