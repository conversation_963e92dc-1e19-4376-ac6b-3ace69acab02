import 'dart:io';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/themes/app_typography.dart';

// المتغيرات العامة
int? pageHor;
int? pageVer;
int? _activePage;

Widget RoomPage({
  String? roomPrivName,
  var image,
  String? homeType,
  String place = 'الغرفة',
  Function()? addRoom,
  Function()? nunDevices,
  required Function() del,
  required Function() asset,
  required Function() roll,
  required Function() camera,
  required Function(String?) editNames,
  required Function(bool?, String?) editPrivName,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  TextEditingController editPriv = TextEditingController(
    text: roomPrivName != 'x' ? roomPrivName : 'X',
  );
  bool privN = false;

  return pageSlide(
    content: GestureDetector(
      onTap: () {
        if (privN) {
          if (editPriv.text == '' ||
              editPriv.text == null ||
              editPriv.text == 'X' ||
              editPriv.text == 'x') {
            editPriv.text = roomPrivName != null ? roomPrivName : 'X';
            privN = false;
          } else {
            for (var i = 0; i < editPriv.text.length; i++) {
              if (arabic.contains(editPriv.text[i]) ||
                  editPriv.text[i].isNumericOnly) {
                privN = true;
              } else {
                editPriv.text = roomPrivName != null ? roomPrivName : 'X';
                privN = false;
                break;
              }
            }

            if (privN) {
              editPrivName(privN, editPriv.text);
              privN = false;
            }
          }
        }
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: PageView(
          scrollDirection: Axis.vertical,
          // shrinkWrap: true,
          onPageChanged: (i) {
            FocusManager.instance.primaryFocus?.unfocus();
            editPriv.text = roomPrivName != null ? roomPrivName : 'X';
          },
          physics: BouncingScrollPhysics(),
          children: [
            SingleChildScrollView(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: sizedWidth * 0.03),
                child: Column(
                  children: [
                    Container(
                      width: sizedWidth * 0.85,
                      child: TextFormField(
                        controller: editPriv,
                        maxLength: 16,
                        showCursor: true,
                        cursorColor: AppColors.primary,
                        textDirection: TextDirection.rtl,
                        style: TextStyle(
                          color: AppColors.textPrimary,
                          fontSize: sized * 0.015,
                          fontWeight: FontWeight.w500,
                        ),
                        onChanged: (i) {
                          privN = true;
                        },
                        onEditingComplete: () {
                          FocusManager.instance.primaryFocus?.unfocus();
                          if (editPriv.text == '' ||
                              editPriv.text == null ||
                              editPriv.text == 'X' ||
                              editPriv.text == 'x') {
                            editPriv.text =
                                roomPrivName != null ? roomPrivName : 'X';
                            privN = false;
                          } else if (editPriv.text != roomPrivName) {
                            for (var i = 0; i < editPriv.text.length; i++) {
                              if (arabic.contains(editPriv.text[i]) ||
                                  editPriv.text[i].isNumericOnly) {
                                privN = true;
                              } else {
                                editPriv.text =
                                    roomPrivName != null ? roomPrivName : 'X';
                                privN = false;
                                break;
                              }
                            }
                            if (privN) {
                              editPrivName(privN, editPriv.text);
                              privN = false;
                            }
                          }
                        },
                        decoration: InputDecoration(
                          hintText: 'اسم $place الخاص',
                          hintStyle: TextStyle(
                            color: AppColors.textHint,
                            fontSize: sized * 0.014,
                            fontWeight: FontWeight.normal,
                          ),
                          filled: true,
                          fillColor: AppColors.surface,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: sizedWidth * 0.04,
                            vertical: sizedHeight * 0.015,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppColors.border,
                              width: 1.0,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppColors.border,
                              width: 1.0,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppColors.primary,
                              width: 2.0,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                    place == 'الغرفة'
                        ? containerPageOption(
                            content: Column(
                              children: [
                                MaterialButton(
                                  padding: EdgeInsets.symmetric(horizontal: 0),
                                  onPressed: () {
                                    editNames('show');
                                  },
                                  child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.arrow_back_ios,
                                          size: sized * 0.02,
                                          color: AppColors.subtitleColor,
                                        ),
                                        Expanded(
                                            child: SizedBox(
                                                width: double.infinity)),
                                        Text(
                                          'اسماء و صفات $place العامة',
                                          textDirection: TextDirection.rtl,
                                          style: AppTypography.bodySmall
                                              .copyWith(
                                                  fontSize: sized * 0.015,
                                                  color:
                                                      AppColors.subtitleColor),
                                        ),
                                      ]),
                                ),
                                Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      IconButton(
                                        onPressed: () {
                                          editNames('edit');
                                        },
                                        icon: Icon(Icons.edit_outlined),
                                        color: AppColors.infoColor,
                                        iconSize: sized * 0.03,
                                      ),
                                      SizedBox(
                                        width: sizedWidth * 0.075,
                                      ),
                                      IconButton(
                                        onPressed: () {
                                          editNames('del');
                                        },
                                        icon: Icon(Icons.delete_sweep_rounded),
                                        color: Color.fromARGB(255, 243, 33, 18),
                                        iconSize: sized * 0.032,
                                      ),
                                      SizedBox(
                                        width: sizedWidth * 0.075,
                                      ),
                                      IconButton(
                                        onPressed: () {
                                          editNames('add');
                                        },
                                        icon: Icon(Icons.add_rounded),
                                        color: AppColors.primaryColor,
                                        iconSize: sized * 0.032,
                                      ),
                                    ]),
                              ],
                            ),
                          )
                        : Column(
                            children: [
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: controller.sizedWidth * 0.02),
                                  onPressed: (() {}),
                                  child: Row(children: [
                                    iconStyle(
                                        icon: Icons.menu_open_rounded,
                                        size: controller.sized * 0.025),
                                    Expanded(
                                        child: txtStyle(
                                            txt: 'الكلمات الروتينية',
                                            align: TextAlign.start))
                                  ]),
                                ),
                              ),
                              SizedBox(
                                height: sizedHeight * 0.015,
                              ),
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: controller.sizedWidth * 0.02),
                                  onPressed: (() {}),
                                  child: Row(children: [
                                    iconStyle(
                                        icon: Icons.more_time_rounded,
                                        color: AppColors.warningColor,
                                        size: controller.sized * 0.025),
                                    Expanded(
                                        child: txtStyle(
                                            txt: 'المهام المجدولة',
                                            align: TextAlign.start))
                                  ]),
                                ),
                              ),
                              SizedBox(
                                height: sizedHeight * 0.015,
                              ),
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: controller.sizedWidth * 0.02),
                                  onPressed: (() {}),
                                  child: Row(children: [
                                    iconStyle(
                                        icon: Icons.people,
                                        color: AppColors.warningColor,
                                        size: controller.sized * 0.025),
                                    Expanded(
                                        child: txtStyle(
                                            txt: 'الاشخاص',
                                            align: TextAlign.start))
                                  ]),
                                ),
                              ),
                              SizedBox(
                                height: sizedHeight * 0.015,
                              ),
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.only(
                                      right: controller.sizedWidth * 0.015),
                                  onPressed: (() {}),
                                  child: Row(children: [
                                    switchStyle(
                                        onChanged: (val) {},
                                        value: false,
                                        size: controller.sized * 0.001),
                                    Expanded(
                                        child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.end,
                                            children: [
                                          txtStyle(
                                              txt: 'التوقيت الصيفي',
                                              align: TextAlign.start),
                                          SizedBox(
                                            width: controller.sizedWidth * 0.01,
                                          ),
                                          iconStyle(
                                              icon: Icons.bedtime,
                                              color: AppColors.warningColor,
                                              size: controller.sized * 0.02),
                                        ]))
                                  ]),
                                ),
                              ),
                              SizedBox(
                                height: sizedHeight * 0.015,
                              ),
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: controller.sizedWidth * 0.02),
                                  onPressed: (() {}),
                                  child: Row(children: [
                                    iconStyle(
                                        icon: Icons.menu_open_rounded,
                                        size: controller.sized * 0.025),
                                    Expanded(
                                        child: txtStyle(
                                            txt: 'المنطقة : قلقيلية',
                                            align: TextAlign.start))
                                  ]),
                                ),
                              ),
                              SizedBox(
                                height: sizedHeight * 0.015,
                              ),
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: sizedWidth * 0.025),
                                  onPressed: () {
                                    addRoom!();
                                  },
                                  child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.add_rounded,
                                          size: sized * 0.025,
                                          color: AppColors.primaryColor
                                              .withOpacity(0.6),
                                        ),
                                        Expanded(
                                            child: SizedBox(
                                                width: double.infinity)),
                                        Text(
                                          'اضافه غرفة',
                                          textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                              fontSize: sized * 0.015,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.textColor
                                                  .withOpacity(0.6)),
                                        ),
                                      ]),
                                ),
                              ),
                              SizedBox(
                                height: sizedHeight * 0.015,
                              ),
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: sizedWidth * 0.025),
                                  onPressed: nunDevices,
                                  child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.dns_rounded,
                                          size: sized * 0.025,
                                          color: AppColors.textColor
                                              .withOpacity(0.6),
                                        ),
                                        Expanded(
                                            child: SizedBox(
                                                width: double.infinity)),
                                        Text(
                                          'الملحقات المتصلة',
                                          textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                              fontSize: sized * 0.015,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.textColor
                                                  .withOpacity(0.6)),
                                        ),
                                      ]),
                                ),
                              ),
                              SizedBox(
                                height: sizedHeight * 0.015,
                              ),
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: sizedWidth * 0.025),
                                  onPressed: () {},
                                  child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.wifi_rounded,
                                          size: sized * 0.025,
                                          color:
                                              Colors.lightBlue.withOpacity(0.6),
                                        ),
                                        Expanded(
                                            child: SizedBox(
                                                width: double.infinity)),
                                        Text(
                                          'ادارة الشبكة و المستخدمين',
                                          textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                              fontSize: sized * 0.015,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.textColor
                                                  .withOpacity(0.6)),
                                        ),
                                      ]),
                                ),
                              ),
                            ],
                          ),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                    containerPageOption(
                        ver: 0.015,
                        content: Column(
                          children: [
                            Container(
                              height: sizedHeight * 0.2,
                              width: sizedWidth * 0.85,
                              child: image!.contains('com.example.zaen')
                                  ? Image.file(
                                      File(image),
                                      color: AppColors.subtitleColor
                                          .withOpacity(0.2),
                                      colorBlendMode: BlendMode.darken,
                                      fit: BoxFit.cover,
                                      filterQuality: FilterQuality.high,
                                    )
                                  : Image.asset(
                                      image.isNotEmpty
                                          ? image
                                          : "assets/images/default_bg.png",
                                      color: AppColors.subtitleColor
                                          .withOpacity(0.2),
                                      colorBlendMode: BlendMode.darken,
                                      fit: BoxFit.cover,
                                      filterQuality: FilterQuality.high,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return Container(
                                          color: AppColors.backgroundColor,
                                          child: Icon(
                                            Icons.image_not_supported,
                                            color: AppColors.textHint,
                                            size: 50,
                                          ),
                                        );
                                      },
                                    ),
                            ),
                            SizedBox(height: sizedHeight * 0.01),
                            MaterialButton(
                              padding: EdgeInsets.symmetric(
                                  horizontal: sizedWidth * 0.01),
                              onPressed: asset,
                              child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.arrow_back_ios,
                                      size: sized * 0.02,
                                      color:
                                          AppColors.textColor.withOpacity(0.6),
                                    ),
                                    Expanded(
                                        child:
                                            SizedBox(width: double.infinity)),
                                    Text(
                                      'اختيار صوره من الموجود',
                                      textDirection: TextDirection.rtl,
                                      style: TextStyle(
                                          fontSize: sized * 0.015,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.textColor
                                              .withOpacity(0.6)),
                                    ),
                                  ]),
                            ),
                            SizedBox(height: sizedHeight * 0.01),
                            MaterialButton(
                              padding: EdgeInsets.symmetric(
                                  horizontal: sizedWidth * 0.01),
                              onPressed: roll,
                              child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.camera_rounded,
                                      size: sized * 0.02,
                                      color:
                                          AppColors.textColor.withOpacity(0.6),
                                    ),
                                    Expanded(
                                        child:
                                            SizedBox(width: double.infinity)),
                                    Text(
                                      'اختيار صوره من البوم الصور',
                                      textDirection: TextDirection.rtl,
                                      style: TextStyle(
                                          fontSize: sized * 0.015,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.textColor
                                              .withOpacity(0.6)),
                                    ),
                                  ]),
                            ),
                            SizedBox(height: sizedHeight * 0.01),
                            MaterialButton(
                              padding: EdgeInsets.symmetric(
                                  horizontal: sizedWidth * 0.01),
                              onPressed: camera,
                              child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.camera_alt_rounded,
                                      size: sized * 0.02,
                                      color:
                                          AppColors.textColor.withOpacity(0.6),
                                    ),
                                    Expanded(
                                        child:
                                            SizedBox(width: double.infinity)),
                                    Text(
                                      'التقاط صورة',
                                      textDirection: TextDirection.rtl,
                                      style: TextStyle(
                                          fontSize: sized * 0.015,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.textColor
                                              .withOpacity(0.6)),
                                    ),
                                  ]),
                            ),
                          ],
                        )),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                    containerPageOption(
                      content: MaterialButton(
                          padding: EdgeInsets.zero,
                          onPressed: del,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              txtStyle(
                                  txt: 'إزالة $place',
                                  color: AppColors.errorColor),
                            ],
                          )),
                    ),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                  ],
                ),
              ),
            )
          ]),
    ),
  );
}
