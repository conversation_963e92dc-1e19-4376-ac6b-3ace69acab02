---
configuration:
  repository:
    name: Repository
    description: Git URL to your repository.
  git_branch:
    name: Git Branch
    description: >-
      Branch name of the Git repo. If left empty, the currently checked out
      branch will be updated. Leave this as 'master' if you are unsure.
  git_remote:
    name: Git Remote
    description: >-
      Name of the tracked repository. Leave this as `origin` if you are unsure.
  auto_restart:
    name: Auto restart
    description: >-
      Restart Home Assistant when the configuration has changed (and is valid).
  restart_ignore:
    name: Restart Ignore
    description: >-
      When `auto_restart` is enabled, changes to these files will not make HA
      restart. Full directories to ignore can be specified.
  git_command:
    name: Git Command
    description: Command to run. Leave this as `pull` if you are unsure.
  git_prune:
    name: Git Prune
    description: >-
      If enabled, the add-on will clean-up branches that are deleted on the
      remote repository, but still have cached entries on the local machine.
      Leave this as `false` if you are unsure.
  deployment_key:
    name: Deployment Key
    description: >-
      A private SSH key that will be used for communication during Git
      operations.
  deployment_user:
    name: Deployment User
    description: >-
      Username to use when authenticating to a repository with a username and
      password.
  deployment_password:
    name: Deployment Password
    description: >-
      Password to use when authenticating to a repository.  Ignored if
      `deployment_user` is not set.
  deployment_key_protocol:
    name: Deployment Key Protocol
    description: The key protocol
  repeat:
    name: Polling
    description: >-
      Configure the Git pull add-on to poll the repository for updates
      periodically automatically.
