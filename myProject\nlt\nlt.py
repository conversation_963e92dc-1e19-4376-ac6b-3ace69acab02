import sys
sys.path.append('/home/<USER>/myProject/resources')
sys.path.append('/home/<USER>/myProject/nlt')
sys.path.append('/home/<USER>/myProject/modules')
import static as st


timerState='OFF'
# إزالة التشكيلات و توحيد الاحرف المتشابهه
def edit_char(phrase):
    # قائمة الكلمات التي تحتوي على "ال" الأصلية
    excluded_words = {"الله", "الان", "الي", "الغي", "اللغاء", "الف", "الفين", "الفان", "الاف", "المانيا", "الا"
                      "اللهم","اللواتي","التي","الذي","اللاتي"}
    
    # إزالة المسافات الزائدة من البداية والنهاية
    phrase = phrase.strip()
    
    # استبدال الحروف والعلامات
    replacements = {
        'أ': 'ا', 'إ': 'ا', 'ئ': 'ء', 'ة': 'ه', 'ى': 'ي', 'ؤ': 'ء',
        'َ': '', 'ً': '', 'ُ': '', 'ٌ': '', 'ِ': '', 'ٍ': '', 'ّ': '', 'ْ': '',
        'آ': 'ا', '`': ''
    }
    for old, new in replacements.items():
        phrase = phrase.replace(old, new)
    
    # إزالة "ال" التعريف مع استثناء الكلمات المحددة
    words = phrase.split()
    for i, word in enumerate(words):
        if word[:2] == 'ال' and word not in excluded_words:
            words[i] = word[2:]  # إزالة "ال"
        elif (word[0] in {'و', 'ل', 'ب'}) and word[1:3] == 'ال' and word[1:] not in excluded_words:
            words[i] = word[0] + word[3:]  # إزالة "ال" بعد الحرف الأول
        elif word[:2] == 'لل':
            words[i] = word[2:]  # إزالة "لل"
        elif word == 'وربع' or word == 'وثلث' or word == 'ونص' or word == 'ونصف':
            words[i] = word[1:]
    
    # إعادة تجميع الكلمات
    phrase = ' '.join(words)
    return phrase

def edit_word(phrase):
        
        NumberS =st.NumberS
        devices=st.devices
        irr=st.irr
        wdays=st.wdays
        months=st.months
        wordS=st.wordS
        doubleWord=st.doubleWord
        doubleChar=st.doubleChar
        words=st.words
        filterWords=st.filterWords
        shortcuts=st.shortcuts
        
        
        editPhrase = phrase
        editPhrase = ' '+editPhrase+' '
        for i in doubleWord:
            for e in doubleChar:
                if i+e in editPhrase:
                    editPhrase=editPhrase.replace(i+e,' 2 '+i+' ')
            if i in editPhrase:
                print(i)
                editPhrase=editPhrase.replace(i,' '+i+' ')
                
        editPhrase = ' '.join(editPhrase.split())
        editPhrase = ' '+editPhrase+' '
        for i in words:
            if i in editPhrase:
                editPhrase=editPhrase.replace(i,' '+words[i]+' ')
        editPhrase = ' '.join(editPhrase.split())
        editPhrase = ' '+editPhrase+' '
        for i in wdays:
            if ' '+i+' ' in editPhrase:
                editPhrase=editPhrase.replace(i,' '+wdays[i]+' ')
        editPhrase = ' '.join(editPhrase.split())
        editPhrase = ' '+editPhrase+' '
        for i in NumberS:
            if 'ثانيه' not in editPhrase and 'مءقت' not in editPhrase and 'عداد' not in editPhrase:
                editPhrase=editPhrase.replace('سالب '+i,'-'+NumberS[i])
                editPhrase=editPhrase.replace('و'+i,'و '+NumberS[i])
                editPhrase=editPhrase.replace(i,NumberS[i])
        editPhrase = ' '.join(editPhrase.split())
        editPhrase = ' '+editPhrase+' '
        for i in devices:
            if i+'ات' in editPhrase:
                editPhrase=editPhrase.replace(i+'ات',' ALL '+devices[i]+' ')
            if i in editPhrase:
                editPhrase=editPhrase.replace(i,' '+devices[i]+' ')
        editPhrase = ' '.join(editPhrase.split())
        editPhrase = ' '+editPhrase+' '
        for i in months:
            if ' '+i+' ' in editPhrase:
                editPhrase=editPhrase.replace(i,' '+months[i]+' ')
        editPhrase = ' '.join(editPhrase.split())
        editPhrase = ' '+editPhrase+' '
        for i in irr:
            if i in editPhrase:
                editPhrase=editPhrase.replace(i,' '+irr[i]+' ')
            #فاعل
            elif i[0]+ 'ا'+i[1:] in editPhrase:
                editPhrase=editPhrase.replace(i[0]+ 'ا'+i[1:],' '+irr[i]+' ')
            #تفعيل
            elif 'ت'+i[0:2]+'ي'+i[-1] in editPhrase:
                editPhrase=editPhrase.replace('ت'+i[0:2]+'ي'+i[-1],' '+irr[i]+' ')
            #فعال
            elif i[0:2]+ 'ا'+i[-1] in editPhrase:
                editPhrase=editPhrase.replace(i[0:2]+ 'ا'+i[-1],' '+irr[i]+' ')
            #مفعول
            elif 'م'+i[0:2]+ 'و'+i[-1] in editPhrase:
                editPhrase=editPhrase.replace('م'+i[0:2]+ 'و'+i[-1],' '+irr[i]+' ')
        editPhrase = ' '.join(editPhrase.split())
#         editPhrase = editPhrase.replace('  ',' ')
        ph = editPhrase.split()
        listPh = []
        for i in ph:
#             if i.isnumeric() or i in filterWords or i in dbItems:
            listPh.append(i)
        editPhrase = ' '.join(listPh)
        
        return editPhrase        
 