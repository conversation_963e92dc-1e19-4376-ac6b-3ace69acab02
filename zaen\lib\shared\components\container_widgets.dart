import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/settings/settings.dart';
import 'package:zaen/shared/components/config.dart';

Widget containerPageOption({
  required Widget content,
  double ver = 0,
}) {
  return GetBuilder<SettingsController>(
    builder: (settingsController) => Container(
      padding: EdgeInsets.symmetric(
          horizontal: controller.sizedWidth * 0.03,
          vertical: controller.sizedHight * ver),
      width: controller.sizedWidth * 0.85,
      // height: controller.sizedHight * 0.4,
      decoration: BoxDecoration(
        color: AppColors.containerPageColor,
        borderRadius: const BorderRadius.all(Radius.circular(17)),
      ),
      child: content,
    ),
  );
}

Widget containerIconsOption({
  required Widget content,
  double ver = 0,
  double radius = 35,
  Color? color,
  EdgeInsets margin = EdgeInsets.zero,
  EdgeInsets padding = EdgeInsets.zero,
}) {
  return Container(
    padding: padding == EdgeInsets.zero
        ? EdgeInsets.symmetric(
            horizontal: controller.sizedWidth * 0.01,
            vertical: controller.sizedHight * ver)
        : padding,
    margin: margin,
    // width: controller.sizedWidth * 0.85,
    // height: controller.sizedHight * 0.4,
    decoration: BoxDecoration(
      gradient: color == null ? AppColors.cardGradient : null,
      color: color,
      borderRadius: BorderRadius.all(Radius.circular(radius)),
      border: Border.all(
        color: AppColors.border,
        width: 1.0,
      ),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.04),
          spreadRadius: 1,
          blurRadius: 8,
          offset: Offset(0, 2),
        ),
      ],
    ),
    child: content,
  );
}
