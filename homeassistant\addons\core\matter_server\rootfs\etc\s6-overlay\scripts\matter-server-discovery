#!/usr/bin/with-contenv bashio
# vim: ft=bash
# shellcheck shell=bash
# ==============================================================================
# Send matter discovery information to Home Assistant
# ==============================================================================
declare ha_config

# Prepare discovery payload
ha_config=$(\
    bashio::var.json \
        host "$(hostname)" \
        port "^5580" \
)

if bashio::discovery "matter" "${ha_config}" > /dev/null; then
    bashio::log.info "Successfully send discovery information to Home Assistant."
else
    bashio::log.error "Discovery message to Home Assistant failed!"
fi
