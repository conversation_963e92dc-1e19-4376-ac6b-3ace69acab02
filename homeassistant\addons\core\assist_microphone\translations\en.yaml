---
configuration:
  awake_wav:
    name: Awake WAV
    description: >-
      Path to WAV file to play when wake word is detected (empty to disable).
  done_wav:
    name: Done WAV
    description: >-
      Path to WAV file to play when voice command is finished (empty to
      disable).
  timer_finished_wav:
    name: Timer Finished WAV
    description: >-
      Path to WAV file to play when timer is finished (empty to disable,
      default is /usr/src/sounds/timer_finished.wav).
  timer_repeat_count:
    name: Timer Finished WAV Repetition Count
    description: >-
      Number of times to repeat timer_finished_wav (default is 3).
  timer_repeat_delay:
    name: Timer Repetition Delay
    description: >-
      Delay before repeating timer_finished_wav, in seconds (default is 0.75).
  noise_suppression:
    name: Noise suppression
    description: >-
      Noise suppression level (0 is disabled, 4 is max).
  auto_gain:
    name: Auto gain
    description: >-
      Automatic volume boost for microphone (0 is disabled, 31 is max).
  mic_volume_multiplier:
    name: Microphone volume multiplier
    description: >-
      Multiply microphone volume by fixed value (1.0 = no change, 2.0 = twice as
      loud).
  sound_enabled:
    name: Sound enabled
    description: >-
      Enables or disables output audio.
  sound_volume_multiplier:
    name: Sound volume multiplier
    description: >-
      Multiply sound output volume by fixed value (1.0 = no change, 2.0 = twice
      as loud). 1.0 is the default.
  debug_logging:
    name: Debug logging
    description: >-
      Enable debug logging.
