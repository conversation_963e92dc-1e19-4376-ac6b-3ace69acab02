From 29c0b6b142c6692a53a83fb4604fc2b<PERSON>beb17af Mon Sep 17 00:00:00 2001
Message-ID: <<EMAIL>>
From: <PERSON> <<EMAIL>>
Date: Thu, 13 Jul 2023 20:00:34 +0200
Subject: [PATCH] [rest] support erasing all persistent info (#1908)

Add REST API to support erasing all persistent information, effectively
factory resetting the OTBR. The implementation follows the semantic of
the D-Bus API and automatically disables the Thread network.

After erasing all persistent information the dataset is cleared too. So
this allows to build a new Thread network with subsequent use of the
PUT method to the /node/dataset/active endpoint.
---
 src/rest/openapi.yaml |  9 +++++++++
 src/rest/resource.cpp | 38 ++++++++++++++++++++++++++++++++++----
 src/rest/resource.hpp |  1 +
 src/rest/response.cpp |  2 +-
 4 files changed, 45 insertions(+), 5 deletions(-)

diff --git a/src/rest/openapi.yaml b/src/rest/openapi.yaml
index 43747e2e01..2ba2a4dd56 100644
--- a/src/rest/openapi.yaml
+++ b/src/rest/openapi.yaml
@@ -43,6 +43,15 @@ paths:
             application/json:
               schema:
                 type: object
+    delete:
+      tags:
+        - node
+      summary: Erase all persistent information, essentially factory reset the Border Router.
+      responses:
+        "200":
+          description: Successful operation
+        "409":
+          description: Thread interface is in wrong state.
   /node/ba-id:
     get:
       tags:
diff --git a/src/rest/resource.cpp b/src/rest/resource.cpp
index 1139a2d788..a60e9d9483 100644
--- a/src/rest/resource.cpp
+++ b/src/rest/resource.cpp
@@ -257,16 +257,46 @@ exit:
     }
 }
 
-void Resource::NodeInfo(const Request &aRequest, Response &aResponse) const
+void Resource::DeleteNodeInfo(Response &aResponse) const
 {
+    otbrError   error = OTBR_ERROR_NONE;
     std::string errorCode;
-    if (aRequest.GetMethod() == HttpMethod::kGet)
+
+    VerifyOrExit(mNcp->GetThreadHelper()->Detach() == OT_ERROR_NONE, error = OTBR_ERROR_INVALID_STATE);
+    VerifyOrExit(otInstanceErasePersistentInfo(mInstance) == OT_ERROR_NONE, error = OTBR_ERROR_REST);
+    mNcp->Reset();
+
+exit:
+    if (error == OTBR_ERROR_NONE)
     {
-        GetNodeInfo(aResponse);
+        errorCode = GetHttpStatus(HttpStatusCode::kStatusOk);
+        aResponse.SetResponsCode(errorCode);
     }
-    else
+    else if (error == OTBR_ERROR_INVALID_STATE)
+    {
+        ErrorHandler(aResponse, HttpStatusCode::kStatusConflict);
+    }
+    else if (error != OTBR_ERROR_NONE)
     {
+        ErrorHandler(aResponse, HttpStatusCode::kStatusInternalServerError);
+    }
+}
+
+void Resource::NodeInfo(const Request &aRequest, Response &aResponse) const
+{
+    std::string errorCode;
+
+    switch (aRequest.GetMethod())
+    {
+    case HttpMethod::kGet:
+        GetNodeInfo(aResponse);
+        break;
+    case HttpMethod::kDelete:
+        DeleteNodeInfo(aResponse);
+        break;
+    default:
         ErrorHandler(aResponse, HttpStatusCode::kStatusMethodNotAllowed);
+        break;
     }
 }
 
diff --git a/src/rest/resource.hpp b/src/rest/resource.hpp
index 0c089c7296..d79085dbfc 100644
--- a/src/rest/resource.hpp
+++ b/src/rest/resource.hpp
@@ -137,6 +137,7 @@ private:
     void HandleDiagnosticCallback(const Request &aRequest, Response &aResponse);
 
     void GetNodeInfo(Response &aResponse) const;
+    void DeleteNodeInfo(Response &aResponse) const;
     void GetDataBaId(Response &aResponse) const;
     void GetDataExtendedAddr(Response &aResponse) const;
     void GetDataState(Response &aResponse) const;
diff --git a/src/rest/response.cpp b/src/rest/response.cpp
index 93cbe0b6df..3460b90e1f 100644
--- a/src/rest/response.cpp
+++ b/src/rest/response.cpp
@@ -34,7 +34,7 @@
 #define OT_REST_RESPONSE_ACCESS_CONTROL_ALLOW_HEADERS                                                              \
     "Access-Control-Allow-Headers, Origin,Accept, X-Requested-With, Content-Type, Access-Control-Request-Method, " \
     "Access-Control-Request-Headers"
-#define OT_REST_RESPONSE_ACCESS_CONTROL_ALLOW_METHOD "GET, OPTIONS, PUT"
+#define OT_REST_RESPONSE_ACCESS_CONTROL_ALLOW_METHOD "DELETE, GET, OPTIONS, PUT"
 #define OT_REST_RESPONSE_CONNECTION "close"
 
 namespace otbr {
-- 
2.41.0

