---
build_from:
  aarch64: ghcr.io/home-assistant/aarch64-base-python:3.13-alpine3.21
  amd64: ghcr.io/home-assistant/amd64-base-python:3.13-alpine3.21
  armhf: ghcr.io/home-assistant/armhf-base-python:3.13-alpine3.21
  armv7: ghcr.io/home-assistant/armv7-base-python:3.13-alpine3.21
  i386: ghcr.io/home-assistant/i386-base-python:3.13-alpine3.21
codenotary:
  signer: <EMAIL>
  base_image: <EMAIL>
args:
  # Developer note: please add a new plugin alphabetically into all lists
  ACME_VERSION: 3.3.0
  CERTBOT_DNS_AZURE_VERSION: 2.6.1
  CERTBOT_DNS_CLOUDNS_VERSION: 0.7.0
  CERTBOT_DNS_DESEC_VERSION: 1.2.1
  CERTBOT_DNS_DIRECTADMIN_VERSION: 1.0.15
  CERTBOT_DNS_DOMAINOFFENSIVE_VERSION: 2.0.0
  CERTBOT_DNS_DREAMHOST_VERSION: "1.0"
  CERTBOT_DNS_DUCKDNS_VERSION: "1.5"
  CERTBOT_DNS_DYNU_VERSION: 0.0.8
  CERTBOT_DNS_EASYDNS_VERSION: 0.1.4
  CERTBOT_DNS_EURODNS_VERSION: 1.8.1
  CERTBOT_DNS_GANDI_VERSION: 1.6.1
  CERTBOT_DNS_GODADDY_VERSION: 2.8.0
  CERTBOT_DNS_HETZNER_VERSION: 2.0.1
  CERTBOT_DNS_HURRICANE_ELECTRIC_VERSION: 0.1.0
  CERTBOT_DNS_INFOMANIAK_VERSION: 0.2.3
  CERTBOT_DNS_INWX_VERSION: 3.0.2
  CERTBOT_DNS_IONOS_VERSION: 2024.11.9
  CERTBOT_DNS_JOKER_VERSION: 1.1.0
  CERTBOT_DNS_LOOPIA_VERSION: 1.0.1
  CERTBOT_DNS_MIJN_HOST_VERSION: 0.0.8
  CERTBOT_DNS_NAMECHEAP_VERSION: 1.0.0
  CERTBOT_DNS_NETCUP_VERSION: 1.4.4
  CERTBOT_DNS_NJALLA_VERSION: 2.0.2
  CERTBOT_DNS_NORISNETWORK_VERSION: 0.3.0
  CERTBOT_DNS_PLESK_VERSION: 0.3.0
  CERTBOT_DNS_PORKBUN_VERSION: 0.9.1
  CERTBOT_DNS_SIMPLY_VERSION: 0.1.2
  CERTBOT_DNS_TRANSIP_VERSION: 0.5.2
  CERTBOT_DNS_WEBSUPPORT_VERSION: 3.0.0
  CERTBOT_VERSION: 3.3.0
  CLOUDFLARE_VERSION: 2.19.4
  CRYPTOGRAPHY_VERSION: 44.0.2
