---
version: 1.3.0
slug: assist_microphone
name: Assist Microphone
description: Use Assist with local microphone
url: https://github.com/home-assistant/addons/blob/master/assist_microphone
hassio_api: true
arch:
  - amd64
  - aarch64
init: false
discovery:
  - wyoming
map:
  - share:ro
options:
  awake_wav: "/usr/src/sounds/awake.wav"
  done_wav: "/usr/src/sounds/done.wav"
  timer_finished_wav: "/usr/src/sounds/timer_finished.wav"
  timer_repeat_count: 3
  timer_repeat_delay: 0.75
  sound_enabled: true
  noise_suppression: 0
  auto_gain: 0
  mic_volume_multiplier: 1.0
  sound_volume_multiplier: 1.0
  debug_logging: false
schema:
  awake_wav: str
  done_wav: str
  timer_finished_wav: str
  timer_repeat_count: int
  timer_repeat_delay: float
  sound_enabled: bool
  noise_suppression: int
  auto_gain: int
  mic_volume_multiplier: float
  sound_volume_multiplier: float
  debug_logging: bool
audio: true
homeassistant: 2023.12.1
image: homeassistant/{arch}-addon-assist_microphone
