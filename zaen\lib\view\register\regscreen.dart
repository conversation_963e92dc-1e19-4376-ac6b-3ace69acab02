import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:get/get.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/controller/controller.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:wifi_scan/wifi_scan.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:http/http.dart' as http;

class Scanner extends StatefulWidget {
  const Scanner({Key? key}) : super(key: key);

  @override
  State<Scanner> createState() => _ScannerState();
}

class _ScannerState extends State<Scanner> {
  HomeController controller = Get.put(HomeController(), permanent: true);

  List<WiFiAccessPoint> accessPoints = <WiFiAccessPoint>[];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();

    startScan();
  }

  void startScan() async {
    setState(() => isLoading = true);
    final canS = await WiFiScan.instance.canStartScan(askPermissions: true);
    print(canS);
    if (canS == CanStartScan.yes) {
      print('22222222');
      final scanning = await WiFiScan.instance.startScan();
      final canR =
          await WiFiScan.instance.canGetScannedResults(askPermissions: true);
      if (canR == CanGetScannedResults.yes) {
        final result = await WiFiScan.instance.getScannedResults();
        if (result.isNotEmpty) {
          print('111111111111');
          setState(() => accessPoints = result);
        } else {
          setState(() => accessPoints = <WiFiAccessPoint>[]);
        }
      }
    } else {
      setState(() => accessPoints = <WiFiAccessPoint>[]);
    }
    setState(() => isLoading = false);
  }

  Widget _buildInfo(String label, dynamic value) => Container(
        decoration: const BoxDecoration(
            border: Border(bottom: BorderSide(color: Colors.grey))),
        child: Row(
          children: [
            Text("$label: ",
                style: const TextStyle(fontWeight: FontWeight.bold)),
            Expanded(child: Text(value.toString()))
          ],
        ),
      );

  Widget _buildAccessPointTile(BuildContext context, WiFiAccessPoint ap) {
    final title = ap.ssid.isNotEmpty ? ap.ssid : "شبكة غير معروفة";

    return Container(
      margin: EdgeInsets.all(controller.sized * 0.005),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(15)),
        gradient: AppColors.cardGradient,
        border: Border.all(
          color: AppColors.borderAccent,
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.12),
            spreadRadius: 2,
            blurRadius: 12,
            offset: Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            spreadRadius: 0,
            blurRadius: 6,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: ListTile(
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'ـــ',
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
                style: TextStyle(
                    color: ap.level >= -60
                        ? AppColors.primary
                        : AppColors.textDisabled,
                    height: 0.2,
                    fontSize: controller.sized * 0.02,
                    fontWeight: FontWeight.w800),
              ),
              Text(
                'ــ',
                textDirection: TextDirection.rtl,
                textAlign: TextAlign.right,
                style: TextStyle(
                    color: ap.level >= -70
                        ? AppColors.primary
                        : AppColors.textDisabled,
                    height: 0.2,
                    fontSize: controller.sized * 0.02,
                    fontWeight: FontWeight.w800),
              ),
              Text(
                'ـ',
                textDirection: TextDirection.rtl,
                textAlign: TextAlign.right,
                style: TextStyle(
                    color: ap.level >= -85
                        ? AppColors.primary
                        : AppColors.textDisabled,
                    height: 0.2,
                    fontSize: controller.sized * 0.02,
                    fontWeight: FontWeight.w800),
              ),
            ],
          ),
          title: txtStyle(
              txt: title, align: TextAlign.left, color: AppColors.textPrimary),
          onTap: () {
            showAwesomeDialog(context, ap);
          }),
    );
  }

  void showAwesomeDialog(BuildContext context, WiFiAccessPoint ap) {
    bool _obscureText = true;
    TextEditingController _passwordController = TextEditingController();

    AwesomeDialog(
      context: context,
      dialogType: DialogType.info,
      animType: AnimType.scale,
      title: 'إدخال كلمة مرور',
      dialogBackgroundColor: AppColors.surface,
      body: StatefulBuilder(
        builder: (BuildContext context, StateSetter setState) {
          return Column(
            children: [
              SizedBox(
                height: controller.sizedHight * 0.02,
              ),
              txtStyle(
                  txt: ap.ssid.isNotEmpty ? ap.ssid : "شبكة غير معروفة",
                  color: AppColors.textPrimary,
                  size: controller.sized * 0.015,
                  maxLines: 2),
              SizedBox(
                height: controller.sizedHight * 0.02,
              ),
              Directionality(
                textDirection: TextDirection.rtl,
                child: Container(
                  width: controller.sizedWidth * 0.7,
                  child: TextField(
                    style: TextStyle(color: AppColors.textPrimary), // لون النص
                    cursorColor: AppColors.primary,
                    controller: _passwordController,
                    obscureText: _obscureText,
                    textAlign: TextAlign.center,
                    textDirection: TextDirection.rtl,
                    decoration: InputDecoration(
                      hintText: 'كلمة السر',
                      hintStyle: TextStyle(
                          color: AppColors.textHint,
                          fontWeight: FontWeight.bold),
                      contentPadding:
                          EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
                      filled: true,
                      fillColor: AppColors.surface.withOpacity(0.3),

                      // حذف خاصية border واستخدام الخصائص التالية
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(32.0),
                        borderSide: BorderSide(
                            color: AppColors.border.withOpacity(0.6),
                            width: 1.0),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(32.0),
                        borderSide:
                            BorderSide(color: AppColors.primary, width: 2),
                      ),

                      suffixIcon: GestureDetector(
                        onTap: () {
                          setState(() {
                            _obscureText = !_obscureText;
                          });
                        },
                        child: Icon(
                          _obscureText
                              ? Icons.visibility_off
                              : Icons.visibility,
                          color: AppColors.textHint,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 20),
              Padding(
                padding: EdgeInsets.symmetric(vertical: 16.0),
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    elevation: 8,
                    shadowColor: AppColors.primary.withOpacity(0.4),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: 40,
                      vertical: 12,
                    ),
                  ).copyWith(
                    overlayColor: WidgetStateProperty.all(
                      AppColors.primary.withOpacity(0.1),
                    ),
                  ),
                  onPressed: () async {
                    try {
                      final url = Uri.parse('http://192.144.4.1:5000/wifi');
                      final response = await http.post(
                        url,
                        headers: {'Content-Type': 'application/json'},
                        body: jsonEncode({
                          'ssid': ap.ssid,
                          'password': _passwordController.text
                        }),
                      );

                      if (response.statusCode == 200) {
                        print('Configuration successful');
                      } else {
                        print('Configuration failed');
                      }
                    } catch (e) {}
                  },
                  child: txtStyle(txt: 'دخول'),
                ),
              ),
            ],
          );
        },
      ),
    ).show();
  }

  @override
  Widget build(BuildContext context) {
    controller.sizedHight = MediaQuery.of(context).size.height;
    controller.sizedWidth = MediaQuery.of(context).size.width;
    controller.sized = controller.sizedHight + controller.sizedWidth;
    return MaterialApp(
      home: Scaffold(
        extendBody: true,
        extendBodyBehindAppBar: true,
        body: Container(
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: AppColors.backgroundGradient,
          ),
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.backgroundSecondary.withOpacity(0.15),
            ),
            child: Builder(
              builder: (context) => SingleChildScrollView(
                physics: NeverScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (controller.apName != 'مفاتيح')
                      SizedBox(
                        height: controller.sizedHight * 0.07,
                      ),
                    Center(
                      child: Image.asset(
                        controller.apName == 'مفاتيح'
                            ? 'assets/images/devices/switch light.png'
                            : controller.apName == 'تلفاز'
                                ? 'assets/images/devices/tv.png'
                                : controller.apName == 'مكيف'
                                    ? 'assets/images/devices/ac.png'
                                    : 'assets/images/devices/zain.png',
                        width: controller.sizedWidth,
                        height: controller.apName != 'مفاتيح'
                            ? controller.sizedHight * 0.24
                            : controller.sizedHight * 0.31,
                      ),
                    ),
                    Center(
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: controller.sizedWidth * 0.05,
                          vertical: controller.sizedHight * 0.02,
                        ),
                        margin: EdgeInsets.symmetric(
                          horizontal: controller.sizedWidth * 0.1,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          color: AppColors.surface.withOpacity(0.25),
                          border: Border.all(
                            color: AppColors.borderAccent.withOpacity(0.3),
                            width: 1.0,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.08),
                              spreadRadius: 1,
                              blurRadius: 8,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            txtStyle(
                                txt: 'نوع الجهاز : ${controller.apName}',
                                color: AppColors.textPrimary,
                                align: TextAlign.center,
                                maxLines: 2,
                                size: controller.sized * 0.015),
                            SizedBox(height: controller.sizedHight * 0.01),
                            txtStyle(
                                txt: 'رمز الجهاز : ${controller.apMAC}',
                                color: AppColors.textPrimary,
                                maxLines: 2,
                                align: TextAlign.center,
                                size: controller.sized * 0.015),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      height: controller.sizedHight * 0.06,
                    ),
                    Center(
                      child: Container(
                        height: controller.sizedHight * 0.49,
                        width: controller.sizedWidth * 0.9,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(30)),
                          gradient: AppColors.backgroundGradient,
                        ),
                        child: Column(
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                  right: controller.sizedWidth * 0.12,
                                  left: controller.sizedWidth * 0.01),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  IconButton(
                                      padding: EdgeInsets.zero,
                                      onPressed: startScan,
                                      icon: iconStyle(
                                          icon: Icons.wifi_find_rounded,
                                          color: AppColors.secondaryColor,
                                          size: controller.sized * 0.025)),
                                  Expanded(
                                    child: txtStyle(
                                        align: TextAlign.center,
                                        txt: 'اضف الجهاز الى شبكتك من هنا',
                                        color: AppColors.textPrimary),
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              child: Container(
                                child: Center(
                                  child: isLoading
                                      ? CircularProgressIndicator(
                                          color: AppColors.primary,
                                          backgroundColor: AppColors.primary
                                              .withOpacity(0.2),
                                        )
                                      : accessPoints.isEmpty
                                          ? Container(
                                              padding: EdgeInsets.all(
                                                  controller.sized * 0.02),
                                              margin: EdgeInsets.symmetric(
                                                horizontal:
                                                    controller.sizedWidth * 0.1,
                                              ),
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(15),
                                                color: AppColors.surface
                                                    .withOpacity(0.3),
                                                border: Border.all(
                                                  color: AppColors.borderAccent
                                                      .withOpacity(0.4),
                                                  width: 1.0,
                                                ),
                                              ),
                                              child: txtStyle(
                                                txt:
                                                    "لا يوجد شبكات متاحة! \n تأكد من تشغيل WIFI في هاتفك",
                                                maxLines: 2,
                                                color: AppColors.textSecondary,
                                                align: TextAlign.center,
                                              ),
                                            )
                                          : ListView.builder(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal:
                                                      controller.sizedWidth *
                                                          0.05),
                                              itemCount: accessPoints.length,
                                              itemBuilder: (context, i) =>
                                                  _buildAccessPointTile(
                                                      context, accessPoints[i]),
                                            ),
                                ),
                              ),
                            ),
                            SizedBox(
                              height: controller.sizedHight * 0.025,
                            )
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      height: controller.sizedHight * 0.04,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Show snackbar.
