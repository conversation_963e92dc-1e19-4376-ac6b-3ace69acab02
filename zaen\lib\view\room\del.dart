import 'package:flutter/cupertino.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mysql1/mysql1.dart';
import 'package:sqflite/sqflite.dart';
import 'package:zaen/modules/local/ip.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/components/permission_widgets.dart';

del(
    {required var context,
    required var roomId,
    required var room,
    required var appDB,
    required var conn}) {
  {
    AwesomeDialog(
      context: context,
      dialogType: DialogType.error,
      headerAnimationLoop: true,
      animType: AnimType.topSlide,
      dialogBackgroundColor: AppColors.surfaceElevated,
      titleTextStyle: TextStyle(
          color: AppColors.textHint,
          fontWeight: FontWeight.bold,
          fontSize: controller.sized * 0.015),
      descTextStyle: TextStyle(
          color: AppColors.textSecondary,
          fontWeight: FontWeight.bold,
          fontSize: controller.sized * 0.017),
      // showCloseIcon: true,
      // closeIcon: const Icon(Icons.close_fullscreen_outlined),
      title: 'هل تريد حذف الغرفة ؟',
      desc: room,
      // btnCancelText:
      //     'لا اريد',
      btnOkText: 'نعم',
      btnOkOnPress: () async {
        // التحقق من صلاحية حذف الغرف
        if (!controller.canManageData()) {
          showNoPermissionDialog(customMessage: 'ليس لديك صلاحية لحذف الغرف');
          return;
        }

        var x = await appDB.rawQuery('SELECT * FROM rooms');

        if (x.length != 1) {
          await appDB.rawQuery('DELETE FROM rooms WHERE id = "${roomId}"');
          await conn.query('ALTER TABLE Rooms DROP ${roomId}');
          await conn
              .query("UPDATE Devices set rooms='x' where rooms='${roomId}'");

          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
            Navigator.of(context).pop();
            await 0.35.seconds.delay();
            getDevices();
          }
        }

        // await appDB.rawQuery(
        //     'DELETE FROM rooms WHERE id = "${roomId}"');
        // await conn.query(
        //     'ALTER TABLE Rooms DROP ${roomId}');
        // await conn.query(
        //     "UPDATE Devices set rooms='x' where rooms='${roomId}'");
        // getDevices();
        // if (Navigator.of(context).canPop()) {
        //     Navigator.of(context).pop();
        //     Navigator.of(context).pop();
        //       }
      },
      // btnCancelOnPress:
      //     () {},
    ).show();
  }
  ;
}
