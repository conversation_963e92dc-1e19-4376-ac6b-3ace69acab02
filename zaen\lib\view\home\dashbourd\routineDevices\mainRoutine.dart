import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/alarm.dart';
import 'package:zaen/shared/components/components.dart';

import 'package:zaen/view/home/<USER>/routineDevices/routine.dart'
    as routine;
import 'package:zaen/view/home/<USER>/routineDevices/main_routine_task_display.dart';
import 'package:zaen/view/home/<USER>/routineDevices/main_routine_shortcut_section.dart';
import 'package:zaen/view/home/<USER>/routineDevices/main_routine_words_section.dart';
import 'package:zaen/view/home/<USER>/routineDevices/main_routine_task_scheduler.dart';
import 'package:zaen/view/home/<USER>/routineDevices/main_routine_action_buttons.dart';

Widget mainRoutine({context, setState, edit}) {
  GlobalKey<FormState> kname1 = GlobalKey<FormState>();
  GlobalKey<FormState> kname2 = GlobalKey<FormState>();

  return GestureDetector(
    onTap: () {
      FocusManager.instance.primaryFocus?.unfocus();
      if (kname1.currentState != null) {
        var formdata = kname1.currentState;
        formdata!.validate();
      }
      if (kname2.currentState != null) {
        var formdata = kname2.currentState;
        formdata!.validate();
      }
    },
    child: GetBuilder<HomeController>(
      builder: (controller) => SingleChildScrollView(
        child: Column(
          children: [
            // عرض المهام المختارة
            taskDisplayWidget(controller: controller),

            SizedBox(height: controller.sizedHight * 0.02),

            // قسم الاختصارات
            shortcutSectionWidget(
              controller: controller,
              add: routine.add,
              isShortcut: routine.isShortcut,
              routineIcon: routine.routineIcon,
              name1: routine.name1,
              kname1: kname1,
              onShortcutChanged: (value) {
                setState(() {
                  routine.isShortcut = value;
                });
              },
              onRoutineIconChanged: (icon) {
                setState(() {
                  routine.routineIcon = icon;
                });
              },
              edit: edit,
            ),

            SizedBox(height: controller.sizedHight * 0.02),

            // قسم الكلمات الروتينية
            wordsSectionWidget(
              controller: controller,
              add: routine.add,
              isWords: routine.isWords,
              name2: routine.name2,
              kname2: kname2,
              onWordsChanged: (value) {
                setState(() {
                  routine.isWords = value;
                });
              },
              edit: edit,
            ),

            SizedBox(height: controller.sizedHight * 0.02),

            // قسم الجدولة
            taskSchedulerWidget(
              controller: controller,
              add: routine.add,
              isTask: routine.isTask,
              isScheduler: isScheduler,
              h: h,
              m: m,
              isAM: isAM,
              days: days.cast<String>(),
              re: re,
              onTaskChanged: (value) {
                setState(() {
                  routine.isTask = value;
                });
              },
              onSchedulerChanged: (value) {
                setState(() {
                  isScheduler = value;
                });
              },
              setState: setState,
            ),

            SizedBox(height: controller.sizedHight * 0.02),

            // أزرار الحفظ والحذف
            actionButtonsWidget(
              controller: controller,
              add: routine.add,
              isShortcut: routine.isShortcut,
              isWords: routine.isWords,
              isTask: routine.isTask,
              isScheduler: isScheduler,
              isSetting: routine.isSetting,
              myId: routine.myId,
              routineIcon: routine.routineIcon,
              name1: routine.name1,
              name2: routine.name2,
              kname1: kname1,
              kname2: kname2,
              days: days.cast<String>(),
              weekDays: weekDays.cast<String, String>(),
              h: h,
              m: m,
              isAM: isAM,
              re: re,
              p: routine.p,
              pageController: routine.pageController,
              setState: setState,
              edit: edit,
            ),

            SizedBox(height: controller.sizedHight * 0.06),
          ],
        ),
      ),
    ),
  );
}
