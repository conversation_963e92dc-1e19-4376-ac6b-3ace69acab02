---
# yamllint disable rule:truthy
name: Lint

env:
  HADOLINT_VERSION: v1.17.2
  SHELLCHECK_OPTS: -e SC1008 -s bash

on:
  pull_request:
    branches: ["master"]
  push:
    branches: ["master"]

jobs:
  hadolint:
    runs-on: ubuntu-latest
    name: hadolint
    steps:
      - name: Check out the repository
        uses: actions/checkout@v4.2.2

      - name: Run linter
        id: changed_files
        run: |
          shopt -s globstar
          for dockerfile in **/Dockerfile; do
            echo "Linting: $dockerfile"
            docker run --rm -i \
              -v $(pwd)/.hadolint.yaml:/.hadolint.yaml:ro \
              hadolint/hadolint:${{ env.HADOLINT_VERSION }} < "$dockerfile"
          done

  yamllint:
    runs-on: ubuntu-latest
    name: YAMLLint
    steps:
      - name: Check out the repository
        uses: actions/checkout@v4.2.2

      - name: Run YAMLLint
        uses: frenck/action-yamllint@v1.5

  shellcheck:
    runs-on: ubuntu-latest
    name: ShellCheck
    steps:
      - name: Check out the repository
        uses: actions/checkout@v4.2.2

      - name: <PERSON> linter
        uses: ludeeus/action-shellcheck@2.0.0
        with:
          # Names of scripts with custom shebangs that the action would not
          # discover on its own:
          additional_files: >-
            check_certificate_renewal
            cpcd-config-up
            discovery
            finish
            lock-tables-for-backup
            matter-server-discovery
            reboot
            run
            shutdown
            universal-silabs-flasher-up
            unlock-tables-for-backup
            vlc-discovery
          ignore_paths: >-
            google_assistant
