import 'package:expansion_tile_card/expansion_tile_card.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Widget taskDisplayWidget({required HomeController controller}) {
  return Column(
    children: [
      Row(children: [
        Padding(
          padding: EdgeInsets.only(
              right: controller.sizedWidth * 0.03,
              top: controller.sizedHight * 0.03,
              bottom: controller.sizedHight * 0.01),
          child: txtStyle(
              txt: 'المهمه',
              align: TextAlign.start,
              color: AppColors.textColor2.withOpacity(0.5),
              size: controller.sized * 0.012),
        ),
      ]),
      containerPageOption(
        content: ExpansionTileCard(
            duration: Duration(milliseconds: 400),
            baseColor: Colors.transparent,
            expandedColor: Colors.transparent,
            borderRadius: BorderRadius.all(Radius.circular(17)),
            shadowColor: Colors.transparent,
            contentPadding: EdgeInsets.zero,
            trailing: Icon(
              Icons.check_circle_rounded,
              color: AppColors.primaryColor,
              size: controller.sized * 0.027,
            ),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                iconStyle(icon: Icons.task_alt, color: AppColors.warningColor),
                SizedBox(
                  width: controller.sizedWidth * 0.02,
                ),
                Expanded(
                  child: txtStyle(
                    align: TextAlign.start,
                    txt: 'المهمه المختارة',
                  ),
                ),
              ],
            ),
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                    horizontal: controller.sizedWidth * 0.02),
                child: Divider(
                  color: AppColors.textColor2.withOpacity(0.3),
                  endIndent: 2,
                  indent: 2,
                ),
              ),
              controller.addRoutine.containsKey('home')
                  ? Text(
                      controller.addRoutine['home'] == true
                          ? 'تشغيل ${controller.home}'
                          : 'اغلاق ${controller.home}',
                      style: TextStyle(
                        fontSize: controller.sized * 0.013,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textColor2.withOpacity(0.75),
                      ))
                  : Container(
                      height: controller.sizedHight * 0.35,
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            for (var r in controller.addRoutine.keys.toList())
                              if (controller.addRoutine[r] == true ||
                                  controller.addRoutine[r] == false)
                                Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                        controller.addRoutine[r] == true
                                            ? 'تشغيل ${controller.rooms[r]['privName']}'
                                            : 'اغلاق ${controller.rooms[r]['privName']}',
                                        style: TextStyle(
                                          fontSize: controller.sized * 0.013,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.textColor2
                                              .withOpacity(0.75),
                                        )),
                                    Divider(
                                      color:
                                          AppColors.textColor2.withOpacity(0.3),
                                      endIndent: 2,
                                      indent: 2,
                                    )
                                  ],
                                )
                              else
                                Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('${controller.rooms[r]['privName']}',
                                        style: TextStyle(
                                          fontSize: controller.sized * 0.013,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.textColor2
                                              .withOpacity(0.75),
                                        )),
                                    for (var d in controller.addRoutine[r].keys
                                        .toList())
                                      controller.rooms[r]['devices'][d]
                                                  ['device'] ==
                                              'AC'
                                          ? Padding(
                                              padding: EdgeInsets.only(
                                                  right: controller.sizedWidth *
                                                      0.06),
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                      '- مكيف : ' +
                                                          (controller.rooms[r][
                                                                          'devices']
                                                                          [d][
                                                                          'priv']
                                                                      .toString() ==
                                                                  'x'
                                                              ? 'لا يوجد اسم'
                                                              : controller
                                                                  .rooms[r][
                                                                      'devices']
                                                                      [d]
                                                                      ['priv']
                                                                  .toString()),
                                                      style: TextStyle(
                                                        fontSize:
                                                            controller.sized *
                                                                0.013,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        color: AppColors
                                                            .textColor2
                                                            .withOpacity(0.75),
                                                      )),
                                                  Padding(
                                                    padding: EdgeInsets.only(
                                                        right: controller
                                                                .sizedWidth *
                                                            0.06),
                                                    child: Text(
                                                        controller.addRoutine[r]
                                                                        [d]
                                                                    ['state'] ==
                                                                true
                                                            ? '*  تشغيل المكيف'
                                                            : '*  اغلاق المكيف',
                                                        style: TextStyle(
                                                          fontSize:
                                                              controller.sized *
                                                                  0.013,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          color: AppColors
                                                              .textColor2
                                                              .withOpacity(
                                                                  0.75),
                                                        )),
                                                  ),
                                                  controller.addRoutine[r][d]
                                                                  ['state'] ==
                                                              true &&
                                                          controller.addRoutine[
                                                                      r][d]
                                                                  ['degree'] !=
                                                              null
                                                      ? Padding(
                                                          padding: EdgeInsets.only(
                                                              right: controller
                                                                      .sizedWidth *
                                                                  0.06),
                                                          child: Text(
                                                              '*  درجة التكييف : ${controller.addRoutine[r][d]['degree']}°',
                                                              style: TextStyle(
                                                                fontSize: controller
                                                                        .sized *
                                                                    0.013,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: AppColors
                                                                    .textColor2
                                                                    .withOpacity(
                                                                        0.75),
                                                              )),
                                                        )
                                                      : Container(),
                                                  controller.addRoutine[r][d]
                                                                  ['state'] ==
                                                              true &&
                                                          controller.addRoutine[
                                                                      r][d]
                                                                  ['type'] !=
                                                              null
                                                      ? Padding(
                                                          padding: EdgeInsets.only(
                                                              right: controller
                                                                      .sizedWidth *
                                                                  0.06),
                                                          child: Text(
                                                              '*  نوع التكييف :  ${controller.addRoutine[r][d]['type']}',
                                                              style: TextStyle(
                                                                fontSize: controller
                                                                        .sized *
                                                                    0.013,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: AppColors
                                                                    .textColor2
                                                                    .withOpacity(
                                                                        0.75),
                                                              )),
                                                        )
                                                      : Container(),
                                                  controller.addRoutine[r][d]
                                                                  ['state'] ==
                                                              true &&
                                                          controller.addRoutine[
                                                                      r][d]
                                                                  ['speed'] !=
                                                              null
                                                      ? Padding(
                                                          padding: EdgeInsets.only(
                                                              right: controller
                                                                      .sizedWidth *
                                                                  0.06),
                                                          child: Text(
                                                              '*  سرعة المروحة : ${controller.addRoutine[r][d]['speed'] == '4' ? 'متغيرة' : controller.addRoutine[r][d]['speed']}',
                                                              style: TextStyle(
                                                                fontSize: controller
                                                                        .sized *
                                                                    0.013,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: AppColors
                                                                    .textColor2
                                                                    .withOpacity(
                                                                        0.75),
                                                              )),
                                                        )
                                                      : Container(),
                                                  controller.addRoutine[r][d]
                                                                  ['state'] ==
                                                              true &&
                                                          controller.addRoutine[
                                                                      r][d]
                                                                  ['swing'] !=
                                                              null
                                                      ? Padding(
                                                          padding: EdgeInsets.only(
                                                              right: controller
                                                                      .sizedWidth *
                                                                  0.06),
                                                          child: Text(
                                                              '*  التأرجح : ${controller.addRoutine[r][d]['swing'] == true ? 'يعمل' : 'توقف'}',
                                                              style: TextStyle(
                                                                fontSize: controller
                                                                        .sized *
                                                                    0.013,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: AppColors
                                                                    .textColor2
                                                                    .withOpacity(
                                                                        0.75),
                                                              )),
                                                        )
                                                      : Container(),
                                                ],
                                              ),
                                            )
                                          : controller.rooms[r]['devices'][d]
                                                      ['device'] ==
                                                  'TV'
                                              ? tvDeviceDisplay(
                                                  controller, r, d)
                                              : swDeviceDisplay(
                                                  controller, r, d),
                                    Divider(
                                      color:
                                          AppColors.textColor2.withOpacity(0.5),
                                      endIndent: 2,
                                      indent: 2,
                                    )
                                  ],
                                ),
                            SizedBox(
                              height: controller.sizedHight * 0.01,
                            )
                          ],
                        ),
                      ),
                    ),
            ]),
      ),
    ],
  );
}

Widget tvDeviceDisplay(HomeController controller, String r, String d) {
  return Padding(
    padding: EdgeInsets.only(right: controller.sizedWidth * 0.06),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
            '- تلفاز : ' +
                (controller.rooms[r]['devices'][d]['priv'].toString() == 'x'
                    ? 'لا يوجد اسم'
                    : controller.rooms[r]['devices'][d]['priv'].toString()),
            style: TextStyle(
              fontSize: controller.sized * 0.013,
              fontWeight: FontWeight.bold,
              color: AppColors.textColor2.withOpacity(0.75),
            )),
        Padding(
          padding: EdgeInsets.only(right: controller.sizedWidth * 0.06),
          child: Text(
              controller.addRoutine[r][d]['state'] == true
                  ? '*  تشغيل التلفاز'
                  : '*  اغلاق التلفاز',
              style: TextStyle(
                fontSize: controller.sized * 0.013,
                fontWeight: FontWeight.bold,
                color: AppColors.textColor2.withOpacity(0.75),
              )),
        ),
        controller.addRoutine[r][d]['state'] == true &&
                controller.addRoutine[r][d]['ch'] != null
            ? Padding(
                padding: EdgeInsets.only(right: controller.sizedWidth * 0.06),
                child: Text(
                    '*  القناة : ${controller.addRoutine[r][d]['ch'].toString()}',
                    style: TextStyle(
                      fontSize: controller.sized * 0.013,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textColor2.withOpacity(0.75),
                    )),
              )
            : Container(),
        controller.addRoutine[r][d]['state'] == true &&
                controller.addRoutine[r][d]['v'] != null
            ? Padding(
                padding: EdgeInsets.only(right: controller.sizedWidth * 0.06),
                child: Text(
                    '*  الصوت :  ${controller.addRoutine[r][d]['v'] == true ? 'كتم الصوت' : controller.addRoutine[r][d]['v'] == false ? 'اللغاء كتم الصوت' : controller.addRoutine[r][d]['v']}',
                    style: TextStyle(
                      fontSize: controller.sized * 0.013,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textColor2.withOpacity(0.75),
                    )),
              )
            : Container(),
      ],
    ),
  );
}

Widget swDeviceDisplay(HomeController controller, String r, String d) {
  return Padding(
    padding: EdgeInsets.only(right: controller.sizedWidth * 0.06),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
            '- مفاتيح : ' +
                (controller.rooms[r]['devices'][d]['priv'].toString() == 'x'
                    ? 'لا يوجد اسم'
                    : controller.rooms[r]['devices'][d]['priv'].split('_')[0]),
            style: TextStyle(
              fontSize: controller.sized * 0.013,
              fontWeight: FontWeight.bold,
              color: AppColors.textColor2.withOpacity(0.75),
            )),
        for (var v in controller.addRoutine[r][d].keys.toList())
          controller.addRoutine[r][d][v] != null
              ? Padding(
                  padding: EdgeInsets.only(right: controller.sizedWidth * 0.06),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('°  ${v}',
                          style: TextStyle(
                            fontSize: controller.sized * 0.013,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textColor2.withOpacity(0.75),
                          )),
                      Padding(
                        padding: EdgeInsets.only(
                            right: controller.sizedWidth * 0.06),
                        child: Text(
                            '*  ${controller.rooms[r]['devices'][d]['priv'].toString() == 'x' ? 'لا يوجد اسم' : controller.rooms[r]['devices'][d]['priv'].split('_')[int.parse(v.toString().replaceFirst('v', ''))]} : ${controller.addRoutine[r][d][v] == true ? 'يعمل' : 'لا يعمل'}',
                            style: TextStyle(
                              fontSize: controller.sized * 0.013,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textColor2.withOpacity(0.75),
                            )),
                      )
                    ],
                  ),
                )
              : Container(),
      ],
    ),
  );
}
