<!DOCTYPE html>
<html>
  <head>
    <style>
      html {
        height: 100%;
      }
      body {
        height: 100%;
        margin: 0;
        font-family: Roboto, sans-serif;
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        font-size: 1rem;
        line-height: 1.375rem;
        font-weight: 500;
        letter-spacing: 0.0071428571em;
        text-decoration: inherit;
        text-transform: inherit;
        color: #212121;
        font-weight: 400;
        line-height: 2;
        background: #fafafa;
      }
      a {
        text-decoration: none;
        color: #03a9f4;
      }
      .toolbar {
        display: flex;
        align-items: center;
        font-size: 20px;
        height: 65px;
        background-color: #03a9f4;
        font-weight: 400;
        color: #fff;
        box-sizing: border-box;
        padding: 0px 16px;
      }
      .flex {
        display: flex;
        flex-direction: column;
        height: 100%;
      }
      .content {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        align-items: center;
        justify-content: center;
        padding: 16px;
      }
      h1 {
        font-family: <PERSON><PERSON>, sans-serif;
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        font-size: 20px;
        font-weight: 400;
        letter-spacing: 0;
        margin: 0 0 0 72px;
      }
      .mdc-button {
        font-family: Roboto, sans-serif;
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        font-size: 0.875rem;
        line-height: 2.25rem;
        font-weight: 500;
        letter-spacing: 0.0892857143em;
        text-decoration: none;
        padding: 0 8px 0 8px;
        display: inline-flex;
        position: relative;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        min-width: 64px;
        border: none;
        outline: none;
        line-height: inherit;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-appearance: none;
        overflow: visible;
        vertical-align: middle;
        border-radius: 4px;
      }

      .mdc-button::-moz-focus-inner {
        padding: 0;
        border: 0;
      }

      .mdc-button:active {
        outline: none;
      }

      .mdc-button:hover {
        cursor: pointer;
      }

      .mdc-button .mdc-button__ripple {
        border-radius: 4px;
      }

      .mdc-button--raised .mdc-button__icon,
      .mdc-button--unelevated .mdc-button__icon,
      .mdc-button--outlined .mdc-button__icon {
        margin-left: -4px;
        margin-right: 8px;
      }

      [dir="rtl"] .mdc-button--raised .mdc-button__icon,
      .mdc-button--raised .mdc-button__icon[dir="rtl"],
      [dir="rtl"] .mdc-button--unelevated .mdc-button__icon,
      .mdc-button--unelevated .mdc-button__icon[dir="rtl"],
      [dir="rtl"] .mdc-button--outlined .mdc-button__icon,
      .mdc-button--outlined .mdc-button__icon[dir="rtl"] {
        margin-left: 8px;
        margin-right: -4px;
      }

      .mdc-button--raised .mdc-button__label + .mdc-button__icon,
      .mdc-button--unelevated .mdc-button__label + .mdc-button__icon,
      .mdc-button--outlined .mdc-button__label + .mdc-button__icon {
        margin-left: 8px;
        margin-right: -4px;
      }

      [dir="rtl"] .mdc-button--raised .mdc-button__label + .mdc-button__icon,
      .mdc-button--raised .mdc-button__label + .mdc-button__icon[dir="rtl"],
      [dir="rtl"]
        .mdc-button--unelevated
        .mdc-button__label
        + .mdc-button__icon,
      .mdc-button--unelevated .mdc-button__label + .mdc-button__icon[dir="rtl"],
      [dir="rtl"] .mdc-button--outlined .mdc-button__label + .mdc-button__icon,
      .mdc-button--outlined .mdc-button__label + .mdc-button__icon[dir="rtl"] {
        margin-left: -4px;
        margin-right: 8px;
      }

      .mdc-button--raised,
      .mdc-button--unelevated {
        padding: 0 16px 0 16px;
      }

      .mdc-button--raised {
        background-color: #03a9f4;
        color: #fff;
        box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2),
          0px 2px 2px 0px rgba(0, 0, 0, 0.14),
          0px 1px 5px 0px rgba(0, 0, 0, 0.12);
        transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
      }

      .mdc-button--raised:hover,
      .mdc-button--raised:focus {
        box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2),
          0px 4px 5px 0px rgba(0, 0, 0, 0.14),
          0px 1px 10px 0px rgba(0, 0, 0, 0.12);
      }

      .mdc-button--raised:active {
        box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
          0px 8px 10px 1px rgba(0, 0, 0, 0.14),
          0px 3px 14px 2px rgba(0, 0, 0, 0.12);
      }

      .mdc-button {
        --mdc-ripple-fg-size: 0;
        --mdc-ripple-left: 0;
        --mdc-ripple-top: 0;
        --mdc-ripple-fg-scale: 1;
        --mdc-ripple-fg-translate-end: 0;
        --mdc-ripple-fg-translate-start: 0;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        will-change: transform, opacity;
      }

      .mdc-button .mdc-button__ripple::before,
      .mdc-button .mdc-button__ripple::after {
        position: absolute;
        border-radius: 50%;
        opacity: 0;
        pointer-events: none;
        content: "";
      }

      .mdc-button .mdc-button__ripple::before {
        transition: opacity 15ms linear, background-color 15ms linear;
        z-index: 1;
      }

      .mdc-button .mdc-button__ripple::before,
      .mdc-button .mdc-button__ripple::after {
        top: calc(50% - 100%);
        left: calc(50% - 100%);
        width: 200%;
        height: 200%;
      }

      .mdc-button.mdc-ripple-upgraded .mdc-button__ripple::after {
        width: var(--mdc-ripple-fg-size, 100%);
        height: var(--mdc-ripple-fg-size, 100%);
      }

      .mdc-button .mdc-button__ripple::before,
      .mdc-button .mdc-button__ripple::after {
        background-color: #03a9f4;
      }

      .mdc-button .mdc-button__ripple::before,
      .mdc-button .mdc-button__ripple::after {
        background-color: var(--mdc-theme-primary, #03a9f4);
      }

      .mdc-button:hover .mdc-button__ripple::before {
        opacity: 0.04;
      }

      .mdc-button .mdc-button__ripple {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .mdc-button .mdc-button__ripple {
        top: 0;
        left: 0;
      }

      .mdc-button--raised .mdc-button__ripple::before {
        background-color: #fff;
      }

      .mdc-button--raised .mdc-button__ripple::before {
        background-color: var(--mdc-theme-on-primary, #fff);
      }

      .mdc-button--raised:hover .mdc-button__ripple::before {
        opacity: 0.08;
      }

      .mdc-button--raised .mdc-button__ripple::after {
        transition: opacity 150ms linear;
      }

      .mdc-button--raised:active .mdc-button__ripple::after {
        transition-duration: 75ms;
        opacity: 0.24;
      }

      .mdc-button {
        height: 36px;
      }

      .mdc-button__ripple {
        box-sizing: content-box;
      }
    </style>
  </head>
  <body>
    <div class="flex">
      <div class="toolbar"><h1>Zigbee network by Dresden Elektronik</h1></div>
      <div class="content">
        <p>
          You can use the
          <a
            href="https://phoscon.de/en/conbee/software#phoscon-app"
            target="_blank"
            rel="noreferrer"
            >Phoscon Web App</a
          >
          or the
          <a
            href="https://phoscon.de/en/conbee/software#deconz"
            target="_blank"
            rel="noreferrer"
            >deCONZ application</a
          >
          through VNC
        </p>

        <div>
          <a href="pwa/index.html">
            <button class="mdc-button mdc-button--raised">
              <div class="mdc-button__ripple"></div>
              <span class="mdc-button__label">Phoscon</span>
            </button>
          </a>
          <a href="novnc/vnc_lite.html?scale=yes">
            <button class="mdc-button mdc-button--raised">
              <div class="mdc-button__ripple"></div>

              <span class="mdc-button__label">deCONZ</span>
            </button>
          </a>
        </div>
      </div>
    </div>
  </body>
</html>
