{"abi": "X86_64", "info": {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}, "cxxBuildFolder": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rive_common-0.4.12\\android\\.cxx\\Debug\\443x4m4q\\x86_64", "soFolder": "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\rive_common\\intermediates\\cxx\\Debug\\443x4m4q\\obj\\x86_64", "soRepublishFolder": "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\rive_common\\intermediates\\cmake\\debug\\obj\\x86_64", "abiPlatformVersion": 19, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": [], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rive_common-0.4.12\\android\\.cxx", "intermediatesBaseFolder": "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\rive_common\\intermediates", "intermediatesFolder": "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\rive_common\\intermediates\\cxx", "gradleModulePathName": ":rive_common", "moduleRootFolder": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rive_common-0.4.12\\android", "moduleBuildFile": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rive_common-0.4.12\\android\\build.gradle", "makeFile": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rive_common-0.4.12\\android\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Android\\Sdk\\ndk\\25.1.8937393", "ndkVersion": "25.1.8937393", "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 19, "max": 33, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33}}, "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "cmakeToolchainFile": "C:\\Android\\Sdk\\ndk\\25.1.8937393\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "C:\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "C:\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "ARM64_V8A": "C:\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "X86": "C:\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "X86_64": "C:\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\android", "sdkFolder": "C:\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "C:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rive_common-0.4.12\\android\\.cxx\\Debug\\443x4m4q\\prefab\\x86_64", "isActiveAbi": true, "fullConfigurationHash": "443x4m4q5l5y1b4y69830681f1v2n2x49q3w4x4e1l2e01v4y2d726z5n553o", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.1.0.\n#   - $NDK is the path to NDK 25.1.8937393.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HC:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rive_common-0.4.12/android\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=19\n-DANDROID_PLATFORM=android-19\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-<PERSON><PERSON><PERSON>_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:/Users/<USER>/Desktop/zain/zaen/build/rive_common/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:/Users/<USER>/Desktop/zain/zaen/build/rive_common/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-BC:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rive_common-0.4.12/android/.cxx/Debug/$HASH/$ABI\n-GNinja", "configurationArguments": ["-HC:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rive_common-0.4.12\\android", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=19", "-DANDROID_PLATFORM=android-19", "-DANDROID_ABI=x86_64", "-DCMAKE_ANDROID_ARCH_ABI=x86_64", "-DANDROID_NDK=C:\\Android\\Sdk\\ndk\\25.1.8937393", "-DCMAKE_ANDROID_NDK=C:\\Android\\Sdk\\ndk\\25.1.8937393", "-DCMAKE_TOOLCHAIN_FILE=C:\\Android\\Sdk\\ndk\\25.1.8937393\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\rive_common\\intermediates\\cxx\\Debug\\443x4m4q\\obj\\x86_64", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\rive_common\\intermediates\\cxx\\Debug\\443x4m4q\\obj\\x86_64", "-DCMAKE_BUILD_TYPE=Debug", "-BC:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rive_common-0.4.12\\android\\.cxx\\Debug\\443x4m4q\\x86_64", "-<PERSON><PERSON><PERSON><PERSON>"], "intermediatesParentFolder": "C:\\Users\\<USER>\\Desktop\\zain\\zaen\\build\\rive_common\\intermediates\\cxx\\Debug\\443x4m4q"}