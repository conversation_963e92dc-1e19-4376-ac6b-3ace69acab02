---
# yamllint disable rule:line-length rule:truthy
name: Build add-on

env:
  BUILD_ARGS: "--test"
  MONITORED_FILES: "apparmor.txt build.yaml config.yaml Dockerfile data rootfs"

on:
  pull_request:
    branches: ["master"]
  push:
    branches: ["master"]

jobs:
  init:
    runs-on: ubuntu-22.04
    name: Initialize builds
    outputs:
      changed_files: ${{ steps.changed_files.outputs.all }}
      changed_addons: ${{ steps.changed_addons.outputs.addons }}
      changed: ${{ steps.changed_addons.outputs.changed }}
    steps:
      - name: Check out the repository
        uses: actions/checkout@v4.2.2

      - name: Get changed files
        id: changed_files
        uses: masesgroup/retrieve-changed-files@v3.0.0

      - name: Get add-ons
        id: addons
        run: |
          declare -a addons
          for addon in $(find ./ -name config.yaml | cut -d "/" -f2 | sort -u); do
            addons+=("$addon");
          done
          echo "addons=${addons[@]}" >> "$GITHUB_OUTPUT"

      - name: Get changed add-ons
        id: changed_addons
        run: |
          declare -a changed_addons
          for addon in ${{ steps.addons.outputs.addons }}; do
            if [[ "${{ steps.changed_files.outputs.all }}" =~ $addon ]]; then
              for file in ${{ env.MONITORED_FILES }}; do
                  if [[ "${{ steps.changed_files.outputs.all }}" =~ $addon/$file ]]; then
                    if [[ ! "${changed_addons[@]}" =~ $addon ]]; then
                      changed_addons+=("\"${addon}\",");
                    fi
                  fi
              done
            fi
          done

          changed=$(echo ${changed_addons[@]} | rev | cut -c 2- | rev)
          if [[ -n ${changed} ]]; then
            echo "Changed add-ons: $changed";
            echo "changed=true" >> "$GITHUB_OUTPUT";
            echo "addons=[$changed]" >> "$GITHUB_OUTPUT";
          else
            echo "No add-on had any monitored files changed (${{ env.MONITORED_FILES }})";
          fi

  build:
    needs: init
    runs-on: ubuntu-22.04
    if: needs.init.outputs.changed == 'true'
    name: Build ${{ matrix.arch }} ${{ matrix.addon }} add-on
    strategy:
      matrix:
        addon: ${{ fromJson(needs.init.outputs.changed_addons) }}
        arch: ["aarch64", "amd64", "armhf", "armv7", "i386"]

    steps:
      - name: Check out repository
        uses: actions/checkout@v4.2.2

      - name: Get information
        id: info
        uses: home-assistant/actions/helpers/info@master
        with:
          path: "./${{ matrix.addon }}"

      - name: Check add-on
        id: check
        run: |
          if [[ "${{ steps.info.outputs.architectures }}" =~ ${{ matrix.arch }} ]]; then
             echo "build_arch=true" >> "$GITHUB_OUTPUT";
           else
             echo "${{ matrix.arch }} is not a valid arch for ${{ matrix.addon }}, skipping build";
          fi

      - name: Set build arguments
        if: steps.check.outputs.build_arch == 'true'
        run: |
          if [[ -z "${{ github.head_ref }}" ]] && [[ "${{ github.event_name }}" == "push" ]]; then
              echo "BUILD_ARGS=--docker-hub-check" >> $GITHUB_ENV;
          fi

      - name: Login to DockerHub
        if: env.BUILD_ARGS == '--docker-hub-check'
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build ${{ matrix.addon }} add-on
        if: steps.check.outputs.build_arch == 'true'
        uses: home-assistant/builder@2025.03.0
        with:
          args: |
            ${{ env.BUILD_ARGS }} \
            --${{ matrix.arch }} \
            --target /data/${{ matrix.addon }} \
            --addon
