import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:expansion_tile_card/expansion_tile_card.dart';
import 'package:flutter/cupertino.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:get/get.dart';
import 'package:mysql1/mysql1.dart';
import 'package:sqflite/sqflite.dart';
import 'package:flutter/material.dart';
import 'package:zaen/models/pages/pages_index.dart';
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/shared/commands/tv.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/view/room/double_tap/del.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/view/room/double_tap/edit_names.dart';
import 'package:zaen/view/room/double_tap/edit_room.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Future<dynamic> tvDoubleTap({
  required var context,
  required var i,
}) async {
  final conn = await MySqlConnection.connect(ConnectionSettings(
      host: controller.hostZain.value,
      // port: 80,
      user: 'root',
      db: 'zain',
      password: 'zain',
      characterSet: CharacterSet.UTF8));
  var appDB = await openDatabase('${controller.system}.db', version: 3);

  Map rooms = {};
  print(11111111111111111);

  var selectroom = 0;
  var selectname = 0;
  for (var r = 0; r < controller.rooms.keys.toList().length; r++) {
    rooms[controller.rooms[controller.rooms.keys.toList()[r]]['id']] =
        controller.rooms[controller.rooms.keys.toList()[r]]['privName'];
    if (roomId == controller.rooms[controller.rooms.keys.toList()[r]]['id']) {
      selectroom = r;
    }
  }
  showBottomSheet(
      enableDrag: true,
      backgroundColor: Colors.transparent,
      context: context,
      builder: (context) {
        return GetBuilder<HomeController>(
          builder: (controller) => TVPage(
              id: i['id'],
              sizedWidth: controller.sizedWidth,
              sizedHeight: controller.sizedHight,
              sized: controller.sized,
              connect: controller.devices[i['id']],
              // acId: i['id'],
              deviceState: controller.rooms[rooms.keys.toList()[selectroom]]
                  ['devices'][i['id']]['state'],
              roomN: rooms[rooms.keys.toList()[selectroom]],
              del: () {
                del(appDB: appDB, i: i, context: context, conn: conn);
              },
              Dfavorite: () {
                Dfavorite(
                    context: context,
                    device: i['id'],
                    appDB: appDB,
                    state: true);
              },
              editRoom: () {
                x() async {
                  var s = await editRoom(
                      context: context,
                      selectroom: selectroom,
                      rooms: rooms,
                      i: i);
                  s = await s.toString();
                  if (selectroom != int.parse(s)) {
                    selectroom = await int.parse(s);
                    final builder = MqttClientPayloadBuilder();
                    builder.addString('1');
                    await client.publishMessage(
                        'edit', MqttQos.atLeastOnce, builder.payload!);
                    await builder.clear();
                    await builder.addString('re');
                    await client.publishMessage(
                        i['id'], MqttQos.atLeastOnce, builder.payload!);
                    Navigator.of(context).pop();
                  }
                }

                x();
              },
              editNames: (string) async {
                selectname = await editNames(
                    context: context,
                    conn: conn,
                    selectname: selectname,
                    string: string,
                    i: i);
              },
              editPrivName: (privN, priv) async {
                print(privN!);
                if (privN) {
                  print(123);
                  s() async {
                    var myHome = await appDB.rawQuery('SELECT * FROM devices');

                    print(myHome);

                    await appDB.transaction((txn) async {
                      print(12345);
                      await txn.rawUpdate(
                          'UPDATE devices SET name = ? WHERE id = ?',
                          [priv, i['id']]);
                      print(123456);
                    });

                    myHome = await appDB.rawQuery('SELECT * FROM devices');
                    print(myHome);
                  }

                  await s();
                  controller.rooms[rooms.keys.toList()[selectroom]]['devices']
                      [i['id']]['priv'] = priv;
                  // i['priv']=priv;
                  privN = false;
                } else {
                  priv = i['priv'] != null ? i['priv'] : 'X';
                }
                controller.update();
              },
              switchState: (val) {
                commandTvSw(val!, i, rooms.keys.toList()[selectroom]);
              },
              tv_task: [
                SizedBox(
                  height: controller.sizedHight * 0.01,
                ),
                Text('اضافة مهمه مجدولة',
                    textDirection: TextDirection.rtl,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: controller.sized * 0.015,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textColor.withOpacity(0.7),
                    )),
                Directionality(
                  textDirection: TextDirection.rtl,
                  child: Container(
                    margin: EdgeInsets.all(controller.sized * 0.01),
                    child: ExpansionTileCard(
                      trailing: IconButton(
                          onPressed: () {},
                          icon: Icon(Icons.arrow_drop_down_circle_outlined,
                              color: AppColors.textColor2.withOpacity(0.4),
                              size: controller.sized * 0.03)),
                      baseColor: AppColors.backgroundColor2.withOpacity(1),
                      animateTrailing: true,
                      duration: Duration(milliseconds: 450),
                      expandedColor: AppColors.backgroundColor2.withOpacity(1),
                      borderRadius: BorderRadius.all(Radius.circular(20)),
                      title: Text('الايقاف و التشغيل',
                          textDirection: TextDirection.rtl,
                          // textAlign: TextAlign.start,
                          style: TextStyle(
                            fontSize: controller.sized * 0.015,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textColor.withOpacity(0.4),
                          )),
                      children: [
                        Switch(value: true, onChanged: (t) {}),
                        Row(
                          children: [MaterialButton(onPressed: () {})],
                        )
                      ],
                    ),
                  ),
                ),
                Container(
                  child: Column(
                    children: [
                      SizedBox(
                        height: controller.sizedHight * 0.01,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text('القنوات',
                              textDirection: TextDirection.rtl,
                              // textAlign: TextAlign.start,
                              style: TextStyle(
                                fontSize: controller.sized * 0.013,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textColor.withOpacity(0.4),
                              )),
                          SizedBox(
                            width: controller.sizedWidth * 0.01,
                          ),
                        ],
                      ),
                      Divider(
                        color: AppColors.textColor2.withOpacity(0.25),
                        endIndent: controller.sizedWidth * 0.1,
                        // height: sizedHeight*0.05,
                      )
                    ],
                  ),
                ),
                Container(
                  child: Column(
                    children: [
                      SizedBox(
                        height: controller.sizedHight * 0.01,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text('الصوت',
                              textDirection: TextDirection.rtl,
                              // textAlign: TextAlign.start,
                              style: TextStyle(
                                fontSize: controller.sized * 0.013,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textColor.withOpacity(0.4),
                              )),
                          SizedBox(
                            width: controller.sizedWidth * 0.01,
                          ),
                        ],
                      ),
                      Divider(
                        color: AppColors.textColor2.withOpacity(0.25),
                        endIndent: controller.sizedWidth * 0.1,
                        // height: sizedHeight*0.05,
                      )
                    ],
                  ),
                ),
              ],
              tapOn_Tv_Icon: () {
                print('يعرض حاله التلفاز');
              },
              tapOn_VolumeUp: () {
                commandTvRemote(
                    'VOICE + 1', i, rooms.keys.toList()[selectroom]);
              },
              tapOn_VolumeDown: () {
                commandTvRemote(
                    'VOICE + 1', i, rooms.keys.toList()[selectroom]);
              },
              tapOn_ChUp: () {
                commandTvRemote('CH + 1', i, rooms.keys.toList()[selectroom]);
              },
              tapOn_ChDown: () {
                commandTvRemote('CH - 1', i, rooms.keys.toList()[selectroom]);
              },
              tapOn_VolumeMute: () {
                commandTvRemote(
                    controller.rooms[roomId]['devices'][i['id']]['sil'] == true
                        ? 'SIL-OFF'
                        : 'SIL-ON',
                    i,
                    roomId);
              },
              tapOn_123: () {
                if (!controller.canControlDevices()) {
                  showNoPermissionDialog(
                      customMessage: 'ليس لديك صلاحية للتحكم في التلفاز');
                  return;
                }
                TextEditingController ch = TextEditingController();
                if (client.connectionStatus!.state.name == 'connected') {
                  AwesomeDialog(
                      context: context,
                      dialogType: DialogType.noHeader,
                      headerAnimationLoop: true,
                      animType: AnimType.topSlide,
                      dialogBackgroundColor: AppColors.surfaceElevated,
                      body: Material(
                        color: Colors.transparent,
                        child: Container(
                          child: Column(
                            children: [
                              TextField(
                                autofocus: true,
                                controller: ch,
                                cursorColor: AppColors.primary,
                                style: TextStyle(
                                    color: AppColors.textPrimary,
                                    fontSize: controller.sized * 0.025,
                                    fontWeight: FontWeight.w600),
                                textInputAction: TextInputAction.done,
                                textAlign: TextAlign.center,
                                decoration: InputDecoration(
                                  hintText: "ادخل الرقم",
                                  hintStyle: TextStyle(
                                    color: AppColors.textHint,
                                    fontSize: controller.sized * 0.02,
                                    fontWeight: FontWeight.normal,
                                  ),
                                  filled: true,
                                  fillColor: AppColors.surface,
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: controller.sizedWidth * 0.04,
                                    vertical: controller.sizedHight * 0.015,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                      color: AppColors.border,
                                      width: 1.0,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                      color: AppColors.border,
                                      width: 1.0,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                      color: AppColors.primary,
                                      width: 2.0,
                                    ),
                                  ),
                                ),
                                keyboardType: TextInputType.number,
                                maxLength: 10,
                              ),
                              CircleAvatar(
                                  radius: controller.sized * 0.0225,
                                  backgroundColor: AppColors.primaryColor,
                                  child: IconButton(
                                    padding: EdgeInsets.only(
                                        right: controller.sizedWidth * 0.01),
                                    onPressed: () {
                                      if (ch.text.isNumericOnly) {
                                        // if (controller.rooms[rooms.keys
                                        //             .toList()[selectroom]]
                                        //         ['devices'][i['id']]['state'] ==
                                        //     false) {
                                        //   switchTap(
                                        //       'state', i['state'], i['id']);
                                        // }
                                        // controller.rooms[
                                        //         rooms.keys.toList()[selectroom]]
                                        //     ['state'] = true;
                                        // controller.homeState = true;

                                        commandTvRemote('CH = ' + ch.text, i,
                                            rooms.keys.toList()[selectroom]);
                                      }
                                    },
                                    icon: Center(
                                      child: iconStyle(
                                        icon: Icons.arrow_back_ios_rounded,
                                        size: controller.sized * 0.03,
                                        color: AppColors.backgroundColor3,
                                      ),
                                    ),
                                  )),
                              SizedBox(
                                height: controller.sizedHight * 0.02,
                              )
                            ],
                          ),
                        ),
                      )).show();
                }
              },
              tapOn_menu: () {
                commandTvRemote('M', i, rooms.keys.toList()[selectroom]);
              },
              tapOn_star: (string) async {
                if (!controller.canControlDevices()) {
                  showNoPermissionDialog(
                      customMessage: 'ليس لديك صلاحية للتحكم في التلفاز');
                  return;
                }
                if (client.connectionStatus!.state.name == 'connected') {
                  bool finish = true;
                  Map ch = controller.rooms[rooms.keys.toList()[selectroom]]
                      ['devices'][i['id']]['ch'];
                  Map chEdit = {};
                  for (var c in ch.keys) {
                    chEdit[editChar(c)] = ch[c];
                  }
                  print(chEdit);
                  if (ch.length == 0) {
                    string = 'add';
                  }
                  GlobalKey<FormState> kname = new GlobalKey<FormState>();
                  TextEditingController name = TextEditingController(
                    text: string == 'edit' ? ch.keys.toList()[0] : '',
                  );
                  GlobalKey<FormState> knumber = new GlobalKey<FormState>();

                  TextEditingController number = TextEditingController(
                    text: string == 'edit' ? ch[ch.keys.toList()[0]] : '',
                  );
                  bool edit = false;
                  bool numEdit = false;
                  bool isValidate = false;

                  AwesomeDialog(
                    context: context,
                    dialogType: DialogType.noHeader,
                    headerAnimationLoop: true,
                    animType: AnimType.topSlide,
                    dialogBackgroundColor: AppColors.surfaceElevated,
                    body: StatefulBuilder(builder: (context, setState) {
                      return Column(
                        children: [
                          SizedBox(
                            height: controller.sizedHight * 0.35,
                            child: CupertinoPicker(
                              squeeze: 0.9,
                              // scrollController:
                              //     FixedExtentScrollController(
                              //         initialItem:
                              //             3),
                              itemExtent: 40, //height of each item

                              looping: false,

                              magnification: 1.2,
                              backgroundColor: Colors.transparent,
                              children: <Widget>[
                                for (var c in ch.keys)
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal:
                                            controller.sizedWidth * 0.2),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Expanded(
                                          child: Text(ch[c],
                                              style: TextStyle(
                                                color: AppColors.textColor2,
                                                fontSize:
                                                    controller.sized * 0.016,
                                                fontWeight: FontWeight.bold,
                                              )),
                                        ),
                                        Text(
                                          c,
                                          style: TextStyle(
                                            color: AppColors.textColor2,
                                            fontSize: controller.sized * 0.016,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                              onSelectedItemChanged: (int index) {
                                selectname = index;
                                var formdata = kname.currentState;
                                var formNumber = knumber.currentState;

                                if (isValidate) {
                                  formdata!.reset();
                                  formNumber!.reset();
                                }
                                if (string == 'edit') {
                                  name.text = ch.keys.toList()[selectname];
                                  number.text =
                                      ch[ch.keys.toList()[selectname]];
                                }
                              },
                            ),
                          ),
                          SizedBox(
                            height: controller.sizedHight * 0.025,
                          ),
                          string == 'edit' || string == 'add'
                              ? Material(
                                  color: Colors.transparent,
                                  child: Padding(
                                    padding:
                                        EdgeInsets.all(controller.sized * 0.01),
                                    child: Column(
                                      children: [
                                        Form(
                                          key: kname,
                                          child: Container(
                                            width: controller.sizedWidth * 0.85,
                                            child: TextFormField(
                                              controller: name,
                                              maxLength: 25,
                                              showCursor: true,
                                              cursorColor: AppColors.primary,
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                color: AppColors.textPrimary,
                                                fontSize:
                                                    controller.sized * 0.015,
                                                fontWeight: FontWeight.w500,
                                              ),
                                              validator: (val) {
                                                if (name.text == '' ||
                                                    name.text == null) {
                                                  name.text = string == 'edit'
                                                      ? ch.keys
                                                          .toList()[selectname]
                                                          .toString()
                                                      : '';
                                                  edit = false;
                                                  return "قم بادخال اسم للقناة";
                                                } else if ((chEdit.containsKey(
                                                                editChar(name
                                                                    .text)) ==
                                                            true &&
                                                        string == 'add') ||
                                                    (string ==
                                                            'edit' &&
                                                        chEdit.containsKey(
                                                                editChar(name
                                                                    .text)) ==
                                                            true &&
                                                        chEdit.keys.toList()[
                                                                selectname] !=
                                                            editChar(
                                                                name.text))) {
                                                  return '"${name.text}" موجود او يشابة اسم موجود في القائمة';
                                                } else if (edit == false &&
                                                    string == 'edit') {
                                                  return "لم تقم باي تعديل";
                                                } else {
                                                  for (var i = 0;
                                                      i < name.text.length;
                                                      i++) {
                                                    if (arabic.contains(
                                                            name.text[i]) ||
                                                        name.text[i]
                                                            .isNumericOnly) {
                                                      edit = true;
                                                    } else {
                                                      name.text = string ==
                                                              'edit'
                                                          ? ch.keys
                                                              .toList()[
                                                                  selectname]
                                                              .toString()
                                                          : '';
                                                      edit = false;
                                                      return "قم بإدخال احرف عربية او ارقام فقط";
                                                    }
                                                  }
                                                  return null;
                                                }
                                              },
                                              onChanged: (s) {
                                                edit = true;
                                              },
                                              onEditingComplete: () {
                                                FocusManager
                                                    .instance.primaryFocus
                                                    ?.unfocus();
                                                var formdata =
                                                    kname.currentState;
                                                formdata!.validate();
                                                // if (name.text == '' || name.text == null) {
                                                //   name.text = string == 'edit' ? ch.keys.toList()[0].toString() : '';
                                                //   edit = false;
                                                // } else if (ch.containsKey(name.text) == false) {
                                                //   for (var i = 0; i < name.text.length; i++) {
                                                //     if (arabic.contains(name.text[i]) || name.text[i].isNumericOnly) {
                                                //       edit = true;
                                                //     } else {
                                                //       name.text = string == 'edit' ? ch.keys.toList()[0].toString() : '';
                                                //       break;
                                                //     }
                                                //   }
                                                // }
                                              },
                                              decoration: InputDecoration(
                                                hintText: 'اسم القناة الجديدة',
                                                hintStyle: TextStyle(
                                                  color: AppColors.textHint,
                                                  fontSize:
                                                      controller.sized * 0.014,
                                                  fontWeight: FontWeight.normal,
                                                ),
                                                filled: true,
                                                fillColor: AppColors.surface,
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                  horizontal:
                                                      controller.sizedWidth *
                                                          0.04,
                                                  vertical:
                                                      controller.sizedHight *
                                                          0.015,
                                                ),
                                                border: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.border,
                                                    width: 1.0,
                                                  ),
                                                ),
                                                enabledBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.border,
                                                    width: 1.0,
                                                  ),
                                                ),
                                                focusedBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.primary,
                                                    width: 2.0,
                                                  ),
                                                ),
                                                errorBorder: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.error,
                                                    width: 1.5,
                                                  ),
                                                ),
                                                focusedErrorBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.error,
                                                    width: 2.0,
                                                  ),
                                                ),
                                                suffixIcon: Icon(
                                                  Icons.edit_rounded,
                                                  color:
                                                      AppColors.textSecondary,
                                                  size: controller.sized * 0.02,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          height: controller.sizedHight * 0.015,
                                        ),
                                        Form(
                                          key: knumber,
                                          child: Container(
                                            width: controller.sizedWidth * 0.85,
                                            child: TextFormField(
                                              controller: number,
                                              maxLength: 25,
                                              showCursor: true,
                                              keyboardType:
                                                  TextInputType.number,
                                              cursorColor: AppColors.primary,
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                color: AppColors.textPrimary,
                                                fontSize:
                                                    controller.sized * 0.015,
                                                fontWeight: FontWeight.w500,
                                              ),
                                              validator: (val) {
                                                if (number.text == '' ||
                                                    number.text == null) {
                                                  number.text = string == 'edit'
                                                      ? ch[ch.keys
                                                          .toList()[selectname]
                                                          .toString()]
                                                      : '';
                                                  numEdit = false;
                                                  return "قم بإدخال رقم اقناة";
                                                } else if (number.text
                                                        .toString()
                                                        .isNumericOnly ==
                                                    false) {
                                                  numEdit = false;
                                                  return "يجب ادخال ارقام فقط";
                                                } else if (number.text
                                                    .toString()
                                                    .isNumericOnly) {
                                                  numEdit = false;
                                                  return null;
                                                }
                                              },
                                              onChanged: (s) {
                                                numEdit = true;
                                              },
                                              onEditingComplete: () {
                                                FocusManager
                                                    .instance.primaryFocus
                                                    ?.unfocus();
                                                var formdata =
                                                    knumber.currentState;
                                                formdata!.validate();

                                                // if (number.text == '' || number.text == null) {
                                                //   number.text = string == 'edit' ? ch.values.toString()[0] : '';
                                                //   numEdit = false;
                                                // } else if (ch.containsValue(number.text) == false && number.text.isNumericOnly) {
                                                //   print('1111111111111111111');
                                                //   numEdit = true;
                                                // }
                                              },
                                              decoration: InputDecoration(
                                                hintText: 'رقم القناة الجديدة',
                                                hintStyle: TextStyle(
                                                  color: AppColors.textHint,
                                                  fontSize:
                                                      controller.sized * 0.014,
                                                  fontWeight: FontWeight.normal,
                                                ),
                                                filled: true,
                                                fillColor: AppColors.surface,
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                  horizontal:
                                                      controller.sizedWidth *
                                                          0.04,
                                                  vertical:
                                                      controller.sizedHight *
                                                          0.015,
                                                ),
                                                border: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.border,
                                                    width: 1.0,
                                                  ),
                                                ),
                                                enabledBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.border,
                                                    width: 1.0,
                                                  ),
                                                ),
                                                focusedBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.primary,
                                                    width: 2.0,
                                                  ),
                                                ),
                                                errorBorder: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.error,
                                                    width: 1.5,
                                                  ),
                                                ),
                                                focusedErrorBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.error,
                                                    width: 2.0,
                                                  ),
                                                ),
                                                suffixIcon: Icon(
                                                  Icons.edit_rounded,
                                                  color:
                                                      AppColors.textSecondary,
                                                  size: controller.sized * 0.02,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          height: controller.sizedHight * 0.055,
                                        ),
                                        Container(
                                          width: double.infinity,
                                          child: ConditionalBuilder(
                                            condition:
                                                controller.home.isNotEmpty &&
                                                    client.connectionStatus!
                                                            .state.name ==
                                                        'connected' &&
                                                    finish,
                                            fallback: (context) => const Center(
                                                child:
                                                    CircularProgressIndicator()),
                                            builder: (context) => submitButtom(
                                              text: string == 'add'
                                                  ? 'موافق'
                                                  : 'تعديل',
                                              onPressed: () async {
                                                FocusManager
                                                    .instance.primaryFocus
                                                    ?.unfocus();
                                                var formdata =
                                                    kname.currentState;
                                                var formNumber =
                                                    knumber.currentState;

                                                if (formdata!.validate() &&
                                                    formNumber!.validate()) {
                                                  edit = true;
                                                  numEdit = true;
                                                }
                                                // if (edit || numEdit) {
                                                //   edit = false;
                                                //   numEdit = false;
                                                //   if (name.text == '' ||
                                                //       name.text == null ||
                                                //       number.text == '' ||
                                                //       number.text == null) {
                                                //     name.text = string == 'edit'
                                                //         ? ch.keys
                                                //             .toList()[0]
                                                //             .toString()
                                                //         : '';
                                                //     edit = false;
                                                //     number.text = string == 'edit'
                                                //         ? ch[ch.keys
                                                //             .toList()[0]
                                                //             .toString()]
                                                //         : '';
                                                //     numEdit = false;
                                                //   } else if ((chEdit.containsKey(
                                                //                   editChar(
                                                //                       name.text)) ==
                                                //               false ||
                                                //           (string == 'edit' &&
                                                //               chEdit.keys.toList()[
                                                //                       selectitem] ==
                                                //                   editChar(
                                                //                       name.text))) &&
                                                //       number.text
                                                //           .toString()
                                                //           .isNumericOnly) {
                                                //     print(
                                                //         '1111111111111111111111111111111111111111111');
                                                //     numEdit = true;
                                                //     for (var i = 0;
                                                //         i < name.text.length;
                                                //         i++) {
                                                //       if (arabic.contains(
                                                //               name.text[i]) ||
                                                //           name.text[i]
                                                //               .isNumericOnly) {
                                                //         edit = true;
                                                //       } else {
                                                //         name.text = string == 'edit'
                                                //             ? ch.keys
                                                //                 .toList()[0]
                                                //                 .toString()
                                                //             : '';
                                                //         edit = false;
                                                //         break;
                                                //       }
                                                //     }
                                                //   }
                                                // }
                                                if (string != 'show') {
                                                  final conn =
                                                      await MySqlConnection.connect(
                                                          ConnectionSettings(
                                                              host: controller
                                                                  .hostZain
                                                                  .value,
                                                              // port: 80,
                                                              user: 'root',
                                                              db: 'zain',
                                                              password: 'zain',
                                                              characterSet:
                                                                  CharacterSet
                                                                      .UTF8));
                                                  if (string == 'edit' &&
                                                      edit &&
                                                      numEdit) {
                                                    // التحقق من صلاحية تعديل قنوات مفضلة
                                                    if (!controller
                                                        .canManageData()) {
                                                      showNoPermissionDialog(
                                                          customMessage:
                                                              'ليس لديك صلاحية لتعديل قنوات مفضلة');
                                                      return;
                                                    }

                                                    setState(
                                                      () {
                                                        finish = false;
                                                      },
                                                    );
                                                    var update = await conn.query(
                                                        'update ${i['id']}_TV set chaneel=? , number=? where chaneel=? and number=?',
                                                        [
                                                          name.text.toString(),
                                                          number.text
                                                              .toString(),
                                                          ch.keys.toList()[
                                                              selectname],
                                                          ch[ch.keys.toList()[
                                                              selectname]]
                                                        ]);

                                                    if (await update
                                                            .affectedRows! >
                                                        0) {
                                                      await getDevices();

                                                      setState(
                                                        () {
                                                          finish = true;
                                                          ch = {};
                                                          for (var c in controller
                                                              .rooms[rooms.keys
                                                                          .toList()[
                                                                      selectroom]]
                                                                  ['devices']
                                                                  [i['id']]
                                                                  ['ch']
                                                              .keys
                                                              .toList()) {
                                                            if (c ==
                                                                controller
                                                                    .rooms[rooms
                                                                            .keys
                                                                            .toList()[selectroom]]
                                                                        [
                                                                        'devices']
                                                                        [
                                                                        i['id']]
                                                                        ['ch']
                                                                    .keys
                                                                    .toList()[selectname]) {
                                                              ch[name.text] =
                                                                  number.text;
                                                            } else {
                                                              ch[c] = controller
                                                                      .rooms[
                                                                  rooms.keys
                                                                          .toList()[
                                                                      selectroom]]['devices'][i[
                                                                  'id']]['ch'][c];
                                                            }
                                                            chEdit = {};
                                                            for (var c
                                                                in ch.keys) {
                                                              chEdit[editChar(
                                                                  c)] = ch[c];
                                                            }
                                                          }
                                                        },
                                                      );
                                                    }
                                                  } else if (string == 'add' &&
                                                      edit &&
                                                      numEdit) {
                                                    // التحقق من صلاحية إضافة قنوات مفضلة
                                                    if (!controller
                                                        .canManageData()) {
                                                      showNoPermissionDialog(
                                                          customMessage:
                                                              'ليس لديك صلاحية لإضافة قنوات مفضلة');
                                                      return;
                                                    }

                                                    setState(
                                                      () {
                                                        finish = false;
                                                      },
                                                    );
                                                    var update = await conn.query(
                                                        'insert INTO ${i['id']}_TV(chaneel,number) values(?,?)',
                                                        [
                                                          name.text.toString(),
                                                          number.text
                                                              .toString(),
                                                        ]);
                                                    print(
                                                        '11111111222222222222222');

                                                    if (await update
                                                            .affectedRows! >
                                                        0) {
                                                      await getDevices();
                                                      setState(
                                                        () {
                                                          finish = true;
                                                          ch[name.text] =
                                                              number.text;
                                                          chEdit = {};
                                                          for (var c
                                                              in ch.keys) {
                                                            chEdit[editChar(
                                                                c)] = ch[c];
                                                          }
                                                          name.text = '';
                                                          number.text = '';
                                                        },
                                                      );
                                                    }
                                                  } else if (string == 'del') {
                                                    // التحقق من صلاحية حذف قنوات مفضلة
                                                    if (!controller
                                                        .canManageData()) {
                                                      showNoPermissionDialog(
                                                          customMessage:
                                                              'ليس لديك صلاحية لحذف قنوات مفضلة');
                                                      return;
                                                    }

                                                    print(ch.keys
                                                        .toList()[selectname]);
                                                    setState(
                                                      () {
                                                        finish = false;
                                                      },
                                                    );
                                                    var dell = await conn.query(
                                                        'DELETE FROM ${i['id']}_TV WHERE chaneel = ? AND number=?',
                                                        [
                                                          ch.keys.toList()[
                                                              selectname],
                                                          ch[ch.keys.toList()[
                                                              selectname]]
                                                        ]);
                                                    if (await dell
                                                            .affectedRows! >
                                                        0) {
                                                      await getDevices();
                                                      setState(
                                                        () {
                                                          ch = {};
                                                          for (var c in controller
                                                              .rooms[rooms.keys
                                                                          .toList()[
                                                                      selectroom]]
                                                                  ['devices']
                                                                  [i['id']]
                                                                  ['ch']
                                                              .keys
                                                              .toList()) {
                                                            if (c !=
                                                                controller
                                                                    .rooms[rooms
                                                                            .keys
                                                                            .toList()[selectroom]]
                                                                        [
                                                                        'devices']
                                                                        [
                                                                        i['id']]
                                                                        ['ch']
                                                                    .keys
                                                                    .toList()[selectname]) {
                                                              ch[c] = controller
                                                                      .rooms[
                                                                  rooms.keys
                                                                          .toList()[
                                                                      selectroom]]['devices'][i[
                                                                  'id']]['ch'][c];
                                                            }
                                                            chEdit = {};
                                                            for (var c
                                                                in ch.keys) {
                                                              chEdit[editChar(
                                                                  c)] = ch[c];
                                                            }
                                                          }
                                                        },
                                                      );
                                                    }
                                                    print(
                                                        '888888888888888888888888888888888888888888888888');
                                                  }

                                                  print(ch);
                                                }
                                              },
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                )
                              : string == 'show'
                                  ? Material(
                                      color: Colors.transparent,
                                      child: CircleAvatar(
                                          radius: controller.sized * 0.025,
                                          backgroundColor:
                                              AppColors.primaryColor,
                                          child: IconButton(
                                            padding: EdgeInsets.only(
                                                right: controller.sizedWidth *
                                                    0.01),
                                            onPressed: () {
                                              // if (controller.rooms[rooms.keys
                                              //                     .toList()[
                                              //                 selectroom]]
                                              //             ['devices'][i['id']]
                                              //         ['state'] ==
                                              //     false) {
                                              //   switchTap('state', i['state'],
                                              //       i['id']);
                                              // }
                                              // controller.rooms[rooms.keys
                                              //         .toList()[selectroom]]
                                              //     ['state'] = true;
                                              // controller.homeState = true;

                                              commandTvRemote(
                                                  'CH = ' +
                                                      ch[ch.keys.toList()[
                                                          selectname]],
                                                  i,
                                                  rooms.keys
                                                      .toList()[selectroom]);
                                              final builder =
                                                  MqttClientPayloadBuilder();
                                              builder.addString(i['id'] +
                                                  ' TV CH = ' +
                                                  ch[ch.keys
                                                      .toList()[selectname]]);
                                              client.publishMessage(
                                                  controller.homeId +
                                                      "/app/zain",
                                                  MqttQos.atLeastOnce,
                                                  builder.payload!);
                                            },
                                            icon: Icon(
                                              Icons.arrow_back_ios_rounded,
                                              size: controller.sized * 0.03,
                                              color: AppColors.backgroundColor3,
                                            ),
                                          )),
                                    )
                                  // string == 'del'
                                  : Padding(
                                      padding: EdgeInsets.all(
                                          controller.sized * 0.01),
                                      child: Column(
                                        children: [
                                          SizedBox(
                                            height:
                                                controller.sizedHight * 0.055,
                                          ),
                                          Container(
                                            width: double.infinity,
                                            child: ConditionalBuilder(
                                              condition:
                                                  controller.home.isNotEmpty &&
                                                      client.connectionStatus!
                                                              .state.name ==
                                                          'connected' &&
                                                      finish,
                                              fallback: (context) => const Center(
                                                  child:
                                                      CircularProgressIndicator()),
                                              builder: (context) => delButtom(
                                                onPressed: () async {
                                                  if (ch.length != 0) {
                                                    setState(
                                                      () {
                                                        finish = false;
                                                        ch = {};
                                                        for (var c in controller
                                                            .rooms[rooms.keys
                                                                        .toList()[
                                                                    selectroom]]
                                                                ['devices']
                                                                [i['id']]['ch']
                                                            .keys
                                                            .toList()) {
                                                          if (c !=
                                                              controller
                                                                  .rooms[
                                                                      rooms.keys
                                                                              .toList()[
                                                                          selectroom]]
                                                                      [
                                                                      'devices']
                                                                      [i['id']]
                                                                      ['ch']
                                                                  .keys
                                                                  .toList()[selectname]) {
                                                            ch[c] = controller
                                                                    .rooms[rooms
                                                                        .keys
                                                                        .toList()[
                                                                    selectroom]]['devices']
                                                                [
                                                                i['id']]['ch'][c];
                                                            print(c);
                                                          } else {
                                                            name.text = c;
                                                            number
                                                                .text = controller
                                                                    .rooms[rooms
                                                                        .keys
                                                                        .toList()[
                                                                    selectroom]]['devices']
                                                                [
                                                                i['id']]['ch'][c];
                                                          }
                                                          chEdit = {};
                                                          for (var c
                                                              in ch.keys) {
                                                            chEdit[editChar(
                                                                c)] = ch[c];
                                                          }
                                                        }
                                                        if (selectname !=
                                                                ch.length &&
                                                            selectname != 0) {
                                                          selectname =
                                                              selectname + 1;
                                                        } else if (selectname ==
                                                                ch.length &&
                                                            selectname != 0) {
                                                          selectname =
                                                              selectname - 1;
                                                        }
                                                      },
                                                    );

                                                    final conn =
                                                        await MySqlConnection.connect(
                                                            ConnectionSettings(
                                                                host: controller
                                                                    .hostZain
                                                                    .value,
                                                                // port: 80,
                                                                user: 'root',
                                                                db: 'zain',
                                                                password:
                                                                    'zain',
                                                                characterSet:
                                                                    CharacterSet
                                                                        .UTF8));
                                                    var del = await conn.query(
                                                        'DELETE FROM ${i['id']}_TV WHERE chaneel = ? AND number=?',
                                                        [
                                                          name.text,
                                                          number.text
                                                        ]);
                                                    if (await del
                                                            .affectedRows! >
                                                        0) {
                                                      await getDevices();
                                                      setState(
                                                        () {
                                                          finish = true;
                                                        },
                                                      );
                                                    }
                                                    print(
                                                        '888888888888888888888888888888888888888888888888');
                                                  }

                                                  print(ch);
                                                },
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                          SizedBox(
                            height: controller.sizedHight * 0.025,
                          )
                        ],
                      );
                    }),

                    // btnCancelOnPress:
                    //     () {},
                  ).show();
                }
              },
              tvPrivName: controller.rooms[rooms.keys.toList()[selectroom]]
                  ['devices'][i['id']]['priv'],
              sil: controller.rooms[rooms.keys.toList()[selectroom]]['devices']
                  [i['id']]['sil']),
        );
      });
}
