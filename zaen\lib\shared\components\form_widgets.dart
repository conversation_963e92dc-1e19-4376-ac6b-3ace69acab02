import 'package:flutter/material.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/components/config.dart';

// === ويدجات الحقول النصية المخصصة ===

/// ويدجت مخصص لـ TextFormField يتناسب مع الوضعين الداكن والفاتح
///
/// مثال على الاستخدام:
/// ```dart
/// customTextFormField(
///   textController: myController,
///   hintText: 'أدخل النص هنا',
///   maxLength: 50,
///   onChanged: (value) => print(value),
/// )
/// ```
Widget customTextFormField({
  required TextEditingController textController,
  String? hintText,
  String? labelText,
  int? maxLength,
  bool obscureText = false,
  TextInputType? keyboardType,
  Function(String)? onChanged,
  Function()? onEditingComplete,
  Function(String)? onFieldSubmitted,
  String? Function(String?)? validator,
  bool enabled = true,
  TextDirection textDirection = TextDirection.rtl,
  TextAlign textAlign = TextAlign.right,
  int maxLines = 1,
  Widget? suffixIcon,
  Widget? prefixIcon,
}) {
  return TextFormField(
    controller: textController,
    maxLength: maxLength,
    obscureText: obscureText,
    keyboardType: keyboardType,
    onChanged: onChanged,
    onEditingComplete: onEditingComplete,
    onFieldSubmitted: onFieldSubmitted,
    validator: validator,
    enabled: enabled,
    textDirection: textDirection,
    textAlign: textAlign,
    maxLines: maxLines,
    showCursor: true,
    cursorColor: AppColors.primary,
    style: TextStyle(
      color: AppColors.textPrimary,
      fontSize: controller.sized * 0.015,
      fontWeight: FontWeight.w500,
    ),
    decoration: InputDecoration(
      hintText: hintText,
      labelText: labelText,
      hintStyle: TextStyle(
        color: AppColors.textHint,
        fontSize: controller.sized * 0.014,
        fontWeight: FontWeight.normal,
      ),
      labelStyle: TextStyle(
        color: AppColors.textSecondary,
        fontSize: controller.sized * 0.014,
        fontWeight: FontWeight.w500,
      ),
      filled: true,
      fillColor: AppColors.surface,
      contentPadding: EdgeInsets.symmetric(
        horizontal: controller.sizedWidth * 0.04,
        vertical: controller.sizedHight * 0.015,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.border,
          width: 1.0,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.border,
          width: 1.0,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.primary,
          width: 2.0,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.error,
          width: 1.5,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.error,
          width: 2.0,
        ),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.textDisabled,
          width: 1.0,
        ),
      ),
      suffixIcon: suffixIcon,
      prefixIcon: prefixIcon,
    ),
  );
}

/// ويدجت مخصص لـ TextField يتناسب مع الوضعين الداكن والفاتح
Widget customTextField({
  required TextEditingController textController,
  String? hintText,
  bool obscureText = false,
  TextInputType? keyboardType,
  Function(String)? onChanged,
  Function()? onEditingComplete,
  Function(String)? onSubmitted,
  bool enabled = true,
  TextDirection textDirection = TextDirection.rtl,
  TextAlign textAlign = TextAlign.right,
  int maxLines = 1,
  int? maxLength,
  Widget? suffixIcon,
  Widget? prefixIcon,
}) {
  return TextField(
    controller: textController,
    obscureText: obscureText,
    keyboardType: keyboardType,
    onChanged: onChanged,
    onEditingComplete: onEditingComplete,
    onSubmitted: onSubmitted,
    enabled: enabled,
    textDirection: textDirection,
    textAlign: textAlign,
    maxLines: maxLines,
    maxLength: maxLength,
    showCursor: true,
    cursorColor: AppColors.primary,
    style: TextStyle(
      color: AppColors.textPrimary,
      fontSize: controller.sized * 0.015,
      fontWeight: FontWeight.w500,
    ),
    decoration: InputDecoration(
      hintText: hintText,
      hintStyle: TextStyle(
        color: AppColors.textHint,
        fontSize: controller.sized * 0.014,
        fontWeight: FontWeight.normal,
      ),
      filled: true,
      fillColor: AppColors.surface,
      contentPadding: EdgeInsets.symmetric(
        horizontal: controller.sizedWidth * 0.04,
        vertical: controller.sizedHight * 0.015,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.border,
          width: 1.0,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.border,
          width: 1.0,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.primary,
          width: 2.0,
        ),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.textDisabled,
          width: 1.0,
        ),
      ),
      suffixIcon: suffixIcon,
      prefixIcon: prefixIcon,
    ),
  );
}

/// ويدجت مبسط لحقل النص مع التنسيق الأساسي
Widget simpleTextFormField({
  required TextEditingController textController,
  String? hintText,
  int? maxLength,
  Function(String)? onChanged,
  Function()? onEditingComplete,
}) {
  return customTextFormField(
    textController: textController,
    hintText: hintText,
    maxLength: maxLength,
    onChanged: onChanged,
    onEditingComplete: onEditingComplete,
  );
}

/// ويدجت مبسط لحقل كلمة المرور
Widget passwordTextFormField({
  required TextEditingController textController,
  String? hintText,
  bool obscureText = true,
  Function(String)? onChanged,
  Function()? onEditingComplete,
  Widget? suffixIcon,
}) {
  return customTextFormField(
    textController: textController,
    hintText: hintText ?? 'كلمة المرور',
    obscureText: obscureText,
    onChanged: onChanged,
    onEditingComplete: onEditingComplete,
    suffixIcon: suffixIcon,
  );
}

// === مثال على الاستخدام ===
/*
// بدلاً من:
TextFormField(
  controller: editPriv,
  maxLength: 16,
  cursorColor: AppColors.textColor.withOpacity(0.3),
  textDirection: TextDirection.rtl,
  style: TextStyle(color: AppColors.textColor.withOpacity(0.6)),
  onChanged: (i) { privN = true; },
  decoration: InputDecoration(...),
)

// استخدم:
customTextFormField(
  textController: editPriv,
  hintText: 'اسم الغرفة',
  maxLength: 16,
  onChanged: (i) { privN = true; },
  onEditingComplete: () {
    // منطق التحقق هنا
  },
)

// أو للاستخدام السريع:
simpleTextFormField(
  textController: editPriv,
  hintText: 'اسم الغرفة',
  maxLength: 16,
  onChanged: (i) { privN = true; },
)
*/
