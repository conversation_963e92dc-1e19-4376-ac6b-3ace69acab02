


def editNumber(phrase):
    # تعديل وجمع الارقام
    STT = phrase.split(' ')
    k = 0
    for i in STT:
        if i != STT[-2] or (i == STT[-2] and k != len(STT)-2):
            if i.isnumeric() and int(i) < 10 and STT[k+1].isnumeric() and int(STT[k+1]) <100 and STT[k+1][1] == '0':
                phrase = phrase.replace(i+' '+STT[k+1], str(int(i)+int(STT[k+1])))
            elif i.isnumeric() and int(i) < 10 and STT[k+1] == 'و' and STT[k+2].isnumeric() and int(STT[k+2]) <100 and STT[k+2][1] == '0':
                phrase = phrase.replace(i+' و '+STT[k+2],str(int(i)+int(STT[k+2])))
        k+=1
    STT=phrase.split(' ')
    k=0
    hundreds=[]
    for i in STT:       
        if i == '100':
            if k > 0 and STT[k-1].isnumeric() and int(STT[k-1]) < 10:
                phrase = phrase.replace(STT[k-1]+' '+i,str(int(i)*int(STT[k-1])))
                hundreds.append(int(i)*int(STT[k-1]))
            else:
                hundreds.append(int(i))
        k+=1
    STT=phrase.split(' ')
    k=0
    for i in STT:
        if i != STT[-2] and i.isnumeric() and int(i) in hundreds:
            if STT[k+1].isnumeric() and int(STT[k+1]) < 100:
                phrase = phrase.replace(i+' '+STT[k+1], str(int(i)+int(STT[k+1])))
            elif STT[k+1] != STT[-2] and STT[k+1]=='و' and STT[k+2].isnumeric() and int(STT[k+2]) < 100 :
                phrase = phrase.replace(i+' و '+STT[k+2], str(int(i)+int(STT[k+2])))
        k+=1
    STT=phrase.split(' ')
    k=0
    thousands=[]
    for i in STT:
        if i =='1000':
            if k > 0 and STT[k-1].isnumeric() and int(STT[k-1]) < 1000:
                phrase = phrase.replace(STT[k-1]+' '+i,str(int(i)*int(STT[k-1])))
                thousands.append(int(i)*int(STT[k-1]))
            else:
                thousands.append(int(i))
        k+=1
    STT=phrase.split(' ')
    k=0
    for i in STT:
        if i != STT[-2] and i.isnumeric() and int(i) in thousands:
            if STT[k+1].isnumeric() and int(STT[k+1]) < 1000:
                phrase = phrase.replace(i+' '+STT[k+1], str(int(i)+int(STT[k+1])))
            elif STT[k+1] != STT[-2] and STT[k+1]=='و' and STT[k+2].isnumeric() and int(STT[k+2]) < 1000 :
                phrase = phrase.replace(i+' و '+STT[k+2], str(int(i)+int(STT[k+2])))
        k+=1
    
    STT=phrase.split(' ')
    k=0
    millions=[]
    for i in STT:
        if i =='1000000':
            if k > 0 and STT[k-1].isnumeric() and int(STT[k-1]) < 1000:
                phrase = phrase.replace(STT[k-1]+' '+i,str(int(i)*int(STT[k-1])))
                millions.append(int(i)*int(STT[k-1]))
            else:
                millions.append(int(i))
        k+=1
    STT=phrase.split(' ')
    k=0
    for i in STT:
        if i != STT[-2] and i.isnumeric() and int(i) in millions:
            if STT[k+1].isnumeric() and int(STT[k+1]) < 1000000:
                phrase = phrase.replace(i+' '+STT[k+1], str(int(i)+int(STT[k+1])))
            elif STT[k+1] != STT[-2] and STT[k+1]=='و' and STT[k+2].isnumeric() and int(STT[k+2]) < 1000000 :
                phrase = phrase.replace(i+' و '+STT[k+2], str(int(i)+int(STT[k+2])))
        k+=1
    return phrase
