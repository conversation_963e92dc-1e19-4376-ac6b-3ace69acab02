import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/services/notification_service.dart';
import 'package:zaen/shared/themes/app_colors.dart';

/// صفحة إعدادات الإشعارات والتوصيات الذكية
class NotificationsSettingsPage extends StatelessWidget {
  const NotificationsSettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final HomeController controller = Get.find();
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        title: Text(
          'الاشعارات',
          style: TextStyle(
            color: AppColors.textColor,
            fontSize: controller.sized * 0.016,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.backgroundColor2.withOpacity(0.8),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: AppColors.textColor,
            size: controller.sized * 0.02,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: Obx(
        () {
          final notificationService = NotificationService.instance;
          return Directionality(
            textDirection: TextDirection.rtl,
            child: SingleChildScrollView(
              padding: EdgeInsets.all(controller.sized * 0.01),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // قسم الإشعارات العامة
                  _buildSectionHeader('🔔 الإشعارات العامة'),
                  SizedBox(height: controller.sizedHight * 0.01),
                  _buildSettingCard(
                    title: 'إشعارات الطقس',
                    subtitle: 'تلقي إشعارات عند تغير حالة الطقس',
                    icon: Icons.wb_sunny,
                    value: notificationService.weatherNotificationsEnabled,
                    onChanged: (value) {
                      notificationService.updateSettings(
                        weatherNotifications: value,
                      );
                    },
                  ),

                  _buildSettingCard(
                    title: 'تنبيهات الطقس المتطرف',
                    subtitle:
                        'تحذيرات عند الطقس الخطير (حر شديد، برد، رياح قوية)',
                    icon: Icons.warning,
                    iconColor: AppColors.errorColor,
                    value: notificationService.extremeWeatherAlerts,
                    onChanged: (value) {
                      notificationService.updateSettings(
                        extremeWeatherAlerts: value,
                      );
                    },
                  ),

                  SizedBox(height: controller.sizedHight * 0.02),

                  // قسم التوصيات الذكية
                  _buildSectionHeader('🤖 التوصيات الذكية'),
                  SizedBox(height: controller.sizedHight * 0.01),
                  _buildSettingCard(
                    title: 'الاقتراحات الذكية',
                    subtitle: 'توصيات مخصصة بناءً على حالة الطقس والوقت',
                    icon: Icons.lightbulb,
                    iconColor: Colors.amber,
                    value: notificationService.smartSuggestionsEnabled,
                    onChanged: (value) {
                      notificationService.updateSettings(
                        smartSuggestions: value,
                      );
                    },
                  ),

                  _buildSettingCard(
                    title: 'اقتراحات أتمتة الأجهزة',
                    subtitle: 'توصيات لتشغيل/إيقاف الأجهزة حسب الطقس',
                    icon: Icons.home_outlined,
                    iconColor: Colors.green,
                    value: notificationService.deviceAutomationSuggestions,
                    onChanged: (value) {
                      notificationService.updateSettings(
                        deviceAutomationSuggestions: value,
                      );
                    },
                  ),

                  SizedBox(height: controller.sizedHight * 0.02),

                  // قسم الإشعارات النشطة
                  _buildSectionHeader('📋 الإشعارات النشطة'),
                  SizedBox(height: controller.sizedHight * 0.01),
                  _buildActiveNotifications(notificationService),

                  SizedBox(height: controller.sizedHight * 0.01),

                  // زر مسح الإشعارات
                  _buildClearButton(notificationService),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 15,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  Widget _buildSettingCard({
    required String title,
    required String subtitle,
    required IconData icon,
    Color? iconColor,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: (iconColor ?? Colors.blue).withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: iconColor ?? Colors.blue,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: value,
              onChanged: onChanged,
              activeColor: Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveNotifications(NotificationService service) {
    if (service.activeNotifications.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Center(
            child: Column(
              children: [
                Icon(
                  Icons.notifications_off,
                  size: 48,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 12),
                Text(
                  'لا توجد إشعارات نشطة',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Column(
      children: service.activeNotifications.take(5).map((notification) {
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getNotificationTypeColor(notification.type),
              child: Icon(
                _getNotificationTypeIcon(notification.type),
                color: Colors.white,
                size: 20,
              ),
            ),
            title: Text(
              notification.title,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(notification.body),
                const SizedBox(height: 4),
                Text(
                  _formatTime(notification.timestamp),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade500,
                  ),
                ),
              ],
            ),
            trailing: IconButton(
              icon: const Icon(Icons.close, size: 20),
              onPressed: () {
                service.clearNotification(notification.id);
              },
            ),
            isThreeLine: true,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildClearButton(NotificationService service) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () async {
          await service.clearAllNotifications();
          Get.snackbar(
            'تم المسح',
            'تم مسح جميع الإشعارات',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
        },
        icon: const Icon(Icons.clear_all),
        label: const Text('مسح جميع الإشعارات'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Color _getNotificationTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.weatherChange:
        return Colors.blue;
      case NotificationType.extremeWeather:
        return Colors.red;
      case NotificationType.smartSuggestion:
        return Colors.green;
      case NotificationType.weatherAlert:
        return Colors.orange;
      case NotificationType.automationNotification:
        return Colors.purple;
    }
  }

  IconData _getNotificationTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.weatherChange:
        return Icons.wb_sunny;
      case NotificationType.extremeWeather:
        return Icons.warning;
      case NotificationType.smartSuggestion:
        return Icons.lightbulb;
      case NotificationType.weatherAlert:
        return Icons.info;
      case NotificationType.automationNotification:
        return Icons.smart_toy;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
}
