#include <tunables/global>

profile dnsmasq flags=(attach_disconnected,mediate_deleted) {
  #include <abstractions/base>

  file,
  signal,

  # S6-Overlay & Bashio
  /init rix,
  /bin/** ix,
  /usr/bin/** ix,
  /etc/s6/** rix,
  /run/s6/** rwix,
  /etc/services.d/** rwix,
  /etc/cont-init.d/** rwix,
  /etc/cont-finish.d/** rwix,
  /run/** rwk,

  # Bashio
  /usr/lib/bashio/** ix,
  /tmp/** rw,

  # Access to Options.json and other files within your addon
  /data/** rw,

  # Execution program
  /usr/sbin/dnsmasq cx,

  profile /usr/sbin/dnsmasq flags=(attach_disconnected,mediate_deleted) {
    #include <abstractions/base>
    #include <abstractions/nameservice>

    signal receive,

    capability net_bind_service,
    capability setgid,
    capability setuid,
    capability dac_override,
    network inet raw,
    network inet6 raw,

    /usr/sbin/dnsmasq rm,

    /etc/dnsmasq.conf rw,
    /run/*dnsmasq*.pid w,
    /run/dnsmasq/** rw,
    /tmp/* rw,
    /proc/* r,
  }
}
