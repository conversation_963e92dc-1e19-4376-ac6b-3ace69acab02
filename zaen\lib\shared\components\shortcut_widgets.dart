import 'package:expansion_tile_card/expansion_tile_card.dart';
import 'package:flutter/material.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/components/config.dart';
import 'text_icon_widgets.dart';
import 'button_widgets.dart';

Widget shortCutStyle(
    {required bool connect,
    required String PrivName,
    required String type,
    required Function() doubleTap,
    required Function() tapOnIcon,
    required List<Widget> content,
    bool? deviceState,
    required Function(bool?) switchState}) {
  return GestureDetector(
    onDoubleTap: doubleTap,
    child: Container(
        margin: EdgeInsets.symmetric(
            horizontal: controller.sizedWidth * 0.055,
            vertical: controller.sizedHight * 0.0075),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(30)),
          color: AppColors.surfaceColor.withOpacity(0.75),
        ),
        child: Directionality(
          textDirection: TextDirection.rtl,
          child: ExpansionTileCard(
              duration: Duration(milliseconds: 400),
              baseColor: Colors.transparent,
              expandedColor: Colors.transparent,
              shadowColor: Colors.transparent,
              borderRadius: BorderRadius.all(Radius.circular(30)),
              contentPadding: EdgeInsets.symmetric(
                  horizontal: controller.sizedWidth * 0.01),
              trailing: connect == false
                  ? Padding(
                      padding: EdgeInsets.all(controller.sized * 0.01),
                      child: txtStyle(
                          txt: 'غير متصل',
                          color: AppColors.errorColor,
                          size: controller.sized * 0.012))
                  : Padding(
                      padding: EdgeInsets.all(controller.sized * 0.0035),
                      child: switchStyle(
                          value: deviceState!, onChanged: switchState),
                    ),
              title: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  IconButton(
                    onPressed: tapOnIcon,
                    icon: iconStyle(
                        icon: type == 'مكيف'
                            ? Icons.ac_unit_rounded
                            : type == 'تلفاز'
                                ? Icons.tv_rounded
                                : Icons.power_outlined,
                        color: AppColors.warningColor),
                  ),
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.zero,
                      // padding:
                      //     EdgeInsets.only(right: controller.sizedWidth * 0.01),
                      // color: Colors.blueGrey.shade600,
                      // alignment: Alignment.bottomRight,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Container(
                            padding: EdgeInsets.only(
                              left: controller.sizedWidth * 0.01,
                            ),
                            decoration: BoxDecoration(
                                border: Border(
                                    left: BorderSide(
                                        color: AppColors.textColor3
                                            .withOpacity(0.45),
                                        width: 1))),
                            child: txtStyle(
                              align: TextAlign.right,
                              txt: type,
                            ),
                          ),
                          Container(
                              width: controller.sizedWidth * 0.32,
                              padding: EdgeInsets.only(
                                  right: controller.sizedWidth * 0.01),
                              child: txtStyle(
                                align: TextAlign.right,
                                txt: PrivName == 'x'
                                    ? 'لا يوجد اسم'
                                    : PrivName.length > 9
                                        ? PrivName.substring(0, 9) + '...'
                                        : PrivName,
                              )),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              children: connect == true ? content : []),
        )),
  );
}
