---
version: 1.8.1
slug: dnsmasq
name: Dnsmasq
description: A simple DNS server
url: https://github.com/home-assistant/addons/tree/master/dnsmasq
advanced: true
arch:
  - armhf
  - armv7
  - aarch64
  - amd64
  - i386
image: homeassistant/{arch}-addon-dnsmasq
init: false
options:
  defaults:
    - *******
    - *******
  forwards: []
  hosts: []
  services: []
  cnames: []
  log_queries: false
  cache_size: 150
ports:
  53/tcp: 53
  53/udp: 53
schema:
  defaults:
    - str
  forwards:
    - domain: str
      server: str
  hosts:
    - host: str
      ip: str
  services:
    - host: str
      port: str
      priority: int
      srv: str
      weight: int
  cnames:
    - name: str
      target: str
  log_queries: bool
  cache_size: int
startup: system
