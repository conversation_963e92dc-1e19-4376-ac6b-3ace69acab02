import mysql.connector
import time
import sys

sys.path.append('/home/<USER>/myProject/resources')
import static as st

class DatabaseHelper:
    def __init__(self):
        self.connection_pool = []
        self.max_connections = 5
    
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات مع إعادة المحاولة"""
        while True:
            try:
                return mysql.connector.connect(
                    user='root',
                    host=st.ip,
                    passwd='zain',
                    database='zain',
                    autocommit=False
                )
            except mysql.connector.Error as e:
                print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
                time.sleep(5)
                continue
            except Exception as e:
                print(f"خطأ غير متوقع في الاتصال: {e}")
                time.sleep(5)
                continue
    
    def execute_query(self, query, params=None, fetch=False):
        """تنفيذ استعلام مع معالجة الأخطاء"""
        connection = None
        cursor = None
        result = None
        
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if fetch:
                if 'SELECT' in query.upper():
                    result = cursor.fetchall() if 'fetchall' in str(fetch) else cursor.fetchone()
                
            connection.commit()
            return result
            
        except mysql.connector.Error as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            if connection:
                connection.rollback()
            return None
        except Exception as e:
            print(f"خطأ غير متوقع: {e}")
            if connection:
                connection.rollback()
            return None
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    
    def get_device_info(self, device_id):
        """الحصول على معلومات الجهاز"""
        query = "SELECT * FROM Devices WHERE id = %s"
        return self.execute_query(query, (device_id,), fetch='one')
    
    def get_all_devices(self):
        """الحصول على جميع الأجهزة"""
        query = "SELECT * FROM Devices"
        return self.execute_query(query, fetch='all')
    
    def update_device_state(self, device_id, state):
        """تحديث حالة الجهاز"""
        query = "UPDATE Devices SET state = %s WHERE id = %s"
        return self.execute_query(query, (state, device_id))
    
    def update_device_connection(self, device_id, connection_time):
        """تحديث وقت اتصال الجهاز"""
        query = "UPDATE Devices SET connect = %s WHERE id = %s"
        return self.execute_query(query, (connection_time, device_id))
    
    def add_new_device(self, device_id, device_type, room, connect_time, state):
        """إضافة جهاز جديد"""
        query = "INSERT INTO Devices(id, Type, Rooms, connect, state) VALUES(%s, %s, %s, %s, %s)"
        return self.execute_query(query, (device_id, device_type, room, connect_time, state))
    
    def get_phone_info(self, mac_address):
        """الحصول على معلومات الهاتف"""
        query = "SELECT * FROM phones WHERE mac = %s"
        return self.execute_query(query, (mac_address,), fetch='one')
    
    def get_all_phones(self):
        """الحصول على جميع الهواتف"""
        query = "SELECT * FROM phones"
        return self.execute_query(query, fetch='all')
    
    def add_new_phone(self, name, phone_type, mac, access):
        """إضافة هاتف جديد"""
        query = "INSERT INTO phones(name, type, mac, access) VALUES(%s, %s, %s, %s)"
        return self.execute_query(query, (name, phone_type, mac, access))
    
    def get_device_room(self, device_id):
        """الحصول على غرفة الجهاز"""
        query = "SELECT Rooms FROM Devices WHERE id = %s"
        result = self.execute_query(query, (device_id,), fetch='one')
        return result[0] if result else None
    
    def create_device_tables(self, device_id, device_type):
        """إنشاء الجداول المطلوبة للجهاز الجديد"""
        connection = None
        cursor = None
        
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            # إضافة عمود للجهاز في جدول NDevice
            if 'SWITCH' not in device_type:
                try:
                    cursor.execute(f"ALTER TABLE NDevice ADD COLUMN {device_id} VARCHAR(30)")
                    connection.commit()
                except mysql.connector.Error as e:
                    if "Duplicate column name" not in str(e):
                        print(f"خطأ في إضافة العمود: {e}")
            
            # إنشاء جدول للتلفزيون
            if 'TV' in device_type:
                cursor.execute(f"CREATE TABLE IF NOT EXISTS {device_id}_TV (chaneel VARCHAR(255), number VARCHAR(255))")
                connection.commit()
            
            # إنشاء جدول للمفاتيح
            if 'SWITCH' in device_type:
                switch_count = int(device_type.split('v')[1])
                
                # إنشاء الجدول
                columns = 'id INT AUTO_INCREMENT PRIMARY KEY, '
                for i in range(1, switch_count + 1):
                    columns += f'v{i} VARCHAR(255), '
                columns = columns.rstrip(', ')
                
                cursor.execute(f"CREATE TABLE IF NOT EXISTS {device_id}_SWITCH ({columns})")
                connection.commit()
                
                # إدراج البيانات الأولية
                values = ', '.join(["'SWITCH'"] * switch_count)
                columns_list = ', '.join([f'v{i}' for i in range(1, switch_count + 1)])
                
                cursor.execute(f"INSERT INTO {device_id}_SWITCH({columns_list}) VALUES({values})")
                connection.commit()
                
                # إدراج صف فارغ
                cursor.execute(f"INSERT INTO {device_id}_SWITCH(v1) VALUES(NULL)")
                connection.commit()
            
            return True
            
        except mysql.connector.Error as e:
            print(f"خطأ في إنشاء جداول الجهاز: {e}")
            if connection:
                connection.rollback()
            return False
        except Exception as e:
            print(f"خطأ غير متوقع في إنشاء الجداول: {e}")
            if connection:
                connection.rollback()
            return False
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    
    def get_tv_channels(self, device_id):
        """الحصول على قنوات التلفزيون"""
        query = f"SELECT * FROM {device_id}_TV"
        return self.execute_query(query, fetch='all')
    
    def get_switch_config(self, device_id):
        """الحصول على تكوين المفاتيح"""
        connection = None
        cursor = None
        result = None
        
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)
            cursor.execute(f"SELECT * FROM {device_id}_SWITCH WHERE id=1")
            result = cursor.fetchone()
            return result
            
        except mysql.connector.Error as e:
            print(f"خطأ في الحصول على تكوين المفاتيح: {e}")
            return None
        except Exception as e:
            print(f"خطأ غير متوقع: {e}")
            return None
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    
    def check_device_connection_status(self, device_id):
        """فحص حالة اتصال الجهاز"""
        from datetime import datetime
        
        query = "SELECT rooms, connect FROM Devices WHERE id = %s"
        result = self.execute_query(query, (device_id,), fetch='one')
        
        if not result:
            return None, 'غير متصل'
        
        room, connect_time = result
        
        if connect_time and '-' in connect_time:
            try:
                time_parts = connect_time.split('-')
                current_time = datetime.now()
                
                if (str(current_time.year) + str(current_time.month) + 
                    str(current_time.day) + str(current_time.hour) == 
                    str(time_parts[0]) + str(time_parts[1]) + 
                    str(time_parts[2]) + str(time_parts[3]) and 
                    current_time.minute - int(time_parts[4]) == 0):
                    return room, 'متصل'
                else:
                    return room, 'غير متصل'
            except (IndexError, ValueError):
                return room, 'غير متصل'
        else:
            return room, 'غير متصل'
    
    def get_devices_by_type(self, device_type):
        """الحصول على الأجهزة حسب النوع"""
        query = "SELECT id, type FROM Devices WHERE type = %s"
        return self.execute_query(query, (device_type,), fetch='all')
    
    def close_all_connections(self):
        """إغلاق جميع الاتصالات"""
        for connection in self.connection_pool:
            try:
                if connection.is_connected():
                    connection.close()
            except:
                pass
        self.connection_pool.clear()
